<?php

namespace App\Services;

use App\Services\Payment\Exceptions\WebhookVerificationException;
use App\Services\Payment\Exceptions\PaymentGatewayException;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Exception;

class WebhookService
{
    public function __construct(
        private PaymentGatewayFactory $gatewayFactory,
        private TransactionService $transactionService
    ) {}

    /**
     * Process webhook from a payment gateway
     *
     * @param string $gateway Gateway name
     * @param array $payload Webhook payload
     * @param array $headers Request headers
     * @return array Processing result
     * @throws WebhookVerificationException
     */
    public function processWebhook(string $gateway, array $payload, array $headers = []): array
    {
        $webhookId = $this->generateWebhookId();
        
        try {
            Log::info('Webhook received', [
                'webhook_id' => $webhookId,
                'gateway' => $gateway,
                'payload_size' => count($payload)
            ]);

            // Validate gateway
            if (!$this->gatewayFactory->isSupported($gateway)) {
                throw new WebhookVerificationException("Unsupported gateway: $gateway");
            }

            // Get gateway service
            $gatewayService = $this->gatewayFactory->create($gateway);

            // Process webhook through gateway service
            $webhookData = $gatewayService->handleWebhook($payload);

            // Update transaction based on webhook data
            $result = $this->updateTransactionFromWebhook($webhookData, $gateway);

            Log::info('Webhook processed successfully', [
                'webhook_id' => $webhookId,
                'gateway' => $gateway,
                'transaction_id' => $webhookData['transaction_id'] ?? 'unknown',
                'status' => $webhookData['status'] ?? 'unknown'
            ]);

            return [
                'success' => true,
                'webhook_id' => $webhookId,
                'gateway' => $gateway,
                'transaction_updated' => $result['transaction_updated'] ?? false,
                'transaction_id' => $webhookData['transaction_id'] ?? null,
                'status' => $webhookData['status'] ?? null,
                'message' => 'Webhook processed successfully'
            ];

        } catch (WebhookVerificationException $e) {
            Log::warning('Webhook verification failed', [
                'webhook_id' => $webhookId,
                'gateway' => $gateway,
                'error' => $e->getMessage()
            ]);

            throw $e;

        } catch (Exception $e) {
            Log::error('Webhook processing failed', [
                'webhook_id' => $webhookId,
                'gateway' => $gateway,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // Try to retry webhook processing
            $retryResult = $this->retryWebhookProcessing($gateway, $payload, $headers, $webhookId);
            
            if ($retryResult['success']) {
                return $retryResult;
            }

            throw new WebhookVerificationException(
                'Webhook processing failed: ' . $e->getMessage(),
                'WEBHOOK_PROCESSING_FAILED',
                ['original_error' => $e->getMessage(), 'webhook_id' => $webhookId]
            );
        }
    }

    /**
     * Update transaction based on webhook data
     *
     * @param array $webhookData
     * @param string $gateway
     * @return array
     */
    private function updateTransactionFromWebhook(array $webhookData, string $gateway): array
    {
        if (!isset($webhookData['transaction_id'])) {
            Log::warning('Webhook data missing transaction_id', [
                'gateway' => $gateway,
                'webhook_data' => $webhookData
            ]);
            return ['transaction_updated' => false, 'reason' => 'Missing transaction_id'];
        }

        try {
            DB::beginTransaction();

            // Find transaction by internal transaction ID or gateway transaction ID
            $transaction = $this->findTransactionFromWebhook($webhookData, $gateway);

            if (!$transaction) {
                Log::warning('Transaction not found for webhook', [
                    'gateway' => $gateway,
                    'transaction_id' => $webhookData['transaction_id'],
                    'gateway_transaction_id' => $webhookData['gateway_transaction_id'] ?? null
                ]);
                DB::rollBack();
                return ['transaction_updated' => false, 'reason' => 'Transaction not found'];
            }

            // Prepare update data
            $updateData = [
                'webhook_verified' => true
            ];

            // Update status if provided
            if (isset($webhookData['status'])) {
                $newStatus = $this->mapWebhookStatusToTransactionStatus($webhookData['status'], $gateway);
                $updateData['status'] = $newStatus;
                
                // Set completed_at timestamp when status is changed to completed
                if ($newStatus === \App\Models\Transaction::STATUS_COMPLETED && $transaction->status !== \App\Models\Transaction::STATUS_COMPLETED) {
                    $updateData['completed_at'] = now();
                }
            }

            // Update gateway transaction ID if provided
            if (isset($webhookData['gateway_transaction_id']) && !$transaction->gateway_transaction_id) {
                $updateData['gateway_transaction_id'] = $webhookData['gateway_transaction_id'];
            }

            // Update payment details with webhook data
            $paymentDetails = $transaction->payment_details ?? [];
            $paymentDetails['webhook_data'] = $webhookData;
            $paymentDetails['webhook_processed_at'] = now();
            $updateData['payment_details'] = $paymentDetails;

            // Update failure reason if payment failed
            if (isset($webhookData['failure_reason'])) {
                $updateData['failure_reason'] = $webhookData['failure_reason'];
            }

            // Update transaction
            $updated = $this->transactionService->updateTransaction($transaction->id, $updateData);

            DB::commit();

            Log::info('Transaction updated from webhook', [
                'transaction_id' => $transaction->transaction_id,
                'gateway' => $gateway,
                'old_status' => $transaction->status,
                'new_status' => $updateData['status'] ?? $transaction->status
            ]);

            return [
                'transaction_updated' => $updated,
                'transaction' => $transaction->fresh(),
                'update_data' => $updateData
            ];

        } catch (Exception $e) {
            DB::rollBack();
            
            Log::error('Failed to update transaction from webhook', [
                'gateway' => $gateway,
                'transaction_id' => $webhookData['transaction_id'] ?? 'unknown',
                'error' => $e->getMessage()
            ]);

            throw $e;
        }
    }

    /**
     * Find transaction from webhook data
     *
     * @param array $webhookData
     * @param string $gateway
     * @return \App\Models\Transaction|null
     */
    private function findTransactionFromWebhook(array $webhookData, string $gateway): ?\App\Models\Transaction
    {
        // Try to find by internal transaction ID first
        if (isset($webhookData['transaction_id'])) {
            $transaction = $this->transactionService->getTransactionByTransactionId($webhookData['transaction_id']);
            if ($transaction) {
                return $transaction;
            }
        }

        // Try to find by gateway transaction ID
        if (isset($webhookData['gateway_transaction_id'])) {
            $transaction = $this->transactionService->getTransactionByGatewayTransactionId(
                $webhookData['gateway_transaction_id'], 
                $gateway
            );
            if ($transaction) {
                return $transaction;
            }
        }

        return null;
    }

    /**
     * Map webhook status to transaction status
     *
     * @param string $webhookStatus
     * @param string $gateway
     * @return string
     */
    private function mapWebhookStatusToTransactionStatus(string $webhookStatus, string $gateway): string
    {
        // Common status mappings
        $commonMappings = [
            'success' => \App\Models\Transaction::STATUS_COMPLETED,
            'completed' => \App\Models\Transaction::STATUS_COMPLETED,
            'paid' => \App\Models\Transaction::STATUS_COMPLETED,
            'failed' => \App\Models\Transaction::STATUS_FAILED,
            'failure' => \App\Models\Transaction::STATUS_FAILED,
            'cancelled' => \App\Models\Transaction::STATUS_CANCELLED,
            'canceled' => \App\Models\Transaction::STATUS_CANCELLED,
            'pending' => \App\Models\Transaction::STATUS_PENDING,
            'processing' => \App\Models\Transaction::STATUS_PROCESSING,
        ];

        // Gateway-specific mappings
        $gatewayMappings = [
            'razorpay' => [
                'captured' => \App\Models\Transaction::STATUS_COMPLETED,
                'authorized' => \App\Models\Transaction::STATUS_PROCESSING,
                'refunded' => \App\Models\Transaction::STATUS_CANCELLED,
            ],
            'payumoney' => [
                'success' => \App\Models\Transaction::STATUS_COMPLETED,
                'failure' => \App\Models\Transaction::STATUS_FAILED,
                'pending' => \App\Models\Transaction::STATUS_PENDING,
            ],
            'cashfree' => [
                'SUCCESS' => \App\Models\Transaction::STATUS_COMPLETED,
                'FAILED' => \App\Models\Transaction::STATUS_FAILED,
                'CANCELLED' => \App\Models\Transaction::STATUS_CANCELLED,
                'PENDING' => \App\Models\Transaction::STATUS_PENDING,
            ]
        ];

        $status = strtolower($webhookStatus);

        // Check gateway-specific mappings first
        if (isset($gatewayMappings[$gateway][$webhookStatus])) {
            return $gatewayMappings[$gateway][$webhookStatus];
        }

        // Check common mappings
        if (isset($commonMappings[$status])) {
            return $commonMappings[$status];
        }

        // Default to current status if mapping not found
        Log::warning('Unknown webhook status', [
            'gateway' => $gateway,
            'webhook_status' => $webhookStatus
        ]);

        return \App\Models\Transaction::STATUS_PROCESSING;
    }

    /**
     * Retry webhook processing
     *
     * @param string $gateway
     * @param array $payload
     * @param array $headers
     * @param string $webhookId
     * @return array
     */
    private function retryWebhookProcessing(string $gateway, array $payload, array $headers, string $webhookId): array
    {
        $maxRetries = config('payment.webhook.max_retries', 3);
        $retryDelay = config('payment.webhook.retry_delay', 5); // seconds

        for ($attempt = 1; $attempt <= $maxRetries; $attempt++) {
            try {
                Log::info('Retrying webhook processing', [
                    'webhook_id' => $webhookId,
                    'gateway' => $gateway,
                    'attempt' => $attempt,
                    'max_retries' => $maxRetries
                ]);

                sleep($retryDelay * $attempt); // Exponential backoff

                $gatewayService = $this->gatewayFactory->create($gateway);
                $webhookData = $gatewayService->handleWebhook($payload);
                $result = $this->updateTransactionFromWebhook($webhookData, $gateway);

                Log::info('Webhook retry successful', [
                    'webhook_id' => $webhookId,
                    'gateway' => $gateway,
                    'attempt' => $attempt
                ]);

                return [
                    'success' => true,
                    'webhook_id' => $webhookId,
                    'gateway' => $gateway,
                    'transaction_updated' => $result['transaction_updated'] ?? false,
                    'transaction_id' => $webhookData['transaction_id'] ?? null,
                    'status' => $webhookData['status'] ?? null,
                    'message' => "Webhook processed successfully on retry attempt $attempt",
                    'retry_attempt' => $attempt
                ];

            } catch (Exception $e) {
                Log::warning('Webhook retry failed', [
                    'webhook_id' => $webhookId,
                    'gateway' => $gateway,
                    'attempt' => $attempt,
                    'error' => $e->getMessage()
                ]);

                if ($attempt === $maxRetries) {
                    Log::error('All webhook retry attempts failed', [
                        'webhook_id' => $webhookId,
                        'gateway' => $gateway,
                        'max_retries' => $maxRetries
                    ]);
                }
            }
        }

        return [
            'success' => false,
            'webhook_id' => $webhookId,
            'message' => 'All retry attempts failed'
        ];
    }

    /**
     * Validate webhook signature (if supported by gateway)
     *
     * @param string $gateway
     * @param array $payload
     * @param array $headers
     * @return bool
     */
    public function validateWebhookSignature(string $gateway, array $payload, array $headers): bool
    {
        try {
            $gatewayService = $this->gatewayFactory->create($gateway);
            
            // This would be implemented in each gateway service
            // For now, we'll assume the gateway service handles verification in handleWebhook
            return true;

        } catch (Exception $e) {
            Log::error('Webhook signature validation failed', [
                'gateway' => $gateway,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Get webhook processing statistics
     *
     * @param array $filters
     * @return array
     */
    public function getWebhookStatistics(array $filters = []): array
    {
        // This would require a webhooks table to track webhook processing
        // For now, return basic stats from transaction updates
        return [
            'total_webhooks_processed' => 0, // Would be tracked in webhooks table
            'successful_webhooks' => 0,
            'failed_webhooks' => 0,
            'webhook_verified_transactions' => $this->transactionService->getTransactions(['webhook_verified' => true])->total(),
        ];
    }

    /**
     * Generate unique webhook ID for tracking
     *
     * @return string
     */
    private function generateWebhookId(): string
    {
        return 'WHK_' . strtoupper(uniqid()) . '_' . time();
    }

    /**
     * Log webhook for debugging
     *
     * @param string $gateway
     * @param array $payload
     * @param array $headers
     * @param string $status
     * @param string|null $error
     * @return void
     */
    public function logWebhook(string $gateway, array $payload, array $headers, string $status, ?string $error = null): void
    {
        $logData = [
            'gateway' => $gateway,
            'status' => $status,
            'payload_size' => count($payload),
            'headers' => array_keys($headers),
            'timestamp' => now()
        ];

        if ($error) {
            $logData['error'] = $error;
        }

        // Log sensitive data only in debug mode
        if (config('app.debug')) {
            $logData['payload'] = $payload;
            $logData['headers_full'] = $headers;
        }

        Log::info('Webhook logged', $logData);
    }
}