<?php

namespace App\Services;

use App\Models\Build;
use App\Models\Component;
use App\Models\ComponentCategory;
use App\Models\User;

class BuilderService
{
    /**
     * Create a new build.
     */
    public function createBuild(array $data, ?User $user = null): Build
    {
        $buildData = [
            'name' => $data['name'],
            'description' => $data['description'] ?? null,
            'is_public' => $data['is_public'] ?? false,
            'user_id' => $user?->id,
        ];
        
        $build = Build::create($buildData);
        
        // Add components if provided
        if (isset($data['components']) && is_array($data['components'])) {
            foreach ($data['components'] as $componentData) {
                if (isset($componentData['component_id'])) {
                    $component = Component::find($componentData['component_id']);
                    if ($component) {
                        $quantity = $componentData['quantity'] ?? 1;
                        $price = $componentData['price'] ?? $component->price;
                        $build->addComponent($component, $quantity, $price);
                    }
                }
            }
        }
        
        // Calculate total price and check if build is complete
        $build->calculateTotalPrice();
        $build->checkIfComplete();
        
        return $build;
    }
    
    /**
     * Update an existing build.
     */
    public function updateBuild(Build $build, array $data): Build
    {
        // Update build details
        if (isset($data['name'])) {
            $build->name = $data['name'];
        }
        
        if (isset($data['description'])) {
            $build->description = $data['description'];
        }
        
        if (isset($data['is_public'])) {
            $build->is_public = $data['is_public'];
        }
        
        $build->save();
        
        // Update components if provided
        if (isset($data['components']) && is_array($data['components'])) {
            // Remove existing components if replace_all is true
            if (isset($data['replace_all']) && $data['replace_all']) {
                $build->components()->delete();
            }
            
            foreach ($data['components'] as $componentData) {
                if (isset($componentData['component_id'])) {
                    $component = Component::find($componentData['component_id']);
                    if ($component) {
                        $quantity = $componentData['quantity'] ?? 1;
                        $price = $componentData['price'] ?? $component->price;
                        
                        // Check if component already exists in build
                        $existingComponent = $build->components()
                            ->where('component_id', $component->id)
                            ->first();
                        
                        if ($existingComponent) {
                            // Update existing component
                            $existingComponent->quantity = $quantity;
                            $existingComponent->price = $price;
                            $existingComponent->save();
                        } else {
                            // Add new component
                            $build->addComponent($component, $quantity, $price);
                        }
                    }
                }
            }
        }
        
        // Calculate total price and check if build is complete
        $build->calculateTotalPrice();
        $build->checkIfComplete();
        
        return $build;
    }
    
    /**
     * Get components by category.
     */
    public function getComponentsByCategory(string $categorySlug, array $filters = [])
    {
        $category = ComponentCategory::where('slug', $categorySlug)->first();
        
        if (!$category) {
            return collect();
        }
        
        $query = Component::where('category_id', $category->id)
            ->where('is_active', true);
        
        // Apply filters
        if (isset($filters['search']) && !empty($filters['search'])) {
            $query->where(function ($q) use ($filters) {
                $q->where('name', 'like', '%' . $filters['search'] . '%')
                  ->orWhere('brand', 'like', '%' . $filters['search'] . '%')
                  ->orWhere('model', 'like', '%' . $filters['search'] . '%')
                  ->orWhere('description', 'like', '%' . $filters['search'] . '%');
            });
        }
        
        if (isset($filters['brand']) && !empty($filters['brand'])) {
            if (is_array($filters['brand'])) {
                $query->whereIn('brand', $filters['brand']);
            } else {
                $query->where('brand', $filters['brand']);
            }
        }
        
        if (isset($filters['price_min']) && is_numeric($filters['price_min'])) {
            $query->where('price', '>=', $filters['price_min']);
        }
        
        if (isset($filters['price_max']) && is_numeric($filters['price_max'])) {
            $query->where('price', '<=', $filters['price_max']);
        }
        
        if (isset($filters['in_stock']) && $filters['in_stock']) {
            $query->where('stock', '>', 0);
        }
        
        // Sort results
        $sortBy = $filters['sort_by'] ?? 'price';
        $sortDir = $filters['sort_dir'] ?? 'asc';
        
        $query->orderBy($sortBy, $sortDir);
        
        return $query->get();
    }
    
    /**
     * Get all component categories.
     */
    public function getComponentCategories(bool $onlyRequired = false)
    {
        $query = ComponentCategory::ordered();
        
        if ($onlyRequired) {
            $query->required();
        }
        
        return $query->get();
    }
    
    /**
     * Get popular builds.
     */
    public function getPopularBuilds(int $limit = 5)
    {
        return Build::where('is_public', true)
            ->where('is_complete', true)
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * Validate build compatibility.
     */
    public function validateBuild(Build $build): \App\Services\ValidationResult
    {
        $compatibilityService = app(\App\Services\CompatibilityService::class);
        return $compatibilityService->validateBuild($build);
    }

    /**
     * Add component to build with compatibility checking.
     */
    public function addComponentToBuild(Build $build, Component $component, int $quantity = 1, ?float $price = null, bool $checkCompatibility = true): array
    {
        $result = [
            'success' => false,
            'message' => '',
            'warnings' => [],
            'buildComponent' => null,
        ];

        // Check stock availability
        if ($component->stock < $quantity) {
            $result['message'] = "Insufficient stock. Available: {$component->stock}, Requested: {$quantity}";
            return $result;
        }

        // Check compatibility if enabled
        if ($checkCompatibility) {
            $compatibilityCheck = $this->checkComponentCompatibilityWithBuild($build, $component);
            
            if (!$compatibilityCheck['compatible']) {
                $result['message'] = $compatibilityCheck['message'];
                return $result;
            }
            
            if (!empty($compatibilityCheck['warnings'])) {
                $result['warnings'] = $compatibilityCheck['warnings'];
            }
        }

        // Add component to build
        $buildComponent = $build->addComponent($component, $quantity, $price);
        
        // Update build totals and status
        $build->calculateTotalPrice();
        $build->checkIfComplete();
        
        // Validate entire build after addition
        $buildValidation = $this->validateBuild($build);
        if (!$buildValidation->isValid()) {
            $result['warnings'] = array_merge(
                $result['warnings'], 
                $buildValidation->getCompatibilityResult()->getWarnings()
            );
        }

        $result['success'] = true;
        $result['message'] = 'Component added successfully';
        $result['buildComponent'] = $buildComponent;
        
        return $result;
    }

    /**
     * Remove component from build.
     */
    public function removeComponentFromBuild(Build $build, int $componentId): array
    {
        $result = [
            'success' => false,
            'message' => '',
        ];

        $removed = $build->removeComponent($componentId);
        
        if ($removed) {
            $build->calculateTotalPrice();
            $build->checkIfComplete();
            
            $result['success'] = true;
            $result['message'] = 'Component removed successfully';
        } else {
            $result['message'] = 'Component not found in build';
        }

        return $result;
    }

    /**
     * Check if a component is compatible with existing build components.
     */
    public function checkComponentCompatibilityWithBuild(Build $build, Component $newComponent): array
    {
        $result = [
            'compatible' => true,
            'message' => '',
            'warnings' => [],
        ];

        $existingComponents = $build->components()->with('component')->get()
            ->pluck('component')
            ->filter();

        // Check if component category already exists (for single-component categories)
        $existingCategories = $existingComponents->pluck('category_id');
        if ($existingCategories->contains($newComponent->category_id)) {
            $categoryName = $newComponent->category->name ?? 'Unknown';
            $result['compatible'] = false;
            $result['message'] = "Build already contains a {$categoryName}. Remove existing component first.";
            return $result;
        }

        // Check compatibility with each existing component (skip missing required check)
        $compatibilityService = app(\App\Services\CompatibilityService::class);
        
        foreach ($existingComponents as $existingComponent) {
            // Use reflection to access the protected method for direct component pair checking
            $reflection = new \ReflectionClass($compatibilityService);
            $method = $reflection->getMethod('checkComponentPair');
            $method->setAccessible(true);
            
            $pairResult = $method->invoke($compatibilityService, $newComponent, $existingComponent);
            
            if (!$pairResult['compatible']) {
                $result['compatible'] = false;
                $result['message'] = $pairResult['message'];
                return $result;
            }
            
            if (!empty($pairResult['warnings'])) {
                $result['warnings'] = array_merge($result['warnings'], $pairResult['warnings']);
            }
        }

        return $result;
    }

    /**
     * Get compatible components for a build and target category.
     */
    public function getCompatibleComponentsForBuild(Build $build, string $categorySlug, array $filters = []): \Illuminate\Support\Collection
    {
        $existingComponents = $build->components()->with('component')->get()
            ->pluck('component')
            ->filter();

        if ($existingComponents->isEmpty()) {
            // No existing components, return all components in category
            return $this->getComponentsByCategory($categorySlug, $filters);
        }

        // Get all components in the target category
        $allComponents = $this->getComponentsByCategory($categorySlug, $filters);
        
        // Filter by compatibility with existing components
        $compatibilityService = app(\App\Services\CompatibilityService::class);
        
        return $allComponents->filter(function ($candidate) use ($existingComponents, $compatibilityService) {
            foreach ($existingComponents as $existingComponent) {
                $result = $compatibilityService->checkCompatibility([$candidate, $existingComponent]);
                if (!$result->isCompatible()) {
                    return false;
                }
            }
            return true;
        });
    }

    /**
     * Calculate build power consumption and efficiency.
     */
    public function calculateBuildPower(Build $build): array
    {
        $components = $build->components()->with('component')->get()
            ->pluck('component')
            ->filter();

        $totalPower = 0;
        $psuPower = null;
        $powerBreakdown = [];

        foreach ($components as $component) {
            $componentPower = $component->getPowerConsumption();
            $totalPower += $componentPower;
            
            if ($componentPower > 0) {
                $powerBreakdown[] = [
                    'component' => $component->name,
                    'category' => $component->category->name ?? 'Unknown',
                    'power' => $componentPower,
                ];
            }

            // Check if this is the PSU
            if ($component->category->slug === 'power-supply') {
                $wattage = $component->getSpec('wattage');
                $psuPower = $wattage ? (int) filter_var($wattage, FILTER_SANITIZE_NUMBER_INT) : null;
            }
        }

        $efficiency = null;
        $recommendation = 'sufficient';
        
        if ($psuPower) {
            $efficiency = ($totalPower / $psuPower) * 100;
            
            if ($efficiency > 90) {
                $recommendation = 'insufficient';
            } elseif ($efficiency > 80) {
                $recommendation = 'tight';
            } elseif ($efficiency < 50) {
                $recommendation = 'oversized';
            }
        }

        return [
            'total_power' => $totalPower,
            'psu_power' => $psuPower,
            'efficiency' => $efficiency,
            'recommendation' => $recommendation,
            'breakdown' => $powerBreakdown,
        ];
    }

    /**
     * Get build completion status with missing components.
     */
    public function getBuildCompletionStatus(Build $build): array
    {
        $requiredCategories = ComponentCategory::required()->get();
        $existingCategories = $build->components()->with('category')->get()
            ->pluck('category.id')
            ->unique();

        $missingCategories = $requiredCategories->filter(function ($category) use ($existingCategories) {
            return !$existingCategories->contains($category->id);
        });

        $completionPercentage = $requiredCategories->count() > 0 
            ? (($requiredCategories->count() - $missingCategories->count()) / $requiredCategories->count()) * 100
            : 100;

        return [
            'is_complete' => $missingCategories->isEmpty(),
            'completion_percentage' => round($completionPercentage, 1),
            'missing_categories' => $missingCategories->values(),
            'total_required' => $requiredCategories->count(),
            'completed_required' => $requiredCategories->count() - $missingCategories->count(),
        ];
    }

    /**
     * Save a build for a user.
     */
    public function saveBuild(Build $build, array $data, ?User $user = null): Build
    {
        // Update build metadata
        if (isset($data['name'])) {
            $build->name = $data['name'];
        }
        
        if (isset($data['description'])) {
            $build->description = $data['description'];
        }
        
        if (isset($data['is_public'])) {
            $build->is_public = $data['is_public'];
        }
        
        // Set user if provided and build doesn't have one
        if ($user && !$build->user_id) {
            $build->user_id = $user->id;
        }
        
        // Recalculate totals and completion status
        $build->calculateTotalPrice();
        $build->checkIfComplete();
        
        $build->save();
        
        return $build;
    }

    /**
     * Create a shareable build from an existing build.
     */
    public function createShareableBuild(Build $build, array $shareData = []): Build
    {
        $shareableBuild = $build->replicate();
        
        // Override sharing settings
        $shareableBuild->name = $shareData['name'] ?? ($build->name . ' (Shared)');
        $shareableBuild->description = $shareData['description'] ?? $build->description;
        $shareableBuild->is_public = $shareData['is_public'] ?? true;
        $shareableBuild->user_id = $build->user_id; // Keep original owner
        
        $shareableBuild->save();
        
        // Copy all build components
        foreach ($build->components as $component) {
            $newComponent = $component->replicate();
            $newComponent->build_id = $shareableBuild->id;
            $newComponent->save();
        }
        
        return $shareableBuild;
    }

    /**
     * Toggle build visibility (public/private).
     */
    public function toggleBuildVisibility(Build $build): Build
    {
        $build->is_public = !$build->is_public;
        $build->save();
        
        return $build;
    }

    /**
     * Generate a shareable URL for a build.
     */
    public function generateShareableUrl(Build $build): string
    {
        // Ensure build is public for sharing
        if (!$build->is_public) {
            throw new \InvalidArgumentException('Build must be public to generate shareable URL');
        }
        
        return route('builder.show', ['id' => $build->id]);
    }

    /**
     * Generate a shareable URL with build token for private sharing.
     */
    public function generatePrivateShareUrl(Build $build): string
    {
        // Generate a unique token for private sharing if not exists
        if (!$build->share_token) {
            $build->share_token = \Illuminate\Support\Str::random(32);
            $build->save();
        }
        
        return route('builder.share', ['id' => $build->id, 'token' => $build->share_token]);
    }

    /**
     * Get user's saved builds.
     */
    public function getUserBuilds(User $user, bool $includePrivate = true): \Illuminate\Database\Eloquent\Collection
    {
        $query = Build::where('user_id', $user->id);
        
        if (!$includePrivate) {
            $query->where('is_public', true);
        }
        
        return $query->with(['components.component.category'])
            ->orderBy('updated_at', 'desc')
            ->get();
    }

    /**
     * Get public builds for browsing.
     */
    public function getPublicBuilds(int $limit = 20, array $filters = []): \Illuminate\Database\Eloquent\Collection
    {
        $query = Build::where('is_public', true);
        
        // Apply filters
        if (isset($filters['complete_only']) && $filters['complete_only']) {
            $query->where('is_complete', true);
        }
        
        if (isset($filters['price_min']) && is_numeric($filters['price_min'])) {
            $query->where('total_price', '>=', $filters['price_min']);
        }
        
        if (isset($filters['price_max']) && is_numeric($filters['price_max'])) {
            $query->where('total_price', '<=', $filters['price_max']);
        }
        
        if (isset($filters['search']) && !empty($filters['search'])) {
            $query->where(function ($q) use ($filters) {
                $q->where('name', 'like', '%' . $filters['search'] . '%')
                  ->orWhere('description', 'like', '%' . $filters['search'] . '%');
            });
        }
        
        // Sort results
        $sortBy = $filters['sort_by'] ?? 'updated_at';
        $sortDir = $filters['sort_dir'] ?? 'desc';
        
        return $query->with(['user', 'components.component.category'])
            ->orderBy($sortBy, $sortDir)
            ->limit($limit)
            ->get();
    }

    /**
     * Clone a build for a user.
     */
    public function cloneBuild(Build $originalBuild, User $user, array $cloneData = []): Build
    {
        // Check if user can access the original build
        if (!$originalBuild->is_public && $originalBuild->user_id !== $user->id) {
            throw new \InvalidArgumentException('Cannot clone private build that does not belong to user');
        }
        
        $clonedBuild = $originalBuild->replicate();
        
        // Set clone-specific data
        $clonedBuild->user_id = $user->id;
        $clonedBuild->name = $cloneData['name'] ?? ('Clone of ' . $originalBuild->name);
        $clonedBuild->description = $cloneData['description'] ?? $originalBuild->description;
        $clonedBuild->is_public = $cloneData['is_public'] ?? false;
        $clonedBuild->share_token = null; // Reset share token for clone
        
        $clonedBuild->save();
        
        // Clone all build components
        foreach ($originalBuild->components as $component) {
            $newComponent = $component->replicate();
            $newComponent->build_id = $clonedBuild->id;
            $newComponent->save();
        }
        
        return $clonedBuild;
    }

    /**
     * Delete a build and all its components.
     */
    public function deleteBuild(Build $build, User $user): bool
    {
        // Check if user owns the build
        if ($build->user_id !== $user->id) {
            throw new \InvalidArgumentException('User does not own this build');
        }
        
        // Delete all build components first
        $build->components()->delete();
        
        // Delete the build
        return $build->delete();
    }

    /**
     * Get build by ID with access control.
     */
    public function getBuildById(int $buildId, ?User $user = null, ?string $shareToken = null): ?Build
    {
        $query = Build::with(['components.component.category', 'user']);
        
        $build = $query->find($buildId);
        
        if (!$build) {
            return null;
        }
        
        // Check access permissions
        if ($build->is_public) {
            return $build; // Public builds are accessible to everyone
        }
        
        // Check if user owns the build
        if ($user && $build->user_id === $user->id) {
            return $build;
        }
        
        // Check if valid share token is provided for private sharing
        if ($shareToken && $build->share_token === $shareToken) {
            return $build;
        }
        
        // No access
        return null;
    }
}