@extends('layouts.user')

@section('title', $product->name)

@section('content')
<div class="container mx-auto px-4 py-8">
    <!-- Breadcrumb -->
    <nav class="mb-6">
        <ol class="flex items-center space-x-2 text-sm text-gray-600">
            <li><a href="{{ route('home') }}" class="hover:text-blue-600">Home</a></li>
            <li>/</li>
            <li><a href="{{ route('products.index') }}" class="hover:text-blue-600">Products</a></li>
            @if($product->category)
                <li>/</li>
                <li><a href="{{ route('products.category', $product->category) }}" class="hover:text-blue-600">{{ $product->category }}</a></li>
            @endif
            <li>/</li>
            <li class="text-gray-900">{{ $product->name }}</li>
        </ol>
    </nav>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
        <!-- Product Images -->
        <div class="space-y-4">
            <div class="aspect-square bg-gray-100 rounded-lg overflow-hidden">
                <img src="{{ $product->primary_image }}" alt="{{ $product->name }}" 
                     class="w-full h-full object-cover">
            </div>
            
            @if(count($product->images) > 1)
                <div class="grid grid-cols-4 gap-2">
                    @foreach($product->images as $image)
                        <div class="aspect-square bg-gray-100 rounded overflow-hidden cursor-pointer hover:opacity-75">
                            <img src="{{ $image }}" alt="{{ $product->name }}" 
                                 class="w-full h-full object-cover">
                        </div>
                    @endforeach
                </div>
            @endif
        </div>

        <!-- Product Info -->
        <div class="space-y-6">
            <div>
                <h1 class="text-3xl font-bold text-gray-900 mb-2">{{ $product->name }}</h1>
                <p class="text-gray-600">SKU: {{ $product->sku }}</p>
                
                @if($product->brand)
                    <p class="text-gray-600">Brand: <span class="font-medium">{{ $product->brand }}</span></p>
                @endif
            </div>

            <!-- Price -->
            <div class="flex items-center space-x-4">
                @if($product->is_on_sale)
                    <span class="text-3xl font-bold text-red-600">{{ $product->formatted_price }}</span>
                    <span class="text-xl text-gray-500 line-through">{{ $product->original_price }}</span>
                    <span class="bg-red-100 text-red-800 px-3 py-1 rounded-full text-sm font-medium">
                        {{ $product->discount_percentage }}% OFF
                    </span>
                @else
                    <span class="text-3xl font-bold text-gray-900">{{ $product->formatted_price }}</span>
                @endif
            </div>

            <!-- Stock Status -->
            <div class="flex items-center space-x-2">
                <span class="text-sm font-medium">Availability:</span>
                <span class="px-3 py-1 rounded-full text-sm {{ $product->in_stock ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                    {{ $product->getStockStatus() }}
                </span>
                
                @if($product->manage_stock && $product->stock_quantity > 0)
                    <span class="text-sm text-gray-600">({{ $product->stock_quantity }} in stock)</span>
                @endif
            </div>

            <!-- Short Description -->
            @if($product->short_description)
                <div class="prose prose-sm">
                    <p>{{ $product->short_description }}</p>
                </div>
            @endif

            <!-- Add to Cart -->
            @if($product->isAvailable())
                <form class="space-y-4">
                    <div class="flex items-center space-x-4">
                        <label for="quantity" class="text-sm font-medium">Quantity:</label>
                        <input type="number" id="quantity" name="quantity" value="1" min="1" 
                               max="{{ $product->manage_stock ? $product->stock_quantity : 999 }}"
                               class="w-20 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                    </div>
                    
                    <div class="flex space-x-4">
                        <button type="submit" 
                                class="flex-1 bg-blue-600 text-white py-3 px-6 rounded-lg hover:bg-blue-700 transition-colors font-medium">
                            Add to Cart
                        </button>
                        <button type="button" 
                                class="bg-gray-200 text-gray-800 py-3 px-6 rounded-lg hover:bg-gray-300 transition-colors">
                            ♡ Wishlist
                        </button>
                    </div>
                </form>
            @else
                <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                    <p class="text-red-800 font-medium">This product is currently out of stock.</p>
                </div>
            @endif

            <!-- Product Attributes -->
            @if($product->attributes && count($product->attributes) > 0)
                <div class="border-t pt-6">
                    <h3 class="text-lg font-semibold mb-3">Specifications</h3>
                    <dl class="grid grid-cols-1 gap-2">
                        @foreach($product->attributes as $key => $value)
                            <div class="flex">
                                <dt class="w-1/3 text-sm font-medium text-gray-600 capitalize">{{ str_replace('_', ' ', $key) }}:</dt>
                                <dd class="text-sm text-gray-900">{{ $value }}</dd>
                            </div>
                        @endforeach
                    </dl>
                </div>
            @endif
        </div>
    </div>

    <!-- Product Description -->
    @if($product->description)
        <div class="border-t pt-8 mb-12">
            <h2 class="text-2xl font-bold mb-4">Description</h2>
            <div class="prose max-w-none">
                {!! nl2br(e($product->description)) !!}
            </div>
        </div>
    @endif

    <!-- Related Products -->
    @if($relatedProducts->count() > 0)
        <div class="border-t pt-8">
            <h2 class="text-2xl font-bold mb-6">Related Products</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                @foreach($relatedProducts as $relatedProduct)
                    <div class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
                        <img src="{{ $relatedProduct->primary_image }}" alt="{{ $relatedProduct->name }}" 
                             class="w-full h-48 object-cover">
                        
                        <div class="p-4">
                            <h3 class="font-semibold mb-2 line-clamp-2">
                                <a href="{{ route('shop.product', $relatedProduct->slug) }}" class="hover:text-blue-600">
                                    {{ $relatedProduct->name }}
                                </a>
                            </h3>
                            
                            <div class="flex items-center justify-between">
                                <span class="font-bold text-gray-900">{{ $relatedProduct->formatted_price }}</span>
                                <a href="{{ route('shop.product', $relatedProduct->slug) }}" 
                                   class="text-blue-600 hover:text-blue-800 text-sm">
                                    View Details
                                </a>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    @endif
</div>
@endsection