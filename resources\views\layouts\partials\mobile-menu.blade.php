<!-- Mobile Menu -->
<div :class="{ 'block': open, 'hidden': !open }" class="hidden md:hidden">
    <div class="pt-2 pb-3 space-y-1 bg-white dark:bg-nexus-dark-900 border-t border-nexus-gray-200 dark:border-nexus-gray-700">
        <!-- Mobile Search Form -->
        <div class="px-4 py-3">
            {{-- <form action="{{ route('search') }}" method="GET" class="flex flex-col space-y-3">
                <div class="relative">
                    <input type="text" name="query" placeholder="Search pincode or post office..."
                        class="w-full border border-nexus-gray-300 dark:border-nexus-gray-700 bg-white dark:bg-nexus-dark-800 text-nexus-dark-800 dark:text-nexus-gray-200 placeholder-nexus-gray-500 dark:placeholder-nexus-gray-400 focus:border-nexus-primary-500 dark:focus:border-nexus-primary-600 focus:ring-nexus-primary-500 dark:focus:ring-nexus-primary-600 rounded-md shadow-sm text-sm transition-all duration-300" />
                </div>
                <div class="flex space-x-4">
                    <label class="inline-flex items-center text-sm text-nexus-gray-600 dark:text-nexus-gray-400">
                        <input type="radio" name="type" value="pincode" checked
                            class="text-nexus-primary-600 dark:text-nexus-primary-500 border-nexus-gray-300 dark:border-nexus-gray-700 focus:ring-nexus-primary-500 dark:focus:ring-nexus-primary-600" />
                        <span class="ml-2">Pincode</span>
                    </label>
                    <label class="inline-flex items-center text-sm text-nexus-gray-600 dark:text-nexus-gray-400">
                        <input type="radio" name="type" value="name"
                            class="text-nexus-primary-600 dark:text-nexus-primary-500 border-nexus-gray-300 dark:border-nexus-gray-700 focus:ring-nexus-primary-500 dark:focus:ring-nexus-primary-600" />
                        <span class="ml-2">Name</span>
                    </label>
                </div>
                <button type="submit"
                    class="w-full inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white nexus-gradient-primary hover:from-nexus-primary-700 hover:to-nexus-secondary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-nexus-primary-500 transition-all duration-300 glow">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24"
                        stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                    Search
                </button>
            </form> --}}
        </div>

        <!-- Mobile Cart Icon -->
        <div class="px-4 py-3 border-b border-nexus-gray-200 dark:border-nexus-gray-700">
            <div class="flex items-center justify-between">
                <span class="text-sm font-medium text-nexus-gray-700 dark:text-nexus-gray-300 tech-font">Shopping Cart</span>
                @livewire('shop.cart-icon')
            </div>
        </div>

        <!-- Mobile Navigation Links -->
        <div class="space-y-1">
            <!-- Main Navigation -->
            <div class="px-4 py-2">
                <div class="space-y-1">
                    <a href="{{ route('shop.index') }}"
                        class="block px-3 py-2 text-base text-nexus-gray-700 dark:text-nexus-gray-300 hover:bg-nexus-gray-100 dark:hover:bg-nexus-dark-700 hover:text-nexus-dark-900 dark:hover:text-white rounded-md transition-colors tech-font">
                        Shop
                    </a>
                    <a href="{{ route('builder.index') }}"
                        class="block px-3 py-2 text-base text-nexus-gray-700 dark:text-nexus-gray-300 hover:bg-nexus-gray-100 dark:hover:bg-nexus-dark-700 hover:text-nexus-dark-900 dark:hover:text-white rounded-md transition-colors tech-font">
                        PC Builder
                    </a>
                    <a href="{{ route('blog.index') }}"
                        class="block px-3 py-2 text-base text-nexus-gray-700 dark:text-nexus-gray-300 hover:bg-nexus-gray-100 dark:hover:bg-nexus-dark-700 hover:text-nexus-dark-900 dark:hover:text-white rounded-md transition-colors tech-font">
                        Blog
                    </a>
                </div>
            </div>

            <!-- Tools Section -->
            <div class="px-4 py-2">
                <div class="text-sm font-semibold text-nexus-gray-500 dark:text-nexus-gray-400 uppercase tracking-wider tech-font">Tools</div>
                <div class="mt-2 space-y-1">
                    {{-- @foreach ($allTools as $tool)
                        <a href="{{ route('tools.show', $tool->slug) }}"
                            class="block px-3 py-2 text-sm text-nexus-gray-700 dark:text-nexus-gray-300 hover:bg-nexus-gray-100 dark:hover:bg-nexus-dark-700 hover:text-nexus-dark-900 dark:hover:text-white rounded-md transition-colors">
                            {{ $tool->name }}
                        </a>
                    @endforeach --}}
                </div>
            </div>

            <!-- Pincodes Section -->
            <div class="px-4 py-2">
                <div class="text-sm font-semibold text-nexus-gray-500 dark:text-nexus-gray-400 uppercase tracking-wider tech-font">Pincodes</div>
                <div class="mt-2 space-y-1">
                    {{-- <a href="{{ route('pincodes.states') }}"
                        class="block px-3 py-2 text-sm text-nexus-gray-700 dark:text-nexus-gray-300 hover:bg-nexus-gray-100 dark:hover:bg-nexus-dark-700 hover:text-nexus-dark-900 dark:hover:text-white rounded-md transition-colors">
                        All Pincodes
                    </a>
                    <a href="{{ url('/tools/district-wise-pincode-download') }}"
                        class="block px-3 py-2 text-sm text-nexus-gray-700 dark:text-nexus-gray-300 hover:bg-nexus-gray-100 dark:hover:bg-nexus-dark-700 hover:text-nexus-dark-900 dark:hover:text-white rounded-md transition-colors">
                        District wise Download
                    </a>
                    <a href="{{ url('/tools/pincode-address-search-tool') }}"
                        class="block px-3 py-2 text-sm text-nexus-gray-700 dark:text-nexus-gray-300 hover:bg-nexus-gray-100 dark:hover:bg-nexus-dark-700 hover:text-nexus-dark-900 dark:hover:text-white rounded-md transition-colors">
                        Pincode Address Search
                    </a>
                    <a href="{{ route('courier_dict.index') }}"
                        class="block px-3 py-2 text-sm text-nexus-gray-700 dark:text-nexus-gray-300 hover:bg-nexus-gray-100 dark:hover:bg-nexus-dark-700 hover:text-nexus-dark-900 dark:hover:text-white rounded-md transition-colors">
                        Courier Dictionary
                    </a>
                    <a href="{{ route('api.docs.index') }}"
                        class="block px-3 py-2 text-sm text-nexus-gray-700 dark:text-nexus-gray-300 hover:bg-nexus-gray-100 dark:hover:bg-nexus-dark-700 hover:text-nexus-dark-900 dark:hover:text-white rounded-md transition-colors">
                        API Documentation
                    </a> --}}
                </div>
            </div>
        </div>

        <!-- Mobile Authentication -->
        <div class="pt-4 pb-1 border-t border-nexus-gray-200 dark:border-nexus-gray-700">
            @guest
                <div class="px-4">
                    <a href="{{ route('login') }}"
                        class="w-full flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white nexus-gradient-primary hover:from-nexus-primary-700 hover:to-nexus-secondary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-nexus-primary-500 transition-all duration-300 glow tech-font">
                        Login
                    </a>
                </div>
            @else
                <div class="px-4">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="h-10 w-10 rounded-full bg-nexus-primary-600 flex items-center justify-center">
                                <span class="text-sm font-medium text-white">{{ substr(Auth::user()->name, 0, 1) }}</span>
                            </div>
                        </div>
                        <div class="ml-3">
                            <div class="text-base font-medium text-nexus-dark-800 dark:text-nexus-gray-200 tech-font">{{ Auth::user()->name }}</div>
                            <div class="text-sm text-nexus-gray-500 dark:text-nexus-gray-400">{{ Auth::user()->email }}</div>
                        </div>
                    </div>
                </div>
                <div class="mt-3 space-y-1">
                    <a href="{{ route('dashboard') }}"
                        class="block px-4 py-2 text-base text-nexus-gray-700 dark:text-nexus-gray-300 hover:bg-nexus-gray-100 dark:hover:bg-nexus-dark-700 hover:text-nexus-dark-900 dark:hover:text-white transition-colors">
                        Dashboard
                    </a>
                    {{-- <a href="{{ route('profile.edit') }}"
                        class="block px-4 py-2 text-base text-nexus-gray-700 dark:text-nexus-gray-300 hover:bg-nexus-gray-100 dark:hover:bg-nexus-dark-700 hover:text-nexus-dark-900 dark:hover:text-white transition-colors">
                        Profile
                    </a> --}}
                    <form method="POST" action="{{ route('logout') }}">
                        @csrf
                        <a href="{{ route('logout') }}" onclick="event.preventDefault(); this.closest('form').submit();"
                            class="block px-4 py-2 text-base text-nexus-gray-700 dark:text-nexus-gray-300 hover:bg-nexus-gray-100 dark:hover:bg-nexus-dark-700 hover:text-nexus-dark-900 dark:hover:text-white transition-colors">
                            Log Out
                        </a>
                    </form>
                </div>
            @endguest
        </div>

        <!-- Mobile Theme Switcher -->
        <div class="px-4 py-3 border-t border-nexus-gray-200 dark:border-nexus-gray-700">
            <div class="flex items-center justify-between">
                <span class="text-sm font-medium text-nexus-gray-700 dark:text-nexus-gray-300 tech-font">Theme</span>
                <button onclick="toggleTheme()"
                    class="relative w-16 h-8 bg-nexus-gray-200 dark:bg-nexus-dark-700 rounded-full p-1 transition-colors duration-300 focus:outline-none focus:ring-2 focus:ring-nexus-primary-500 dark:focus:ring-nexus-primary-600 nexus-border-glow">
                    <div
                        class="absolute left-1 top-1 w-6 h-6 bg-nexus-warning-400 dark:bg-nexus-primary-600 rounded-full flex items-center justify-center transition-transform duration-300 dark:translate-x-8 shadow-glow-blue">
                        <span class="text-sm dark:hidden">☀️</span>
                        <span class="text-sm hidden dark:inline">🌙</span>
                    </div>
                    <div class="flex justify-between items-center px-2">
                        <span class="text-xs text-nexus-gray-600 dark:text-nexus-gray-400">☀️</span>
                        <span class="text-xs text-nexus-gray-600 dark:text-nexus-gray-400">🌙</span>
                    </div>
                </button>
            </div>
        </div>
    </div>
</div>