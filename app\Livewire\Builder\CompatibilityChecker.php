<?php

namespace App\Livewire\Builder;

use App\Models\Component;
use App\Services\CompatibilityService;
use Livewire\Component as LivewireComponent;

class Compatibility<PERSON>hecker extends LivewireComponent
{
    public $components = [];
    public $compatibilityResult = [];
    public $showDetails = false;
    
    protected $listeners = [
        'checkCompatibility' => 'checkBuildCompatibility',
        'componentsUpdated' => 'updateComponents',
    ];
    
    public function mount($components = [])
    {
        $this->components = $components;
        $this->checkBuildCompatibility();
    }
    
    public function updateComponents($components)
    {
        $this->components = $components;
        $this->checkBuildCompatibility();
    }
    
    public function checkBuildCompatibility()
    {
        if (empty($this->components)) {
            $this->compatibilityResult = [];
            return;
        }
        
        // Convert component IDs to Component models if needed
        $componentModels = collect($this->components)->map(function ($component) {
            if (is_array($component) && isset($component['component_id'])) {
                return Component::find($component['component_id']);
            } elseif (is_numeric($component)) {
                return Component::find($component);
            } elseif ($component instanceof Component) {
                return $component;
            }
            return null;
        })->filter();
        
        if ($componentModels->count() < 2) {
            $this->compatibilityResult = [];
            return;
        }
        
        $compatibilityService = app(CompatibilityService::class);
        $result = $compatibilityService->checkCompatibility($componentModels->toArray());
        $this->compatibilityResult = $result->toArray();
    }
    
    public function toggleDetails()
    {
        $this->showDetails = !$this->showDetails;
    }
    
    public function getCompatibilityStatusProperty()
    {
        if (empty($this->compatibilityResult)) {
            return 'unknown';
        }
        
        if (!($this->compatibilityResult['compatible'] ?? false)) {
            return 'incompatible';
        }
        
        if ($this->compatibilityResult['has_warnings'] ?? false) {
            return 'warning';
        }
        
        return 'compatible';
    }
    
    public function getCompatibilityMessageProperty()
    {
        if (empty($this->compatibilityResult)) {
            return 'Add more components to check compatibility';
        }
        
        if (!($this->compatibilityResult['compatible'] ?? false)) {
            return 'Compatibility issues detected';
        }
        
        if ($this->compatibilityResult['has_warnings'] ?? false) {
            return 'Compatible with warnings';
        }
        
        return 'All components are compatible';
    }
    
    public function getCompatibilityIconProperty()
    {
        switch ($this->compatibilityStatus) {
            case 'compatible':
                return 'check-circle';
            case 'warning':
                return 'exclamation-triangle';
            case 'incompatible':
                return 'x-circle';
            default:
                return 'question-mark-circle';
        }
    }
    
    public function getCompatibilityColorProperty()
    {
        switch ($this->compatibilityStatus) {
            case 'compatible':
                return 'text-green-600';
            case 'warning':
                return 'text-yellow-600';
            case 'incompatible':
                return 'text-red-600';
            default:
                return 'text-gray-400';
        }
    }
    
    public function render()
    {
        return view('livewire.builder.compatibility-checker', [
            'compatibilityColor' => $this->compatibilityColor,
            'compatibilityStatus' => $this->compatibilityStatus,
            'compatibilityMessage' => $this->compatibilityMessage,
        ]);
    }
}