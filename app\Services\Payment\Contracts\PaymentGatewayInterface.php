<?php

namespace App\Services\Payment\Contracts;

interface PaymentGatewayInterface
{
    /**
     * Create a payment order/session with the gateway
     *
     * @param array $data Payment data including amount, currency, etc.
     * @return array Gateway response with payment details
     */
    public function createPayment(array $data): array;

    /**
     * Verify a payment with the gateway
     *
     * @param string $transactionId Internal transaction ID
     * @param array $data Verification data from gateway
     * @return bool Whether the payment is verified
     */
    public function verifyPayment(string $transactionId, array $data): bool;

    /**
     * Handle webhook from the gateway
     *
     * @param array $payload Webhook payload
     * @return array Processed webhook data
     */
    public function handleWebhook(array $payload): array;

    /**
     * Get payment status from the gateway
     *
     * @param string $transactionId Internal transaction ID
     * @return string Payment status
     */
    public function getPaymentStatus(string $transactionId): string;

    /**
     * Get gateway name
     *
     * @return string Gateway name
     */
    public function getGatewayName(): string;

    /**
     * Check if gateway is enabled
     *
     * @return bool Whether gateway is enabled
     */
    public function isEnabled(): bool;
}