<?php

namespace Tests\Feature;

use App\Livewire\User\OrderHistory;
use App\Models\User;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Component;
use App\Models\ComponentCategory;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Livewire\Livewire;
use Tests\TestCase;

class OrderHistoryTest extends TestCase
{
    use RefreshDatabase;

    private User $user;
    private Order $order;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->user = User::factory()->create();
        
        $category = ComponentCategory::factory()->create(['name' => 'CPU']);
        $component = Component::factory()->create([
            'category_id' => $category->id,
            'name' => 'Intel Core i7-12700K',
            'price' => 399.99,
        ]);
        
        $this->order = Order::factory()->create([
            'user_id' => $this->user->id,
            'order_number' => 'PCB20240101001',
            'status' => 'completed',
            'total' => 399.99,
            'subtotal' => 399.99,
            'shipping_name' => '<PERSON>',
            'shipping_address' => '123 Main St',
            'shipping_city' => 'Anytown',
            'shipping_state' => 'CA',
            'shipping_zipcode' => '12345',
            'shipping_country' => 'USA',
        ]);
        
        OrderItem::factory()->create([
            'order_id' => $this->order->id,
            'component_id' => $component->id,
            'quantity' => 1,
            'price' => 399.99,
            'name' => 'Intel Core i7-12700K',
        ]);
    }

    public function test_component_loads_user_orders()
    {
        Livewire::actingAs($this->user)
            ->test(OrderHistory::class)
            ->assertSee('PCB20240101001')
            ->assertSee('completed')
            ->assertSee('$399.99');
    }

    public function test_user_can_select_order_to_view_details()
    {
        Livewire::actingAs($this->user)
            ->test(OrderHistory::class)
            ->call('selectOrder', $this->order->id)
            ->assertSet('selectedOrder.id', $this->order->id)
            ->assertSee('Order #PCB20240101001')
            ->assertSee('Intel Core i7-12700K')
            ->assertSee('John Doe')
            ->assertSee('123 Main St');
    }

    public function test_user_can_clear_selected_order()
    {
        Livewire::actingAs($this->user)
            ->test(OrderHistory::class)
            ->call('selectOrder', $this->order->id)
            ->assertSet('selectedOrder.id', $this->order->id)
            ->call('clearSelectedOrder')
            ->assertSet('selectedOrder', null);
    }

    public function test_search_filters_orders()
    {
        $anotherOrder = Order::factory()->create([
            'user_id' => $this->user->id,
            'order_number' => 'PCB20240102001',
            'status' => 'pending',
        ]);

        Livewire::actingAs($this->user)
            ->test(OrderHistory::class)
            ->set('search', 'PCB20240101001')
            ->assertSee('PCB20240101001')
            ->assertDontSee('PCB20240102001');
    }

    public function test_status_filter_works()
    {
        Order::factory()->create([
            'user_id' => $this->user->id,
            'order_number' => 'PCB20240102001',
            'status' => 'pending',
        ]);

        Livewire::actingAs($this->user)
            ->test(OrderHistory::class)
            ->set('statusFilter', 'completed')
            ->assertSee('PCB20240101001')
            ->assertDontSee('PCB20240102001');
    }

    public function test_sorting_works()
    {
        $newerOrder = Order::factory()->create([
            'user_id' => $this->user->id,
            'order_number' => 'PCB20240102001',
            'created_at' => now()->addDay(),
        ]);

        Livewire::actingAs($this->user)
            ->test(OrderHistory::class)
            ->call('sortBy', 'order_number')
            ->assertSet('sortBy', 'order_number')
            ->assertSet('sortDirection', 'asc');
    }

    public function test_reorder_items_adds_to_cart()
    {
        $this->markTestSkipped('Requires CartService implementation');
        
        // This test would verify that reorderItems method works correctly
        // but requires the CartService to be properly mocked or implemented
    }

    public function test_track_order_redirects_to_tracking_url()
    {
        $this->order->update(['tracking_url' => 'https://tracking.example.com/123']);

        $response = Livewire::actingAs($this->user)
            ->test(OrderHistory::class)
            ->call('trackOrder', $this->order->id);

        // This would test the redirect functionality
        // Implementation depends on how Livewire handles redirects in tests
    }

    public function test_pagination_works()
    {
        // Create 15 orders to test pagination
        Order::factory()->count(14)->create(['user_id' => $this->user->id]);

        Livewire::actingAs($this->user)
            ->test(OrderHistory::class)
            ->assertSee('PCB20240101001'); // Should see the first order
    }

    public function test_unauthenticated_user_sees_empty_collection()
    {
        Livewire::test(OrderHistory::class)
            ->assertSee('Please log in');
    }

    public function test_user_cannot_access_other_users_orders()
    {
        $otherUser = User::factory()->create();
        $otherOrder = Order::factory()->create([
            'user_id' => $otherUser->id,
            'order_number' => 'PCB20240103001',
        ]);

        Livewire::actingAs($this->user)
            ->test(OrderHistory::class)
            ->call('selectOrder', $otherOrder->id)
            ->assertSet('selectedOrder', null);
    }

    public function test_download_invoice_shows_placeholder_message()
    {
        Livewire::actingAs($this->user)
            ->test(OrderHistory::class)
            ->call('downloadInvoice', $this->order->id)
            ->assertDispatched('notify', 'Invoice download feature coming soon!');
    }
}