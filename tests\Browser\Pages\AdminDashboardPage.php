<?php

namespace Tests\Browser\Pages;

use <PERSON><PERSON>\Dusk\Browser;

class AdminDashboardPage extends Page
{
    /**
     * Get the URL for the page.
     */
    public function url(): string
    {
        return '/admin/dashboard';
    }

    /**
     * Assert that the browser is on the page.
     */
    public function assert(Browser $browser): void
    {
        $browser->assertPathIs($this->url())
                ->assertSee('Admin Dashboard');
    }

    /**
     * Get the element shortcuts for the page.
     */
    public function elements(): array
    {
        return [
            '@sidebar' => '.admin-sidebar',
            '@mainContent' => '.admin-main-content',
            '@transactionsLink' => 'a[href*="/admin/transactions"]',
            '@gatewaysLink' => 'a[href*="/admin/gateways"]',
            '@productsLink' => 'a[href*="/admin/products"]',
            '@usersLink' => 'a[href*="/admin/users"]',
            '@settingsLink' => 'a[href*="/admin/settings"]',
            '@logoutButton' => 'button[onclick*="logout"]',
            '@statsCards' => '.stats-card',
            '@recentTransactions' => '.recent-transactions',
        ];
    }

    /**
     * Navigate to transactions page.
     */
    public function goToTransactions(Browser $browser): void
    {
        $browser->click('@transactionsLink');
    }

    /**
     * Navigate to gateways page.
     */
    public function goToGateways(Browser $browser): void
    {
        $browser->click('@gatewaysLink');
    }

    /**
     * Navigate to products page.
     */
    public function goToProducts(Browser $browser): void
    {
        $browser->click('@productsLink');
    }
}