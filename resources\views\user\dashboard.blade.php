@extends('layouts.user')

@section('page-title', 'Dashboard')

@section('content')
<style>
    /* Hide scrollbar for Chrome, Safari and Opera */
    .hide-scrollbar::-webkit-scrollbar {
        display: none;
    }
    /* Hide scrollbar for IE, Edge and Firefox */
    .hide-scrollbar {
        -ms-overflow-style: none;  /* IE and Edge */
        scrollbar-width: none;  /* Firefox */
    }
    
    /* Smooth tab transitions */
    .tab-content {
        transition: all 0.3s ease-in-out;
    }
    
    /* Active tab styling */
    .tab-button[aria-selected="true"] {
        position: relative;
    }
    
    .tab-button[aria-selected="true"]::after {
        content: '';
        position: absolute;
        bottom: -2px;
        left: 0;
        width: 100%;
        height: 3px;
        background: linear-gradient(90deg, #6366f1, #8b5cf6);
        border-radius: 2px;
        box-shadow: 0 0 10px rgba(99, 102, 241, 0.5);
    }
    
    /* Enhanced card hover effects */
    .dashboard-card {
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;
    }

    .dashboard-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
        transition: left 0.5s;
    }

    .dashboard-card:hover::before {
        left: 100%;
    }

    .dashboard-card:hover {
        transform: translateY(-8px) scale(1.02);
        box-shadow: 0 20px 40px -10px rgba(99, 102, 241, 0.2);
    }
    
    /* Mobile-specific card improvements */
    @media (max-width: 1023px) {
        .dashboard-card:hover {
            transform: translateY(-4px) scale(1.01);
            box-shadow: 0 10px 25px -5px rgba(99, 102, 241, 0.15);
        }
    }
    
    @media (max-width: 640px) {
        .dashboard-card {
            padding: 1rem !important;
        }
        
        .dashboard-card:hover {
            transform: none;
            box-shadow: 0 4px 12px -2px rgba(99, 102, 241, 0.1);
        }
    }
    
    /* Enhanced stats counter animation */
    @keyframes countUp {
        from { 
            opacity: 0; 
            transform: translateY(20px) scale(0.8); 
        }
        to { 
            opacity: 1; 
            transform: translateY(0) scale(1); 
        }
    }
    
    .stat-value {
        animation: countUp 1.2s cubic-bezier(0.4, 0, 0.2, 1) forwards;
    }
    
    /* Enhanced pulse animation */
    @keyframes modernPulse {
        0%, 100% { 
            transform: scale(1);
            box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.7);
        }
        50% { 
            transform: scale(1.05);
            box-shadow: 0 0 0 10px rgba(239, 68, 68, 0);
        }
    }
    
    .notification-badge {
        animation: modernPulse 2s infinite;
    }

    /* Modern gradient backgrounds */
    .stat-card-blue {
        background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    }
    
    .stat-card-purple {
        background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
    }
    
    .stat-card-red {
        background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    }
    
    .stat-card-emerald {
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    }

    /* Glass morphism for activity items */
    .activity-item {
        backdrop-filter: blur(10px);
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        transition: all 0.3s ease;
    }

    .dark .activity-item {
        background: rgba(15, 23, 42, 0.4);
        border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .activity-item:hover {
        background: rgba(255, 255, 255, 0.2);
        transform: translateX(4px);
    }

    .dark .activity-item:hover {
        background: rgba(15, 23, 42, 0.6);
    }

    /* Modern scrollbar */
    .modern-scrollbar::-webkit-scrollbar {
        width: 6px;
        height: 6px;
    }

    .modern-scrollbar::-webkit-scrollbar-track {
        background: transparent;
    }

    .modern-scrollbar::-webkit-scrollbar-thumb {
        background: linear-gradient(45deg, #6366f1, #8b5cf6);
        border-radius: 10px;
    }

    .modern-scrollbar::-webkit-scrollbar-thumb:hover {
        background: linear-gradient(45deg, #4f46e5, #7c3aed);
    }
</style>

<!-- Dashboard content container -->
<div class="w-full max-w-full">
    <!-- Page Title with modern styling -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold bg-gradient-to-r from-indigo-600 via-purple-600 to-blue-600 bg-clip-text text-transparent">
                    Welcome back!
                </h1>
                <p class="text-gray-600 dark:text-gray-400 mt-2 text-lg">Here's what's happening with your account</p>
            </div>
            <div class="hidden md:flex items-center space-x-3">
                <div class="text-right">
                    <p class="text-sm text-gray-500 dark:text-gray-400">Last login</p>
                    <p class="text-sm font-medium text-gray-900 dark:text-white">{{ now()->format('M d, Y') }}</p>
                </div>
                <div class="w-12 h-12 bg-gradient-to-br from-green-400 to-blue-500 rounded-2xl flex items-center justify-center">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5.121 17.804A13.937 13.937 0 0112 15c2.5 0 4.847.655 6.879 1.804M15 11a3 3 0 11-6 0 3 3 0 016 0z"/>
                    </svg>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Modern Stats Cards with enhanced design -->
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-2 xl:grid-cols-4 gap-4 sm:gap-6 mb-6 sm:mb-8">
        <!-- Saved Builds Card -->
        <div class="dashboard-card glass-card rounded-2xl p-6 border border-white/20 dark:border-gray-700/50 backdrop-blur-xl">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-indigo-600 dark:text-indigo-400 text-sm font-semibold tracking-wide uppercase">Saved Builds</p>
                    <p class="text-3xl font-bold text-gray-900 dark:text-white stat-value mt-2">3</p>
                    <div class="flex items-center mt-2">
                        <svg class="w-4 h-4 text-green-500 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 11l5-5m0 0l5 5m-5-5v12"/>
                        </svg>
                        <span class="text-green-600 text-sm font-medium">+2 this week</span>
                    </div>
                </div>
                <div class="w-16 h-16 stat-card-blue rounded-2xl flex items-center justify-center shadow-lg">
                    <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3zM6 8a2 2 0 11-4 0 2 2 0 014 0zM16 18v-3a5.972 5.972 0 00-.75-2.906A3.005 3.005 0 0119 15v3h-3zM4.75 12.094A5.973 5.973 0 004 15v3H1v-3a3 3 0 013.75-2.906z"></path>
                    </svg>
                </div>
            </div>
        </div>
        
        <!-- Orders Card -->
        <div class="dashboard-card glass-card rounded-2xl p-6 border border-white/20 dark:border-gray-700/50 backdrop-blur-xl">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-purple-600 dark:text-purple-400 text-sm font-semibold tracking-wide uppercase">Orders</p>
                    <p class="text-3xl font-bold text-gray-900 dark:text-white stat-value mt-2">2</p>
                    <div class="flex items-center mt-2">
                        <div class="w-2 h-2 bg-yellow-400 rounded-full mr-2"></div>
                        <span class="text-gray-600 dark:text-gray-400 text-sm">1 in progress</span>
                    </div>
                </div>
                <div class="w-16 h-16 stat-card-purple rounded-2xl flex items-center justify-center shadow-lg">
                    <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 2a4 4 0 00-4 4v1H5a1 1 0 00-.994.89l-1 9A1 1 0 004 18h12a1 1 0 00.994-1.11l-1-9A1 1 0 0015 7h-1V6a4 4 0 00-4-4zm2 5V6a2 2 0 10-4 0v1h4zm-6 3a1 1 0 112 0 1 1 0 01-2 0zm7-1a1 1 0 100 2 1 1 0 000-2z" clip-rule="evenodd"></path>
                    </svg>
                </div>
            </div>
        </div>
        
        <!-- Notifications Card -->
        <div class="dashboard-card glass-card rounded-2xl p-6 border border-white/20 dark:border-gray-700/50 backdrop-blur-xl">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-red-600 dark:text-red-400 text-sm font-semibold tracking-wide uppercase">Notifications</p>
                    <p class="text-3xl font-bold text-gray-900 dark:text-white stat-value mt-2">3</p>
                    <div class="flex items-center mt-2">
                        <div class="w-2 h-2 bg-red-400 rounded-full mr-2 notification-badge"></div>
                        <span class="text-red-600 dark:text-red-400 text-sm">2 unread</span>
                    </div>
                </div>
                <div class="w-16 h-16 stat-card-red rounded-2xl flex items-center justify-center shadow-lg">
                    <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M10 2a6 6 0 00-6 6v3.586l-.707.707A1 1 0 004 14h12a1 1 0 00.707-1.707L16 11.586V8a6 6 0 00-6-6zM10 18a3 3 0 01-3-3h6a3 3 0 01-3 3z"></path>
                    </svg>
                </div>
            </div>
        </div>
        
        <!-- Completed Builds Card -->
        <div class="dashboard-card glass-card rounded-2xl p-6 border border-white/20 dark:border-gray-700/50 backdrop-blur-xl">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-emerald-600 dark:text-emerald-400 text-sm font-semibold tracking-wide uppercase">Completed</p>
                    <p class="text-3xl font-bold text-gray-900 dark:text-white stat-value mt-2">1</p>
                    <div class="flex items-center mt-2">
                        <svg class="w-4 h-4 text-emerald-500 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                        </svg>
                        <span class="text-emerald-600 text-sm font-medium">100% success</span>
                    </div>
                </div>
                <div class="w-16 h-16 stat-card-emerald rounded-2xl flex items-center justify-center shadow-lg">
                    <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                    </svg>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Dashboard Layout -->
    <div class="grid grid-cols-1 xl:grid-cols-3 gap-4 sm:gap-6 lg:gap-8">
        <!-- Main Content Area - 2/3 width on extra large screens -->
        <div class="xl:col-span-2 space-y-4 sm:space-y-6 lg:space-y-8">
            <!-- Modern Dashboard Container -->
            <div x-data="dashboardTabs()" class="glass-card rounded-3xl backdrop-blur-xl border border-white/20 dark:border-gray-700/50 overflow-hidden modern-shadow">
                <!-- Enhanced Tab Navigation -->
                <nav class="border-b border-white/10 dark:border-gray-700/50 bg-gradient-to-r from-white/5 to-transparent dark:from-gray-800/5 relative" aria-label="Dashboard Tabs">
                    <!-- Loading Indicator -->
                    <div class="tab-loading-indicator hidden absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-indigo-500 to-purple-600 animate-pulse z-10 rounded-full"></div>
                    
                    <div class="px-4 sm:px-6 lg:px-8">
                        <div class="flex space-x-1 overflow-x-auto modern-scrollbar scrollbar-hide">
                            <template x-for="(tabData, tabKey) in tabs" :key="tabKey">
                                <button 
                                    @click="setActiveTab(tabKey)" 
                                    :class="activeTab === tabKey ? 
                                        'border-transparent text-indigo-600 dark:text-indigo-400 bg-white/10 dark:bg-indigo-900/30' : 
                                        'border-transparent text-gray-600 dark:text-gray-400 hover:text-indigo-600 dark:hover:text-indigo-400 hover:bg-white/5 dark:hover:bg-gray-800/30'"
                                    class="group whitespace-nowrap py-3 sm:py-4 px-4 sm:px-6 border-b-2 font-semibold text-xs sm:text-sm transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 rounded-t-2xl flex items-center space-x-2 sm:space-x-3 min-w-fit relative overflow-hidden"
                                    :aria-current="activeTab === tabKey ? 'page' : undefined"
                                    :aria-selected="activeTab === tabKey"
                                    role="tab">
                                    
                                    <!-- Tab Icon -->
                                    <div class="w-5 h-5 flex-shrink-0 transition-transform group-hover:scale-110" 
                                         :class="activeTab === tabKey ? 'text-indigo-600 dark:text-indigo-400' : 'text-gray-500 dark:text-gray-400'"
                                         x-html="tabData.icon"></div>
                                    
                                    <!-- Tab Label -->
                                    <span x-text="tabData.label" class="hidden sm:block font-medium"></span>
                                    
                                    <!-- Badge -->
                                    <span x-show="tabData.badge && tabData.badge > 0" 
                                        x-text="tabData.badge" 
                                        class="bg-red-500 text-white text-xs font-bold px-2.5 py-1 rounded-full min-w-[24px] text-center notification-badge shadow-lg"></span>
                                    
                                    <!-- Active indicator -->
                                    <div x-show="activeTab === tabKey" 
                                         class="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-full"
                                         x-transition:enter="transition ease-out duration-300"
                                         x-transition:enter-start="opacity-0 scale-x-0"
                                         x-transition:enter-end="opacity-100 scale-x-100"></div>
                                </button>
                            </template>
                        </div>
                    </div>
                </nav>

                <!-- Enhanced Tab Content -->
                <div class="p-8 sm:p-10 min-h-[500px]">
                    <!-- Loading State -->
                    <div x-show="loading" class="flex items-center justify-center py-20">
                        <div class="relative">
                            <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 dark:border-indigo-400"></div>
                            <div class="absolute inset-0 rounded-full border-2 border-indigo-200 dark:border-indigo-800"></div>
                        </div>
                        <span class="ml-4 text-gray-600 dark:text-gray-300 text-lg">Loading content...</span>
                    </div>

                    <!-- Account Settings Tab -->
                    <div x-show="activeTab === 'account' && !loading" 
                        x-transition:enter="transition ease-out duration-300"
                        x-transition:enter-start="opacity-0 transform translate-y-8"
                        x-transition:enter-end="opacity-100 transform translate-y-0"
                        role="tabpanel" 
                        aria-labelledby="account-tab">
                        <div class="mb-8">
                            <div class="flex items-center space-x-4 mb-4">
                                <div class="w-12 h-12 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-2xl flex items-center justify-center">
                                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5.121 17.804A13.937 13.937 0 0112 15c2.5 0 4.847.655 6.879 1.804M15 11a3 3 0 11-6 0 3 3 0 016 0z"/>
                                    </svg>
                                </div>
                                <div>
                                    <h2 class="text-2xl font-bold text-gray-900 dark:text-white">Account Settings</h2>
                                    <p class="text-gray-600 dark:text-gray-400">Manage your personal information and preferences</p>
                                </div>
                            </div>
                        </div>
                        @livewire('user.account-settings', ['lazy' => true])
                    </div>

                    <!-- Order History Tab -->
                    <div x-show="activeTab === 'orders' && !loading" 
                        x-transition:enter="transition ease-out duration-300"
                        x-transition:enter-start="opacity-0 transform translate-y-8"
                        x-transition:enter-end="opacity-100 transform translate-y-0"
                        role="tabpanel" 
                        aria-labelledby="orders-tab">
                        <div class="mb-8">
                            <div class="flex items-center space-x-4 mb-4">
                                <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-600 rounded-2xl flex items-center justify-center">
                                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M8 11v6h8v-6H8z"/>
                                    </svg>
                                </div>
                                <div>
                                    <h2 class="text-2xl font-bold text-gray-900 dark:text-white">Order History</h2>
                                    <p class="text-gray-600 dark:text-gray-400">View and track your recent orders</p>
                                </div>
                            </div>
                        </div>
                        @livewire('user.order-history', ['lazy' => true])
                    </div>

                    <!-- Saved Builds Tab -->
                    <div x-show="activeTab === 'builds' && !loading" 
                        x-transition:enter="transition ease-out duration-300"
                        x-transition:enter-start="opacity-0 transform translate-y-8"
                        x-transition:enter-end="opacity-100 transform translate-y-0"
                        role="tabpanel" 
                        aria-labelledby="builds-tab">
                        <div class="mb-8">
                            <div class="flex items-center space-x-4 mb-4">
                                <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-cyan-600 rounded-2xl flex items-center justify-center">
                                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
                                    </svg>
                                </div>
                                <div>
                                    <h2 class="text-2xl font-bold text-gray-900 dark:text-white">Saved Builds</h2>
                                    <p class="text-gray-600 dark:text-gray-400">Access your saved configurations and builds</p>
                                </div>
                            </div>
                        </div>
                        @livewire('user.saved-builds', ['lazy' => true])
                    </div>

                    <!-- Notifications Tab -->
                    <div x-show="activeTab === 'notifications' && !loading" 
                        x-transition:enter="transition ease-out duration-300"
                        x-transition:enter-start="opacity-0 transform translate-y-8"
                        x-transition:enter-end="opacity-100 transform translate-y-0"
                        role="tabpanel" 
                        aria-labelledby="notifications-tab">
                        <div class="mb-8">
                            <div class="flex items-center space-x-4 mb-4">
                                <div class="w-12 h-12 bg-gradient-to-br from-red-500 to-orange-600 rounded-2xl flex items-center justify-center">
                                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V4a2 2 0 10-4 0v1.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"/>
                                    </svg>
                                </div>
                                <div>
                                    <h2 class="text-2xl font-bold text-gray-900 dark:text-white">Notifications</h2>
                                    <p class="text-gray-600 dark:text-gray-400">Manage your notification preferences and history</p>
                                </div>
                            </div>
                        </div>
                        @livewire('user.notifications', ['lazy' => true])
                    </div>

                    <!-- Reviews Tab -->
                    <div x-show="activeTab === 'reviews' && !loading" 
                        x-transition:enter="transition ease-out duration-300"
                        x-transition:enter-start="opacity-0 transform translate-y-8"
                        x-transition:enter-end="opacity-100 transform translate-y-0"
                        role="tabpanel" 
                        aria-labelledby="reviews-tab">
                        <div class="mb-8">
                            <div class="flex items-center space-x-4 mb-4">
                                <div class="w-12 h-12 bg-gradient-to-br from-yellow-500 to-orange-600 rounded-2xl flex items-center justify-center">
                                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l2.036 6.29a1 1 0 00.95.69h6.631c.969 0 1.371 1.24.588 1.81l-5.37 3.905a1 1 0 00-.364 1.118l2.036 6.29c.3.921-.755 1.688-1.54 1.118l-5.37-3.905a1 1 0 00-1.176 0l-5.37 3.905c-.784.57-1.838-.197-1.54-1.118l2.036-6.29a1 1 0 00-.364-1.118l-5.37-3.905c-.783-.57-.38-1.81.588-1.81h6.631a1 1 0 00.95-.69l2.036-6.29z"/>
                                    </svg>
                                </div>
                                <div>
                                    <h2 class="text-2xl font-bold text-gray-900 dark:text-white">My Reviews</h2>
                                    <p class="text-gray-600 dark:text-gray-400">View and manage your product reviews</p>
                                </div>
                            </div>
                        </div>
                        @livewire('user.reviews', ['lazy' => true])
                    </div>
                </div>
            </div>
        </div>

        <!-- Enhanced Sidebar - 1/3 width on extra large screens -->
        <div class="space-y-4 sm:space-y-6 lg:space-y-8">
            <!-- Quick Actions Card -->
            <div class="glass-card rounded-3xl backdrop-blur-xl border border-white/20 dark:border-gray-700/50 overflow-hidden modern-shadow">
                <div class="bg-gradient-to-r from-indigo-500/10 to-purple-500/10 dark:from-indigo-600/20 dark:to-purple-600/20 px-6 py-5 border-b border-white/10 dark:border-gray-700/50">
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-xl flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                            </svg>
                        </div>
                        <h3 class="text-xl font-bold text-gray-900 dark:text-white">Quick Actions</h3>
                    </div>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-2 gap-4">
                        <a href="#" class="dashboard-card flex flex-col items-center p-5 glass-card rounded-2xl hover:shadow-lg transition-all duration-300 group border border-white/20 dark:border-gray-700/50">
                            <div class="h-14 w-14 rounded-2xl bg-gradient-to-br from-blue-500 to-cyan-600 flex items-center justify-center mb-3 group-hover:scale-110 transition-transform shadow-lg">
                                <svg class="w-7 h-7 text-white" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M16 11V7a4 4 0 00-8 0v4M8 11v6h8v-6H8zM8 11H6a2 2 0 00-2 2v6a2 2 0 002 2h12a2 2 0 002-2v-6a2 2 0 00-2-2h-2" />
                                </svg>
                            </div>
                            <span class="text-sm font-semibold text-gray-900 dark:text-white">Security</span>
                            <span class="text-xs text-gray-500 dark:text-gray-400 mt-1">Account protection</span>
                        </a>
                        
                        <a href="#" class="dashboard-card flex flex-col items-center p-5 glass-card rounded-2xl hover:shadow-lg transition-all duration-300 group border border-white/20 dark:border-gray-700/50">
                            <div class="h-14 w-14 rounded-2xl bg-gradient-to-br from-green-500 to-teal-600 flex items-center justify-center mb-3 group-hover:scale-110 transition-transform shadow-lg">
                                <svg class="w-7 h-7 text-white" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                                </svg>
                            </div>
                            <span class="text-sm font-semibold text-gray-900 dark:text-white">Support</span>
                            <span class="text-xs text-gray-500 dark:text-gray-400 mt-1">Get help</span>
                        </a>
                        
                        <a href="#" class="dashboard-card flex flex-col items-center p-5 glass-card rounded-2xl hover:shadow-lg transition-all duration-300 group border border-white/20 dark:border-gray-700/50">
                            <div class="h-14 w-14 rounded-2xl bg-gradient-to-br from-purple-500 to-pink-600 flex items-center justify-center mb-3 group-hover:scale-110 transition-transform shadow-lg">
                                <svg class="w-7 h-7 text-white" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                                </svg>
                            </div>
                            <span class="text-sm font-semibold text-gray-900 dark:text-white">Docs</span>
                            <span class="text-xs text-gray-500 dark:text-gray-400 mt-1">Documentation</span>
                        </a>
                        
                        <a href="#" class="dashboard-card flex flex-col items-center p-5 glass-card rounded-2xl hover:shadow-lg transition-all duration-300 group border border-white/20 dark:border-gray-700/50">
                            <div class="h-14 w-14 rounded-2xl bg-gradient-to-br from-orange-500 to-red-600 flex items-center justify-center mb-3 group-hover:scale-110 transition-transform shadow-lg">
                                <svg class="w-7 h-7 text-white" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                            </div>
                            <span class="text-sm font-semibold text-gray-900 dark:text-white">Help</span>
                            <span class="text-xs text-gray-500 dark:text-gray-400 mt-1">Need assistance</span>
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Enhanced Recent Activity Card -->
            <div class="glass-card rounded-3xl backdrop-blur-xl border border-white/20 dark:border-gray-700/50 overflow-hidden modern-shadow">
                <div class="bg-gradient-to-r from-blue-500/10 to-purple-500/10 dark:from-blue-600/20 dark:to-purple-600/20 px-6 py-5 border-b border-white/10 dark:border-gray-700/50">
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                        </div>
                        <h3 class="text-xl font-bold text-gray-900 dark:text-white">Recent Activity</h3>
                    </div>
                </div>
                <div class="p-6">
                    <div class="space-y-4">
                        <div class="activity-item flex items-start space-x-4 p-4 rounded-2xl">
                            <div class="flex-shrink-0 h-10 w-10 rounded-2xl bg-gradient-to-br from-blue-500 to-cyan-600 flex items-center justify-center shadow-md">
                                <svg class="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
                                </svg>
                            </div>
                            <div class="flex-1 min-w-0">
                                <p class="text-sm font-semibold text-gray-900 dark:text-white">Order #12345 placed</p>
                                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">Gaming PC Build - $2,499</p>
                                <div class="flex items-center mt-2">
                                    <div class="w-2 h-2 bg-blue-400 rounded-full mr-2"></div>
                                    <span class="text-xs text-blue-600 dark:text-blue-400 font-medium">Processing</span>
                                </div>
                            </div>
                            <span class="text-xs text-gray-400">2d ago</span>
                        </div>
                        
                        <div class="activity-item flex items-start space-x-4 p-4 rounded-2xl">
                            <div class="flex-shrink-0 h-10 w-10 rounded-2xl bg-gradient-to-br from-green-500 to-teal-600 flex items-center justify-center shadow-md">
                                <svg class="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                            <div class="flex-1 min-w-0">
                                <p class="text-sm font-semibold text-gray-900 dark:text-white">Build saved successfully</p>
                                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">Workstation Build V2</p>
                                <div class="flex items-center mt-2">
                                    <div class="w-2 h-2 bg-green-400 rounded-full mr-2"></div>
                                    <span class="text-xs text-green-600 dark:text-green-400 font-medium">Completed</span>
                                </div>
                            </div>
                            <span class="text-xs text-gray-400">3d ago</span>
                        </div>
                        
                        <div class="activity-item flex items-start space-x-4 p-4 rounded-2xl">
                            <div class="flex-shrink-0 h-10 w-10 rounded-2xl bg-gradient-to-br from-purple-500 to-pink-600 flex items-center justify-center shadow-md">
                                <svg class="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                </svg>
                            </div>
                            <div class="flex-1 min-w-0">
                                <p class="text-sm font-semibold text-gray-900 dark:text-white">Profile updated</p>
                                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">Contact information changed</p>
                                <div class="flex items-center mt-2">
                                    <div class="w-2 h-2 bg-purple-400 rounded-full mr-2"></div>
                                    <span class="text-xs text-purple-600 dark:text-purple-400 font-medium">Updated</span>
                                </div>
                            </div>
                            <span class="text-xs text-gray-400">1w ago</span>
                        </div>
                        
                        <div class="activity-item flex items-start space-x-4 p-4 rounded-2xl">
                            <div class="flex-shrink-0 h-10 w-10 rounded-2xl bg-gradient-to-br from-yellow-500 to-orange-600 flex items-center justify-center shadow-md">
                                <svg class="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l2.036 6.29a1 1 0 00.95.69h6.631c.969 0 1.371 1.24.588 1.81l-5.37 3.905a1 1 0 00-.364 1.118l2.036 6.29c.3.921-.755 1.688-1.54 1.118l-5.37-3.905a1 1 0 00-1.176 0l-5.37 3.905c-.784.57-1.838-.197-1.54-1.118l2.036-6.29a1 1 0 00-.364-1.118l-5.37-3.905c-.783-.57-.38-1.81.588-1.81h6.631a1 1 0 00.95-.69l2.036-6.29z"/>
                                </svg>
                            </div>
                            <div class="flex-1 min-w-0">
                                <p class="text-sm font-semibold text-gray-900 dark:text-white">Review submitted</p>
                                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">RTX 4080 Graphics Card</p>
                                <div class="flex items-center mt-2">
                                    <div class="flex space-x-1">
                                        <svg class="w-3 h-3 text-yellow-400 fill-current" viewBox="0 0 20 20">
                                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                                        </svg>
                                        <span class="text-xs text-yellow-600 dark:text-yellow-400 font-medium">5 stars</span>
                                    </div>
                                </div>
                            </div>
                            <span class="text-xs text-gray-400">2w ago</span>
                        </div>
                    </div>
                    
                    <!-- View All Activity Button -->
                    <div class="mt-6 pt-4 border-t border-white/10 dark:border-gray-700/50">
                        <button class="w-full bg-gradient-to-r from-indigo-500 to-purple-600 hover:from-indigo-600 hover:to-purple-700 text-white font-semibold py-3 px-4 rounded-2xl transition-all duration-300 transform hover:scale-105 shadow-lg">
                            View All Activity
                        </button>
                    </div>
                </div>
            </div>

            <!-- System Status Card -->
            <div class="glass-card rounded-3xl backdrop-blur-xl border border-white/20 dark:border-gray-700/50 overflow-hidden modern-shadow">
                <div class="bg-gradient-to-r from-green-500/10 to-blue-500/10 dark:from-green-600/20 dark:to-blue-600/20 px-6 py-5 border-b border-white/10 dark:border-gray-700/50">
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-gradient-to-br from-green-500 to-blue-600 rounded-xl flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                        </div>
                        <h3 class="text-xl font-bold text-gray-900 dark:text-white">System Status</h3>
                    </div>
                </div>
                <div class="p-6">
                    <div class="space-y-4">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-3">
                                <div class="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
                                <span class="text-sm font-medium text-gray-900 dark:text-white">Account Status</span>
                            </div>
                            <span class="text-sm text-green-600 dark:text-green-400 font-semibold">Active</span>
                        </div>
                        
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-3">
                                <div class="w-3 h-3 bg-blue-400 rounded-full animate-pulse"></div>
                                <span class="text-sm font-medium text-gray-900 dark:text-white">Build Service</span>
                            </div>
                            <span class="text-sm text-blue-600 dark:text-blue-400 font-semibold">Online</span>
                        </div>
                        
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-3">
                                <div class="w-3 h-3 bg-yellow-400 rounded-full animate-pulse"></div>
                                <span class="text-sm font-medium text-gray-900 dark:text-white">Support</span>
                            </div>
                            <span class="text-sm text-yellow-600 dark:text-yellow-400 font-semibold">Available</span>
                        </div>
                        
                        <div class="mt-4 p-4 bg-green-50 dark:bg-green-900/20 rounded-2xl border border-green-200 dark:border-green-800/50">
                            <div class="flex items-center space-x-2">
                                <svg class="w-5 h-5 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                </svg>
                                <span class="text-sm font-medium text-green-800 dark:text-green-300">All systems operational</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function dashboardTabs() {
    return {
        activeTab: 'account',
        loading: false,
        initialized: false,
        tabs: {
            account: {
                label: 'Account Settings',
                icon: `<svg fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M5.121 17.804A13.937 13.937 0 0112 15c2.5 0 4.847.655 6.879 1.804M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>`,
                badge: null
            },
            orders: {
                label: 'Order History',
                icon: `<svg fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M16 11V7a4 4 0 00-8 0v4M8 11v6h8v-6H8z" />
                </svg>`,
                badge: null
            },
            builds: {
                label: 'Saved Builds',
                icon: `<svg fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                </svg>`,
                badge: null
            },
            notifications: {
                label: 'Notifications',
                icon: `<svg fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V4a2 2 0 10-4 0v1.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
                </svg>`,
                badge: 3
            },
            reviews: {
                label: 'My Reviews',
                icon: `<svg fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l2.036 6.29a1 1 0 00.95.69h6.631c.969 0 1.371 1.24.588 1.81l-5.37 3.905a1 1 0 00-.364 1.118l2.036 6.29c.3.921-.755 1.688-1.54 1.118l-5.37-3.905a1 1 0 00-1.176 0l-5.37 3.905c-.784.57-1.838-.197-1.54-1.118l2.036-6.29a1 1 0 00-.364-1.118l-5.37-3.905c-.783-.57-.38-1.81.588-1.81h6.631a1 1 0 00.95-.69l2.036-6.29z" />
                </svg>`,
                badge: null
            }
        },
        
        init() {
            const hash = window.location.hash.substring(1);
            if (hash && this.tabs[hash]) {
                this.activeTab = hash;
            }
            
            this.$watch('activeTab', (value) => {
                history.replaceState(null, null, `#${value}`);
            });
            
            this.$nextTick(() => {
                this.setupAccessibility();
                this.initialized = true;
                this.initStatCounters();
            });
        },
        
        setupAccessibility() {
            document.addEventListener('keydown', (e) => {
                if (e.target.classList.contains('tab-button')) {
                    const tabButtons = Array.from(document.querySelectorAll('.tab-button'));
                    const currentIndex = tabButtons.indexOf(e.target);
                    
                    if (e.key === 'ArrowRight') {
                        const nextTab = tabButtons[(currentIndex + 1) % tabButtons.length];
                        nextTab.focus();
                        this.setActiveTab(nextTab.getAttribute('data-tab'));
                        e.preventDefault();
                    } else if (e.key === 'ArrowLeft') {
                        const prevTab = tabButtons[(currentIndex - 1 + tabButtons.length) % tabButtons.length];
                        prevTab.focus();
                        this.setActiveTab(prevTab.getAttribute('data-tab'));
                        e.preventDefault();
                    }
                }
            });
        },
        
        setActiveTab(tab) {
            if (this.activeTab === tab) return;
            
            this.loading = true;
            this.activeTab = tab;
            
            const loadingIndicator = document.querySelector('.tab-loading-indicator');
            if (loadingIndicator) {
                loadingIndicator.classList.remove('hidden');
            }
            
            setTimeout(() => {
                this.loading = false;
                if (loadingIndicator) {
                    loadingIndicator.classList.add('hidden');
                }
                this.announceTabChange(tab);
            }, 400);
        },
        
        announceTabChange(tab) {
            const announcement = document.createElement('div');
            announcement.setAttribute('aria-live', 'polite');
            announcement.classList.add('sr-only');
            announcement.textContent = `Tab changed to ${this.tabs[tab].label}`;
            document.body.appendChild(announcement);
            
            setTimeout(() => {
                document.body.removeChild(announcement);
            }, 1000);
        },
        
        initStatCounters() {
            const statElements = document.querySelectorAll('.stat-value');
            
            statElements.forEach((el, index) => {
                const targetValue = parseInt(el.textContent, 10);
                let startValue = 0;
                const duration = 2000;
                const increment = Math.ceil(targetValue / (duration / 16));
                
                const updateCounter = () => {
                    startValue += increment;
                    if (startValue > targetValue) {
                        el.textContent = targetValue;
                    } else {
                        el.textContent = startValue;
                        requestAnimationFrame(updateCounter);
                    }
                };
                
                setTimeout(() => {
                    el.textContent = '0';
                    requestAnimationFrame(updateCounter);
                }, index * 200);
            });
        }
    }
}
</script>

<style>
.scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
}
.scrollbar-hide::-webkit-scrollbar {
    display: none;
}
</style>
@endsection