<?php

namespace Tests\Performance;

use App\Models\Build;
use App\Models\Component;
use App\Models\ComponentCategory;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\DB;
use Tests\TestCase;

class DatabasePerformanceTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create large dataset for database performance testing
        $this->createLargeDataset();
    }

    /** @test */
    public function component_queries_use_proper_indexes()
    {
        DB::enableQueryLog();
        
        // Test queries that should use indexes
        $queries = [
            // Category index
            fn() => Component::where('category_id', 1)->limit(10)->get(),
            
            // Active status index
            fn() => Component::where('is_active', true)->limit(10)->get(),
            
            // Stock index
            fn() => Component::where('stock', '>', 0)->limit(10)->get(),
            
            // Price range index
            fn() => Component::whereBetween('price', [100, 500])->limit(10)->get(),
            
            // Compound index test
            fn() => Component::where('category_id', 1)
                             ->where('is_active', true)
                             ->where('stock', '>', 0)
                             ->limit(10)->get(),
            
            // Featured components
            fn() => Component::where('is_featured', true)->limit(10)->get(),
        ];
        
        foreach ($queries as $index => $query) {
            DB::flushQueryLog();
            
            $startTime = microtime(true);
            $query();
            $endTime = microtime(true);
            
            $queryTime = ($endTime - $startTime) * 1000;
            $queryLog = DB::getQueryLog();
            
            // Each indexed query should complete under 50ms
            $this->assertLessThan(50, $queryTime, 
                "Query {$index} took {$queryTime}ms, which exceeds 50ms limit");
            
            // Should only execute one query
            $this->assertCount(1, $queryLog, 
                "Query {$index} executed " . count($queryLog) . " queries, expected 1");
        }
    }

    /** @test */
    public function complex_joins_perform_efficiently()
    {
        $startTime = microtime(true);
        
        // Complex query with multiple joins
        $result = Component::select([
                'components.*',
                'categories.name as category_name',
                DB::raw('AVG(reviews.rating) as avg_rating'),
                DB::raw('COUNT(order_items.id) as sales_count')
            ])
            ->join('component_categories as categories', 'components.category_id', '=', 'categories.id')
            ->leftJoin('reviews', 'components.id', '=', 'reviews.component_id')
            ->leftJoin('order_items', 'components.id', '=', 'order_items.component_id')
            ->where('components.is_active', true)
            ->groupBy('components.id', 'categories.name')
            ->having('avg_rating', '>', 4.0)
            ->orderBy('sales_count', 'desc')
            ->limit(50)
            ->get();
        
        $endTime = microtime(true);
        $queryTime = ($endTime - $startTime) * 1000;
        
        // Complex join query should complete under 500ms
        $this->assertLessThan(500, $queryTime, 
            "Complex join query took {$queryTime}ms, which exceeds 500ms limit");
        
        $this->assertNotEmpty($result);
    }

    /** @test */
    public function pagination_queries_are_optimized()
    {
        $pageSizes = [10, 25, 50, 100];
        $pages = [1, 5, 10, 20];
        
        foreach ($pageSizes as $pageSize) {
            foreach ($pages as $page) {
                $startTime = microtime(true);
                
                $result = Component::where('is_active', true)
                                  ->orderBy('created_at', 'desc')
                                  ->offset(($page - 1) * $pageSize)
                                  ->limit($pageSize)
                                  ->get();
                
                $endTime = microtime(true);
                $queryTime = ($endTime - $startTime) * 1000;
                
                // Pagination should complete under 100ms regardless of page
                $this->assertLessThan(100, $queryTime, 
                    "Pagination (page {$page}, size {$pageSize}) took {$queryTime}ms");
                
                $this->assertLessThanOrEqual($pageSize, $result->count());
            }
        }
    }

    /** @test */
    public function search_queries_perform_efficiently()
    {
        $searchTerms = [
            'Intel',
            'gaming',
            'DDR4',
            'RTX',
            'SSD',
            'motherboard',
            'high performance'
        ];
        
        foreach ($searchTerms as $term) {
            $startTime = microtime(true);
            
            // Full-text search simulation
            $result = Component::where(function($query) use ($term) {
                $query->where('name', 'LIKE', "%{$term}%")
                      ->orWhere('description', 'LIKE', "%{$term}%")
                      ->orWhere('brand', 'LIKE', "%{$term}%");
            })
            ->where('is_active', true)
            ->limit(50)
            ->get();
            
            $endTime = microtime(true);
            $searchTime = ($endTime - $startTime) * 1000;
            
            // Search should complete under 200ms
            $this->assertLessThan(200, $searchTime, 
                "Search for '{$term}' took {$searchTime}ms, which exceeds 200ms limit");
        }
    }

    /** @test */
    public function aggregate_queries_are_optimized()
    {
        $aggregateQueries = [
            // Count by category
            fn() => Component::select('category_id', DB::raw('COUNT(*) as count'))
                             ->where('is_active', true)
                             ->groupBy('category_id')
                             ->get(),
            
            // Average price by brand
            fn() => Component::select('brand', DB::raw('AVG(price) as avg_price'))
                             ->where('is_active', true)
                             ->groupBy('brand')
                             ->get(),
            
            // Stock summary
            fn() => Component::select(
                        DB::raw('SUM(stock) as total_stock'),
                        DB::raw('AVG(stock) as avg_stock'),
                        DB::raw('COUNT(CASE WHEN stock = 0 THEN 1 END) as out_of_stock_count')
                    )->first(),
            
            // Sales statistics
            fn() => OrderItem::select(
                        'component_id',
                        DB::raw('SUM(quantity) as total_sold'),
                        DB::raw('SUM(quantity * price) as total_revenue')
                    )
                    ->groupBy('component_id')
                    ->orderBy('total_sold', 'desc')
                    ->limit(10)
                    ->get(),
        ];
        
        foreach ($aggregateQueries as $index => $query) {
            $startTime = microtime(true);
            $result = $query();
            $endTime = microtime(true);
            
            $queryTime = ($endTime - $startTime) * 1000;
            
            // Aggregate queries should complete under 300ms
            $this->assertLessThan(300, $queryTime, 
                "Aggregate query {$index} took {$queryTime}ms, which exceeds 300ms limit");
            
            $this->assertNotEmpty($result);
        }
    }

    /** @test */
    public function bulk_operations_are_efficient()
    {
        // Test bulk insert
        $startTime = microtime(true);
        
        $bulkData = [];
        for ($i = 0; $i < 1000; $i++) {
            $bulkData[] = [
                'category_id' => rand(1, 7),
                'name' => "Bulk Component {$i}",
                'brand' => 'Test Brand',
                'model' => "Model-{$i}",
                'price' => rand(50, 1000),
                'stock' => rand(0, 100),
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }
        
        Component::insert($bulkData);
        
        $endTime = microtime(true);
        $insertTime = ($endTime - $startTime) * 1000;
        
        // Bulk insert of 1000 records should complete under 2 seconds
        $this->assertLessThan(2000, $insertTime, 
            "Bulk insert took {$insertTime}ms, which exceeds 2000ms limit");
        
        // Test bulk update
        $startTime = microtime(true);
        
        Component::where('name', 'LIKE', 'Bulk Component%')
                 ->update(['is_featured' => true]);
        
        $endTime = microtime(true);
        $updateTime = ($endTime - $startTime) * 1000;
        
        // Bulk update should complete under 500ms
        $this->assertLessThan(500, $updateTime, 
            "Bulk update took {$updateTime}ms, which exceeds 500ms limit");
        
        // Test bulk delete
        $startTime = microtime(true);
        
        Component::where('name', 'LIKE', 'Bulk Component%')->delete();
        
        $endTime = microtime(true);
        $deleteTime = ($endTime - $startTime) * 1000;
        
        // Bulk delete should complete under 500ms
        $this->assertLessThan(500, $deleteTime, 
            "Bulk delete took {$deleteTime}ms, which exceeds 500ms limit");
    }

    /** @test */
    public function transaction_performance_is_acceptable()
    {
        $transactionTimes = [];
        
        // Test 50 transactions
        for ($i = 0; $i < 50; $i++) {
            $startTime = microtime(true);
            
            DB::transaction(function() use ($i) {
                // Create order
                $order = Order::create([
                    'user_id' => User::inRandomOrder()->first()->id,
                    'order_number' => "PERF-{$i}",
                    'status' => 'pending',
                    'total_amount' => 0,
                ]);
                
                // Add order items
                $total = 0;
                for ($j = 0; $j < 5; $j++) {
                    $component = Component::inRandomOrder()->first();
                    $quantity = rand(1, 3);
                    $price = $component->price;
                    
                    OrderItem::create([
                        'order_id' => $order->id,
                        'component_id' => $component->id,
                        'quantity' => $quantity,
                        'price' => $price,
                    ]);
                    
                    $total += $quantity * $price;
                    
                    // Update stock
                    $component->decrement('stock', $quantity);
                }
                
                // Update order total
                $order->update(['total_amount' => $total]);
            });
            
            $endTime = microtime(true);
            $transactionTime = ($endTime - $startTime) * 1000;
            $transactionTimes[] = $transactionTime;
            
            // Individual transaction should complete under 200ms
            $this->assertLessThan(200, $transactionTime, 
                "Transaction {$i} took {$transactionTime}ms, which exceeds 200ms limit");
        }
        
        $averageTransactionTime = array_sum($transactionTimes) / count($transactionTimes);
        
        // Average transaction time should be under 100ms
        $this->assertLessThan(100, $averageTransactionTime, 
            "Average transaction time was {$averageTransactionTime}ms, which exceeds 100ms limit");
    }

    /** @test */
    public function connection_pool_handles_concurrent_queries()
    {
        $startTime = microtime(true);
        $queryTimes = [];
        
        // Simulate 100 concurrent database connections
        for ($i = 0; $i < 100; $i++) {
            $queryStartTime = microtime(true);
            
            // Mix of different query types to stress connection pool
            switch ($i % 4) {
                case 0:
                    Component::where('is_active', true)->count();
                    break;
                case 1:
                    User::where('created_at', '>', now()->subDays(30))->get();
                    break;
                case 2:
                    Order::with('items')->where('status', 'completed')->limit(10)->get();
                    break;
                case 3:
                    ComponentCategory::withCount('components')->get();
                    break;
            }
            
            $queryEndTime = microtime(true);
            $queryTime = ($queryEndTime - $queryStartTime) * 1000;
            $queryTimes[] = $queryTime;
        }
        
        $endTime = microtime(true);
        $totalTime = ($endTime - $startTime) * 1000;
        $averageQueryTime = array_sum($queryTimes) / count($queryTimes);
        
        // 100 concurrent queries should complete under 10 seconds
        $this->assertLessThan(10000, $totalTime, 
            "100 concurrent queries took {$totalTime}ms, which exceeds 10000ms limit");
        
        // Average query time should remain reasonable under load
        $this->assertLessThan(100, $averageQueryTime, 
            "Average query time under load was {$averageQueryTime}ms, which exceeds 100ms limit");
    }

    /** @test */
    public function query_cache_improves_performance()
    {
        // Expensive query that should benefit from caching
        $expensiveQuery = function() {
            return Component::select([
                    'components.*',
                    'categories.name as category_name',
                    DB::raw('COUNT(order_items.id) as sales_count')
                ])
                ->join('component_categories as categories', 'components.category_id', '=', 'categories.id')
                ->leftJoin('order_items', 'components.id', '=', 'order_items.component_id')
                ->where('components.is_active', true)
                ->groupBy('components.id', 'categories.name')
                ->orderBy('sales_count', 'desc')
                ->limit(100)
                ->get();
        };
        
        // First execution (cold)
        $startTime = microtime(true);
        $result1 = $expensiveQuery();
        $endTime = microtime(true);
        $coldTime = ($endTime - $startTime) * 1000;
        
        // Second execution (should be faster if cached)
        $startTime = microtime(true);
        $result2 = $expensiveQuery();
        $endTime = microtime(true);
        $warmTime = ($endTime - $startTime) * 1000;
        
        // Results should be identical
        $this->assertEquals($result1->count(), $result2->count());
        
        // Warm execution should be faster (or at least not significantly slower)
        $performanceRatio = $warmTime / $coldTime;
        $this->assertLessThan(1.5, $performanceRatio, 
            "Warm query was {$performanceRatio}x slower than cold query, indicating poor caching");
    }

    /** @test */
    public function database_handles_deadlock_scenarios()
    {
        $deadlockCount = 0;
        $successCount = 0;
        
        // Simulate potential deadlock scenario with concurrent stock updates
        for ($i = 0; $i < 20; $i++) {
            try {
                DB::transaction(function() {
                    $component1 = Component::lockForUpdate()->find(1);
                    $component2 = Component::lockForUpdate()->find(2);
                    
                    // Simulate processing time
                    usleep(rand(10000, 50000)); // 10-50ms
                    
                    $component1->decrement('stock', 1);
                    $component2->decrement('stock', 1);
                });
                
                $successCount++;
            } catch (\Exception $e) {
                if (strpos($e->getMessage(), 'deadlock') !== false) {
                    $deadlockCount++;
                } else {
                    throw $e;
                }
            }
        }
        
        // Should handle deadlocks gracefully
        $this->assertLessThan(5, $deadlockCount, 
            "Too many deadlocks occurred: {$deadlockCount}");
        
        // Most transactions should succeed
        $this->assertGreaterThan(15, $successCount, 
            "Too few successful transactions: {$successCount}");
    }

    protected function createLargeDataset(): void
    {
        // Create categories
        ComponentCategory::factory()->count(10)->create();
        
        // Create users
        User::factory()->count(1000)->create();
        
        // Create components
        Component::factory()->count(10000)->create([
            'category_id' => function() {
                return ComponentCategory::inRandomOrder()->first()->id;
            },
            'is_active' => true,
            'stock' => rand(0, 100),
        ]);
        
        // Create orders
        Order::factory()->count(5000)->create([
            'user_id' => function() {
                return User::inRandomOrder()->first()->id;
            },
            'status' => 'completed',
        ]);
        
        // Create order items
        $orders = Order::all();
        foreach ($orders as $order) {
            $itemCount = rand(1, 5);
            for ($i = 0; $i < $itemCount; $i++) {
                OrderItem::factory()->create([
                    'order_id' => $order->id,
                    'component_id' => Component::inRandomOrder()->first()->id,
                ]);
            }
        }
        
        // Create builds
        Build::factory()->count(2000)->create([
            'user_id' => function() {
                return User::inRandomOrder()->first()->id;
            },
        ]);
    }
}