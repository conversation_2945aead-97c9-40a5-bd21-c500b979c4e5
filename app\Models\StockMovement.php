<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class StockMovement extends Model
{
    use HasFactory;

    protected $fillable = [
        'component_id',
        'product_id',
        'quantity_change',
        'previous_stock',
        'new_stock',
        'type',
        'reason',
        'reference',
        'metadata'
    ];

    protected $casts = [
        'metadata' => 'array',
        'quantity_change' => 'integer',
        'previous_stock' => 'integer',
        'new_stock' => 'integer'
    ];

    public function component(): BelongsTo
    {
        return $this->belongsTo(Component::class);
    }

    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    public function scopeForComponent($query, $componentId)
    {
        return $query->where('component_id', $componentId);
    }

    public function scopeForProduct($query, $productId)
    {
        return $query->where('product_id', $productId);
    }

    public function scopeByType($query, string $type)
    {
        return $query->where('type', $type);
    }

    public function scopeRecent($query, int $days = 30)
    {
        return $query->where('created_at', '>=', now()->subDays($days));
    }
}
