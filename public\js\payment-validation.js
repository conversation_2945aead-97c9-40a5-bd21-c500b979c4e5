/**
 * Payment Form Validation
 * Provides comprehensive client-side validation for payment forms
 * Requirements: 1.2, 8.2
 */

class PaymentValidator {
    constructor(formSelector = '#payment-form') {
        this.form = document.querySelector(formSelector);
        this.errors = {};
        this.rules = {
            gateway: {
                required: true,
                message: 'Please select a payment gateway'
            },
            amount: {
                required: true,
                numeric: true,
                min: 1,
                max: 999999.99,
                decimal: 2,
                message: 'Please enter a valid amount'
            },
            currency: {
                required: true,
                in: ['INR', 'USD', 'EUR'],
                message: 'Please select a valid currency'
            },
            description: {
                maxLength: 255,
                pattern: /^[a-zA-Z0-9\s\-_.,#()]*$/,
                message: 'Description contains invalid characters'
            }
        };
        
        this.init();
    }
    
    init() {
        if (!this.form) return;
        
        this.setupEventListeners();
        this.setupRealTimeValidation();
        this.preventFormSubmission();
    }
    
    setupEventListeners() {
        // Real-time validation on input
        this.form.addEventListener('input', (e) => {
            this.validateField(e.target);
        });
        
        // Validation on blur for better UX
        this.form.addEventListener('blur', (e) => {
            this.validateField(e.target);
        }, true);
        
        // Gateway selection validation
        this.form.addEventListener('change', (e) => {
            if (e.target.name === 'gateway') {
                this.validateGateway();
                this.updatePaymentButton();
            }
        });
        
        // Amount formatting
        const amountField = this.form.querySelector('[name="amount"]');
        if (amountField) {
            amountField.addEventListener('input', (e) => {
                this.formatAmount(e.target);
            });
            
            amountField.addEventListener('blur', (e) => {
                this.finalizeAmountFormat(e.target);
            });
        }
        
        // Currency change handling
        const currencyField = this.form.querySelector('[name="currency"]');
        if (currencyField) {
            currencyField.addEventListener('change', (e) => {
                this.updateCurrencySymbol(e.target.value);
                this.validateAmount(); // Re-validate amount with new currency
            });
        }
    }
    
    setupRealTimeValidation() {
        // Debounced validation for better performance
        let validationTimeout;
        this.form.addEventListener('input', (e) => {
            clearTimeout(validationTimeout);
            validationTimeout = setTimeout(() => {
                this.validateField(e.target);
                this.updateFormState();
            }, 300);
        });
    }
    
    preventFormSubmission() {
        this.form.addEventListener('submit', (e) => {
            e.preventDefault();
            
            if (this.validateForm()) {
                this.handleFormSubmission();
            } else {
                this.showValidationErrors();
                this.focusFirstError();
            }
        });
    }
    
    validateField(field) {
        const fieldName = field.name;
        const value = field.value;
        const rule = this.rules[fieldName];
        
        if (!rule) return true;
        
        this.clearFieldError(field);
        
        // Required validation
        if (rule.required && (!value || value.trim() === '')) {
            if (fieldName === 'gateway') {
                const checkedGateway = this.form.querySelector('input[name="gateway"]:checked');
                if (!checkedGateway) {
                    this.setFieldError(field, rule.message || `${fieldName} is required`);
                    return false;
                }
            } else {
                this.setFieldError(field, rule.message || `${fieldName} is required`);
                return false;
            }
        }
        
        // Skip other validations if field is empty and not required
        if (!value && !rule.required) return true;
        
        // Numeric validation
        if (rule.numeric && value) {
            const numValue = parseFloat(value);
            if (isNaN(numValue)) {
                this.setFieldError(field, 'Must be a valid number');
                return false;
            }
            
            if (rule.min !== undefined && numValue < rule.min) {
                this.setFieldError(field, `Minimum value is ${this.formatCurrency(rule.min)}`);
                return false;
            }
            
            if (rule.max !== undefined && numValue > rule.max) {
                this.setFieldError(field, `Maximum value is ${this.formatCurrency(rule.max)}`);
                return false;
            }
            
            if (rule.decimal !== undefined) {
                const decimalPlaces = (value.split('.')[1] || '').length;
                if (decimalPlaces > rule.decimal) {
                    this.setFieldError(field, `Maximum ${rule.decimal} decimal places allowed`);
                    return false;
                }
            }
        }
        
        // Length validation
        if (rule.maxLength && value.length > rule.maxLength) {
            this.setFieldError(field, `Maximum ${rule.maxLength} characters allowed`);
            return false;
        }
        
        // Pattern validation
        if (rule.pattern && value && !rule.pattern.test(value)) {
            this.setFieldError(field, rule.message || 'Invalid format');
            return false;
        }
        
        // In validation
        if (rule.in && value && !rule.in.includes(value)) {
            this.setFieldError(field, rule.message || 'Invalid selection');
            return false;
        }
        
        // Custom currency-specific amount validation
        if (fieldName === 'amount' && value) {
            const currency = this.form.querySelector('[name="currency"]')?.value || 'INR';
            const amount = parseFloat(value);
            
            if (currency === 'USD' && amount < 0.50) {
                this.setFieldError(field, 'Minimum amount for USD is $0.50');
                return false;
            }
            
            if (currency === 'EUR' && amount < 0.50) {
                this.setFieldError(field, 'Minimum amount for EUR is €0.50');
                return false;
            }
        }
        
        return true;
    }
    
    validateGateway() {
        const gatewayInputs = this.form.querySelectorAll('input[name="gateway"]');
        const checkedGateway = this.form.querySelector('input[name="gateway"]:checked');
        
        if (gatewayInputs.length > 0 && !checkedGateway) {
            const gatewayContainer = gatewayInputs[0].closest('.gateway-selection') || gatewayInputs[0].parentElement;
            this.setContainerError(gatewayContainer, 'Please select a payment gateway');
            return false;
        }
        
        if (gatewayInputs.length > 0) {
            const gatewayContainer = gatewayInputs[0].closest('.gateway-selection') || gatewayInputs[0].parentElement;
            this.clearContainerError(gatewayContainer);
        }
        
        return true;
    }
    
    validateAmount() {
        const amountField = this.form.querySelector('[name="amount"]');
        if (amountField) {
            return this.validateField(amountField);
        }
        return true;
    }
    
    validateForm() {
        let isValid = true;
        this.errors = {};
        
        // Validate all fields
        const fields = this.form.querySelectorAll('input, select, textarea');
        fields.forEach(field => {
            if (!this.validateField(field)) {
                isValid = false;
            }
        });
        
        // Special validation for gateway selection
        if (!this.validateGateway()) {
            isValid = false;
        }
        
        return isValid;
    }
    
    setFieldError(field, message) {
        this.errors[field.name] = message;
        
        // Add error styling
        field.classList.add('border-red-500', 'focus:border-red-500', 'focus:ring-red-500');
        field.classList.remove('border-gray-300', 'focus:border-indigo-500', 'focus:ring-indigo-500');
        
        // Create or update error message
        const fieldGroup = field.closest('.form-group') || field.parentElement;
        let errorElement = fieldGroup.querySelector('.field-error');
        
        if (!errorElement) {
            errorElement = document.createElement('div');
            errorElement.className = 'field-error text-red-600 text-sm mt-1';
            fieldGroup.appendChild(errorElement);
        }
        
        errorElement.textContent = message;
        errorElement.style.display = 'block';
    }
    
    clearFieldError(field) {
        delete this.errors[field.name];
        
        // Remove error styling
        field.classList.remove('border-red-500', 'focus:border-red-500', 'focus:ring-red-500');
        field.classList.add('border-gray-300', 'focus:border-indigo-500', 'focus:ring-indigo-500');
        
        // Remove error message
        const fieldGroup = field.closest('.form-group') || field.parentElement;
        const errorElement = fieldGroup.querySelector('.field-error');
        if (errorElement) {
            errorElement.remove();
        }
    }
    
    setContainerError(container, message) {
        // Add error styling to container
        container.classList.add('border-red-200', 'bg-red-50');
        
        // Create or update error message
        let errorElement = container.querySelector('.container-error');
        if (!errorElement) {
            errorElement = document.createElement('div');
            errorElement.className = 'container-error text-red-600 text-sm mt-2 flex items-center';
            errorElement.innerHTML = `
                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <span class="error-text"></span>
            `;
            container.appendChild(errorElement);
        }
        
        errorElement.querySelector('.error-text').textContent = message;
        errorElement.style.display = 'flex';
    }
    
    clearContainerError(container) {
        // Remove error styling
        container.classList.remove('border-red-200', 'bg-red-50');
        
        // Remove error message
        const errorElement = container.querySelector('.container-error');
        if (errorElement) {
            errorElement.remove();
        }
    }
    
    formatAmount(field) {
        let value = field.value;
        
        // Remove non-numeric characters except decimal point
        value = value.replace(/[^0-9.]/g, '');
        
        // Ensure only one decimal point
        const parts = value.split('.');
        if (parts.length > 2) {
            value = parts[0] + '.' + parts.slice(1).join('');
        }
        
        // Limit decimal places to 2
        if (parts[1] && parts[1].length > 2) {
            value = parts[0] + '.' + parts[1].substring(0, 2);
        }
        
        field.value = value;
    }
    
    finalizeAmountFormat(field) {
        if (field.value && !isNaN(field.value)) {
            const value = parseFloat(field.value);
            field.value = value.toFixed(2);
        }
    }
    
    updateCurrencySymbol(currency) {
        const symbols = {
            'INR': '₹',
            'USD': '$',
            'EUR': '€'
        };
        
        const symbolElement = this.form.querySelector('.currency-symbol');
        if (symbolElement) {
            symbolElement.textContent = symbols[currency] || '₹';
        }
    }
    
    updatePaymentButton() {
        const submitBtn = this.form.querySelector('[type="submit"]');
        const selectedGateway = this.form.querySelector('input[name="gateway"]:checked');
        
        if (submitBtn && selectedGateway) {
            const gatewayName = selectedGateway.value;
            const gatewayNames = {
                'razorpay': 'Razorpay',
                'payumoney': 'PayUMoney',
                'cashfree': 'Cashfree'
            };
            
            const btnText = submitBtn.querySelector('.btn-text') || submitBtn;
            btnText.innerHTML = `
                <svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                </svg>
                Pay with ${gatewayNames[gatewayName] || 'Selected Gateway'}
            `;
        }
    }
    
    updateFormState() {
        const submitBtn = this.form.querySelector('[type="submit"]');
        const hasErrors = Object.keys(this.errors).length > 0;
        
        if (submitBtn) {
            submitBtn.disabled = hasErrors;
            submitBtn.classList.toggle('opacity-50', hasErrors);
            submitBtn.classList.toggle('cursor-not-allowed', hasErrors);
        }
    }
    
    showValidationErrors() {
        // Show a summary of validation errors
        const errorMessages = Object.values(this.errors);
        if (errorMessages.length > 0) {
            this.showAlert('Please fix the following errors:\n• ' + errorMessages.join('\n• '), 'error');
        }
    }
    
    focusFirstError() {
        const firstErrorField = this.form.querySelector('.border-red-500');
        if (firstErrorField) {
            firstErrorField.focus();
            firstErrorField.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
    }
    
    formatCurrency(amount) {
        const currency = this.form.querySelector('[name="currency"]')?.value || 'INR';
        const symbols = {
            'INR': '₹',
            'USD': '$',
            'EUR': '€'
        };
        
        return symbols[currency] + amount.toFixed(2);
    }
    
    showAlert(message, type = 'info') {
        // Create alert element
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} fixed top-4 right-4 max-w-sm p-4 rounded-lg shadow-lg z-50 transform transition-all duration-300`;
        
        const bgColors = {
            'error': 'bg-red-50 border border-red-200',
            'success': 'bg-green-50 border border-green-200',
            'info': 'bg-blue-50 border border-blue-200'
        };
        
        const textColors = {
            'error': 'text-red-800',
            'success': 'text-green-800',
            'info': 'text-blue-800'
        };
        
        const iconColors = {
            'error': 'text-red-400',
            'success': 'text-green-400',
            'info': 'text-blue-400'
        };
        
        alertDiv.className += ` ${bgColors[type]}`;
        
        alertDiv.innerHTML = `
            <div class="flex items-start">
                <svg class="w-5 h-5 ${iconColors[type]} mt-0.5 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <div class="flex-1">
                    <p class="text-sm font-medium ${textColors[type]}">Validation Error</p>
                    <p class="text-sm ${textColors[type]} mt-1 whitespace-pre-line">${message}</p>
                </div>
                <button type="button" class="ml-2 ${iconColors[type]} hover:opacity-75" onclick="this.parentElement.parentElement.remove()">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
        `;
        
        document.body.appendChild(alertDiv);
        
        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (alertDiv.parentElement) {
                alertDiv.style.transform = 'translateX(100%)';
                setTimeout(() => alertDiv.remove(), 300);
            }
        }, 5000);
    }
    
    async handleFormSubmission() {
        // This method should be overridden by the implementing page
        console.log('Form validation passed, ready for submission');
        
        // Dispatch custom event for form submission
        const event = new CustomEvent('paymentFormValidated', {
            detail: {
                formData: new FormData(this.form),
                validator: this
            }
        });
        
        document.dispatchEvent(event);
    }
}

// Auto-initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    if (document.querySelector('#payment-form')) {
        window.paymentValidator = new PaymentValidator();
    }
});

// Export for use in other scripts
window.PaymentValidator = PaymentValidator;