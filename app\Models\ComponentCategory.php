<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class ComponentCategory extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'slug',
        'description',
        'icon',
        'display_order',
        'is_required',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'display_order' => 'integer',
        'is_required' => 'boolean',
    ];

    /**
     * Get the components for the category.
     */
    public function components(): HasMany
    {
        return $this->hasMany(Component::class, 'category_id');
    }

    /**
     * Get the build components for the category.
     */
    public function buildComponents(): Has<PERSON>any
    {
        return $this->hasMany(BuildComponent::class, 'category_id');
    }

    /**
     * Get the compatibility rules for the category.
     */
    public function compatibilityRules(): HasMany
    {
        return $this->hasMany(ComponentCompatibility::class, 'category_id');
    }

    /**
     * Scope a query to order by display order.
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('display_order');
    }

    /**
     * Scope a query to only include required categories.
     */
    public function scopeRequired($query)
    {
        return $query->where('is_required', true);
    }
}