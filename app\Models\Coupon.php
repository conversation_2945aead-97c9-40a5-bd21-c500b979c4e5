<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Facades\Auth;

class Coupon extends Model
{
    use HasFactory;

    protected $fillable = [
        'code',
        'name',
        'description',
        'type',
        'value',
        'minimum_amount',
        'maximum_discount',
        'usage_limit',
        'usage_limit_per_user',
        'used_count',
        'starts_at',
        'expires_at',
        'is_active',
        'applicable_products',
        'applicable_categories',
        'excluded_products',
        'excluded_categories',
    ];

    protected $casts = [
        'value' => 'decimal:2',
        'minimum_amount' => 'decimal:2',
        'maximum_discount' => 'decimal:2',
        'usage_limit' => 'integer',
        'usage_limit_per_user' => 'integer',
        'used_count' => 'integer',
        'starts_at' => 'datetime',
        'expires_at' => 'datetime',
        'is_active' => 'boolean',
        'applicable_products' => 'array',
        'applicable_categories' => 'array',
        'excluded_products' => 'array',
        'excluded_categories' => 'array',
    ];

    /**
     * Get coupon usages.
     */
    public function usages(): HasMany
    {
        return $this->hasMany(CouponUsage::class);
    }

    /**
     * Scope to get only active coupons.
     */
    public function scopeActive(Builder $query): Builder
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get valid coupons (active and within date range).
     */
    public function scopeValid(Builder $query): Builder
    {
        $now = now();
        
        return $query->active()
            ->where(function ($q) use ($now) {
                $q->whereNull('starts_at')->orWhere('starts_at', '<=', $now);
            })
            ->where(function ($q) use ($now) {
                $q->whereNull('expires_at')->orWhere('expires_at', '>=', $now);
            });
    }

    /**
     * Check if coupon is valid for use.
     */
    public function isValid(): bool
    {
        if (!$this->is_active) {
            return false;
        }

        $now = now();

        // Check start date
        if ($this->starts_at && $this->starts_at->gt($now)) {
            return false;
        }

        // Check expiry date
        if ($this->expires_at && $this->expires_at->lt($now)) {
            return false;
        }

        // Check usage limit
        if ($this->usage_limit && $this->used_count >= $this->usage_limit) {
            return false;
        }

        return true;
    }

    /**
     * Check if coupon can be used by a specific user.
     */
    public function canBeUsedBy(int $userId): bool
    {
        if (!$this->isValid()) {
            return false;
        }

        // Check per-user usage limit
        if ($this->usage_limit_per_user) {
            $userUsageCount = $this->usages()->where('user_id', $userId)->count();
            if ($userUsageCount >= $this->usage_limit_per_user) {
                return false;
            }
        }

        return true;
    }

    /**
     * Check if coupon is applicable to specific products.
     */
    public function isApplicableToProducts(array $productIds): bool
    {
        // If no specific products/categories are set, coupon applies to all
        if (empty($this->applicable_products) && empty($this->applicable_categories)) {
            return !$this->hasExcludedProducts($productIds);
        }

        // Check if any product is in applicable products
        if (!empty($this->applicable_products)) {
            $hasApplicableProduct = !empty(array_intersect($productIds, $this->applicable_products));
            if ($hasApplicableProduct && !$this->hasExcludedProducts($productIds)) {
                return true;
            }
        }

        // Check if any product is in applicable categories (including child categories)
        if (!empty($this->applicable_categories)) {
            $products = Product::whereIn('id', $productIds)->get();

            // Get all applicable category IDs including children
            $applicableCategoryIds = collect($this->applicable_categories);
            foreach ($this->applicable_categories as $categoryId) {
                $category = ProductCategory::find($categoryId);
                if ($category) {
                    $childrenIds = $category->getAllChildren()->pluck('id');
                    $applicableCategoryIds = $applicableCategoryIds->merge($childrenIds);
                }
            }
            $applicableCategoryIds = $applicableCategoryIds->unique()->toArray();

            foreach ($products as $product) {
                if ($product->category_id && in_array($product->category_id, $applicableCategoryIds)) {
                    if (!$this->hasExcludedProducts([$product->id])) {
                        return true;
                    }
                }
            }
        }

        return false;
    }

    /**
     * Check if products are in excluded list.
     */
    private function hasExcludedProducts(array $productIds): bool
    {
        // Check excluded products
        if (!empty($this->excluded_products)) {
            if (!empty(array_intersect($productIds, $this->excluded_products))) {
                return true;
            }
        }

        // Check excluded categories
        if (!empty($this->excluded_categories)) {
            $products = Product::whereIn('id', $productIds)->get();
            foreach ($products as $product) {
                if ($product->category_id && in_array($product->category_id, $this->excluded_categories)) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * Calculate discount amount for given subtotal.
     */
    public function calculateDiscount(float $subtotal): float
    {
        if ($this->minimum_amount && $subtotal < $this->minimum_amount) {
            return 0;
        }

        $discount = 0;

        if ($this->type === 'fixed') {
            $discount = $this->value;
        } elseif ($this->type === 'percentage') {
            $discount = ($subtotal * $this->value) / 100;
        }

        // Apply maximum discount limit
        if ($this->maximum_discount && $discount > $this->maximum_discount) {
            $discount = $this->maximum_discount;
        }

        // Discount cannot exceed subtotal
        return min($discount, $subtotal);
    }

    /**
     * Apply coupon and record usage.
     */
    public function apply(int $userId, float $subtotal, ?int $orderId = null): float
    {
        if (!$this->canBeUsedBy($userId)) {
            throw new \Exception('Coupon cannot be used by this user.');
        }

        $discountAmount = $this->calculateDiscount($subtotal);

        if ($discountAmount > 0) {
            // Record usage
            CouponUsage::create([
                'coupon_id' => $this->id,
                'user_id' => $userId,
                'order_id' => $orderId,
                'discount_amount' => $discountAmount,
            ]);

            // Increment usage count
            $this->increment('used_count');
        }

        return $discountAmount;
    }

    /**
     * Get formatted discount value for display.
     */
    public function getFormattedValueAttribute(): string
    {
        if ($this->type === 'fixed') {
            return '₹' . number_format($this->value, 2);
        }

        return $this->value . '%';
    }

    /**
     * Get coupon status.
     */
    public function getStatusAttribute(): string
    {
        if (!$this->is_active) {
            return 'Inactive';
        }

        $now = now();

        if ($this->starts_at && $this->starts_at->gt($now)) {
            return 'Scheduled';
        }

        if ($this->expires_at && $this->expires_at->lt($now)) {
            return 'Expired';
        }

        if ($this->usage_limit && $this->used_count >= $this->usage_limit) {
            return 'Used Up';
        }

        return 'Active';
    }

    /**
     * Find coupon by code.
     */
    public static function findByCode(string $code): ?self
    {
        return static::where('code', strtoupper($code))->first();
    }

    /**
     * Validate and get coupon by code.
     */
    public static function validateCode(string $code, int $userId, array $productIds = [], float $subtotal = 0): array
    {
        $coupon = static::findByCode($code);

        if (!$coupon) {
            return ['valid' => false, 'message' => 'Invalid coupon code.'];
        }

        if (!$coupon->canBeUsedBy($userId)) {
            return ['valid' => false, 'message' => 'This coupon cannot be used.'];
        }

        if (!empty($productIds) && !$coupon->isApplicableToProducts($productIds)) {
            return ['valid' => false, 'message' => 'This coupon is not applicable to selected products.'];
        }

        if ($coupon->minimum_amount && $subtotal < $coupon->minimum_amount) {
            return [
                'valid' => false, 
                'message' => 'Minimum order amount of ₹' . number_format($coupon->minimum_amount, 2) . ' required.'
            ];
        }

        $discount = $coupon->calculateDiscount($subtotal);

        return [
            'valid' => true,
            'coupon' => $coupon,
            'discount' => $discount,
            'message' => 'Coupon applied successfully!'
        ];
    }
}