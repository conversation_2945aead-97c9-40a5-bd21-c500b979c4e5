<?php

// =============================================================================
// MIGRATIONS
// =============================================================================

// database/migrations/create_payment_gateways_table.php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('payment_gateways', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('slug')->unique();
            $table->string('provider'); // razorpay, payumoney, cashfree
            $table->boolean('is_active')->default(false);
            $table->boolean('is_sandbox')->default(true);
            $table->json('settings'); // Store gateway-specific settings
            $table->decimal('transaction_fee_percentage', 5, 2)->default(0);
            $table->decimal('transaction_fee_fixed', 10, 2)->default(0);
            $table->integer('priority')->default(0); // For ordering gateways
            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::dropIfExists('payment_gateways');
    }
};

// database/migrations/create_payment_transactions_table.php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('payment_transactions', function (Blueprint $table) {
            $table->id();
            $table->string('transaction_id')->unique();
            $table->string('gateway_transaction_id')->nullable();
            $table->foreignId('payment_gateway_id')->constrained();
            $table->foreignId('order_id')->constrained();
            $table->foreignId('user_id')->constrained();
            $table->decimal('amount', 10, 2);
            $table->string('currency', 3)->default('INR');
            $table->enum('status', ['pending', 'processing', 'completed', 'failed', 'cancelled', 'refunded'])->default('pending');
            $table->enum('type', ['payment', 'refund'])->default('payment');
            $table->json('gateway_response')->nullable();
            $table->json('metadata')->nullable();
            $table->timestamp('processed_at')->nullable();
            $table->timestamps();
            
            $table->index(['status', 'created_at']);
            $table->index(['gateway_transaction_id']);
        });
    }

    public function down()
    {
        Schema::dropIfExists('payment_transactions');
    }
};

// =============================================================================
// MODELS
// =============================================================================

// app/Models/PaymentGateway.php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PaymentGateway extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'slug',
        'provider',
        'is_active',
        'is_sandbox',
        'settings',
        'transaction_fee_percentage',
        'transaction_fee_fixed',
        'priority'
    ];

    protected $casts = [
        'settings' => 'array',
        'is_active' => 'boolean',
        'is_sandbox' => 'boolean',
        'transaction_fee_percentage' => 'decimal:2',
        'transaction_fee_fixed' => 'decimal:2'
    ];

    public function transactions()
    {
        return $this->hasMany(PaymentTransaction::class);
    }

    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeOrderedByPriority($query)
    {
        return $query->orderBy('priority', 'desc');
    }

    public function calculateTransactionFee($amount)
    {
        $percentageFee = ($amount * $this->transaction_fee_percentage) / 100;
        return $percentageFee + $this->transaction_fee_fixed;
    }
}

// app/Models/PaymentTransaction.php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class PaymentTransaction extends Model
{
    use HasFactory;

    protected $fillable = [
        'transaction_id',
        'gateway_transaction_id',
        'payment_gateway_id',
        'order_id',
        'user_id',
        'amount',
        'currency',
        'status',
        'type',
        'gateway_response',
        'metadata',
        'processed_at'
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'gateway_response' => 'array',
        'metadata' => 'array',
        'processed_at' => 'datetime'
    ];

    protected static function boot()
    {
        parent::boot();
        
        static::creating(function ($transaction) {
            if (empty($transaction->transaction_id)) {
                $transaction->transaction_id = 'TXN_' . strtoupper(Str::random(12));
            }
        });
    }

    public function gateway()
    {
        return $this->belongsTo(PaymentGateway::class, 'payment_gateway_id');
    }

    public function order()
    {
        return $this->belongsTo(Order::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    public function scopeFailed($query)
    {
        return $query->where('status', 'failed');
    }

    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    public function isCompleted()
    {
        return $this->status === 'completed';
    }

    public function isFailed()
    {
        return $this->status === 'failed';
    }

    public function isPending()
    {
        return $this->status === 'pending';
    }
}


// =============================================================================
// INTERFACES
// =============================================================================

// app/Contracts/PaymentGatewayInterface.php
<?php

namespace App\Contracts;

use App\Models\PaymentTransaction;

interface PaymentGatewayInterface
{
    /**
     * Initialize payment with the gateway
     */
    public function initiatePayment(array $paymentData): array;

    /**
     * Process payment callback/webhook
     */
    public function processCallback(array $callbackData): array;

    /**
     * Verify payment status
     */
    public function verifyPayment(string $transactionId): array;

    /**
     * Process refund
     */
    public function processRefund(PaymentTransaction $transaction, float $amount): array;

    /**
     * Get payment status from gateway
     */
    public function getPaymentStatus(string $gatewayTransactionId): string;

    /**
     * Validate webhook signature
     */
    public function validateWebhook(array $payload, string $signature): bool;

    /**
     * Get gateway configuration requirements
     */
    public function getConfigurationFields(): array;

    /**
     * Test gateway connection
     */
    public function testConnection(): bool;
}


// =============================================================================
// SERVICES
// =============================================================================

// app/Services/PaymentGatewayFactory.php


// app/Services/PaymentService.php


// app/Services/Gateways/RazorpayService.php

// app/Services/Gateways/PayUmoneyService.php


// app/Services/Gateways/CashfreeService.php


// app/Services/WebhookService.php


// app/Services/TransactionService.php

// app/Services/GatewaySettingsService.php


// =============================================================================
// CONTROLLERS
// =============================================================================

// app/Http/Controllers/PaymentController.php

// app/Http/Controllers/WebhookController.php

// app/Http/Controllers/Admin/GatewayController.php


// =============================================================================
// ROUTES
// =============================================================================

// routes/web.php







// =============================================================================
// BLADE VIEWS
// =============================================================================

// resources/views/payments/create.blade.php


// resources/views/payments/payumoney-redirect.blade.php


// resources/views/payments/success.blade.php

// resources/views/payments/failed.blade.php

// resources/views/admin/gateways/index.blade.php
// resources/views/layouts/app.blade.php

// =============================================================================
// SERVICE PROVIDER
// =============================================================================

// app/Providers/PaymentServiceProvider.php

// =============================================================================
// SEEDERS
// =============================================================================

// database/seeders/GatewaySettingsSeeder.php
