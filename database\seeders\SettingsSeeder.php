<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class SettingsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $settings = [
            [
                'key' => 'site_name',
                'value' => 'PC Builder',
                'type' => 'string',
                'group' => 'general'
            ],
            [
                'key' => 'site_description',
                'value' => 'Build your custom PC with our easy-to-use PC Builder tool',
                'type' => 'string',
                'group' => 'general'
            ],
            [
                'key' => 'site_logo',
                'value' => null,
                'type' => 'file',
                'group' => 'general'
            ],
            [
                'key' => 'site_favicon',
                'value' => null,
                'type' => 'file',
                'group' => 'general'
            ],
            [
                'key' => 'maintenance_mode',
                'value' => '0',
                'type' => 'boolean',
                'group' => 'general'
            ],
            [
                'key' => 'login_enabled',
                'value' => '1',
                'type' => 'boolean',
                'group' => 'general'
            ]
        ];

        foreach ($settings as $setting) {
            DB::table('settings')->insert($setting);
        }
    }
}