<?php

namespace Database\Factories;

use App\Models\Order;
use App\Models\Component;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\OrderItem>
 */
class OrderItemFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $component = Component::factory()->create();
        $quantity = $this->faker->numberBetween(1, 5);
        
        return [
            'order_id' => Order::factory(),
            'component_id' => $component->id,
            'name' => $component->name,
            'quantity' => $quantity,
            'price' => $component->price,
            'options' => $this->faker->optional(0.3)->passthrough([
                'color' => $this->faker->colorName(),
                'warranty' => $this->faker->randomElement(['1 year', '2 years', '3 years']),
            ]),
        ];
    }
}