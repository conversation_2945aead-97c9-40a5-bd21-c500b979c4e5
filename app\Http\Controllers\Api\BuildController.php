<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Build;
use App\Models\Component;
use App\Services\BuilderService;
use App\Services\CompatibilityService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;

class BuildController extends Controller
{
    public function __construct(
        private BuilderService $builderService,
        private CompatibilityService $compatibilityService
    ) {}

    /**
     * Display a listing of builds.
     */
    public function index(Request $request): JsonResponse
    {
        $request->validate([
            'public' => 'sometimes|boolean',
            'user_id' => 'sometimes|integer|exists:users,id',
            'search' => 'sometimes|string|max:255',
            'sort' => 'sometimes|string|in:name,created_at,total_price',
            'direction' => 'sometimes|string|in:asc,desc',
            'per_page' => 'sometimes|integer|min:1|max:100',
        ]);

        $query = Build::with(['user', 'components.component.category']);

        // Filter by public builds or user's own builds
        if ($request->boolean('public')) {
            $query->where('is_public', true);
        } elseif (Auth::check()) {
            $query->where(function ($q) {
                $q->where('user_id', Auth::id())
                  ->orWhere('is_public', true);
            });
        } else {
            $query->where('is_public', true);
        }

        // Filter by specific user
        if ($request->has('user_id')) {
            $query->where('user_id', $request->user_id);
        }

        // Search functionality
        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // Apply sorting
        $sort = $request->get('sort', 'created_at');
        $direction = $request->get('direction', 'desc');
        $query->orderBy($sort, $direction);

        $perPage = $request->get('per_page', 15);
        $builds = $query->paginate($perPage);

        return response()->json([
            'data' => $builds->items(),
            'meta' => [
                'current_page' => $builds->currentPage(),
                'last_page' => $builds->lastPage(),
                'per_page' => $builds->perPage(),
                'total' => $builds->total(),
                'from' => $builds->firstItem(),
                'to' => $builds->lastItem(),
            ],
            'links' => [
                'first' => $builds->url(1),
                'last' => $builds->url($builds->lastPage()),
                'prev' => $builds->previousPageUrl(),
                'next' => $builds->nextPageUrl(),
            ]
        ]);
    }

    /**
     * Store a newly created build.
     */
    public function store(Request $request): JsonResponse
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'is_public' => 'boolean',
            'components' => 'required|array|min:1',
            'components.*.component_id' => 'required|integer|exists:components,id',
            'components.*.quantity' => 'sometimes|integer|min:1|max:10',
        ]);

        $user = Auth::user();
        if (!$user) {
            return response()->json(['message' => 'Authentication required'], 401);
        }

        // Create the build
        $build = Build::create([
            'user_id' => $user->id,
            'name' => $request->name,
            'description' => $request->description,
            'is_public' => $request->boolean('is_public', false),
            'share_token' => Str::random(32),
        ]);

        // Add components to the build
        $totalPrice = 0;
        $components = [];
        
        foreach ($request->components as $componentData) {
            $component = Component::findOrFail($componentData['component_id']);
            $quantity = $componentData['quantity'] ?? 1;
            
            $build->addComponent($component, $quantity);
            $totalPrice += $component->price * $quantity;
            $components[] = $component;
        }

        // Update total price and check compatibility
        $build->update(['total_price' => $totalPrice]);
        $compatibilityResult = $this->compatibilityService->checkCompatibility($components);
        $build->update(['compatibility_issues' => $compatibilityResult->getIssues()]);

        $build->load(['components.component.category', 'user']);

        return response()->json([
            'data' => $build,
            'compatibility' => [
                'is_compatible' => $compatibilityResult->isCompatible(),
                'issues' => $compatibilityResult->getIssues(),
                'warnings' => $compatibilityResult->getWarnings(),
            ]
        ], 201);
    }

    /**
     * Display the specified build.
     */
    public function show(Build $build): JsonResponse
    {
        // Check if user can view this build
        if (!$build->is_public && (!Auth::check() || Auth::id() !== $build->user_id)) {
            return response()->json(['message' => 'Build not found'], 404);
        }

        $build->load(['user', 'components.component.category']);

        // Get compatibility information
        $components = $build->components->pluck('component')->toArray();
        $compatibilityResult = $this->compatibilityService->checkCompatibility($components);

        return response()->json([
            'data' => $build,
            'compatibility' => [
                'is_compatible' => $compatibilityResult->isCompatible(),
                'issues' => $compatibilityResult->getIssues(),
                'warnings' => $compatibilityResult->getWarnings(),
            ]
        ]);
    }

    /**
     * Update the specified build.
     */
    public function update(Request $request, Build $build): JsonResponse
    {
        // Check if user owns this build
        if (!Auth::check() || Auth::id() !== $build->user_id) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $request->validate([
            'name' => 'sometimes|string|max:255',
            'description' => 'nullable|string|max:1000',
            'is_public' => 'sometimes|boolean',
            'components' => 'sometimes|array',
            'components.*.component_id' => 'required_with:components|integer|exists:components,id',
            'components.*.quantity' => 'sometimes|integer|min:1|max:10',
        ]);

        // Update basic build information
        $build->update($request->only(['name', 'description', 'is_public']));

        // Update components if provided
        if ($request->has('components')) {
            // Remove existing components
            $build->components()->delete();

            // Add new components
            $totalPrice = 0;
            $components = [];
            
            foreach ($request->components as $componentData) {
                $component = Component::findOrFail($componentData['component_id']);
                $quantity = $componentData['quantity'] ?? 1;
                
                $build->addComponent($component, $quantity);
                $totalPrice += $component->price * $quantity;
                $components[] = $component;
            }

            // Update total price and check compatibility
            $build->update(['total_price' => $totalPrice]);
            $compatibilityResult = $this->compatibilityService->checkCompatibility($components);
            $build->update(['compatibility_issues' => $compatibilityResult->getIssues()]);
        }

        $build->load(['components.component.category', 'user']);

        return response()->json([
            'data' => $build,
            'compatibility' => isset($compatibilityResult) ? [
                'is_compatible' => $compatibilityResult->isCompatible(),
                'issues' => $compatibilityResult->getIssues(),
                'warnings' => $compatibilityResult->getWarnings(),
            ] : null
        ]);
    }

    /**
     * Remove the specified build.
     */
    public function destroy(Build $build): JsonResponse
    {
        // Check if user owns this build
        if (!Auth::check() || Auth::id() !== $build->user_id) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $build->delete();

        return response()->json(['message' => 'Build deleted successfully']);
    }

    /**
     * Get build by share token.
     */
    public function shared(string $token): JsonResponse
    {
        $build = Build::where('share_token', $token)
            ->where('is_public', true)
            ->with(['user', 'components.component.category'])
            ->first();

        if (!$build) {
            return response()->json(['message' => 'Build not found'], 404);
        }

        // Get compatibility information
        $components = $build->components->pluck('component')->toArray();
        $compatibilityResult = $this->compatibilityService->checkCompatibility($components);

        return response()->json([
            'data' => $build,
            'compatibility' => [
                'is_compatible' => $compatibilityResult->isCompatible(),
                'issues' => $compatibilityResult->getIssues(),
                'warnings' => $compatibilityResult->getWarnings(),
            ]
        ]);
    }

    /**
     * Check compatibility for a set of components.
     */
    public function checkCompatibility(Request $request): JsonResponse
    {
        $request->validate([
            'component_ids' => 'required|array|min:1',
            'component_ids.*' => 'integer|exists:components,id',
        ]);

        $components = Component::whereIn('id', $request->component_ids)->get();
        $compatibilityResult = $this->compatibilityService->checkCompatibility($components->toArray());

        return response()->json([
            'is_compatible' => $compatibilityResult->isCompatible(),
            'issues' => $compatibilityResult->getIssues(),
            'warnings' => $compatibilityResult->getWarnings(),
            'components' => $components
        ]);
    }
}