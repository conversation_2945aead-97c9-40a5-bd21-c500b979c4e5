<?php
namespace App\Http\Controllers;

use App\Models\BlogPost;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Models\Comment;
use App\Http\Requests\CommentRequest;
use Illuminate\Support\Facades\Auth;
use App\Models\BlogPostCategory;
use App\Models\BlogPostTag;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\JsonResponse;

class BlogPostController extends Controller
{

    use AuthorizesRequests;

    public function index()
    {
        $posts = BlogPost::published()
            ->with([
                'author',
                'comments' => function ($query) {
                    $query->whereNull('parent_id')
                        ->where('is_approved', true);
                },
                'tags',
                'category'
            ])
            ->latest('published_at')
            ->paginate(10);

        $popularBlogCategories = BlogPostCategory::withCount('posts')
            ->orderBy('posts_count', 'desc')
            ->limit(5)
            ->get();

        $featuredBlogPosts = BlogPost::published()
            ->where('featured', true)
            ->latest('published_at')
            ->limit(5)
            ->get();

        $path = 'blog';
        $breadcrumbController = new BreadcrumbController();
        $breadcrumbs = $breadcrumbController->generateBreadcrumb($path);

        $pageTitle = 'Blog';
        $metaDescription = 'Blog';

        return view('blog-post.index', compact('posts', 'breadcrumbs', 'pageTitle', 'metaDescription', 'popularBlogCategories', 'featuredBlogPosts'));
    }



    /**
     * API endpoint for blog post search autocomplete
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function autocomplete(Request $request): JsonResponse
    {
        $query = $request->get('query');
        
        if (empty($query) || strlen($query) < 2) {
            return response()->json([]);
        }
        
        $posts = BlogPost::published()
            ->where('title', 'like', "%{$query}%")
            ->orderBy('title')
            ->limit(5)
            ->get(['id', 'title', 'slug']);
            
        return response()->json($posts);
    }

    public function show($slug)
    {
        $post = BlogPost::published()
            ->where('slug', $slug)
            ->with([
                'author',
                'comments' => function ($query) {
                    $query->whereNull('parent_id')
                        ->where('is_approved', true)
                        ->with([
                            'author',
                            'replies' => function ($q) {
                                $q->where('is_approved', true)
                                    ->with('author');
                            }
                        ])
                        ->latest();
                },
                'tags',
                'category'
            ])
            ->firstOrFail();

        $relatedPosts = BlogPost::published()
            ->where('id', '!=', $post->id)
            ->where('blog_post_category_id', $post->blog_post_category_id)
            ->latest()
            ->limit(5)
            ->get();

        // Fix: Use post title instead of pageTitle for the breadcrumb
        $path = 'blog/' . $post->title;
        $breadcrumbController = new BreadcrumbController();
        $breadcrumbs = $breadcrumbController->generateBreadcrumb($path);

        // SEO handling
        $pageTitle = $post->pageTitle ?? $post->title;
        $metaDescription = $post->meta_metaDescription ?? $post->pageTitle ?? $post->title;
        $keywords = is_array($post->meta_keywords)
            ? implode(', ', $post->meta_keywords)
            : ($post->meta_keywords ?? $post->pageTitle);
        $image = $post->featured_image;

        setSEO($pageTitle, $metaDescription, $keywords, $image);

        return view('blog-post.show', compact(
            'post',
            'breadcrumbs',
            'pageTitle',
            'metaDescription',
            'keywords',
            'relatedPosts',
            'image'
        ));
    }

    public function viewCategory($slug)
    {
        $category = BlogPostCategory::where('slug', $slug)->firstOrFail();
        $posts = $category->posts()
            ->published()
            ->with(['author', 'tags'])
            ->latest('published_at')
            ->paginate(10);

        $path = 'blog/' . $slug;
        $breadcrumbController = new BreadcrumbController();
        $breadcrumbs = $breadcrumbController->generateBreadcrumb($path);
        $pageTitle = $category->name;
        $metaDescription = $category->metaDescription;

        return view('blog-post.category', compact('posts', 'category', 'breadcrumbs', 'pageTitle', 'metaDescription'));
    }

    public function viewTag($slug)
    {
        $tag = BlogPostTag::where('slug', $slug)->firstOrFail();
        $posts = $tag->posts()
            ->published()
            ->with(['author', 'category'])
            ->latest('published_at')
            ->paginate(10);

        $path = 'blog/' . $slug;
        $breadcrumbController = new BreadcrumbController();
        $breadcrumbs = $breadcrumbController->generateBreadcrumb($path);

        $pageTitle = 'Tag: ' . $tag->name;
        $metaDescription = 'Posts tagged with ' . $tag->name;

        return view('blog-post.tag', compact('posts', 'tag', 'breadcrumbs', 'pageTitle', 'metaDescription'));
    }

    // Comment-related methods
    public function storeComment(CommentRequest $request, BlogPost $post)
    {
        $comment = new Comment($request->validated());
        $comment->blogPost()->associate($post);
        $comment->author()->associate(Auth::user());
        $comment->is_approved = false;

        if ($request->has('parent_id')) {
            $parentComment = Comment::findOrFail($request->parent_id);
            if ($parentComment->blog_post_id === $post->id) {
                $comment->parent()->associate($parentComment);
            }
        }

        $comment->save();

        return redirect()
            ->back()
            ->with('success', 'Comment added successfully');
    }

    public function updateComment(CommentRequest $request, Comment $comment)
    {
        $this->authorize('update', $comment);
        $comment->update($request->validated());

        return redirect()
            ->back()
            ->with('success', 'Comment updated successfully');
    }

    public function deleteComment(Comment $comment)
    {
        $this->authorize('delete', $comment);

        if ($comment->replies()->exists()) {
            $comment->content = '[Comment deleted]';
            $comment->save();
        } else {
            $comment->delete();
        }

        return redirect()
            ->back()
            ->with('success', 'Comment deleted successfully');
    }

    // Add new method for tag management
    public function manageTags(Request $request, BlogPost $post)
    {
        $validated = $request->validate([
            'tags' => 'required|array',
            'tags.*' => 'exists:blog_post_tags,id'
        ]);

        $post->tags()->sync($validated['tags']);

        return redirect()->back()
            ->with('success', 'Tags updated successfully');
    }
}
