<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use App\Services\PaymentGatewayFactory;
use App\Services\PaymentService;
use App\Services\WebhookService;
use App\Services\TransactionService;
use App\Services\GatewaySettingsService;

class PaymentServiceProvider extends ServiceProvider
{
    public function register(): void
    {
        $this->app->singleton(PaymentGatewayFactory::class);
        $this->app->singleton(PaymentService::class);
        $this->app->singleton(WebhookService::class);
        $this->app->singleton(TransactionService::class);
        $this->app->singleton(GatewaySettingsService::class);
    }

    public function boot(): void
    {
        //
    }
}
