<?php

namespace Tests\Unit\Models;

use App\Models\Coupon;
use App\Models\CouponUsage;
use App\Models\Product;
use App\Models\ProductCategory;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class CouponTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        $this->artisan('migrate');
    }

    /** @test */
    public function it_can_create_a_coupon()
    {
        $coupon = Coupon::factory()->create([
            'code' => 'TEST20',
            'name' => 'Test Coupon',
            'type' => 'percentage',
            'value' => 20,
        ]);

        $this->assertDatabaseHas('coupons', [
            'code' => 'TEST20',
            'name' => 'Test Coupon',
            'type' => 'percentage',
            'value' => 20,
        ]);
    }

    /** @test */
    public function it_has_usages_relationship()
    {
        $coupon = Coupon::factory()->create();
        $user = User::factory()->create();

        CouponUsage::factory()->create([
            'coupon_id' => $coupon->id,
            'user_id' => $user->id,
        ]);

        $this->assertCount(1, $coupon->usages);
    }

    /** @test */
    public function it_has_active_scope()
    {
        Coupon::factory()->create(['is_active' => true,]);
        Coupon::factory()->create(['is_active' => false]);

        $activeCoupons = Coupon::active()->get();

        $this->assertCount(1, $activeCoupons);
        $this->assertTrue($activeCoupons->first()->is_active);
    }

    /** @test */
    public function it_has_valid_scope()
    {
        $now = Carbon::now();

        // Valid coupon
        Coupon::factory()->create([
            'is_active' => true,
            'starts_at' => $now->copy()->subDay(),
            'expires_at' => $now->copy()->addDay(),
        ]);

        // Inactive coupon
        Coupon::factory()->create([
            'is_active' => false,
            'starts_at' => $now->copy()->subDay(),
            'expires_at' => $now->copy()->addDay(),
        ]);

        // Not started yet
        Coupon::factory()->create([
            'is_active' => true,
            'starts_at' => $now->copy()->addDay(),
            'expires_at' => $now->copy()->addWeek(),
        ]);

        // Expired
        Coupon::factory()->create([
            'is_active' => true,
            'starts_at' => $now->copy()->subWeek(),
            'expires_at' => $now->copy()->subDay(),
        ]);

        $validCoupons = Coupon::valid()->get();

        $this->assertCount(1, $validCoupons);
    }

    /** @test */
    public function it_checks_if_coupon_is_valid()
    {
        $now = Carbon::now();

        $validCoupon = Coupon::factory()->create([
            'is_active' => true,
            'starts_at' => $now->copy()->subDay(),
            'expires_at' => $now->copy()->addDay(),
            'usage_limit' => 100,
            'used_count' => 50,
        ]);

        $inactiveCoupon = Coupon::factory()->create(['is_active' => false]);
        $notStartedCoupon = Coupon::factory()->create(['starts_at' => $now->copy()->addDay()]);
        $expiredCoupon = Coupon::factory()->create(['expires_at' => $now->copy()->subDay()]);
        $usedUpCoupon = Coupon::factory()->create([
            'usage_limit' => 10,
            'used_count' => 10,
        ]);

        $this->assertTrue($validCoupon->isValid());
        $this->assertFalse($inactiveCoupon->isValid());
        $this->assertFalse($notStartedCoupon->isValid());
        $this->assertFalse($expiredCoupon->isValid());
        $this->assertFalse($usedUpCoupon->isValid());
    }

    /** @test */
    public function it_checks_if_coupon_can_be_used_by_user()
    {
        $user = User::factory()->create();
        $coupon = Coupon::factory()->create([
            'is_active' => true,
            'usage_limit_per_user' => 2,
            'starts_at' => null,
            'expires_at' => null,
        ]);

        // User hasn't used the coupon yet
        $this->assertTrue($coupon->canBeUsedBy($user->id));

        // User has used it once
        CouponUsage::factory()->create([
            'coupon_id' => $coupon->id,
            'user_id' => $user->id,
        ]);

        $this->assertTrue($coupon->canBeUsedBy($user->id));

        // User has used it twice (limit reached)
        CouponUsage::factory()->create([
            'coupon_id' => $coupon->id,
            'user_id' => $user->id,
        ]);

        $this->assertFalse($coupon->canBeUsedBy($user->id));
    }

    /** @test */
    public function it_checks_if_applicable_to_products()
    {
        $category = ProductCategory::factory()->create();
        $product1 = Product::factory()->create(['category_id' => $category->id]);
        $product2 = Product::factory()->create();

        // Coupon applicable to all products
        $allProductsCoupon = Coupon::factory()->create();
        $this->assertTrue($allProductsCoupon->isApplicableToProducts([$product1->id, $product2->id]));

        // Coupon applicable to specific products
        $specificProductCoupon = Coupon::factory()->create([
            'applicable_products' => [$product1->id],
        ]);
        $this->assertTrue($specificProductCoupon->isApplicableToProducts([$product1->id]));
        $this->assertFalse($specificProductCoupon->isApplicableToProducts([$product2->id]));

        // Coupon applicable to specific categories
        $categoryCoupon = Coupon::factory()->create([
            'applicable_categories' => [$category->id],
        ]);
        $this->assertTrue($categoryCoupon->isApplicableToProducts([$product1->id]));
        $this->assertFalse($categoryCoupon->isApplicableToProducts([$product2->id]));

        // Coupon with excluded products
        $excludedProductCoupon = Coupon::factory()->create([
            'excluded_products' => [$product1->id],
        ]);
        $this->assertFalse($excludedProductCoupon->isApplicableToProducts([$product1->id]));
        $this->assertTrue($excludedProductCoupon->isApplicableToProducts([$product2->id]));
    }

    /** @test */
    public function it_calculates_fixed_discount()
    {
        $coupon = Coupon::factory()->create([
            'type' => 'fixed',
            'value' => 50,
            'minimum_amount' => 100,
        ]);

        // Below minimum amount
        $this->assertEquals(0, $coupon->calculateDiscount(80));

        // Above minimum amount
        $this->assertEquals(50, $coupon->calculateDiscount(200));

        // Discount exceeds subtotal - test with coupon that has no minimum amount
        $couponNoMinimum = Coupon::factory()->create([
            'type' => 'fixed',
            'value' => 50,
            'minimum_amount' => null,
        ]);
        $this->assertEquals(30, $couponNoMinimum->calculateDiscount(30));
    }

    /** @test */
    public function it_calculates_percentage_discount()
    {
        $coupon = Coupon::factory()->create([
            'type' => 'percentage',
            'value' => 20,
            'minimum_amount' => 100,
            'maximum_discount' => 150,
        ]);

        // Below minimum amount
        $this->assertEquals(0, $coupon->calculateDiscount(80));

        // Normal calculation
        $this->assertEquals(40, $coupon->calculateDiscount(200)); // 20% of 200

        // Maximum discount applied
        $this->assertEquals(150, $coupon->calculateDiscount(1000)); // 20% would be 200, but max is 150
    }

    /** @test */
    public function it_applies_coupon_and_records_usage()
    {
        $user = User::factory()->create();
        $coupon = Coupon::factory()->create([
            'type' => 'fixed',
            'value' => 50,
            'used_count' => 0,
            'minimum_amount' => null, // No minimum amount requirement
            'is_active' => true,
            'starts_at' => null, // No start date restriction
            'expires_at' => null, // No expiry date restriction
            'usage_limit' => null, // No usage limit
            'usage_limit_per_user' => null, // No per-user limit
        ]);

        $discount = $coupon->apply($user->id, 200);

        $this->assertEquals(50, $discount);
        $this->assertEquals(1, $coupon->fresh()->used_count);

        $this->assertDatabaseHas('coupon_usages', [
            'coupon_id' => $coupon->id,
            'user_id' => $user->id,
            'discount_amount' => 50,
        ]);
    }

    /** @test */
    public function it_gets_formatted_value()
    {
        $fixedCoupon = Coupon::factory()->create([
            'type' => 'fixed',
            'value' => 100.50,
        ]);

        $percentageCoupon = Coupon::factory()->create([
            'type' => 'percentage',
            'value' => 25,
        ]);

        $this->assertEquals('₹100.50', $fixedCoupon->formatted_value);
        $this->assertEquals('25.00%', $percentageCoupon->formatted_value);
    }

    /** @test */
    public function it_gets_coupon_status()
    {
        $now = Carbon::now();

        $activeCoupon = Coupon::factory()->create([
            'is_active' => true,
            'starts_at' => $now->copy()->subDay(),
            'expires_at' => $now->copy()->addDay(),
        ]);

        $inactiveCoupon = Coupon::factory()->create(['is_active' => false]);
        $scheduledCoupon = Coupon::factory()->create([
            'starts_at' => $now->copy()->addDay(),
            'is_active' => true, // Ensure it's active so we can test the "Scheduled" status
        ]);
        $expiredCoupon = Coupon::factory()->create([
            'expires_at' => $now->copy()->subDay(),
            'is_active' => true, // Ensure it's active so we can test the "Expired" status
            'starts_at' => null, // Ensure no start date conflicts
        ]);
        $usedUpCoupon = Coupon::factory()->create([
            'usage_limit' => 10,
            'used_count' => 10,
            'is_active' => true, // Ensure it's active so we can test the "Used Up" status
            'starts_at' => null, // Ensure no start date conflicts
            'expires_at' => null, // Ensure no expiry date conflicts
        ]);

        $this->assertEquals('Active', $activeCoupon->status);
        $this->assertEquals('Inactive', $inactiveCoupon->status);
        $this->assertEquals('Scheduled', $scheduledCoupon->status);
        $this->assertEquals('Expired', $expiredCoupon->status);
        $this->assertEquals('Used Up', $usedUpCoupon->status);
    }

    /** @test */
    public function it_finds_coupon_by_code()
    {
        $coupon = Coupon::factory()->create(['code' => 'TEST20']);

        $found = Coupon::findByCode('test20'); // Case insensitive
        $notFound = Coupon::findByCode('INVALID');

        $this->assertEquals($coupon->id, $found->id);
        $this->assertNull($notFound);
    }

    /** @test */
    public function it_validates_coupon_code()
    {
        $user = User::factory()->create();
        $product = Product::factory()->create();

        $validCoupon = Coupon::factory()->create([
            'code' => 'VALID20',
            'type' => 'percentage',
            'value' => 20,
            'minimum_amount' => 100,
            'is_active' => true,
            'starts_at' => null, // No start date restriction
            'expires_at' => null, // No expiry date restriction
            'usage_limit' => null, // No usage limit
            'usage_limit_per_user' => null, // No per-user limit
        ]);

        // Valid coupon
        $result = Coupon::validateCode('VALID20', $user->id, [$product->id], 200);
        $this->assertTrue($result['valid']);
        $this->assertEquals(40, $result['discount']); // 20% of 200

        // Invalid code
        $result = Coupon::validateCode('INVALID', $user->id, [$product->id], 200);
        $this->assertFalse($result['valid']);
        $this->assertEquals('Invalid coupon code.', $result['message']);

        // Below minimum amount
        $result = Coupon::validateCode('VALID20', $user->id, [$product->id], 50);
        $this->assertFalse($result['valid']);
        $this->assertStringContainsString('Minimum order amount', $result['message']);
    }

    /** @test */
    public function it_prevents_coupon_usage_when_limit_reached()
    {
        $user = User::factory()->create();
        $coupon = Coupon::factory()->create([
            'usage_limit_per_user' => 1,
            'is_active' => true,
            'starts_at' => null,
            'expires_at' => null,
        ]);

        // First usage should work
        $this->assertTrue($coupon->canBeUsedBy($user->id));

        // Create usage record
        CouponUsage::factory()->create([
            'coupon_id' => $coupon->id,
            'user_id' => $user->id,
        ]);

        // Second usage should fail
        $this->assertFalse($coupon->canBeUsedBy($user->id));
    }

    /** @test */
    public function it_handles_category_exclusions()
    {
        $category1 = ProductCategory::factory()->create();
        $category2 = ProductCategory::factory()->create();
        $product1 = Product::factory()->create(['category_id' => $category1->id]);
        $product2 = Product::factory()->create(['category_id' => $category2->id]);

        $coupon = Coupon::factory()->create([
            'excluded_categories' => [$category1->id],
        ]);

        $this->assertFalse($coupon->isApplicableToProducts([$product1->id]));
        $this->assertTrue($coupon->isApplicableToProducts([$product2->id]));
    }
}