<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Cart;
use App\Models\Component;
use App\Services\CartService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;

class CartController extends Controller
{
    public function __construct(
        private CartService $cartService
    ) {}

    /**
     * Get the current user's cart.
     */
    public function index(): JsonResponse
    {
        $user = Auth::user();
        $cart = $this->cartService->getCart($user);

        return response()->json([
            'data' => [
                'items' => $cart->items()->with(['component.category'])->get(),
                'total' => $this->cartService->getCartTotal($user),
                'item_count' => $cart->items()->sum('quantity'),
            ]
        ]);
    }

    /**
     * Add item to cart.
     */
    public function store(Request $request): JsonResponse
    {
        $request->validate([
            'component_id' => 'required|integer|exists:components,id',
            'quantity' => 'sometimes|integer|min:1|max:10',
        ]);

        $component = Component::findOrFail($request->component_id);
        $quantity = $request->get('quantity', 1);

        // Check stock availability
        if ($component->stock < $quantity) {
            return response()->json([
                'message' => 'Insufficient stock available',
                'available_stock' => $component->stock
            ], 422);
        }

        $user = Auth::user();
        $cartItem = $this->cartService->addToCart($component, $quantity, $user);

        return response()->json([
            'data' => $cartItem->load(['component.category']),
            'message' => 'Item added to cart successfully'
        ], 201);
    }

    /**
     * Update cart item quantity.
     */
    public function update(Request $request, int $itemId): JsonResponse
    {
        $request->validate([
            'quantity' => 'required|integer|min:1|max:10',
        ]);

        $user = Auth::user();
        $cart = $this->cartService->getCart($user);
        
        $cartItem = $cart->items()->findOrFail($itemId);

        // Check stock availability
        if ($cartItem->component->stock < $request->quantity) {
            return response()->json([
                'message' => 'Insufficient stock available',
                'available_stock' => $cartItem->component->stock
            ], 422);
        }

        $cartItem->update(['quantity' => $request->quantity]);

        return response()->json([
            'data' => $cartItem->load(['component.category']),
            'message' => 'Cart item updated successfully'
        ]);
    }

    /**
     * Remove item from cart.
     */
    public function destroy(int $itemId): JsonResponse
    {
        $user = Auth::user();
        $cart = $this->cartService->getCart($user);
        
        $cartItem = $cart->items()->findOrFail($itemId);
        $cartItem->delete();

        return response()->json([
            'message' => 'Item removed from cart successfully'
        ]);
    }

    /**
     * Clear entire cart.
     */
    public function clear(): JsonResponse
    {
        $user = Auth::user();
        $this->cartService->clearCart($user);

        return response()->json([
            'message' => 'Cart cleared successfully'
        ]);
    }

    /**
     * Get cart summary.
     */
    public function summary(): JsonResponse
    {
        $user = Auth::user();
        $cart = $this->cartService->getCart($user);

        $subtotal = $this->cartService->getCartTotal($user);
        $shipping = $this->cartService->calculateShipping($cart);
        $tax = $subtotal * 0.1; // 10% tax rate
        $total = $subtotal + $shipping + $tax;

        return response()->json([
            'data' => [
                'subtotal' => $subtotal,
                'shipping' => $shipping,
                'tax' => $tax,
                'total' => $total,
                'item_count' => $cart->items()->sum('quantity'),
                'free_shipping_threshold' => 100.00,
                'free_shipping_eligible' => $subtotal >= 100.00,
            ]
        ]);
    }
}