<?php

namespace App\Services\Payment\Gateways;

use App\Models\GatewaySetting;
use App\Models\Transaction;
use App\Services\Payment\Contracts\PaymentGatewayInterface;
use App\Services\Payment\Exceptions\GatewayConfigurationException;
use App\Services\Payment\Exceptions\InvalidPaymentDataException;
use App\Services\Payment\Exceptions\PaymentGatewayException;
use App\Services\Payment\Exceptions\WebhookVerificationException;
use Illuminate\Support\Facades\Log;
use Exception;

class PayUmoneyService implements PaymentGatewayInterface
{
    private ?GatewaySetting $gatewaySetting;
    private array $config;

    // PayUmoney endpoints
    private const TEST_URL = 'https://test.payu.in/_payment';
    private const LIVE_URL = 'https://secure.payu.in/_payment';

    public function __construct()
    {
        $this->loadConfiguration();
    }

    /**
     * Load gateway configuration from database
     */
    private function loadConfiguration(): void
    {
        $this->gatewaySetting = GatewaySetting::byGateway(GatewaySetting::GATEWAY_PAYUMONEY)->first();
        
        if (!$this->gatewaySetting) {
            throw new GatewayConfigurationException(
                'PayUmoney gateway configuration not found',
                'PAYUMONEY_CONFIG_NOT_FOUND'
            );
        }

        // Handle both encrypted and plain JSON settings (for testing)
        $settings = $this->gatewaySetting->settings;
        if (is_string($settings)) {
            $settings = json_decode($settings, true);
        }
        $this->config = $settings ?? [];
        
        if (empty($this->config['merchant_key']) || empty($this->config['salt'])) {
            throw new GatewayConfigurationException(
                'PayUmoney API credentials not configured',
                'PAYUMONEY_CREDENTIALS_MISSING'
            );
        }
    }

    /**
     * Create a payment with PayUmoney
     */
    public function createPayment(array $data): array
    {
        $this->validatePaymentData($data);

        try {
            $transaction = Transaction::where('transaction_id', $data['transaction_id'])->first();
            
            if (!$transaction) {
                throw new InvalidPaymentDataException(
                    'Transaction not found',
                    'TRANSACTION_NOT_FOUND'
                );
            }

            // Prepare payment data for PayUmoney
            $paymentData = [
                'key' => $this->config['merchant_key'],
                'txnid' => $data['transaction_id'],
                'amount' => number_format($data['amount'], 2, '.', ''),
                'productinfo' => $data['product_info'] ?? 'Payment',
                'firstname' => $data['firstname'] ?? 'Customer',
                'email' => $data['email'] ?? $transaction->user->email ?? '<EMAIL>',
                'phone' => $data['phone'] ?? '**********',
                'surl' => $data['success_url'] ?? route('payment.payumoney.success'),
                'furl' => $data['failure_url'] ?? route('payment.payumoney.failure'),
                'service_provider' => 'payu_paisa',
                'curl' => $data['cancel_url'] ?? route('payment.payumoney.cancel'),
                'udf1' => $transaction->user_id ?? '',
                'udf2' => $transaction->id ?? '',
                'udf3' => '',
                'udf4' => '',
                'udf5' => '',
            ];

            // Generate hash
            $hash = $this->generateHash($paymentData);
            $paymentData['hash'] = $hash;

            // Get payment URL
            $paymentUrl = $this->gatewaySetting->is_test_mode ? self::TEST_URL : self::LIVE_URL;

            Log::info('PayUmoney payment data prepared', [
                'transaction_id' => $data['transaction_id'],
                'amount' => $data['amount'],
                'test_mode' => $this->gatewaySetting->is_test_mode
            ]);

            return [
                'success' => true,
                'payment_url' => $paymentUrl,
                'payment_data' => $paymentData,
                'test_mode' => $this->gatewaySetting->is_test_mode,
                'method' => 'POST'
            ];

        } catch (Exception $e) {
            Log::error('PayUmoney payment creation failed', [
                'error' => $e->getMessage(),
                'transaction_id' => $data['transaction_id'] ?? null
            ]);

            throw new PaymentGatewayException(
                'Payment creation failed: ' . $e->getMessage(),
                'PAYUMONEY_PAYMENT_FAILED',
                ['error' => $e->getMessage()]
            );
        }
    }

    /**
     * Verify payment with PayUmoney
     */
    public function verifyPayment(string $transactionId, array $data): bool
    {
        try {
            $transaction = Transaction::where('transaction_id', $transactionId)->first();
            
            if (!$transaction) {
                Log::error('Transaction not found for verification', ['transaction_id' => $transactionId]);
                return false;
            }

            // Required fields for verification
            $requiredFields = ['status', 'txnid', 'amount', 'hash'];
            foreach ($requiredFields as $field) {
                if (!isset($data[$field])) {
                    Log::error('Missing required field for payment verification', [
                        'field' => $field,
                        'transaction_id' => $transactionId
                    ]);
                    return false;
                }
            }
            
            // Determine status based on PayUmoney response early
            // This ensures we set the correct status even if we return early
            $status = strtolower($data['status']);
            $newStatus = match($status) {
                'success' => Transaction::STATUS_COMPLETED,
                'failure', 'failed' => Transaction::STATUS_FAILED,
                'pending' => Transaction::STATUS_PROCESSING,
                default => Transaction::STATUS_FAILED
            };

            // Verify hash
            if (!$this->verifyHash($data)) {
                Log::error('Hash verification failed for PayUmoney payment', [
                    'transaction_id' => $transactionId
                ]);
                
                // Update transaction status to failed even if hash verification fails
                // But preserve the original error message if available
                $transaction->update([
                    'status' => Transaction::STATUS_FAILED,
                    'failure_reason' => $data['error'] ?? 'Hash verification failed'
                ]);
                
                return false;
            }
            
            // If we get here, hash verification passed

            // Check if transaction ID matches
            if ($data['txnid'] !== $transactionId) {
                Log::error('Transaction ID mismatch in PayUmoney verification', [
                    'expected' => $transactionId,
                    'received' => $data['txnid']
                ]);
                
                // Update transaction status to failed if transaction ID doesn't match
                $transaction->update([
                    'status' => Transaction::STATUS_FAILED,
                    'failure_reason' => 'Transaction ID mismatch'
                ]);
                
                return false;
            }

            // Check amount
            $expectedAmount = number_format($transaction->amount, 2, '.', '');
            if ($data['amount'] !== $expectedAmount) {
                Log::error('Amount mismatch in PayUmoney verification', [
                    'expected' => $expectedAmount,
                    'received' => $data['amount'],
                    'transaction_id' => $transactionId
                ]);
                
                // Update transaction status to failed if amount doesn't match
                $transaction->update([
                    'status' => Transaction::STATUS_FAILED,
                    'failure_reason' => 'Amount mismatch'
                ]);
                
                return false;
            }

            // Status was already determined earlier
            $isSuccess = $status === 'success';
            
            // Log the verification result
            Log::info('PayUmoney payment verification', [
                'transaction_id' => $transactionId,
                'status' => $status,
                'is_success' => $isSuccess
            ]);

            // Update transaction with payment details
            $transaction->update([
                'gateway_transaction_id' => $data['payuMoneyId'] ?? $data['mihpayid'] ?? null,
                'status' => $newStatus,
                'failure_reason' => $isSuccess ? null : ($data['error'] ?? 'Payment failed'),
                'payment_details' => array_merge($transaction->payment_details ?? [], [
                    'payumoney_txnid' => $data['txnid'],
                    'payumoney_status' => $data['status'],
                    'payumoney_id' => $data['payuMoneyId'] ?? $data['mihpayid'] ?? null,
                    'payment_mode' => $data['mode'] ?? null,
                    'bank_ref_num' => $data['bank_ref_num'] ?? null,
                    'bankcode' => $data['bankcode'] ?? null,
                    'name_on_card' => $data['name_on_card'] ?? null,
                    'cardnum' => $data['cardnum'] ?? null,
                    'verified_at' => now()->toISOString(),
                    'hash_verified' => true
                ])
            ]);
            
            // Force the transaction to be saved to the database
            $transaction->save();

            Log::info('PayUmoney payment verified', [
                'transaction_id' => $transactionId,
                'status' => $data['status'],
                'payumoney_id' => $data['payuMoneyId'] ?? $data['mihpayid'] ?? null
            ]);

            // Refresh the transaction to ensure we have the latest data
            $transaction->refresh();
            
            // Return true for successful payments, false otherwise
            return $status === 'success';

        } catch (Exception $e) {
            Log::error('PayUmoney payment verification failed', [
                'error' => $e->getMessage(),
                'transaction_id' => $transactionId
            ]);
            return false;
        }
    }

    /**
     * Handle webhook from PayUmoney (callback processing)
     */
    public function handleWebhook(array $payload): array
    {
        try {
            Log::info('Processing PayUmoney callback', [
                'txnid' => $payload['txnid'] ?? null,
                'status' => $payload['status'] ?? null
            ]);

            // Verify callback hash
            if (!$this->verifyHash($payload)) {
                throw new WebhookVerificationException(
                    'PayUmoney callback hash verification failed',
                    'PAYUMONEY_CALLBACK_HASH_INVALID'
                );
            }

            $transactionId = $payload['txnid'] ?? '';
            if (empty($transactionId)) {
                throw new InvalidPaymentDataException(
                    'Transaction ID missing in callback',
                    'PAYUMONEY_TXNID_MISSING'
                );
            }

            $transaction = Transaction::where('transaction_id', $transactionId)->first();
            if (!$transaction) {
                Log::warning('Transaction not found for PayUmoney callback', [
                    'txnid' => $transactionId
                ]);
                return ['success' => false, 'message' => 'Transaction not found'];
            }

            // Process callback based on status
            $status = strtolower($payload['status'] ?? '');
            $isSuccess = $status === 'success';
            
            $newStatus = match($status) {
                'success' => Transaction::STATUS_COMPLETED,
                'failure' => Transaction::STATUS_FAILED,
                'pending' => Transaction::STATUS_PROCESSING,
                default => Transaction::STATUS_FAILED
            };

            $transaction->update([
                'status' => $newStatus,
                'gateway_transaction_id' => $payload['payuMoneyId'] ?? $payload['mihpayid'] ?? null,
                'webhook_verified' => true,
                'failure_reason' => $isSuccess ? null : ($payload['error'] ?? 'Payment failed'),
                'payment_details' => array_merge($transaction->payment_details ?? [], [
                    'callback_received_at' => now()->toISOString(),
                    'callback_status' => $payload['status'],
                    'payumoney_id' => $payload['payuMoneyId'] ?? $payload['mihpayid'] ?? null,
                    'payment_mode' => $payload['mode'] ?? null,
                    'bank_ref_num' => $payload['bank_ref_num'] ?? null,
                    'bankcode' => $payload['bankcode'] ?? null,
                    'error_message' => $payload['error'] ?? null
                ])
            ]);

            Log::info('PayUmoney callback processed successfully', [
                'transaction_id' => $transactionId,
                'status' => $status,
                'new_transaction_status' => $newStatus
            ]);

            return [
                'success' => true,
                'message' => 'Callback processed successfully',
                'transaction_status' => $newStatus
            ];

        } catch (WebhookVerificationException $e) {
            Log::error('PayUmoney callback verification failed', [
                'error' => $e->getMessage(),
                'payload' => $payload
            ]);
            throw $e;

        } catch (Exception $e) {
            Log::error('PayUmoney callback processing failed', [
                'error' => $e->getMessage(),
                'payload' => $payload
            ]);

            throw new PaymentGatewayException(
                'Callback processing failed: ' . $e->getMessage(),
                'PAYUMONEY_CALLBACK_FAILED',
                ['error' => $e->getMessage()]
            );
        }
    }

    /**
     * Get payment status from PayUmoney (limited functionality)
     */
    public function getPaymentStatus(string $transactionId): string
    {
        try {
            $transaction = Transaction::where('transaction_id', $transactionId)->first();
            
            if (!$transaction) {
                return Transaction::STATUS_PENDING;
            }

            // PayUmoney doesn't have a direct status check API
            // Return the current transaction status
            return $transaction->status;

        } catch (Exception $e) {
            Log::error('Failed to get payment status for PayUmoney', [
                'error' => $e->getMessage(),
                'transaction_id' => $transactionId
            ]);
            
            return Transaction::STATUS_PENDING;
        }
    }

    /**
     * Get gateway name
     */
    public function getGatewayName(): string
    {
        return GatewaySetting::GATEWAY_PAYUMONEY;
    }

    /**
     * Check if gateway is enabled
     */
    public function isEnabled(): bool
    {
        return $this->gatewaySetting->is_enabled;
    }

    /**
     * Generate hash for PayUmoney payment
     */
    private function generateHash(array $data): string
    {
        $hashString = $data['key'] . '|' . 
                     $data['txnid'] . '|' . 
                     $data['amount'] . '|' . 
                     $data['productinfo'] . '|' . 
                     $data['firstname'] . '|' . 
                     $data['email'] . '|' . 
                     ($data['udf1'] ?? '') . '|' . 
                     ($data['udf2'] ?? '') . '|' . 
                     ($data['udf3'] ?? '') . '|' . 
                     ($data['udf4'] ?? '') . '|' . 
                     ($data['udf5'] ?? '') . '|||||' . 
                     $this->config['salt'];

        return strtolower(hash('sha512', $hashString));
    }

    /**
     * Verify hash for PayUmoney response/callback
     */
    private function verifyHash(array $data): bool
    {
        $receivedHash = $data['hash'] ?? '';
        
        // Debug logging
        Log::debug('PayUmoney verifyHash called', [
            'data' => $data,
            'received_hash' => $receivedHash,
            'config' => $this->config
        ]);
        
        if (empty($receivedHash)) {
            Log::debug('PayUmoney hash is empty');
            return false;
        }

        // For success response
        if (isset($data['status']) && strtolower($data['status']) === 'success') {
            $hashString = $this->config['salt'] . '|' . 
                         $data['status'] . '|||||' . 
                         ($data['udf5'] ?? '') . '|' . 
                         ($data['udf4'] ?? '') . '|' . 
                         ($data['udf3'] ?? '') . '|' . 
                         ($data['udf2'] ?? '') . '|' . 
                         ($data['udf1'] ?? '') . '|' . 
                         $data['email'] . '|' . 
                         $data['firstname'] . '|' . 
                         $data['productinfo'] . '|' . 
                         $data['amount'] . '|' . 
                         $data['txnid'] . '|' . 
                         $data['key'];
        } else {
            // For failure response
            $hashString = $this->config['salt'] . '|' . 
                         $data['status'] . '|||||' . 
                         ($data['udf5'] ?? '') . '|' . 
                         ($data['udf4'] ?? '') . '|' . 
                         ($data['udf3'] ?? '') . '|' . 
                         ($data['udf2'] ?? '') . '|' . 
                         ($data['udf1'] ?? '') . '|' . 
                         $data['email'] . '|' . 
                         $data['firstname'] . '|' . 
                         $data['productinfo'] . '|' . 
                         $data['amount'] . '|' . 
                         $data['txnid'] . '|' . 
                         $data['key'];
        }

        $expectedHash = strtolower(hash('sha512', $hashString));
        
        // Debug logging
        Log::debug('PayUmoney hash comparison', [
            'expected_hash' => $expectedHash,
            'received_hash' => strtolower($receivedHash),
            'hash_string' => $hashString,
            'match' => hash_equals($expectedHash, strtolower($receivedHash))
        ]);
        
        return hash_equals($expectedHash, strtolower($receivedHash));
    }

    /**
     * Validate payment data
     */
    private function validatePaymentData(array $data): void
    {
        $requiredFields = ['amount', 'transaction_id'];
        
        foreach ($requiredFields as $field) {
            if (!isset($data[$field]) || empty($data[$field])) {
                throw new InvalidPaymentDataException(
                    "Missing required field: {$field}",
                    'MISSING_REQUIRED_FIELD',
                    ['field' => $field]
                );
            }
        }

        if (!is_numeric($data['amount']) || $data['amount'] <= 0) {
            throw new InvalidPaymentDataException(
                'Amount must be a positive number',
                'INVALID_AMOUNT',
                ['amount' => $data['amount']]
            );
        }
    }
}