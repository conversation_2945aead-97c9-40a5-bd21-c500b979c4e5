<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('stock_movements', function (Blueprint $table) {
            $table->foreignId('component_id')->constrained()->onDelete('cascade');
            $table->integer('quantity_change');
            $table->integer('previous_stock');
            $table->integer('new_stock');
            $table->enum('type', ['sale', 'restock', 'adjustment', 'return', 'damage', 'supplier_sync']);
            $table->string('reason')->nullable();
            $table->string('reference')->nullable();
            $table->json('metadata')->nullable();
            
            $table->index(['component_id', 'created_at']);
            $table->index('type');
        });
    }

    public function down(): void
    {
        Schema::table('stock_movements', function (Blueprint $table) {
            $table->dropForeign(['component_id']);
            $table->dropColumn([
                'component_id', 'quantity_change', 'previous_stock', 'new_stock',
                'type', 'reason', 'reference', 'metadata'
            ]);
        });
    }
};