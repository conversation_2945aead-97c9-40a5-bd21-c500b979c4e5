<?php

namespace App\Http\Controllers;

use App\Services\PaymentService;
use App\Services\PaymentGatewayFactory;
use App\Models\Transaction;
use App\Services\Payment\Exceptions\PaymentException;
use App\Services\Payment\Exceptions\PaymentGatewayException;
use App\Services\Payment\Exceptions\InvalidPaymentDataException;
use App\Services\Payment\Exceptions\GatewayConfigurationException;
use App\Http\Requests\PaymentRequest;
use App\Http\Requests\PaymentVerificationRequest;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class PaymentController extends Controller
{
    public function __construct(
        private PaymentService $paymentService,
        private PaymentGatewayFactory $gatewayFactory
    ) {}

    /**
     * Display payment form with available gateways
     * Requirements: 1.1, 1.2
     */
    public function create()
    {
        $availableGateways = $this->gatewayFactory->getAvailableGateways();
        
        // Format gateways for the component
        $gatewayOptions = collect($availableGateways)->map(function ($gateway) {
            return [
                'key' => $gateway['name'],
                'name' => $this->getGatewayDisplayName($gateway['name']),
                'description' => $this->getGatewayDescription($gateway['name']),
                'logo' => $this->getGatewayLogo($gateway['name']),
                'enabled' => true,
                'test_mode' => $gateway['is_test_mode'] ?? false
            ];
        })->toArray();
        
        // Keep legacy format for backward compatibility
        $gateways = collect($availableGateways)->pluck('name')->toArray();
        
        return view('payments.create', compact('gateways', 'gatewayOptions'));
    }
    
    /**
     * Get display name for gateway
     */
    private function getGatewayDisplayName(string $gateway): string
    {
        return match($gateway) {
            'razorpay' => 'Razorpay',
            'payumoney' => 'PayUMoney',
            'cashfree' => 'Cashfree',
            default => ucfirst($gateway)
        };
    }
    
    /**
     * Get description for gateway
     */
    private function getGatewayDescription(string $gateway): string
    {
        return match($gateway) {
            'razorpay' => 'Credit/Debit Cards, Net Banking, UPI, Wallets',
            'payumoney' => 'Credit/Debit Cards, Net Banking, EMI',
            'cashfree' => 'Cards, UPI, Net Banking, Wallets',
            default => 'Secure payment processing'
        };
    }
    
    /**
     * Get logo path for gateway
     */
    private function getGatewayLogo(string $gateway): string
    {
        return match($gateway) {
            'razorpay' => '/images/gateways/razorpay-logo.svg',
            'payumoney' => '/images/gateways/payumoney-logo.svg',
            'cashfree' => '/images/gateways/cashfree-logo.svg',
            default => ''
        };
    }

    /**
     * Handle payment processing requests
     * Requirements: 1.3, 8.2
     */
    public function store(PaymentRequest $request)
    {

        try {
            $paymentData = [
                'user_id' => Auth::id(),
                'gateway' => $request->gateway,
                'amount' => $request->amount,
                'currency' => $request->currency ?? 'INR',
                'description' => $request->description ?? 'Payment',
                'callback_url' => route('payment.callback')
            ];

            $result = $this->paymentService->initiatePayment($paymentData);

            if ($request->gateway === 'payumoney') {
                return view('payments.payumoney-redirect', [
                    'payment_data' => $result['gateway_data']
                ]);
            }

            return response()->json($result);

        } catch (PaymentException|PaymentGatewayException|InvalidPaymentDataException|GatewayConfigurationException $e) {
            Log::error('Payment initiation failed', [
                'user_id' => Auth::id(),
                'gateway' => $request->gateway,
                'amount' => $request->amount,
                'error' => $e->getMessage()
            ]);
            return response()->json($e->toArray(), 400);
        }
    }

    /**
     * Handle payment gateway callbacks
     * Requirements: 2.1
     */
    public function callback(Request $request)
    {
        $transactionId = $request->get('transaction_id') ?? $request->get('txnid');
        
        if (!$transactionId) {
            Log::warning('Payment callback received without transaction ID', [
                'request_data' => $request->all()
            ]);
            return redirect()->route('payment.create')
                           ->with('error', 'Invalid payment callback');
        }

        $transaction = Transaction::where('transaction_id', $transactionId)->first();
        
        if (!$transaction) {
            Log::warning('Payment callback for non-existent transaction', [
                'transaction_id' => $transactionId
            ]);
            return redirect()->route('payment.create')
                           ->with('error', 'Transaction not found');
        }

        try {
            $verificationData = $request->all();
            $isVerified = $this->paymentService->verifyPayment($transactionId, $verificationData);

            if ($isVerified) {
                return redirect()->route('payment.success', $transaction->id);
            } else {
                return redirect()->route('payment.failed', $transaction->id);
            }
        } catch (PaymentException $e) {
            Log::error('Payment callback verification failed', [
                'transaction_id' => $transactionId,
                'error' => $e->getMessage()
            ]);
            return redirect()->route('payment.failed', $transaction->id);
        }
    }

    /**
     * Display payment success page
     * Requirements: 2.2
     */
    public function success(Transaction $transaction)
    {
        if ($transaction->user_id !== Auth::id()) {
            abort(403);
        }

        return view('payments.success', compact('transaction'));
    }

    /**
     * Display payment failure page
     * Requirements: 2.3
     */
    public function failed(Transaction $transaction)
    {
        if ($transaction->user_id !== Auth::id()) {
            abort(403);
        }

        return view('payments.failed', compact('transaction'));
    }

    public function verify(PaymentVerificationRequest $request)
    {

        try {
            $isVerified = $this->paymentService->verifyPayment(
                $request->transaction_id,
                $request->all()
            );

            return response()->json(['verified' => $isVerified]);

        } catch (PaymentException $e) {
            return response()->json($e->toArray(), 400);
        }
    }

    /**
     * Get payment status for checking endpoints
     * Requirements: 2.4
     */
    public function status(Transaction $transaction)
    {
        if ($transaction->user_id !== Auth::id()) {
            abort(403);
        }

        // If it's an AJAX request, return JSON
        if (request()->wantsJson()) {
            return response()->json([
                'transaction_id' => $transaction->transaction_id,
                'status' => $transaction->status,
                'amount' => $transaction->amount,
                'gateway' => $transaction->gateway_name,
                'created_at' => $transaction->created_at,
                'updated_at' => $transaction->updated_at,
                'failure_reason' => $transaction->failure_reason
            ]);
        }

        // Otherwise, return the status view
        return view('payments.status', compact('transaction'));
    }

    /**
     * Handle PayUmoney success callback
     */
    public function payumoneySuccess(Request $request)
    {
        return $this->handlePayumoneyCallback($request, 'success');
    }

    /**
     * Handle PayUmoney failure callback
     */
    public function payumoneyFailure(Request $request)
    {
        return $this->handlePayumoneyCallback($request, 'failure');
    }

    /**
     * Handle PayUmoney cancel callback
     */
    public function payumoneyCancel(Request $request)
    {
        return $this->handlePayumoneyCallback($request, 'cancel');
    }

    /**
     * Common method to handle PayUmoney callbacks
     */
    private function handlePayumoneyCallback(Request $request, string $type)
    {
        $transactionId = $request->get('txnid');
        
        if (!$transactionId) {
            return redirect()->route('payment.create')
                           ->with('error', 'Invalid PayUmoney callback - missing transaction ID');
        }

        $transaction = Transaction::where('transaction_id', $transactionId)->first();
        
        if (!$transaction) {
            return redirect()->route('payment.create')
                           ->with('error', 'Transaction not found');
        }

        try {
            // Process the callback through PayUmoney service
            $gateway = $this->gatewayFactory->create('payumoney');
            $result = $gateway->handleWebhook($request->all());

            // Update transaction status based on webhook result
            if (isset($result['status'])) {
                $transaction->update(['status' => $result['status']]);
            }

            // Refresh transaction to get updated status
            $transaction->refresh();

            // Redirect based on final transaction status
            if ($transaction->isCompleted()) {
                return redirect()->route('payment.success', $transaction->id)
                               ->with('success', 'Payment completed successfully');
            } elseif ($transaction->isFailed() || $type === 'failure') {
                return redirect()->route('payment.failed', $transaction->id)
                               ->with('error', $transaction->failure_reason ?? 'Payment failed');
            } elseif ($transaction->isCancelled() || $type === 'cancel') {
                return redirect()->route('payment.failed', $transaction->id)
                               ->with('error', 'Payment was cancelled');
            } else {
                return redirect()->route('payment.status', $transaction->id)
                               ->with('info', 'Payment is being processed');
            }

        } catch (PaymentException $e) {
            Log::error('PayUmoney callback processing failed', [
                'transaction_id' => $transactionId,
                'type' => $type,
                'error' => $e->getMessage()
            ]);

            return redirect()->route('payment.failed', $transaction->id)
                           ->with('error', 'Payment processing failed');
        }
    }
}
