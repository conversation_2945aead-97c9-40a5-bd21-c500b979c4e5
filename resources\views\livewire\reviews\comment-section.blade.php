<div class="bg-white rounded-lg shadow-sm border border-gray-200">
    <!-- Review Statistics -->
    <div class="p-6 border-b border-gray-200">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-900">Customer Reviews</h3>
            @if($userCanReview && !$userHasReviewed)
                <button 
                    wire:click="toggleReviewForm"
                    class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors"
                >
                    Write a Review
                </button>
            @elseif($userHasReviewed)
                <span class="text-sm text-gray-500">You have already reviewed this product</span>
            @elseif(!auth()->check())
                <a href="{{ route('login') }}" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors">
                    Login to Review
                </a>
            @else
                <span class="text-sm text-gray-500">Purchase required to review</span>
            @endif
        </div>

        @if($reviewStats['total_reviews'] > 0)
            <div class="flex items-center space-x-6">
                <div class="flex items-center">
                    <div class="flex items-center">
                        @for($i = 1; $i <= 5; $i++)
                            <svg class="w-5 h-5 {{ $i <= floor($reviewStats['average_rating']) ? 'text-yellow-400' : 'text-gray-300' }}" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                            </svg>
                        @endfor
                    </div>
                    <span class="ml-2 text-lg font-semibold text-gray-900">{{ $reviewStats['average_rating'] }}</span>
                    <span class="ml-1 text-sm text-gray-500">out of 5</span>
                </div>
                <div class="text-sm text-gray-500">
                    {{ $reviewStats['total_reviews'] }} {{ Str::plural('review', $reviewStats['total_reviews']) }}
                    @if($reviewStats['verified_purchases'] > 0)
                        ({{ $reviewStats['verified_purchases'] }} verified purchases)
                    @endif
                </div>
            </div>

            <!-- Rating Distribution -->
            <div class="mt-4 space-y-2">
                @foreach(array_reverse($reviewStats['rating_distribution'], true) as $rating => $count)
                    <div class="flex items-center text-sm">
                        <span class="w-8 text-gray-600">{{ $rating }}★</span>
                        <div class="flex-1 mx-3 bg-gray-200 rounded-full h-2">
                            <div class="bg-yellow-400 h-2 rounded-full" style="width: {{ $reviewStats['total_reviews'] > 0 ? ($count / $reviewStats['total_reviews']) * 100 : 0 }}%"></div>
                        </div>
                        <span class="w-8 text-gray-500">{{ $count }}</span>
                    </div>
                @endforeach
            </div>
        @else
            <div class="text-center py-8 text-gray-500">
                <p>No reviews yet. Be the first to review this product!</p>
            </div>
        @endif
    </div>

    <!-- Review Form -->
    @if($showReviewForm)
        <div class="p-6 border-b border-gray-200 bg-gray-50">
            <h4 class="text-lg font-medium text-gray-900 mb-4">Write Your Review</h4>
            
            @if (session()->has('success'))
                <div class="mb-4 bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded">
                    {{ session('success') }}
                </div>
            @endif

            @error('general')
                <div class="mb-4 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
                    {{ $message }}
                </div>
            @enderror

            <form wire:submit="submitReview">
                <!-- Rating -->
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Rating *</label>
                    <div class="flex items-center space-x-1">
                        @for($i = 1; $i <= 5; $i++)
                            <button 
                                type="button"
                                wire:click="$set('rating', {{ $i }})"
                                class="focus:outline-none"
                            >
                                <svg class="w-8 h-8 {{ $i <= $rating ? 'text-yellow-400' : 'text-gray-300' }} hover:text-yellow-400 transition-colors" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                                </svg>
                            </button>
                        @endfor
                    </div>
                    @error('rating') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                </div>

                <!-- Title -->
                <div class="mb-4">
                    <label for="title" class="block text-sm font-medium text-gray-700 mb-2">Review Title</label>
                    <input 
                        type="text" 
                        id="title"
                        wire:model="title"
                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="Summarize your experience..."
                    >
                    @error('title') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                </div>

                <!-- Comment -->
                <div class="mb-4">
                    <label for="comment" class="block text-sm font-medium text-gray-700 mb-2">Your Review</label>
                    <textarea 
                        id="comment"
                        wire:model="comment"
                        rows="4"
                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="Share your thoughts about this product..."
                    ></textarea>
                    @error('comment') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                </div>

                <!-- Submit Buttons -->
                <div class="flex items-center space-x-3">
                    <button 
                        type="submit"
                        class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md font-medium transition-colors"
                    >
                        Submit Review
                    </button>
                    <button 
                        type="button"
                        wire:click="toggleReviewForm"
                        class="bg-gray-300 hover:bg-gray-400 text-gray-700 px-6 py-2 rounded-md font-medium transition-colors"
                    >
                        Cancel
                    </button>
                </div>
            </form>
        </div>
    @endif

    <!-- Filters and Sorting -->
    @if($reviewStats['total_reviews'] > 0)
        <div class="p-4 border-b border-gray-200 bg-gray-50">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <div>
                        <label for="filterByRating" class="text-sm font-medium text-gray-700 mr-2">Filter by rating:</label>
                        <select 
                            id="filterByRating"
                            wire:model.live="filterByRating"
                            class="text-sm border border-gray-300 rounded-md px-3 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                            <option value="all">All ratings</option>
                            <option value="5">5 stars</option>
                            <option value="4">4 stars</option>
                            <option value="3">3 stars</option>
                            <option value="2">2 stars</option>
                            <option value="1">1 star</option>
                        </select>
                    </div>
                </div>
                
                <div>
                    <label for="sortBy" class="text-sm font-medium text-gray-700 mr-2">Sort by:</label>
                    <select 
                        id="sortBy"
                        wire:model.live="sortBy"
                        class="text-sm border border-gray-300 rounded-md px-3 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                        <option value="newest">Newest first</option>
                        <option value="oldest">Oldest first</option>
                        <option value="highest_rated">Highest rated</option>
                        <option value="lowest_rated">Lowest rated</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- Reviews List -->
        <div class="divide-y divide-gray-200">
            @forelse($reviews as $review)
                <div class="p-6">
                    <div class="flex items-start justify-between">
                        <div class="flex-1">
                            <div class="flex items-center mb-2">
                                <div class="flex items-center">
                                    @for($i = 1; $i <= 5; $i++)
                                        <svg class="w-4 h-4 {{ $i <= $review->rating ? 'text-yellow-400' : 'text-gray-300' }}" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                                        </svg>
                                    @endfor
                                </div>
                                <span class="ml-2 text-sm font-medium text-gray-900">{{ $review->user->name }}</span>
                                @if($review->isVerifiedPurchase())
                                    <span class="ml-2 bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">Verified Purchase</span>
                                @endif
                            </div>
                            
                            @if($review->title)
                                <h5 class="font-medium text-gray-900 mb-2">{{ $review->title }}</h5>
                            @endif
                            
                            @if($review->comment)
                                <p class="text-gray-700 mb-2">{{ $review->comment }}</p>
                            @endif
                            
                            <p class="text-sm text-gray-500">{{ $review->created_at->format('M j, Y') }}</p>
                        </div>
                    </div>
                </div>
            @empty
                <div class="p-6 text-center text-gray-500">
                    No reviews match your current filters.
                </div>
            @endforelse
        </div>

        <!-- Pagination -->
        @if($reviews->hasPages())
            <div class="p-4 border-t border-gray-200">
                {{ $reviews->links() }}
            </div>
        @endif
    @endif
</div>