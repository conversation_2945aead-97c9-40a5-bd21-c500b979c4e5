<?php

namespace App\Http\Controllers;

use App\Services\WebhookService;
use App\Services\Payment\Exceptions\WebhookVerificationException;
use App\Services\Payment\Exceptions\PaymentException;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

class WebhookController extends Controller
{
    public function __construct(
        private WebhookService $webhookService
    ) {}

    /**
     * Generic webhook handler for all payment gateways
     * Requirements: 6.1, 6.2, 6.3, 6.4
     */
    public function handle(Request $request, string $gateway)
    {
        // Validate gateway
        if (!in_array($gateway, ['razorpay', 'payumoney', 'cashfree'])) {
            Log::warning('Webhook received for unsupported gateway', [
                'gateway' => $gateway,
                'ip' => $request->ip()
            ]);
            return response()->json(['error' => 'Unsupported gateway'], Response::HTTP_BAD_REQUEST);
        }

        try {
            // Log webhook receipt with security context
            Log::channel('security')->info('Webhook received', [
                'gateway' => $gateway,
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'payload_size' => strlen(json_encode($request->all())),
                'headers' => $this->sanitizeHeaders($request->headers->all()),
                'timestamp' => now()->toISOString()
            ]);
            
            Log::info('Webhook received', [
                'gateway' => $gateway,
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'payload_size' => strlen(json_encode($request->all()))
            ]);

            // Process webhook through service
            $result = $this->webhookService->processWebhook($gateway, $request->all());
            
            Log::info('Webhook processed successfully', [
                'gateway' => $gateway,
                'transaction_id' => $result['transaction_id'] ?? null,
                'status' => $result['status'] ?? null
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Webhook processed successfully'
            ], Response::HTTP_OK);

        } catch (WebhookVerificationException $e) {
            // Log security violation
            Log::channel('security')->error('Webhook verification failed', [
                'gateway' => $gateway,
                'ip' => $request->ip(),
                'error' => $e->getMessage(),
                'user_agent' => $request->userAgent(),
                'timestamp' => now()->toISOString()
            ]);
            
            Log::error('Webhook verification failed', [
                'gateway' => $gateway,
                'ip' => $request->ip(),
                'error' => $e->getMessage(),
                'payload' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'error' => 'Webhook verification failed'
            ], Response::HTTP_UNAUTHORIZED);

        } catch (PaymentException $e) {
            Log::error('Webhook processing failed', [
                'gateway' => $gateway,
                'ip' => $request->ip(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'error' => 'Webhook processing failed'
            ], Response::HTTP_BAD_REQUEST);

        } catch (\Exception $e) {
            Log::error('Unexpected webhook error', [
                'gateway' => $gateway,
                'ip' => $request->ip(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'error' => 'Internal server error'
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Handle Razorpay webhooks
     * Requirements: 6.1, 6.2, 6.3, 6.4
     */
    public function razorpay(Request $request)
    {
        return $this->handle($request, 'razorpay');
    }

    /**
     * Handle PayUmoney webhooks
     * Requirements: 6.1, 6.2, 6.3, 6.4
     */
    public function payumoney(Request $request)
    {
        return $this->handle($request, 'payumoney');
    }

    /**
     * Handle Cashfree webhooks
     * Requirements: 6.1, 6.2, 6.3, 6.4
     */
    public function cashfree(Request $request)
    {
        return $this->handle($request, 'cashfree');
    }
    
    /**
     * Sanitize headers for logging (remove sensitive data)
     */
    private function sanitizeHeaders(array $headers): array
    {
        $sensitiveHeaders = [
            'authorization',
            'x-razorpay-signature',
            'x-cashfree-signature',
            'x-payumoney-signature'
        ];
        
        foreach ($sensitiveHeaders as $header) {
            if (isset($headers[$header])) {
                $headers[$header] = ['[REDACTED]'];
            }
        }
        
        return $headers;
    }
}
