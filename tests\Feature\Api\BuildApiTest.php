<?php

namespace Tests\Feature\Api;

use App\Models\Build;
use App\Models\Component;
use App\Models\ComponentCategory;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Laravel\Sanctum\Sanctum;
use Tests\TestCase;

class BuildApiTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;
    protected ComponentCategory $cpuCategory;
    protected ComponentCategory $gpuCategory;
    protected Component $cpu;
    protected Component $gpu;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->user = User::factory()->create();
        
        // Create test categories
        $this->cpuCategory = ComponentCategory::factory()->create([
            'name' => 'CPU',
            'slug' => 'cpu'
        ]);
        
        $this->gpuCategory = ComponentCategory::factory()->create([
            'name' => 'GPU',
            'slug' => 'gpu'
        ]);

        // Create test components
        $this->cpu = Component::factory()->create([
            'category_id' => $this->cpuCategory->id,
            'price' => 300.00,
            'is_active' => true
        ]);
        
        $this->gpu = Component::factory()->create([
            'category_id' => $this->gpuCategory->id,
            'price' => 500.00,
            'is_active' => true
        ]);
    }

    public function test_can_list_public_builds()
    {
        // Create public builds
        Build::factory()->count(3)->create([
            'user_id' => $this->user->id,
            'is_public' => true
        ]);
        
        // Create private build
        Build::factory()->create([
            'user_id' => $this->user->id,
            'is_public' => false
        ]);

        $response = $this->getJson('/api/v1/builds?public=1');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'name',
                        'description',
                        'total_price',
                        'is_public',
                        'user',
                        'components'
                    ]
                ],
                'meta',
                'links'
            ]);

        $data = $response->json('data');
        $this->assertCount(3, $data);
        
        foreach ($data as $build) {
            $this->assertTrue($build['is_public']);
        }
    }

    public function test_authenticated_user_can_create_build()
    {
        Sanctum::actingAs($this->user);

        $buildData = [
            'name' => 'My Gaming PC',
            'description' => 'High-end gaming build',
            'is_public' => true,
            'components' => [
                [
                    'component_id' => $this->cpu->id,
                    'quantity' => 1
                ],
                [
                    'component_id' => $this->gpu->id,
                    'quantity' => 1
                ]
            ]
        ];

        $response = $this->postJson('/api/v1/builds', $buildData);

        $response->assertStatus(201)
            ->assertJsonStructure([
                'data' => [
                    'id',
                    'name',
                    'description',
                    'total_price',
                    'is_public',
                    'user',
                    'components'
                ],
                'compatibility' => [
                    'is_compatible',
                    'issues',
                    'warnings'
                ]
            ]);

        $this->assertDatabaseHas('builds', [
            'name' => 'My Gaming PC',
            'user_id' => $this->user->id,
            'total_price' => 800.00 // 300 + 500
        ]);
    }

    public function test_unauthenticated_user_cannot_create_build()
    {
        $buildData = [
            'name' => 'My Gaming PC',
            'components' => [
                ['component_id' => $this->cpu->id]
            ]
        ];

        $response = $this->postJson('/api/v1/builds', $buildData);

        $response->assertStatus(401);
    }

    public function test_can_show_public_build()
    {
        $build = Build::factory()->create([
            'user_id' => $this->user->id,
            'is_public' => true
        ]);

        $response = $this->getJson("/api/v1/builds/{$build->id}");

        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    'id',
                    'name',
                    'user',
                    'components'
                ],
                'compatibility'
            ]);
    }

    public function test_cannot_show_private_build_without_auth()
    {
        $build = Build::factory()->create([
            'user_id' => $this->user->id,
            'is_public' => false
        ]);

        $response = $this->getJson("/api/v1/builds/{$build->id}");

        $response->assertStatus(404);
    }

    public function test_owner_can_show_private_build()
    {
        Sanctum::actingAs($this->user);

        $build = Build::factory()->create([
            'user_id' => $this->user->id,
            'is_public' => false
        ]);

        $response = $this->getJson("/api/v1/builds/{$build->id}");

        $response->assertStatus(200);
    }

    public function test_owner_can_update_build()
    {
        Sanctum::actingAs($this->user);

        $build = Build::factory()->create([
            'user_id' => $this->user->id,
            'name' => 'Original Name'
        ]);

        $updateData = [
            'name' => 'Updated Name',
            'description' => 'Updated description',
            'is_public' => true
        ];

        $response = $this->putJson("/api/v1/builds/{$build->id}", $updateData);

        $response->assertStatus(200);
        
        $this->assertDatabaseHas('builds', [
            'id' => $build->id,
            'name' => 'Updated Name',
            'description' => 'Updated description',
            'is_public' => true
        ]);
    }

    public function test_non_owner_cannot_update_build()
    {
        $otherUser = User::factory()->create();
        Sanctum::actingAs($otherUser);

        $build = Build::factory()->create([
            'user_id' => $this->user->id
        ]);

        $response = $this->putJson("/api/v1/builds/{$build->id}", [
            'name' => 'Hacked Name'
        ]);

        $response->assertStatus(403);
    }

    public function test_owner_can_delete_build()
    {
        Sanctum::actingAs($this->user);

        $build = Build::factory()->create([
            'user_id' => $this->user->id
        ]);

        $response = $this->deleteJson("/api/v1/builds/{$build->id}");

        $response->assertStatus(200);
        $this->assertDatabaseMissing('builds', ['id' => $build->id]);
    }

    public function test_non_owner_cannot_delete_build()
    {
        $otherUser = User::factory()->create();
        Sanctum::actingAs($otherUser);

        $build = Build::factory()->create([
            'user_id' => $this->user->id
        ]);

        $response = $this->deleteJson("/api/v1/builds/{$build->id}");

        $response->assertStatus(403);
        $this->assertDatabaseHas('builds', ['id' => $build->id]);
    }

    public function test_can_access_shared_build_by_token()
    {
        $build = Build::factory()->create([
            'user_id' => $this->user->id,
            'is_public' => true,
            'share_token' => 'test-token-123'
        ]);

        $response = $this->getJson("/api/v1/builds/shared/test-token-123");

        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    'id',
                    'name',
                    'user',
                    'components'
                ],
                'compatibility'
            ]);
    }

    public function test_cannot_access_private_build_by_token()
    {
        $build = Build::factory()->create([
            'user_id' => $this->user->id,
            'is_public' => false,
            'share_token' => 'test-token-123'
        ]);

        $response = $this->getJson("/api/v1/builds/shared/test-token-123");

        $response->assertStatus(404);
    }

    public function test_can_check_component_compatibility()
    {
        $componentIds = [$this->cpu->id, $this->gpu->id];

        $response = $this->postJson('/api/v1/builds/compatibility', [
            'component_ids' => $componentIds
        ]);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'is_compatible',
                'issues',
                'warnings',
                'components'
            ]);
    }

    public function test_validates_build_creation_data()
    {
        Sanctum::actingAs($this->user);

        // Test missing name
        $response = $this->postJson('/api/v1/builds', [
            'components' => [['component_id' => $this->cpu->id]]
        ]);
        $response->assertStatus(422);

        // Test missing components
        $response = $this->postJson('/api/v1/builds', [
            'name' => 'Test Build'
        ]);
        $response->assertStatus(422);

        // Test invalid component ID
        $response = $this->postJson('/api/v1/builds', [
            'name' => 'Test Build',
            'components' => [['component_id' => 99999]]
        ]);
        $response->assertStatus(422);
    }

    public function test_can_search_builds()
    {
        Build::factory()->create([
            'user_id' => $this->user->id,
            'name' => 'Gaming PC Build',
            'is_public' => true
        ]);
        
        Build::factory()->create([
            'user_id' => $this->user->id,
            'name' => 'Office Computer',
            'is_public' => true
        ]);

        $response = $this->getJson('/api/v1/builds?search=Gaming');

        $response->assertStatus(200);
        $data = $response->json('data');
        
        $this->assertCount(1, $data);
        $this->assertStringContainsString('Gaming', $data[0]['name']);
    }

    public function test_can_sort_builds()
    {
        Build::factory()->create([
            'user_id' => $this->user->id,
            'name' => 'Z Build',
            'total_price' => 1000,
            'is_public' => true
        ]);
        
        Build::factory()->create([
            'user_id' => $this->user->id,
            'name' => 'A Build',
            'total_price' => 500,
            'is_public' => true
        ]);

        // Test sorting by name
        $response = $this->getJson('/api/v1/builds?sort=name&direction=asc');
        $response->assertStatus(200);
        $data = $response->json('data');
        $this->assertEquals('A Build', $data[0]['name']);

        // Test sorting by price
        $response = $this->getJson('/api/v1/builds?sort=total_price&direction=desc');
        $response->assertStatus(200);
        $data = $response->json('data');
        $this->assertEquals(1000, $data[0]['total_price']);
    }
}