<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class PaymentVerificationRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'transaction_id' => [
                'required',
                'string',
                'max:100',
                'regex:/^[a-zA-Z0-9_-]+$/'
            ],
            'razorpay_payment_id' => [
                'required_if:gateway,razorpay',
                'string',
                'max:100',
                'regex:/^pay_[a-zA-Z0-9]+$/'
            ],
            'razorpay_order_id' => [
                'required_if:gateway,razorpay',
                'string',
                'max:100',
                'regex:/^order_[a-zA-Z0-9]+$/'
            ],
            'razorpay_signature' => [
                'required_if:gateway,razorpay',
                'string',
                'max:200'
            ],
            'gateway' => [
                'sometimes',
                'string',
                Rule::in(['razorpay', 'payumoney', 'cashfree'])
            ]
        ];
    }

    /**
     * Get custom validation messages.
     */
    public function messages(): array
    {
        return [
            'transaction_id.required' => 'Transaction ID is required for verification.',
            'transaction_id.regex' => 'Invalid transaction ID format.',
            'razorpay_payment_id.required_if' => 'Razorpay payment ID is required for Razorpay payments.',
            'razorpay_payment_id.regex' => 'Invalid Razorpay payment ID format.',
            'razorpay_order_id.required_if' => 'Razorpay order ID is required for Razorpay payments.',
            'razorpay_order_id.regex' => 'Invalid Razorpay order ID format.',
            'razorpay_signature.required_if' => 'Razorpay signature is required for verification.',
            'gateway.in' => 'Invalid payment gateway specified.'
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // Validate transaction exists and belongs to user
            if ($this->transaction_id) {
                $transaction = \App\Models\Transaction::where('transaction_id', $this->transaction_id)
                    ->where('user_id', auth()->id())
                    ->first();
                
                if (!$transaction) {
                    $validator->errors()->add('transaction_id', 'Transaction not found or access denied.');
                } elseif ($transaction->status === 'completed') {
                    $validator->errors()->add('transaction_id', 'Transaction has already been completed.');
                }
            }
        });
    }

    /**
     * Handle a failed validation attempt.
     */
    protected function failedValidation(\Illuminate\Contracts\Validation\Validator $validator): void
    {
        $response = response()->json([
            'success' => false,
            'error' => [
                'code' => 'VERIFICATION_VALIDATION_ERROR',
                'message' => 'Payment verification data is invalid.',
                'details' => $validator->errors()->toArray()
            ]
        ], 422);
        
        throw new \Illuminate\Http\Exceptions\HttpResponseException($response);
    }
}