<?php

namespace App\Livewire\User;

use Livewire\Component;
use Illuminate\Support\Facades\Auth;
use App\Models\Build;
use App\Jobs\SendBuildShared;

class SavedBuilds extends Component
{
    public $builds;
    public $selectedBuild = null;
    public $shareToken = null;
    public $showShareModal = false;
    public $recipientEmail = '';

    public function mount()
    {
        $this->builds = Build::where('user_id', Auth::id())->orderByDesc('created_at')->get();
    }

    public function selectBuild($buildId)
    {
        $this->selectedBuild = $this->builds->where('id', $buildId)->first();
    }

    public function clearSelectedBuild()
    {
        $this->selectedBuild = null;
    }

    public function generateShareToken($buildId)
    {
        $build = $this->builds->where('id', $buildId)->first();
        if ($build) {
            if (!$build->share_token) {
                $build->share_token = bin2hex(random_bytes(16));
                $build->save();
            }
            $this->shareToken = $build->share_token;
            $this->showShareModal = true;
            // Only send if recipient email is set and valid
            if (filter_var($this->recipientEmail, FILTER_VALIDATE_EMAIL)) {
                \App\Jobs\SendBuildShared::dispatch($build, $this->recipientEmail);
            }
        }
    }

    public function closeShareModal()
    {
        $this->showShareModal = false;
        $this->shareToken = null;
    }

    public function togglePrivacy($buildId)
    {
        $build = $this->builds->where('id', $buildId)->first();
        if ($build) {
            $build->is_public = !$build->is_public;
            $build->save();
            $this->mount(); // reload builds
        }
    }
    public function revokeShare($buildId)
    {
        $build = $this->builds->where('id', $buildId)->first();
        if ($build && $build->share_token) {
            $build->share_token = null;
            $build->save();
            $this->mount(); // reload builds
        }
    }

    public function render()
    {
        return view('livewire.user.saved-builds');
    }
} 