<div class="bg-white dark:bg-nexus-dark-900 min-h-screen text-gray-900 dark:text-white transition-colors duration-300">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Search and Sort Controls -->
        <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
            <div>
                <h1 class="tech-font text-3xl font-bold text-gray-900 dark:text-white neon-text">
                    @if ($selectedCategory)
                        @php
                            $categoryName = collect($categories)->where('slug', $selectedCategory)->first()['name'] ??
                                           \Illuminate\Support\Str::title(str_replace('-', ' ', $selectedCategory));
                        @endphp
                        {{ $categoryName }}
                        <button wire:click="clearCategory"
                            class="ml-2 text-sm text-nexus-primary-600 dark:text-nexus-primary-400 hover:text-nexus-primary-700 dark:hover:text-nexus-primary-300 focus:outline-none tech-font transition-colors duration-300">
                            (Clear)
                        </button>
                    @else
                        All Products
                    @endif
                </h1>

                <!-- Active Filters Display -->
                @if ($search || $selectedBrands || $priceMin || $priceMax || $selectedSpecs || $minRating || $inStockOnly)
                    <div class="mt-2 flex flex-wrap gap-2">
                        @if ($search)
                            <span
                                class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-nexus-primary-100 dark:bg-nexus-bg-blue-medium text-nexus-primary-800 dark:text-nexus-primary-300 border border-nexus-primary-300 dark:border-nexus-border-blue-light tech-font">
                                Search: "{{ $search }}"
                                <button wire:click="$set('search', '')"
                                    class="ml-2 text-nexus-primary-600 dark:text-nexus-primary-400 hover:text-nexus-primary-700 dark:hover:text-nexus-primary-300 transition-colors">×</button>
                            </span>
                        @endif

                        @foreach ($selectedBrands as $brand)
                            <span
                                class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-nexus-success-100 dark:bg-nexus-bg-green-medium text-nexus-success-800 dark:text-nexus-success-300 border border-nexus-success-300 dark:border-nexus-border-green-light tech-font">
                                Brand: {{ $brand }}
                                <button wire:click="removeBrandFilter('{{ $brand }}')"
                                    class="ml-2 text-nexus-success-600 dark:text-nexus-success-400 hover:text-nexus-success-700 dark:hover:text-nexus-success-300 transition-colors">×</button>
                            </span>
                        @endforeach

                        @if ($priceMin)
                            <span
                                class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-nexus-warning-100 dark:bg-nexus-bg-yellow-medium text-nexus-warning-800 dark:text-nexus-warning-300 border border-nexus-warning-300 dark:border-nexus-border-yellow-light tech-font">
                                Min: ${{ $priceMin }}
                                <button wire:click="$set('priceMin', null)"
                                    class="ml-2 text-nexus-warning-600 dark:text-nexus-warning-400 hover:text-nexus-warning-700 dark:hover:text-nexus-warning-300 transition-colors">×</button>
                            </span>
                        @endif

                        @if ($priceMax)
                            <span
                                class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-nexus-warning-100 dark:bg-nexus-bg-yellow-medium text-nexus-warning-800 dark:text-nexus-warning-300 border border-nexus-warning-300 dark:border-nexus-border-yellow-light tech-font">
                                Max: ${{ $priceMax }}
                                <button wire:click="$set('priceMax', null)"
                                    class="ml-2 text-nexus-warning-600 dark:text-nexus-warning-400 hover:text-nexus-warning-700 dark:hover:text-nexus-warning-300 transition-colors">×</button>
                            </span>
                        @endif

                        @foreach ($selectedSpecs as $specKey => $specValues)
                            @if (is_array($specValues))
                                @foreach ($specValues as $specValue)
                                    <span
                                        class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-nexus-accent-100 dark:bg-nexus-bg-purple-medium text-nexus-accent-800 dark:text-nexus-accent-300 border border-nexus-accent-300 dark:border-nexus-border-purple-light tech-font">
                                        {{ ucfirst(str_replace('_', ' ', $specKey)) }}: {{ $specValue }}
                                        <button
                                            wire:click="removeSpecFilter('{{ $specKey }}', '{{ $specValue }}')"
                                            class="ml-2 text-nexus-accent-600 dark:text-nexus-accent-400 hover:text-nexus-accent-700 dark:hover:text-nexus-accent-300 transition-colors">×</button>
                                    </span>
                                @endforeach
                            @else
                                <span
                                    class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-nexus-accent-100 dark:bg-nexus-bg-purple-medium text-nexus-accent-800 dark:text-nexus-accent-300 border border-nexus-accent-300 dark:border-nexus-border-purple-light tech-font">
                                    {{ ucfirst(str_replace('_', ' ', $specKey)) }}: {{ $specValues }}
                                    <button
                                        wire:click="removeSpecFilter('{{ $specKey }}', '{{ $specValues }}')"
                                        class="ml-2 text-nexus-accent-600 dark:text-nexus-accent-400 hover:text-nexus-accent-700 dark:hover:text-nexus-accent-300 transition-colors">×</button>
                                </span>
                            @endif
                        @endforeach

                        @if ($minRating)
                            <span
                                class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-orange-100 dark:bg-orange-500/20 text-orange-800 dark:text-orange-300 border border-orange-300 dark:border-orange-500/30 tech-font">
                                {{ $minRating }}+ Stars
                                <button wire:click="$set('minRating', null)"
                                    class="ml-2 text-orange-600 dark:text-orange-400 hover:text-orange-700 dark:hover:text-orange-300 transition-colors">×</button>
                            </span>
                        @endif

                        @if ($inStockOnly)
                            <span
                                class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-nexus-error-100 dark:bg-nexus-error-500/20 text-nexus-error-800 dark:text-nexus-error-300 border border-nexus-error-300 dark:border-nexus-error-500/30 tech-font">
                                In Stock Only
                                <button wire:click="$set('inStockOnly', false)"
                                    class="ml-2 text-nexus-error-600 dark:text-nexus-error-400 hover:text-nexus-error-700 dark:hover:text-nexus-error-300 transition-colors">×</button>
                            </span>
                        @endif
                    </div>
                @endif
            </div>

            <div class="w-full md:w-auto flex flex-col md:flex-row gap-4">
                <!-- Search Bar -->
                <div class="relative">
                    <input type="text" wire:model.live.debounce.300ms="search" placeholder="Search products..."
                        class="w-full md:w-64 pl-10 pr-4 py-2 bg-white dark:bg-nexus-dark-800 border border-gray-300 dark:border-nexus-border-blue-light rounded-lg text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-nexus-gray-400 focus:outline-none focus:ring-2 focus:ring-nexus-primary-500 focus:border-nexus-primary-500 transition-all duration-300 nexus-border-glow">
                    <div class="absolute left-3 top-2.5 text-gray-500 dark:text-nexus-gray-400">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd"
                                d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z"
                                clip-rule="evenodd" />
                        </svg>
                    </div>
                </div>

                <!-- Sort and Display Controls -->
                <div class="flex gap-2">
                    <select wire:model.live="sortBy"
                        class="bg-white dark:bg-nexus-dark-800 border border-gray-300 dark:border-nexus-border-blue-light text-gray-900 dark:text-white rounded-md shadow-sm focus:ring-nexus-primary-500 focus:border-nexus-primary-500 sm:text-sm tech-font nexus-border-glow">
                        <option value="relevance">Relevance</option>
                        <option value="name">Name</option>
                        <option value="price">Price</option>
                        <option value="rating">Rating</option>
                        <option value="popularity">Popularity</option>
                        <option value="stock">Stock</option>
                        <option value="created_at">Newest</option>
                    </select>

                    <select wire:model.live="sortDirection"
                        class="bg-white dark:bg-nexus-dark-800 border border-gray-300 dark:border-nexus-border-blue-light text-gray-900 dark:text-white rounded-md shadow-sm focus:ring-nexus-primary-500 focus:border-nexus-primary-500 sm:text-sm tech-font nexus-border-glow">
                        <option value="asc">Ascending</option>
                        <option value="desc">Descending</option>
                    </select>

                    <select wire:model.live="perPage"
                        class="bg-white dark:bg-nexus-dark-800 border border-gray-300 dark:border-nexus-border-blue-light text-gray-900 dark:text-white rounded-md shadow-sm focus:ring-nexus-primary-500 focus:border-nexus-primary-500 sm:text-sm tech-font nexus-border-glow">
                        <option value="12">12 per page</option>
                        <option value="24">24 per page</option>
                        <option value="48">48 per page</option>
                        <option value="96">96 per page</option>
                    </select>
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
            <!-- Filters Sidebar -->
            <div class="lg:col-span-1 space-y-6">
                <!-- Categories Filter -->
                <div class="bg-white dark:bg-nexus-dark-800 p-6 rounded-xl border border-gray-300 dark:border-nexus-border-blue-light nexus-border-glow shadow-sm">
                    <h3 class="tech-font font-medium text-gray-900 dark:text-nexus-primary-400 mb-4 text-lg">Categories</h3>
                    <div class="space-y-3">
                        <div>
                            <a href="#" wire:click.prevent="clearCategory"
                                class="block px-3 py-2 rounded-md text-gray-700 dark:text-nexus-gray-300 hover:text-nexus-primary-600 dark:hover:text-nexus-primary-400 hover:bg-gray-100 dark:hover:bg-nexus-bg-blue-light transition-all duration-300 {{ $selectedCategory === null ? 'font-semibold text-nexus-primary-600 dark:text-nexus-primary-400 bg-gray-100 dark:bg-nexus-bg-blue-medium border border-gray-300 dark:border-nexus-border-blue-light' : '' }}">
                                All Products
                            </a>
                        </div>

                        @foreach ($categories as $category)
                            <div>
                                <a href="#" wire:click.prevent="selectCategory('{{ $category['slug'] }}')"
                                    class="block px-3 py-2 rounded-md text-gray-700 dark:text-nexus-gray-300 hover:text-nexus-primary-600 dark:hover:text-nexus-primary-400 hover:bg-gray-100 dark:hover:bg-nexus-bg-blue-light transition-all duration-300 {{ $selectedCategory === $category['slug'] ? 'font-semibold text-nexus-primary-600 dark:text-nexus-primary-400 bg-gray-100 dark:bg-nexus-bg-blue-medium border border-gray-300 dark:border-nexus-border-blue-light' : '' }}">
                                    {{ $category['name'] }}
                                </a>
                            </div>
                        @endforeach
                    </div>
                </div>

                <!-- Brand Filter -->
                @if (count($availableBrands) > 0)
                    <div class="bg-white dark:bg-nexus-dark-800 p-6 rounded-xl border border-gray-300 dark:border-nexus-border-blue-light nexus-border-glow shadow-sm">
                        <h3 class="tech-font font-medium text-gray-900 dark:text-nexus-primary-400 mb-4 text-lg">Brands</h3>
                        <div class="space-y-3 max-h-48 overflow-y-auto">
                            @foreach ($availableBrands as $brand)
                                <label class="flex items-center cursor-pointer hover:bg-gray-100 dark:hover:bg-nexus-bg-blue-light p-2 rounded-md transition-colors duration-300">
                                    <input type="checkbox" wire:model.live="selectedBrands" value="{{ $brand }}"
                                        class="h-4 w-4 text-nexus-primary-600 focus:ring-nexus-primary-500 border-gray-300 dark:border-nexus-gray-300 rounded bg-white dark:bg-nexus-dark-700">
                                    <span class="ml-3 text-gray-700 dark:text-nexus-gray-300">{{ $brand }}</span>
                                </label>
                            @endforeach
                        </div>
                    </div>
                @endif

                <!-- Price Range Filter -->
                <div class="bg-white dark:bg-nexus-dark-800 p-6 rounded-xl border border-gray-300 dark:border-nexus-border-blue-light nexus-border-glow shadow-sm">
                    <h3 class="tech-font font-medium text-gray-900 dark:text-nexus-primary-400 mb-4 text-lg">Price Range</h3>
                    <div class="grid grid-cols-2 gap-3">
                        <div>
                            <label for="priceMin" class="block text-sm text-gray-600 dark:text-nexus-gray-400 mb-2">Min ($)</label>
                            <input type="number" id="priceMin" wire:model.live.debounce.500ms="priceMin"
                                min="0"
                                class="w-full bg-white dark:bg-nexus-dark-700 border border-gray-300 dark:border-nexus-border-blue-light text-gray-900 dark:text-white rounded-md shadow-sm focus:ring-nexus-primary-500 focus:border-nexus-primary-500 sm:text-sm nexus-border-glow">
                        </div>
                        <div>
                            <label for="priceMax" class="block text-sm text-gray-600 dark:text-nexus-gray-400 mb-2">Max ($)</label>
                            <input type="number" id="priceMax" wire:model.live.debounce.500ms="priceMax"
                                min="0"
                                class="w-full bg-white dark:bg-nexus-dark-700 border border-gray-300 dark:border-nexus-border-blue-light text-gray-900 dark:text-white rounded-md shadow-sm focus:ring-nexus-primary-500 focus:border-nexus-primary-500 sm:text-sm nexus-border-glow">
                        </div>
                    </div>
                </div>

                <!-- Rating Filter -->
                <div class="bg-white dark:bg-nexus-dark-800 p-6 rounded-xl border border-gray-300 dark:border-nexus-border-blue-light nexus-border-glow shadow-sm">
                    <h3 class="tech-font font-medium text-gray-900 dark:text-nexus-primary-400 mb-4 text-lg">Minimum Rating</h3>
                    <select wire:model.live="minRating"
                        class="w-full bg-white dark:bg-nexus-dark-700 border border-gray-300 dark:border-nexus-border-blue-light text-gray-900 dark:text-white rounded-md shadow-sm focus:ring-nexus-primary-500 focus:border-nexus-primary-500 sm:text-sm nexus-border-glow">
                        <option value="">Any Rating</option>
                        <option value="4">4+ Stars</option>
                        <option value="3">3+ Stars</option>
                        <option value="2">2+ Stars</option>
                        <option value="1">1+ Stars</option>
                    </select>
                </div>

                <!-- Specifications Filter -->
                @if (count($availableSpecs) > 0)
                    <div>
                        <h3 class="font-medium text-gray-900 mb-2">Specifications</h3>
                        <div class="space-y-3 max-h-64 overflow-y-auto">
                            @foreach ($availableSpecs as $specKey => $specValues)
                                <div>
                                    <h4 class="text-sm font-medium text-gray-700 mb-1">
                                        {{ ucfirst(str_replace('_', ' ', $specKey)) }}</h4>
                                    <div class="space-y-1 ml-2">
                                        @foreach ($specValues as $specValue)
                                            <label class="flex items-center">
                                                <input type="checkbox"
                                                    wire:model.live="selectedSpecs.{{ $specKey }}"
                                                    value="{{ $specValue }}"
                                                    class="h-3 w-3 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                                <span class="ml-2 text-sm text-gray-600">{{ $specValue }}</span>
                                            </label>
                                        @endforeach
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                @endif

                <!-- Stock Filter -->
                <div class="bg-white dark:bg-nexus-dark-800 p-6 rounded-xl border border-gray-300 dark:border-nexus-border-blue-light nexus-border-glow shadow-sm">
                    <label class="flex items-center cursor-pointer hover:bg-gray-100 dark:hover:bg-nexus-bg-blue-light p-2 rounded-md transition-colors duration-300">
                        <input type="checkbox" wire:model.live="inStockOnly"
                            class="h-4 w-4 text-nexus-primary-600 focus:ring-nexus-primary-500 border-gray-300 dark:border-nexus-gray-300 rounded bg-white dark:bg-nexus-dark-700">
                        <span class="ml-3 text-gray-700 dark:text-nexus-gray-300 tech-font">In Stock Only</span>
                    </label>
                </div>

                <!-- Clear Filters Button -->
                <div>
                    <button wire:click="clearFilters"
                        class="w-full px-4 py-3 text-sm font-medium text-gray-700 dark:text-white bg-white dark:bg-nexus-dark-800 border border-gray-300 dark:border-nexus-border-blue-light rounded-md hover:bg-gray-50 dark:hover:bg-nexus-dark-700 focus:outline-none focus:ring-2 focus:ring-nexus-primary-500 focus:ring-offset-2 tech-font nexus-hover-glow transition-all duration-300 shadow-sm">
                        Clear All Filters
                    </button>
                </div>
            </div>

            <!-- Products Grid -->
            <div class="lg:col-span-3">
                <!-- Results Count -->
                <div class="mb-6 text-sm text-gray-600 dark:text-nexus-gray-400 tech-font">
                    Showing {{ $components->firstItem() ?? 0 }} to {{ $components->lastItem() ?? 0 }} of
                    {{ $components->total() }} results
                </div>

                @if ($components->isEmpty())
                    <div class="text-center py-12 bg-gray-50 dark:bg-nexus-dark-800 rounded-xl border border-gray-200 dark:border-nexus-border-blue-light nexus-border-glow shadow-sm">
                        <svg xmlns="http://www.w3.org/2000/svg" class="mx-auto h-12 w-12 text-gray-400 dark:text-nexus-gray-400"
                            fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <h3 class="mt-4 text-lg font-medium text-gray-900 dark:text-white tech-font">No products found</h3>
                        <p class="mt-2 text-sm text-gray-500 dark:text-nexus-gray-400">Try adjusting your search or filter criteria.</p>
                    </div>
                @else
                    <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
                        @foreach ($components as $component)
                            <div class="bg-white dark:bg-nexus-dark-800 border border-gray-200 dark:border-nexus-border-blue-light rounded-xl overflow-hidden hover:shadow-lg dark:hover:shadow-glow-blue transition-all duration-300 transform hover:scale-105 nexus-border-glow shadow-sm">
                                <!-- Product Image -->
                                <div class="relative">
                                    @if ($component->image)
                                        <img src="{{ $component->image }}" alt="{{ $component->name }}"
                                            class="w-full h-48 object-cover">
                                    @else
                                        <div class="w-full h-48 bg-gray-200 dark:bg-nexus-dark-700 flex items-center justify-center">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 text-gray-400 dark:text-nexus-gray-500"
                                                fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                            </svg>
                                        </div>
                                    @endif

                                    <!-- Stock Badge -->
                                    @if ($component->stock <= 0)
                                        <div
                                            class="absolute top-3 left-3 px-3 py-1 bg-nexus-error-500 text-white text-xs rounded-full tech-font border border-nexus-error-400">
                                            Out of Stock
                                        </div>
                                    @elseif($component->stock <= 5)
                                        <div
                                            class="absolute top-3 left-3 px-3 py-1 bg-nexus-warning-500 text-white text-xs rounded-full tech-font border border-nexus-warning-400">
                                            Low Stock
                                        </div>
                                    @endif
                                </div>

                                <!-- Product Info -->
                                <div class="p-6">
                                    <!-- Category and Type -->
                                    <div class="flex justify-between items-center mb-2">
                                        <div class="text-xs text-nexus-primary-600 dark:text-nexus-primary-400 uppercase tracking-wide tech-font">
                                            {{ $component->category->name }}
                                        </div>
                                        @if(isset($component->type) && $component->type === 'product')
                                            <span class="text-xs bg-nexus-accent-100 dark:bg-nexus-accent-500/20 text-nexus-accent-700 dark:text-nexus-accent-300 px-2 py-1 rounded-full tech-font">
                                                Product
                                            </span>
                                        @else
                                            <span class="text-xs bg-nexus-secondary-100 dark:bg-nexus-secondary-500/20 text-nexus-secondary-700 dark:text-nexus-secondary-300 px-2 py-1 rounded-full tech-font">
                                                Component
                                            </span>
                                        @endif
                                    </div>

                                    <!-- Product Name -->
                                    <h3 class="font-semibold text-lg mb-3 line-clamp-2">
                                        @if(isset($component->type) && $component->type === 'product')
                                            <a href="{{ route('products.show', $component->slug ?? $component->id) }}"
                                                class="text-gray-900 dark:text-white hover:text-nexus-primary-600 dark:hover:text-nexus-primary-400 transition-colors duration-300">
                                                {{ $component->name }}
                                            </a>
                                        @else
                                            <a href="{{ route('shop.product', $component->slug ?? $component->id) }}"
                                                class="text-gray-900 dark:text-white hover:text-nexus-primary-600 dark:hover:text-nexus-primary-400 transition-colors duration-300">
                                                {{ $component->name }}
                                            </a>
                                        @endif
                                    </h3>

                                    <!-- Brand and Model -->
                                    <p class="text-gray-600 dark:text-nexus-gray-400 mb-3 text-sm">
                                        {{ $component->brand }}
                                        @if ($component->model)
                                            {{ $component->model }}
                                        @endif
                                    </p>

                                    <!-- Key Specifications -->
                                    @if ($component->specs && is_array($component->specs))
                                        <div class="mb-3">
                                            @php
                                                $keySpecs = array_slice($component->specs, 0, 2);
                                            @endphp
                                            @foreach ($keySpecs as $key => $value)
                                                <div class="text-xs text-gray-600 dark:text-nexus-gray-400 mb-1">
                                                    <span
                                                        class="font-medium text-nexus-secondary-600 dark:text-nexus-secondary-400">{{ ucfirst(str_replace('_', ' ', $key)) }}:</span>
                                                    {{ $value }}
                                                </div>
                                            @endforeach
                                        </div>
                                    @endif

                                    <!-- Price -->
                                    <div class="mb-4">
                                        <span
                                            class="text-2xl font-bold text-gray-900 dark:text-white tech-font">${{ number_format($component->price, 2) }}</span>
                                    </div>

                                    <!-- Stock Status -->
                                    <div class="mb-4">
                                        @if ($component->stock > 0)
                                            <span class="text-nexus-success-600 dark:text-nexus-success-400 text-sm font-medium tech-font">
                                                In Stock ({{ $component->stock }} available)
                                            </span>
                                        @else
                                            <span class="text-nexus-error-600 dark:text-nexus-error-400 text-sm font-medium tech-font">Out of Stock</span>
                                        @endif
                                    </div>

                                    <!-- Action Buttons -->
                                    <div class="flex space-x-3">
                                        <button wire:click="addToCart({{ $component->id }}, '{{ $component->type ?? 'component' }}')"
                                            class="flex-1 px-4 py-2 nexus-gradient-primary text-white rounded-md hover:from-nexus-primary-700 hover:to-nexus-secondary-700 focus:outline-none focus:ring-2 focus:ring-nexus-primary-500 focus:ring-offset-2 transition-all duration-300 tech-font glow {{ $component->stock <= 0 ? 'opacity-50 cursor-not-allowed' : '' }}"
                                            {{ $component->stock <= 0 ? 'disabled' : '' }}>
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 inline mr-2"
                                                fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m6-5v6a2 2 0 01-2 2H9a2 2 0 01-2-2v-6m8 0V9a2 2 0 00-2-2H9a2 2 0 00-2-2v6.01" />
                                            </svg>
                                            Add to Cart
                                        </button>

                                        @if(isset($component->type) && $component->type === 'product')
                                            <a href="{{ route('products.show', $component->slug ?? $component->id) }}"
                                                class="px-4 py-2 border border-gray-300 dark:border-nexus-border-blue-light text-gray-700 dark:text-nexus-gray-300 rounded-md hover:bg-gray-50 dark:hover:bg-nexus-bg-blue-light hover:text-gray-900 dark:hover:text-white focus:outline-none focus:ring-2 focus:ring-nexus-primary-500 focus:ring-offset-2 transition-all duration-300 text-center tech-font nexus-hover-glow">
                                                Details
                                            </a>
                                        @else
                                            <a href="{{ route('shop.product', $component->slug ?? $component->id) }}"
                                                class="px-4 py-2 border border-gray-300 dark:border-nexus-border-blue-light text-gray-700 dark:text-nexus-gray-300 rounded-md hover:bg-gray-50 dark:hover:bg-nexus-bg-blue-light hover:text-gray-900 dark:hover:text-white focus:outline-none focus:ring-2 focus:ring-nexus-primary-500 focus:ring-offset-2 transition-all duration-300 text-center tech-font nexus-hover-glow">
                                                Details
                                            </a>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>

                    <!-- Pagination -->
                    <div class="mt-8">
                        {{ $components->links() }}
                    </div>
                @endif
            </div>
        </div>
    </div>

    <!-- Flash Messages -->
    @if (session()->has('message'))
        <div class="fixed bottom-4 right-4 bg-white dark:bg-nexus-dark-800 border-l-4 border-nexus-success-500 text-nexus-success-700 dark:text-nexus-success-300 p-4 rounded-xl shadow-lg dark:shadow-glow-green nexus-border-glow"
            x-data="{ show: true }" x-show="show" x-init="setTimeout(() => show = false, 3000)">
            <p class="tech-font">{{ session('message') }}</p>
        </div>
    @endif

    @if (session()->has('error'))
        <div class="fixed bottom-4 right-4 bg-white dark:bg-nexus-dark-800 border-l-4 border-nexus-error-500 text-nexus-error-700 dark:text-nexus-error-300 p-4 rounded-xl shadow-lg dark:shadow-glow-red nexus-border-glow"
            x-data="{ show: true }" x-show="show" x-init="setTimeout(() => show = false, 3000)">
            <p class="tech-font">{{ session('error') }}</p>
        </div>
    @endif
</div>
