<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Build;
use App\Models\Component;
use App\Models\BuildComponent;
use App\Models\ComponentCategory;

class BuildComponentSeeder extends Seeder
{
    public function run()
    {
        $build = Build::first();
        $cpu = Component::where('slug', 'intel-core-i7')->first();
        $mb = Component::where('slug', 'asus-prime-z590')->first();

        BuildComponent::create([
            'build_id' => $build->id,
            'component_id' => $cpu->id,
            'category_id' => $cpu->category_id,
            'quantity' => 1,
            'price' => $cpu->price,
        ]);

        BuildComponent::create([
            'build_id' => $build->id,
            'component_id' => $mb->id,
            'category_id' => $mb->category_id,
            'quantity' => 1,
            'price' => $mb->price,
        ]);
    }
}