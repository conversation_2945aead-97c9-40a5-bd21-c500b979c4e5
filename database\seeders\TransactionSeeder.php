<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Transaction;
use App\Models\User;
use App\Models\Product;
use App\Models\Component;

class TransactionSeeder extends Seeder
{
    public function run(): void
    {
        $users = User::all();
        
        // Get products, fallback to components if no products
        $products = Product::where('status', 'active')->get();
        $useComponents = false;
        
        if ($products->isEmpty()) {
            // Use components as products if no products exist
            $products = Component::where('is_active', true)->get();
            $useComponents = true;
        }
        
        // Skip if no products/components available
        if ($products->isEmpty()) {
            $this->command->warn('No products or components found. Skipping transaction seeding.');
            return;
        }

        foreach ($users as $user) {
            // Create 2-5 transactions per user
            $numTransactions = rand(2, 5);
            
            for ($i = 0; $i < $numTransactions; $i++) {
                $gateway = $this->getRandomGateway();
                $status = $this->getRandomStatus();
                $amount = rand(500, 50000);
                $product = $products->random();

                Transaction::create([
                    'user_id' => $user->id,
                    'product_id' => $product->id,
                    'quantity' => rand(1, 3),
                    'gateway_name' => $gateway,
                    'transaction_id' => $this->generateTransactionId($gateway),
                    'gateway_transaction_id' => $this->generateGatewayTransactionId($gateway),
                    'amount' => $amount,
                    'currency' => 'INR',
                    'status' => $status,
                    'payment_details' => $this->getPaymentDetails($gateway, $status),
                    'webhook_verified' => $status === Transaction::STATUS_COMPLETED,
                    'failure_reason' => $status === Transaction::STATUS_FAILED ? $this->getFailureReason() : null,
                ]);
            }
        }
    }

    private function getRandomGateway(): string
    {
        $gateways = ['razorpay', 'payumoney', 'cashfree'];
        return $gateways[array_rand($gateways)];
    }

    private function getRandomStatus(): string
    {
        $statuses = [
            Transaction::STATUS_PENDING,
            Transaction::STATUS_COMPLETED,
            Transaction::STATUS_FAILED,
            Transaction::STATUS_CANCELLED,
        ];

        // Weight towards completed transactions
        $weights = [10, 70, 15, 5]; // 70% completed, 15% failed, 10% pending, 5% cancelled
        $rand = rand(1, 100);
        $cumulative = 0;

        foreach ($weights as $index => $weight) {
            $cumulative += $weight;
            if ($rand <= $cumulative) {
                return $statuses[$index];
            }
        }

        return Transaction::STATUS_COMPLETED;
    }

    private function generateTransactionId(string $gateway): string
    {
        return 'TXN_' . strtoupper($gateway) . '_' . uniqid() . '_' . rand(1000, 9999);
    }

    private function generateGatewayTransactionId(string $gateway): string
    {
        return match($gateway) {
            'razorpay' => 'pay_' . uniqid() . rand(1000, 9999),
            'payumoney' => 'PU_' . rand(*********, *********),
            'cashfree' => 'CF_' . rand(*********, *********),
            default => 'GW_' . uniqid(),
        };
    }

    private function getPaymentDetails(string $gateway, string $status): array
    {
        $baseDetails = [
            'gateway' => $gateway,
            'payment_method' => $this->getRandomPaymentMethod(),
            'created_at' => now()->subDays(rand(1, 30))->toISOString(),
        ];

        return match($gateway) {
            'razorpay' => array_merge($baseDetails, [
                'razorpay_order_id' => 'order_' . uniqid(),
                'razorpay_payment_id' => $status === Transaction::STATUS_COMPLETED ? 'pay_' . uniqid() : null,
                'razorpay_signature' => $status === Transaction::STATUS_COMPLETED ? hash('sha256', 'test_signature') : null,
                'method' => 'card',
                'card_network' => 'Visa',
                'card_last4' => rand(1000, 9999),
                'international' => false,
            ]),
            'payumoney' => array_merge($baseDetails, [
                'key' => 'test_merchant_key',
                'txnid' => 'TXN' . rand(*********, *********),
                'mihpayid' => $status === Transaction::STATUS_COMPLETED ? rand(*********, *********) : null,
                'mode' => 'CC',
                'bank_ref_num' => $status === Transaction::STATUS_COMPLETED ? rand(*********000, *********999) : null,
                'bankcode' => 'CC',
                'cardnum' => '****' . rand(1000, 9999),
            ]),
            'cashfree' => array_merge($baseDetails, [
                'appId' => 'test_app_id',
                'orderId' => 'ORDER_' . uniqid(),
                'orderAmount' => rand(500, 50000),
                'orderCurrency' => 'INR',
                'paymentMode' => 'CARD',
                'txStatus' => $status === Transaction::STATUS_COMPLETED ? 'SUCCESS' : 'FAILED',
                'txMsg' => $status === Transaction::STATUS_COMPLETED ? 'Transaction Successful' : 'Transaction Failed',
                'txTime' => now()->toISOString(),
            ]),
            default => $baseDetails,
        };
    }

    private function getWebhookData(string $gateway): array
    {
        return match($gateway) {
            'razorpay' => [
                'entity' => 'event',
                'account_id' => 'acc_' . uniqid(),
                'event' => 'payment.captured',
                'contains' => ['payment'],
                'payload' => [
                    'payment' => [
                        'entity' => [
                            'id' => 'pay_' . uniqid(),
                            'amount' => rand(50000, 5000000), // in paise
                            'currency' => 'INR',
                            'status' => 'captured',
                            'method' => 'card',
                        ]
                    ]
                ],
                'created_at' => now()->timestamp,
            ],
            'payumoney' => [
                'mihpayid' => rand(*********, *********),
                'mode' => 'CC',
                'status' => 'success',
                'key' => 'test_merchant_key',
                'txnid' => 'TXN' . rand(*********, *********),
                'amount' => rand(500, 50000),
                'productinfo' => 'PC Component Purchase',
                'firstname' => 'Test User',
                'email' => '<EMAIL>',
                'phone' => '*********9',
                'hash' => hash('sha512', 'test_webhook_hash'),
            ],
            'cashfree' => [
                'orderId' => 'ORDER_' . uniqid(),
                'orderAmount' => rand(500, 50000),
                'referenceId' => rand(*********, *********),
                'txStatus' => 'SUCCESS',
                'paymentMode' => 'CARD',
                'txMsg' => 'Transaction Successful',
                'txTime' => now()->toISOString(),
                'signature' => hash('sha256', 'test_cashfree_signature'),
            ],
            default => ['webhook_received_at' => now()->toISOString()],
        };
    }

    private function getRandomPaymentMethod(): string
    {
        $methods = ['card', 'netbanking', 'upi', 'wallet', 'emi'];
        return $methods[array_rand($methods)];
    }

    private function getFailureReason(): string
    {
        $reasons = [
            'Insufficient funds',
            'Card declined by bank',
            'Invalid card details',
            'Transaction timeout',
            'Payment gateway error',
            'User cancelled transaction',
            'Authentication failed',
            'Card expired',
            'Daily limit exceeded',
            'Technical error',
        ];

        return $reasons[array_rand($reasons)];
    }
}