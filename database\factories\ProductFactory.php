<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Product>
 */
class ProductFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $name = $this->faker->words(3, true);
        $price = $this->faker->randomFloat(2, 10, 5000);
        $salePrice = $this->faker->boolean(30) ? $this->faker->randomFloat(2, 5, $price - 1) : null;

        $categories = [
            'Electronics',
            'PC Components',
            'Laptops',
            'Mobile Phones',
            'Accessories',
            'Gaming',
            'Software',
            'Books',
            'Clothing',
            'Home & Garden'
        ];

        $brands = [
            'Apple',
            'Samsung',
            'Intel',
            'AMD',
            'NVIDIA',
            'ASUS',
            'MSI',
            'Corsair',
            'Logitech',
            'Generic'
        ];

        return [
            'name' => ucwords($name),
            'slug' => \Illuminate\Support\Str::slug($name),
            'description' => $this->faker->paragraphs(3, true),
            'short_description' => $this->faker->sentence(20),
            'sku' => strtoupper($this->faker->unique()->bothify('??###??')),
            'price' => $price,
            'sale_price' => $salePrice,
            'stock_quantity' => $this->faker->numberBetween(0, 100),
            'manage_stock' => $this->faker->boolean(95),
            'in_stock' => $this->faker->boolean(85),
            'status' => $this->faker->randomElement(['active', 'inactive', 'draft']),
            'type' => $this->faker->randomElement(['simple', 'variable']),
            'category' => $this->faker->randomElement($categories),
            'images' => [
                '/images/products/' . $this->faker->uuid() . '.jpg',
                '/images/products/' . $this->faker->uuid() . '.jpg'
            ],
            'attributes' => [
                'color' => $this->faker->colorName(),
                'material' => $this->faker->randomElement(['Plastic', 'Metal', 'Glass', 'Fabric']),
                'warranty' => $this->faker->randomElement(['1 Year', '2 Years', '3 Years', 'Lifetime'])
            ],
            'weight' => $this->faker->randomFloat(2, 0.1, 10),
            'dimensions' => [
                'length' => $this->faker->randomFloat(2, 5, 50),
                'width' => $this->faker->randomFloat(2, 5, 50),
                'height' => $this->faker->randomFloat(2, 1, 20)
            ],
            'brand' => $this->faker->randomElement($brands),
            'model' => strtoupper($this->faker->bothify('??-###')),
            'sort_order' => $this->faker->numberBetween(0, 1000),
            'featured' => $this->faker->boolean(20),
            'meta_data' => [
                'seo_title' => $name . ' - Best Price Online',
                'seo_description' => 'Buy ' . $name . ' at best price with fast delivery',
                'keywords' => implode(', ', $this->faker->words(5))
            ]
        ];
    }

    public function featured()
    {
        return $this->state(fn (array $attributes) => [
            'featured' => true,
            'status' => 'active',
            'in_stock' => true
        ]);
    }

    public function onSale()
    {
        return $this->state(function (array $attributes) {
            return [
                'sale_price' => $this->faker->randomFloat(2, 5, $attributes['price'] - 1)
            ];
        });
    }

    public function outOfStock()
    {
        return $this->state(fn (array $attributes) => [
            'stock_quantity' => 0,
            'in_stock' => false
        ]);
    }

    public function pcComponent()
    {
        return $this->state(fn (array $attributes) => [
            'category' => 'PC Components',
            'brand' => $this->faker->randomElement(['Intel', 'AMD', 'NVIDIA', 'ASUS', 'MSI', 'Corsair']),
            'attributes' => [
                'socket' => $this->faker->randomElement(['LGA1700', 'AM4', 'AM5']),
                'power_consumption' => $this->faker->numberBetween(65, 300) . 'W',
                'warranty' => $this->faker->randomElement(['2 Years', '3 Years'])
            ]
        ]);
    }
}
