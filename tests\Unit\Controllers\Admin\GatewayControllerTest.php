<?php

namespace Tests\Unit\Controllers\Admin;

use App\Http\Controllers\Admin\GatewayController;
use App\Models\GatewaySetting;
use App\Models\User;
use App\Services\GatewaySettingsService;
use App\Services\PaymentGatewayFactory;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\Request;
use Tests\TestCase;

class GatewayControllerTest extends TestCase
{
    use RefreshDatabase;

    private GatewayController $controller;
    private GatewaySettingsService $gatewaySettingsService;
    private PaymentGatewayFactory $gatewayFactory;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create and authenticate an admin user for the tests
        $user = User::factory()->admin()->create();
        $this->actingAs($user);
        
        $this->gatewaySettingsService = app(GatewaySettingsService::class);
        $this->gatewayFactory = app(PaymentGatewayFactory::class);
        $this->controller = new GatewayController(
            $this->gatewaySettingsService,
            $this->gatewayFactory
        );
    }

    public function test_toggle_enables_gateway()
    {
        GatewaySetting::create([
            'gateway_name' => 'razorpay',
            'is_enabled' => false,
            'is_test_mode' => true,
            'settings' => ['key_id' => 'test_key', 'key_secret' => 'test_secret']
        ]);

        $request = Request::create('/', 'POST', ['enabled' => true]);
        $response = $this->controller->toggle($request, 'razorpay');

        $this->assertEquals(200, $response->getStatusCode());
        
        $data = json_decode($response->getContent(), true);
        $this->assertTrue($data['success']);
        $this->assertEquals('razorpay', $data['data']['gateway']);
        $this->assertTrue($data['data']['is_enabled']);

        $this->assertDatabaseHas('gateway_settings', [
            'gateway_name' => 'razorpay',
            'is_enabled' => true
        ]);
    }

    public function test_switch_mode_changes_test_mode()
    {
        GatewaySetting::create([
            'gateway_name' => 'razorpay',
            'is_enabled' => true,
            'is_test_mode' => true,
            'settings' => ['key_id' => 'test_key', 'key_secret' => 'test_secret']
        ]);

        $request = Request::create('/', 'POST', ['test_mode' => false]);
        $response = $this->controller->switchMode($request, 'razorpay');

        $this->assertEquals(200, $response->getStatusCode());
        
        $data = json_decode($response->getContent(), true);
        $this->assertTrue($data['success']);
        $this->assertEquals('razorpay', $data['data']['gateway']);
        $this->assertFalse($data['data']['is_test_mode']);

        $this->assertDatabaseHas('gateway_settings', [
            'gateway_name' => 'razorpay',
            'is_test_mode' => false
        ]);
    }

    public function test_test_gateway_configuration()
    {
        GatewaySetting::create([
            'gateway_name' => 'razorpay',
            'is_enabled' => true,
            'is_test_mode' => true,
            'settings' => ['key_id' => 'test_key', 'key_secret' => 'test_secret']
        ]);

        $response = $this->controller->test('razorpay');

        $this->assertEquals(200, $response->getStatusCode());
        
        $data = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('success', $data);
        $this->assertArrayHasKey('message', $data);
        $this->assertEquals('razorpay', $data['gateway']);
    }

    public function test_enabled_returns_enabled_gateways()
    {
        GatewaySetting::create([
            'gateway_name' => 'razorpay',
            'is_enabled' => true,
            'is_test_mode' => true,
            'settings' => ['key_id' => 'test_key', 'key_secret' => 'test_secret']
        ]);

        GatewaySetting::create([
            'gateway_name' => 'payumoney',
            'is_enabled' => false,
            'is_test_mode' => true,
            'settings' => ['merchant_key' => 'test_key', 'salt' => 'test_salt']
        ]);

        $response = $this->controller->enabled();

        $this->assertEquals(200, $response->getStatusCode());
        
        $data = json_decode($response->getContent(), true);
        $this->assertTrue($data['success']);
        $this->assertEquals(['razorpay'], $data['data']);
    }

    public function test_unsupported_gateway_returns_404()
    {
        $request = Request::create('/', 'POST', ['enabled' => true]);
        $response = $this->controller->toggle($request, 'unsupported');

        $this->assertEquals(404, $response->getStatusCode());
        
        $data = json_decode($response->getContent(), true);
        $this->assertFalse($data['success']);
        $this->assertEquals('Gateway not supported', $data['message']);
    }

    public function test_toggle_validates_input()
    {
        GatewaySetting::create([
            'gateway_name' => 'razorpay',
            'is_enabled' => false,
            'is_test_mode' => true,
            'settings' => ['key_id' => 'test_key', 'key_secret' => 'test_secret']
        ]);

        $request = Request::create('/', 'POST', ['enabled' => 'invalid']);
        $response = $this->controller->toggle($request, 'razorpay');

        $this->assertEquals(422, $response->getStatusCode());
        
        $data = json_decode($response->getContent(), true);
        $this->assertFalse($data['success']);
        $this->assertEquals('Validation failed', $data['message']);
    }
}