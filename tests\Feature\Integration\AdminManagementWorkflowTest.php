<?php

namespace Tests\Feature\Integration;

use App\Models\Component;
use App\Models\ComponentCategory;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;

class AdminManagementWorkflowTest extends TestCase
{
    use RefreshDatabase;

    protected User $admin;
    protected User $regularUser;
    protected ComponentCategory $category;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->admin = User::factory()->create([
            'role' => 'admin',
            'name' => 'Admin User',
            'email' => '<EMAIL>'
        ]);
        
        $this->regularUser = User::factory()->create([
            'role' => 'user',
            'name' => 'Regular User',
            'email' => '<EMAIL>'
        ]);
        
        $this->category = ComponentCategory::factory()->create([
            'name' => 'CPU',
            'slug' => 'cpu'
        ]);
        
        Storage::fake('public');
    }

    /** @test */
    public function complete_component_management_workflow()
    {
        $this->actingAs($this->admin);
        
        // Step 1: Access admin dashboard
        $response = $this->get(route('admin.dashboard'));
        $response->assertStatus(200);
        $response->assertSee('Admin Dashboard');
        
        // Step 2: Navigate to component management
        $response = $this->get(route('admin.components.index'));
        $response->assertStatus(200);
        $response->assertSee('Manage Components');
        
        // Step 3: Create new component
        $componentData = [
            'name' => 'Intel Core i9-13900K',
            'description' => 'High-performance gaming CPU',
            'category_id' => $this->category->id,
            'brand' => 'Intel',
            'model' => 'i9-13900K',
            'price' => 589.99,
            'stock' => 25,
            'is_active' => true,
            'is_featured' => false,
            'specifications' => [
                'Cores' => '24',
                'Threads' => '32',
                'Base Clock' => '3.0 GHz',
                'Boost Clock' => '5.8 GHz',
                'Socket' => 'LGA1700',
                'TDP' => '125W'
            ],
            'compatibility_data' => [
                'socket' => 'LGA1700',
                'chipset_support' => ['Z790', 'B760', 'H770'],
                'power_consumption' => 125
            ]
        ];
        
        $response = $this->post(route('admin.components.store'), $componentData);
        $response->assertStatus(201);
        $response->assertJson(['success' => true]);
        
        $component = Component::where('name', 'Intel Core i9-13900K')->first();
        $this->assertNotNull($component);
        $this->assertEquals(589.99, $component->price);
        $this->assertEquals(25, $component->stock);
        
        // Step 4: Upload component image
        $image = UploadedFile::fake()->image('cpu.jpg', 800, 600);
        
        $response = $this->post(route('admin.components.upload-image', $component->id), [
            'image' => $image
        ]);
        $response->assertStatus(200);
        
        $component->refresh();
        $this->assertNotNull($component->image);
        Storage::disk('public')->assertExists($component->image);
        
        // Step 5: Update component details
        $updateData = [
            'name' => 'Intel Core i9-13900KS',
            'price' => 699.99,
            'stock' => 15,
            'is_featured' => true
        ];
        
        $response = $this->patch(route('admin.components.update', $component->id), $updateData);
        $response->assertStatus(200);
        
        $component->refresh();
        $this->assertEquals('Intel Core i9-13900KS', $component->name);
        $this->assertEquals(699.99, $component->price);
        $this->assertTrue($component->is_featured);
        
        // Step 6: Bulk operations - create multiple components
        $components = Component::factory()->count(5)->create([
            'category_id' => $this->category->id,
            'is_active' => true,
            'is_featured' => false
        ]);
        
        $componentIds = $components->pluck('id')->toArray();
        
        // Bulk toggle featured status
        $response = $this->post(route('admin.components.bulk-action'), [
            'action' => 'toggle_featured',
            'component_ids' => $componentIds,
            'value' => true
        ]);
        $response->assertStatus(200);
        
        // Verify all components are now featured
        foreach ($components as $comp) {
            $this->assertTrue($comp->fresh()->is_featured);
        }
        
        // Step 7: Bulk price update
        $response = $this->post(route('admin.components.bulk-price-update'), [
            'component_ids' => $componentIds,
            'price_adjustment' => 10,
            'adjustment_type' => 'percentage_increase'
        ]);
        $response->assertStatus(200);
        
        // Verify prices were increased by 10%
        foreach ($components as $comp) {
            $originalPrice = $comp->price;
            $expectedPrice = $originalPrice * 1.10;
            $this->assertEquals(number_format($expectedPrice, 2), number_format($comp->fresh()->price, 2));
        }
        
        // Step 8: Inventory management - low stock alert
        $lowStockComponent = Component::factory()->create([
            'category_id' => $this->category->id,
            'stock' => 2,
            'is_active' => true
        ]);
        
        $response = $this->get(route('admin.inventory.low-stock'));
        $response->assertStatus(200);
        $response->assertSee($lowStockComponent->name);
        
        // Step 9: Bulk stock update
        $response = $this->post(route('admin.inventory.bulk-stock-update'), [
            'updates' => [
                ['component_id' => $lowStockComponent->id, 'stock' => 50],
                ['component_id' => $component->id, 'stock' => 100]
            ]
        ]);
        $response->assertStatus(200);
        
        $this->assertEquals(50, $lowStockComponent->fresh()->stock);
        $this->assertEquals(100, $component->fresh()->stock);
        
        // Step 10: Component search and filtering
        $response = $this->get(route('admin.components.index'), [
            'search' => 'Intel',
            'category' => $this->category->id,
            'status' => 'active'
        ]);
        $response->assertStatus(200);
        $response->assertSee('Intel Core i9-13900KS');
        
        // Step 11: Export component data
        $response = $this->get(route('admin.components.export'), [
            'format' => 'csv',
            'category' => $this->category->id
        ]);
        $response->assertStatus(200);
        $response->assertHeader('Content-Type', 'text/csv; charset=UTF-8');
        
        // Step 12: Deactivate component
        $response = $this->patch(route('admin.components.toggle-status', $component->id));
        $response->assertStatus(200);
        
        $this->assertFalse($component->fresh()->is_active);
        
        // Step 13: Delete component
        $componentToDelete = $components->first();
        $response = $this->delete(route('admin.components.destroy', $componentToDelete->id));
        $response->assertStatus(200);
        
        $this->assertDatabaseMissing('components', ['id' => $componentToDelete->id]);
    }

    /** @test */
    public function complete_order_management_workflow()
    {
        $this->actingAs($this->admin);
        
        // Create test orders with different statuses
        $pendingOrder = Order::factory()->create([
            'user_id' => $this->regularUser->id,
            'status' => 'pending',
            'payment_status' => 'pending',
            'total' => 599.99
        ]);
        
        $processingOrder = Order::factory()->create([
            'user_id' => $this->regularUser->id,
            'status' => 'processing',
            'payment_status' => 'paid',
            'total' => 1299.99
        ]);
        
        $shippedOrder = Order::factory()->create([
            'user_id' => $this->regularUser->id,
            'status' => 'shipped',
            'payment_status' => 'paid',
            'total' => 899.99,
            'tracking_number' => 'TRK123456789'
        ]);
        
        // Add order items
        $component = Component::factory()->create([
            'category_id' => $this->category->id,
            'price' => 299.99
        ]);
        
        OrderItem::factory()->create([
            'order_id' => $pendingOrder->id,
            'component_id' => $component->id,
            'quantity' => 2,
            'price' => $component->price
        ]);
        
        // Step 1: Access order management dashboard
        $response = $this->get(route('admin.orders.index'));
        $response->assertStatus(200);
        $response->assertSee('Order Management');
        $response->assertSee($pendingOrder->order_number);
        $response->assertSee($processingOrder->order_number);
        $response->assertSee($shippedOrder->order_number);
        
        // Step 2: Filter orders by status
        // Note: In a real application, this would be handled by the Livewire component
        // For testing purposes, we'll just verify we can access the page
        $response = $this->get(route('admin.orders.index'));
        $response->assertStatus(200);
        $response->assertSee($pendingOrder->order_number);
        
        // Step 3: View order details
        $response = $this->get(route('admin.orders.show', $pendingOrder->id));
        $response->assertStatus(200);
        $response->assertSee($pendingOrder->order_number);
        $response->assertSee($this->regularUser->name);
        $response->assertSee($component->name);
        $response->assertSee('$599.99');
        
        // Step 4: Update order status
        $response = $this->patch(route('admin.orders.update-status', $pendingOrder->id), [
            'status' => 'processing',
            'notes' => 'Payment confirmed, preparing for shipment'
        ]);
        $response->assertStatus(200);
        
        $pendingOrder->refresh();
        $this->assertEquals('processing', $pendingOrder->status);
        
        // Step 5: Add tracking information
        $response = $this->patch(route('admin.orders.add-tracking', $processingOrder->id), [
            'tracking_number' => 'TRK987654321',
            'carrier' => 'UPS',
            'status' => 'shipped'
        ]);
        $response->assertStatus(200);
        
        $processingOrder->refresh();
        $this->assertEquals('shipped', $processingOrder->status);
        $this->assertEquals('TRK987654321', $processingOrder->tracking_number);
        $this->assertEquals('UPS', $processingOrder->tracking_carrier);
        
        // Step 6: Bulk order operations
        $orders = Order::factory()->count(3)->create([
            'status' => 'processing',
            'payment_status' => 'paid'
        ]);
        
        $orderIds = $orders->pluck('id')->toArray();
        
        $response = $this->post(route('admin.orders.bulk-update'), [
            'order_ids' => $orderIds,
            'status' => 'shipped',
            'send_notification' => true
        ]);
        $response->assertStatus(200);
        
        // Verify all orders were updated
        foreach ($orders as $order) {
            $this->assertEquals('shipped', $order->fresh()->status);
        }
        
        // Step 7: Generate order reports
        $response = $this->get(route('admin.reports.orders'), [
            'date_from' => now()->subDays(30)->format('Y-m-d'),
            'date_to' => now()->format('Y-m-d'),
            'status' => 'all'
        ]);
        $response->assertStatus(200);
        $response->assertJsonStructure([
            'total_orders',
            'total_revenue',
            'orders_by_status',
            'daily_stats'
        ]);
        
        // Step 8: Export order data
        $response = $this->get(route('admin.orders.export'), [
            'format' => 'csv',
            'date_from' => now()->subDays(30)->format('Y-m-d'),
            'date_to' => now()->format('Y-m-d')
        ]);
        $response->assertStatus(200);
        $response->assertHeader('Content-Type', 'text/csv; charset=UTF-8');
        
        // Step 9: Handle order cancellation
        $orderToCancel = Order::factory()->create([
            'status' => 'pending',
            'payment_status' => 'pending'
        ]);
        
        $response = $this->patch(route('admin.orders.cancel', $orderToCancel->id), [
            'reason' => 'Customer requested cancellation',
            'refund_amount' => $orderToCancel->total
        ]);
        $response->assertStatus(200);
        
        $orderToCancel->refresh();
        $this->assertEquals('canceled', $orderToCancel->status);
        
        // Step 10: Process refund
        $response = $this->post(route('admin.orders.process-refund', $orderToCancel->id), [
            'refund_amount' => $orderToCancel->total_amount,
            'reason' => 'Order cancellation'
        ]);
        $response->assertStatus(200);
        
        $this->assertEquals('refunded', $orderToCancel->fresh()->payment_status);
    }

    /** @test */
    public function complete_user_management_workflow()
    {
        $this->actingAs($this->admin);
        
        // Step 1: Access user management
        $response = $this->get(route('admin.users.index'));
        $response->assertStatus(200);
        $response->assertSee('User Management');
        $response->assertSee($this->regularUser->name);
        $response->assertSee($this->regularUser->email);
        
        // Step 2: View user details
        $response = $this->get(route('admin.users.show', $this->regularUser->id));
        $response->assertStatus(200);
        $response->assertSee($this->regularUser->name);
        $response->assertSee($this->regularUser->email);
        
        // Step 3: Update user role
        $response = $this->patch(route('admin.users.update-role', $this->regularUser->id), [
            'role' => 'moderator'
        ]);
        $response->assertStatus(200);
        
        $this->assertEquals('moderator', $this->regularUser->fresh()->role);
        
        // Step 4: Suspend user account
        $response = $this->patch(route('admin.users.suspend', $this->regularUser->id), [
            'reason' => 'Violation of terms of service',
            'duration' => 30 // days
        ]);
        $response->assertStatus(200);
        
        $this->regularUser->refresh();
        $this->assertNotNull($this->regularUser->suspended_until);
        
        // Step 5: Reactivate user account
        $response = $this->patch(route('admin.users.reactivate', $this->regularUser->id));
        $response->assertStatus(200);
        
        $this->assertNull($this->regularUser->fresh()->suspended_until);
        
        // Step 6: View user activity log
        $response = $this->get(route('admin.users.activity', $this->regularUser->id));
        $response->assertStatus(200);
        $response->assertJsonStructure([
            'activities' => [
                '*' => ['action', 'description', 'created_at']
            ]
        ]);
    }

    /** @test */
    public function admin_dashboard_analytics_workflow()
    {
        $this->actingAs($this->admin);
        
        // Create test data for analytics
        $components = Component::factory()->count(10)->create([
            'category_id' => $this->category->id,
            'is_active' => true
        ]);
        
        $orders = Order::factory()->count(5)->create([
            'status' => 'completed',
            'payment_status' => 'paid',
            'created_at' => now()->subDays(rand(1, 30))
        ]);
        
        // Step 1: View main dashboard
        $response = $this->get(route('admin.dashboard'));
        $response->assertStatus(200);
        $response->assertSee('Total Components');
        $response->assertSee('Total Orders');
        $response->assertSee('Total Revenue');
        
        // Step 2: View sales analytics
        $response = $this->get(route('admin.analytics.sales'), [
            'period' => 'last_30_days'
        ]);
        $response->assertStatus(200);
        $response->assertJsonStructure([
            'total_sales',
            'order_count',
            'average_order_value',
            'daily_sales',
            'top_selling_components'
        ]);
        
        // Step 3: View inventory analytics
        $response = $this->get(route('admin.analytics.inventory'));
        $response->assertStatus(200);
        $response->assertJsonStructure([
            'total_components',
            'low_stock_count',
            'out_of_stock_count',
            'inventory_value',
            'category_breakdown'
        ]);
        
        // Step 4: View customer analytics
        $response = $this->get(route('admin.analytics.customers'));
        $response->assertStatus(200);
        $response->assertJsonStructure([
            'total_customers',
            'new_customers_this_month',
            'customer_lifetime_value',
            'top_customers'
        ]);
        
        // Step 5: Generate custom report
        $response = $this->post(route('admin.reports.custom'), [
            'report_type' => 'sales_by_category',
            'date_from' => now()->subDays(30)->format('Y-m-d'),
            'date_to' => now()->format('Y-m-d'),
            'categories' => [$this->category->id]
        ]);
        $response->assertStatus(200);
        $response->assertJsonStructure([
            'report_data',
            'summary',
            'charts'
        ]);
    }

    /** @test */
    public function regular_user_cannot_access_admin_functions()
    {
        $this->actingAs($this->regularUser);
        
        // Test various admin routes
        $adminRoutes = [
            'admin.dashboard',
            'admin.components.index',
            'admin.orders.index',
            'admin.users.index',
            'admin.analytics.sales'
        ];
        
        foreach ($adminRoutes as $route) {
            $response = $this->get(route($route));
            $response->assertStatus(403);
        }
        
        // Test admin API endpoints
        $component = Component::factory()->create(['category_id' => $this->category->id]);
        
        $response = $this->patch(route('admin.components.update', $component->id), [
            'name' => 'Unauthorized Update'
        ]);
        $response->assertStatus(403);
        
        $response = $this->delete(route('admin.components.destroy', $component->id));
        $response->assertStatus(403);
    }

    /** @test */
    public function admin_audit_trail_workflow()
    {
        $this->actingAs($this->admin);
        
        $component = Component::factory()->create([
            'category_id' => $this->category->id,
            'name' => 'Original Name',
            'price' => 100.00
        ]);
        
        // Step 1: Update component (should create audit log)
        $this->patch(route('admin.components.update', $component->id), [
            'name' => 'Updated Name',
            'price' => 150.00
        ]);
        
        // Step 2: View audit logs
        $response = $this->get(route('admin.audit.index'));
        $response->assertStatus(200);
        $response->assertSee('Component Updated');
        $response->assertSee($this->admin->name);
        
        // Step 3: View specific audit details
        $response = $this->get(route('admin.audit.component', $component->id));
        $response->assertStatus(200);
        $response->assertJsonStructure([
            'changes' => [
                '*' => ['field', 'old_value', 'new_value', 'changed_at', 'changed_by']
            ]
        ]);
        
        // Step 4: Export audit logs
        $response = $this->get(route('admin.audit.export'), [
            'date_from' => now()->subDays(7)->format('Y-m-d'),
            'date_to' => now()->format('Y-m-d')
        ]);
        $response->assertStatus(200);
        $response->assertHeader('Content-Type', 'text/csv; charset=UTF-8');
    }
}