<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('inventory_alerts', function (Blueprint $table) {
            $table->foreignId('component_id')->constrained()->onDelete('cascade');
            $table->enum('type', [
                'low_stock',
                'out_of_stock',
                'restock_needed',
                'overstock',
                'price_change',
                'supplier_issue',
                'quality_issue',
                'demand_spike'
            ]);
            $table->integer('current_stock');
            $table->integer('threshold');
            $table->boolean('is_resolved')->default(false);
            $table->timestamp('resolved_at')->nullable();
            $table->json('metadata')->nullable();
            
            $table->index(['component_id', 'type', 'is_resolved']);
            $table->index('created_at');
        });
    }

    public function down(): void
    {
        Schema::table('inventory_alerts', function (Blueprint $table) {
            $table->dropForeign(['component_id']);
            $table->dropColumn([
                'component_id', 'type', 'current_stock', 'threshold',
                'is_resolved', 'resolved_at', 'metadata'
            ]);
        });
    }
};