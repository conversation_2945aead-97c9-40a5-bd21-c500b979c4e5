<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Component;
use App\Models\Order;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class DashboardController extends Controller
{
    /**
     * Display the admin dashboard.
     */
    public function index()
    {
        // Get total sales
        $totalSales = Order::where('status', 'completed')->sum('total');

        // Get total orders
        $totalOrders = Order::count();

        // Get total users
        $totalUsers = User::count();

        // Get total components
        $totalComponents = Component::count();

        // Get low stock components
        $lowStockComponents = Component::where('stock', '<', 10)->get();

        // Get recent orders
        $recentOrders = Order::with('user')->latest()->take(5)->get();

        // Get sales by month for the current year
        $this->salesByMonth = Order::where('status', 'completed')
            ->whereYear('created_at', date('Y'))
            ->select(DB::raw('MONTH(created_at) as month'), DB::raw('SUM(total) as total'))
            ->groupBy('month')
            ->get()
            ->keyBy('month')
            ->map(function ($item) {
                return $item->total;
            })
            ->toArray(); // <-- Convert to array here

        // Fill in missing months with zero
        for ($i = 1; $i <= 12; $i++) {
            if (!isset($salesByMonth[$i])) {
                $salesByMonth[$i] = 0;
            }
        }

        // Sort by month
        ksort($this->salesByMonth);

        return view('admin.dashboard', compact(
            'totalSales',
            'totalOrders',
            'totalUsers',
            'totalComponents',
            'lowStockComponents',
            'recentOrders',
            'salesByMonth'
        ));
    }
}