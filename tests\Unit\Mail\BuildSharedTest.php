<?php

namespace Tests\Unit\Mail;

use App\Mail\BuildShared;
use App\Models\Build;
use App\Models\BuildComponent;
use App\Models\Component;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class BuildSharedTest extends TestCase
{
    use RefreshDatabase;

    public function test_build_shared_email_has_correct_subject()
    {
        $sharedBy = User::factory()->create(['name' => 'John Doe']);
        $build = Build::factory()->create([
            'user_id' => $sharedBy->id,
            'name' => 'Gaming Beast Build',
        ]);

        $mail = new BuildShared($build, $sharedBy, '<EMAIL>');
        $envelope = $mail->envelope();

        $this->assertEquals('<PERSON> shared a PC build with you: Gaming Beast Build', $envelope->subject);
    }

    public function test_build_shared_email_contains_build_details()
    {
        $sharedBy = User::factory()->create(['name' => '<PERSON> Doe']);
        $build = Build::factory()->create([
            'user_id' => $sharedBy->id,
            'name' => 'Gaming Beast Build',
            'description' => 'High-end gaming build',
            'total_price' => 2499.99,
            'share_token' => 'abc123',
        ]);

        $component = Component::factory()->create([
            'name' => 'AMD Ryzen 7 5800X',
            'price' => 299.99,
        ]);

        BuildComponent::factory()->create([
            'build_id' => $build->id,
            'component_id' => $component->id,
            'quantity' => 1,
            'price' => 299.99,
        ]);

        $mail = new BuildShared($build, $sharedBy, '<EMAIL>', 'Check out this awesome build!');
        $content = $mail->content();

        $this->assertEquals('emails.build-shared', $content->view);
        $this->assertArrayHasKey('build', $content->with);
        $this->assertArrayHasKey('sharedBy', $content->with);
        $this->assertArrayHasKey('components', $content->with);
        $this->assertArrayHasKey('message', $content->with);
        $this->assertArrayHasKey('buildUrl', $content->with);
        
        $this->assertEquals($build->id, $content->with['build']->id);
        $this->assertEquals($sharedBy->id, $content->with['sharedBy']->id);
        $this->assertEquals('Check out this awesome build!', $content->with['message']);
        $this->assertStringContainsString('abc123', $content->with['buildUrl']);
    }

    public function test_build_shared_email_handles_null_message()
    {
        $sharedBy = User::factory()->create();
        $build = Build::factory()->create([
            'user_id' => $sharedBy->id,
            'share_token' => 'test123',
        ]);

        $mail = new BuildShared($build, $sharedBy, '<EMAIL>');
        $content = $mail->content();

        $this->assertNull($content->with['message']);
    }

    public function test_build_shared_email_is_queueable()
    {
        $sharedBy = User::factory()->create();
        $build = Build::factory()->create(['user_id' => $sharedBy->id]);

        $mail = new BuildShared($build, $sharedBy, '<EMAIL>');

        $this->assertInstanceOf(\Illuminate\Contracts\Queue\ShouldQueue::class, $mail);
    }

    public function test_build_shared_email_serializes_models()
    {
        $sharedBy = User::factory()->create();
        $build = Build::factory()->create([
            'user_id' => $sharedBy->id,
            'share_token' => 'test123',
        ]);

        $mail = new BuildShared($build, $sharedBy, '<EMAIL>', 'Test message');
        
        // Test that the mail can be serialized and unserialized
        $serialized = serialize($mail);
        $unserialized = unserialize($serialized);
        
        $this->assertEquals($build->id, $unserialized->build->id);
        $this->assertEquals($sharedBy->id, $unserialized->sharedBy->id);
        $this->assertEquals('<EMAIL>', $unserialized->recipientEmail);
        $this->assertEquals('Test message', $unserialized->message);
    }
}