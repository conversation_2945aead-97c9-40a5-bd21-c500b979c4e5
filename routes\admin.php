<?php

use App\Http\Controllers\Admin\AdminAuthController;
use App\Http\Controllers\Admin\BlogCategoryController;
use App\Http\Controllers\Admin\BlogController;
use App\Http\Controllers\Admin\BlogTagController;
use App\Http\Controllers\Admin\CommentController;
use App\Http\Controllers\Admin\LandingPageController;
use App\Http\Controllers\Admin\MailConfigController;
use App\Http\Controllers\Admin\SettingController;
use App\Http\Controllers\Admin\UserController;
use App\Http\Controllers\Admin\AdminController;
use App\Http\Controllers\Admin\CacheController;
use App\Http\Controllers\Admin\GatewayController;
use App\Livewire\ComponentManager;

// Admin Routes
Route::prefix('admin')->name('admin.')->group(function () {
    // Route::prefix('admin')->name('admin.')->group(function () {
    // Admin authentication routes
    Route::middleware(['guest', \App\Http\Middleware\RedirectIfAdminAuthenticated::class])->group(function () {
        Route::get('login', [AdminAuthController::class, 'showLoginForm'])->name('login');
        Route::post('login', [AdminAuthController::class, 'login'])->name('login.post');
    });

    Route::middleware(['admin'])->group(function () {
        Route::post('logout', [AdminAuthController::class, 'logout'])->name('logout');


        Route::get('/dashboard', [\App\Http\Controllers\Admin\DashboardController::class, 'index'])->name('dashboard');


        // Payment Gateway Management - Requires payment admin permissions
        Route::middleware(['payment.admin'])->group(function () {
            Route::resource('gateways', GatewayController::class)->only(['index', 'show', 'update']);
            Route::post('/gateways/{gateway}/toggle', [GatewayController::class, 'toggle'])->name('gateways.toggle');
            Route::post('/gateways/{gateway}/switch-mode', [GatewayController::class, 'switchMode'])->name('gateways.switch-mode');
            Route::post('/gateways/{gateway}/test', [GatewayController::class, 'test'])->name('gateways.test');
            Route::get('/gateways-enabled', [GatewayController::class, 'enabled'])->name('gateways.enabled');
            
            // Transaction Management
            Route::get('/transactions', [\App\Http\Controllers\Admin\TransactionController::class, 'index'])->name('transactions.index');
            Route::get('/transactions/{transaction}', [\App\Http\Controllers\Admin\TransactionController::class, 'show'])->name('transactions.show');
            Route::post('/transactions/{transaction}/refund', [\App\Http\Controllers\Admin\TransactionController::class, 'refund'])->name('transactions.refund');
            Route::delete('/transactions/{transaction}', [\App\Http\Controllers\Admin\TransactionController::class, 'destroy'])->name('transactions.destroy');
            Route::get('/transactions/export', [\App\Http\Controllers\Admin\TransactionController::class, 'export'])->name('transactions.export');
            
            // Payment Dashboard API Endpoints
            Route::prefix('api')->name('api.')->group(function () {
                Route::get('/dashboard-metrics', [\App\Http\Controllers\Admin\StatsController::class, 'dashboardMetrics'])->name('dashboard-metrics');
                Route::get('/live-transactions', [\App\Http\Controllers\Admin\StatsController::class, 'liveTransactions'])->name('live-transactions');
                Route::get('/payment-stats', [\App\Http\Controllers\Admin\StatsController::class, 'paymentStats'])->name('payment-stats');
                Route::get('/recent-transactions', [\App\Http\Controllers\Admin\StatsController::class, 'recentTransactions'])->name('recent-transactions');
                Route::get('/payment-chart', [\App\Http\Controllers\Admin\StatsController::class, 'paymentChart'])->name('payment-chart');
                Route::post('/export-payment-report', [\App\Http\Controllers\Admin\StatsController::class, 'exportPaymentReport'])->name('export-payment-report');
            });
        });


        // Product routes
        Route::resource('products', \App\Http\Controllers\Admin\ProductController::class);
        Route::post('/products/{product}/stock', [\App\Http\Controllers\Admin\ProductController::class, 'updateStock'])->name('products.update-stock');

        // Component routes
        Route::get('/components', [\App\Http\Controllers\Admin\ComponentController::class, 'index'])->name('components.index');
        Route::get('/components/create', [\App\Http\Controllers\Admin\ComponentController::class, 'create'])->name('components.create');
        Route::post('/components', [\App\Http\Controllers\Admin\ComponentController::class, 'store'])->name('components.store');
        Route::get('/components/{component}/edit', [\App\Http\Controllers\Admin\ComponentController::class, 'edit'])->name('components.edit');
        Route::put('/components/{component}', [\App\Http\Controllers\Admin\ComponentController::class, 'update'])->name('components.update');
        Route::patch('/components/{component}', [\App\Http\Controllers\Admin\ComponentController::class, 'update'])->name('components.update');
        Route::delete('/components/{component}', [\App\Http\Controllers\Admin\ComponentController::class, 'destroy'])->name('components.destroy');
        Route::post('/components/{component}/upload-image', [\App\Http\Controllers\Admin\ComponentController::class, 'uploadImage'])->name('components.upload-image');
        Route::patch('/components/{component}/toggle-status', [\App\Http\Controllers\Admin\ComponentController::class, 'toggleStatus'])->name('components.toggle-status');
        Route::post('/components/bulk-action', [\App\Http\Controllers\Admin\ComponentController::class, 'bulkAction'])->name('components.bulk-action');
        Route::post('/components/bulk-price-update', [\App\Http\Controllers\Admin\ComponentController::class, 'bulkPriceUpdate'])->name('components.bulk-price-update');
        Route::get('/components/export', [\App\Http\Controllers\Admin\ComponentController::class, 'export'])->name('components.export');

        // Component Manager Livewire route
        // Route::get('/component-manager', ComponentManager::class)->name('component-manager');

        // Order routes
        Route::get('/orders', [\App\Http\Controllers\Admin\OrderController::class, 'index'])->name('orders.index');
        Route::get('/orders/export', [\App\Http\Controllers\Admin\OrderController::class, 'export'])->name('orders.export');
        Route::get('/orders/{order}', [\App\Http\Controllers\Admin\OrderController::class, 'show'])->name('orders.show');
        Route::put('/orders/{order}/status', [\App\Http\Controllers\Admin\OrderController::class, 'updateStatus'])->name('orders.update-status');
        Route::patch('/orders/{order}/status', [\App\Http\Controllers\Admin\OrderController::class, 'updateStatus'])->name('orders.update-status');
        Route::post('/orders/{order}/tracking', [\App\Http\Controllers\Admin\OrderController::class, 'addTracking'])->name('orders.add-tracking');
        Route::patch('/orders/{order}/add-tracking', [\App\Http\Controllers\Admin\OrderController::class, 'addTracking'])->name('orders.add-tracking');
        Route::post('/orders/bulk-update', [\App\Http\Controllers\Admin\OrderController::class, 'bulkUpdate'])->name('orders.bulk-update');
        Route::patch('/orders/{order}/cancel', [\App\Http\Controllers\Admin\OrderController::class, 'cancel'])->name('orders.cancel');
        Route::post('/orders/{order}/process-refund', [\App\Http\Controllers\Admin\OrderController::class, 'processRefund'])->name('orders.process-refund');

        // Inventory Management routes
        Route::get('/inventory', \App\Livewire\Admin\InventoryManagement::class)->name('inventory');
        Route::get('/inventory/low-stock', [\App\Http\Controllers\Admin\InventoryController::class, 'lowStock'])->name('inventory.low-stock');
        Route::post('/inventory/bulk-stock-update', [\App\Http\Controllers\Admin\InventoryController::class, 'bulkStockUpdate'])->name('inventory.bulk-stock-update');


        // User Management
        Route::resource('users', UserController::class);
        Route::patch('/users/{user}/update-role', [UserController::class, 'updateRole'])->name('users.update-role');
        Route::patch('/users/{user}/suspend', [UserController::class, 'suspend'])->name('users.suspend');
        Route::patch('/users/{user}/reactivate', [UserController::class, 'reactivate'])->name('users.reactivate');
        Route::get('/users/{user}/activity', [UserController::class, 'activity'])->name('users.activity');

        // Blog Management
        Route::prefix('blog')->name('blog.')->group(function () {
            Route::get('/', function () {
                return redirect()->route('admin.blog.posts.index');
            })->name('index');

            Route::resource('posts', BlogController::class)->parameters([
                'posts' => 'post'
            ])->except(['show']);

            // Blog Categories
            Route::resource('categories', BlogCategoryController::class)->except(['show']);
            Route::get('categories/api', [BlogCategoryController::class, 'apiIndex'])->name('categories.api');

            // Blog Tags
            Route::resource('tags', BlogTagController::class)->except(['show']);
            Route::get('tags/api', [BlogTagController::class, 'apiIndex'])->name('tags.api');

            // Blog Image Upload
            Route::post('upload-image', [BlogController::class, 'uploadImage'])->name('upload-image');
        });

        // Comments Management
        Route::resource('comments', CommentController::class)->except(['create', 'store', 'edit', 'update']);
        Route::patch('comments/{comment}/approve', [CommentController::class, 'approve'])->name('comments.approve');
        Route::patch('comments/{comment}/reject', [CommentController::class, 'reject'])->name('comments.reject');

        // Pages Management
        Route::resource('pages', \App\Http\Controllers\Admin\PageController::class);

        // Landing Page Management
        Route::prefix('landing-page')->name('landing-page.')->group(function () {
            Route::get('/', [LandingPageController::class, 'index'])->name('index');
            Route::get('/{section}/edit', [LandingPageController::class, 'edit'])->name('edit');
            Route::put('/{section}', [LandingPageController::class, 'update'])->name('update');
            Route::patch('/{section}/toggle', [LandingPageController::class, 'toggleActive'])->name('toggle-active');
            Route::post('/reorder', [LandingPageController::class, 'reorder'])->name('reorder');
        });
        // System Settings
        Route::prefix('settings')->name('settings.')->group(function () {
            Route::get('/', [SettingController::class, 'index'])->name('index');
            Route::put('/', [SettingController::class, 'update'])->name('update');
            Route::post('/maintenance', [SettingController::class, 'maintenanceMode'])->name('maintenance');
            Route::post('/login', [SettingController::class, 'toggleLogin'])->name('login');

            Route::post('/api', [AdminController::class, 'updateApiSettings'])->name('api.update');
            Route::post('/email', [AdminController::class, 'updateEmailSettings'])->name('email.update');
        });

        // Mail Configuration
        Route::prefix('mail-config')->name('mail-config.')->group(function () {
            Route::get('/', [MailConfigController::class, 'index'])->name('index');

            Route::post('/update', [MailConfigController::class, 'update'])->name('update');
            Route::post('/test-mail', [MailConfigController::class, 'sendTestEmail'])->name('test');
        });

        // Profile Management
        Route::prefix('profile')->name('profile.')->group(function () {
            Route::get('/', [\App\Http\Controllers\Admin\ProfileController::class, 'index'])->name('index');
            Route::put('/update', [\App\Http\Controllers\Admin\ProfileController::class, 'update'])->name('update');
        });

        // Analytics routes
        Route::prefix('analytics')->name('analytics.')->group(function () {
            Route::get('/sales', [\App\Http\Controllers\Admin\AnalyticsController::class, 'sales'])->name('sales');
            Route::get('/inventory', [\App\Http\Controllers\Admin\AnalyticsController::class, 'inventory'])->name('inventory');
            Route::get('/customers', [\App\Http\Controllers\Admin\AnalyticsController::class, 'customers'])->name('customers');
        });

        // Reports routes
        Route::prefix('reports')->name('reports.')->group(function () {
            Route::get('/orders', [\App\Http\Controllers\Admin\ReportController::class, 'orders'])->name('orders');
            Route::post('/custom', [\App\Http\Controllers\Admin\ReportController::class, 'custom'])->name('custom');
        });

        // Audit routes
        Route::prefix('audit')->name('audit.')->group(function () {
            Route::get('/', [\App\Http\Controllers\Admin\AuditController::class, 'index'])->name('index');
            Route::get('/component/{component}', [\App\Http\Controllers\Admin\AuditController::class, 'component'])->name('component');
            Route::get('/export', [\App\Http\Controllers\Admin\AuditController::class, 'export'])->name('export');
        });

        // System Maintenance
        Route::post('/maintenance/toggle', [AdminController::class, 'toggleMaintenance'])->name('maintenance.toggle');
        Route::post('/cache/clear', [AdminController::class, 'clearCache'])->name('cache.clear');
        Route::get('/clear-cache', [CacheController::class, 'clearCache'])->name('clear-cache');

    });

});
