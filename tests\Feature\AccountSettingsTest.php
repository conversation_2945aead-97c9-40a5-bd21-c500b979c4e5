<?php

namespace Tests\Feature;

use App\Livewire\User\AccountSettings;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Hash;
use Livewire\Livewire;
use Tests\TestCase;

class AccountSettingsTest extends TestCase
{
    use RefreshDatabase;

    private User $user;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->user = User::factory()->create([
            'name' => '<PERSON> Do<PERSON>',
            'email' => '<EMAIL>',
            'phone' => '************',
            'address' => '123 Main St',
            'preferences' => [
                'currency' => 'USD',
                'theme' => 'light',
                'items_per_page' => 20,
                'default_build_visibility' => 'private',
            ],
            'notification_settings' => [
                'order_confirmation' => true,
                'order_status_updates' => true,
                'build_shared' => true,
                'marketing_emails' => false,
                'price_alerts' => true,
                'stock_alerts' => true,
            ],
        ]);
    }

    public function test_component_loads_user_data_correctly()
    {
        Livewire::actingAs($this->user)
            ->test(AccountSettings::class)
            ->assertSet('name', '<PERSON>')
            ->assertSet('email', '<EMAIL>')
            ->assertSet('phone', '************')
            ->assertSet('address', '123 Main St')
            ->assertSet('currency', 'USD')
            ->assertSet('theme', 'light')
            ->assertSet('items_per_page', 20)
            ->assertSet('default_build_visibility', 'private')
            ->assertSet('order_confirmation', true)
            ->assertSet('marketing_emails', false);
    }

    public function test_user_can_update_profile()
    {
        Livewire::actingAs($this->user)
            ->test(AccountSettings::class)
            ->set('name', 'Jane Doe')
            ->set('email', '<EMAIL>')
            ->set('phone', '************')
            ->set('address', '456 Oak Ave')
            ->call('updateProfile')
            ->assertSet('successMessage', 'Profile updated successfully!');

        $this->user->refresh();
        $this->assertEquals('Jane Doe', $this->user->name);
        $this->assertEquals('<EMAIL>', $this->user->email);
        $this->assertEquals('************', $this->user->phone);
        $this->assertEquals('456 Oak Ave', $this->user->address);
    }

    public function test_profile_update_validation()
    {
        Livewire::actingAs($this->user)
            ->test(AccountSettings::class)
            ->set('name', '')
            ->set('email', 'invalid-email')
            ->call('updateProfile')
            ->assertHasErrors(['name', 'email']);
    }

    public function test_user_can_change_password()
    {
        $this->user->update(['password' => Hash::make('oldpassword')]);

        Livewire::actingAs($this->user)
            ->test(AccountSettings::class)
            ->set('current_password', 'oldpassword')
            ->set('new_password', 'newpassword123')
            ->set('new_password_confirmation', 'newpassword123')
            ->call('changePassword')
            ->assertSet('successMessage', 'Password changed successfully!')
            ->assertSet('current_password', '')
            ->assertSet('new_password', '')
            ->assertSet('new_password_confirmation', '');

        $this->user->refresh();
        $this->assertTrue(Hash::check('newpassword123', $this->user->password));
    }

    public function test_password_change_requires_correct_current_password()
    {
        $this->user->update(['password' => Hash::make('oldpassword')]);

        Livewire::actingAs($this->user)
            ->test(AccountSettings::class)
            ->set('current_password', 'wrongpassword')
            ->set('new_password', 'newpassword123')
            ->set('new_password_confirmation', 'newpassword123')
            ->call('changePassword')
            ->assertHasErrors(['current_password']);
    }

    public function test_password_change_validation()
    {
        Livewire::actingAs($this->user)
            ->test(AccountSettings::class)
            ->set('current_password', 'oldpassword')
            ->set('new_password', 'short')
            ->set('new_password_confirmation', 'different')
            ->call('changePassword')
            ->assertHasErrors(['new_password']);
    }

    public function test_user_can_update_notification_settings()
    {
        Livewire::actingAs($this->user)
            ->test(AccountSettings::class)
            ->set('order_confirmation', false)
            ->set('marketing_emails', true)
            ->set('price_alerts', false)
            ->call('updateNotificationSettings')
            ->assertSet('successMessage', 'Notification settings updated successfully!');

        $this->user->refresh();
        $settings = $this->user->notification_settings;
        $this->assertFalse($settings['order_confirmation']);
        $this->assertTrue($settings['marketing_emails']);
        $this->assertFalse($settings['price_alerts']);
    }

    public function test_user_can_update_preferences()
    {
        Livewire::actingAs($this->user)
            ->test(AccountSettings::class)
            ->set('currency', 'EUR')
            ->set('theme', 'dark')
            ->set('items_per_page', 50)
            ->set('default_build_visibility', 'public')
            ->call('updatePreferences')
            ->assertSet('successMessage', 'Preferences updated successfully!');

        $this->user->refresh();
        $preferences = $this->user->preferences;
        $this->assertEquals('EUR', $preferences['currency']);
        $this->assertEquals('dark', $preferences['theme']);
        $this->assertEquals(50, $preferences['items_per_page']);
        $this->assertEquals('public', $preferences['default_build_visibility']);
    }

    public function test_preferences_validation()
    {
        Livewire::actingAs($this->user)
            ->test(AccountSettings::class)
            ->set('currency', 'INVALID')
            ->set('theme', 'invalid')
            ->set('items_per_page', 5)
            ->set('default_build_visibility', 'invalid')
            ->call('updatePreferences')
            ->assertHasErrors(['currency', 'theme', 'items_per_page', 'default_build_visibility']);
    }

    public function test_tab_switching()
    {
        Livewire::actingAs($this->user)
            ->test(AccountSettings::class)
            ->assertSet('activeTab', 'profile')
            ->call('setActiveTab', 'security')
            ->assertSet('activeTab', 'security')
            ->assertSet('successMessage', '')
            ->call('setActiveTab', 'notifications')
            ->assertSet('activeTab', 'notifications')
            ->call('setActiveTab', 'preferences')
            ->assertSet('activeTab', 'preferences');
    }

    public function test_component_loads_defaults_for_new_user()
    {
        $newUser = User::factory()->create([
            'preferences' => null,
            'notification_settings' => null,
        ]);

        Livewire::actingAs($newUser)
            ->test(AccountSettings::class)
            ->assertSet('currency', 'USD')
            ->assertSet('theme', 'light')
            ->assertSet('items_per_page', 20)
            ->assertSet('default_build_visibility', 'private')
            ->assertSet('order_confirmation', true)
            ->assertSet('marketing_emails', false);
    }
}