<?php

namespace Tests\Unit\Jobs;

use App\Jobs\UpdateComponentPrices;
use App\Services\PriceTrackingService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Log;
use Tests\TestCase;

class UpdateComponentPricesTest extends TestCase
{
    use RefreshDatabase;

    public function test_job_calls_price_tracking_service()
    {
        $mockService = $this->createMock(PriceTrackingService::class);
        $mockService->expects($this->once())
            ->method('updateAllPrices')
            ->willReturn([
                'updated' => 5,
                'failed' => 1,
                'unchanged' => 10
            ]);

        $this->app->instance(PriceTrackingService::class, $mockService);

        Log::shouldReceive('info')->twice();

        $job = new UpdateComponentPrices();
        $job->handle($mockService);
    }

    public function test_job_logs_results()
    {
        $mockService = $this->createMock(PriceTrackingService::class);
        $mockService->method('updateAllPrices')
            ->willReturn([
                'updated' => 3,
                'failed' => 2,
                'unchanged' => 8
            ]);

        Log::shouldReceive('info')
            ->once()
            ->with('Starting automated price update job');

        Log::shouldReceive('info')
            ->once()
            ->with('Price update job completed', [
                'updated' => 3,
                'failed' => 2,
                'unchanged' => 8
            ]);

        $job = new UpdateComponentPrices();
        $job->handle($mockService);
    }

    public function test_job_handles_failure()
    {
        $exception = new \Exception('Service error');
        
        Log::shouldReceive('error')
            ->once()
            ->with('Price update job failed: Service error');

        $job = new UpdateComponentPrices();
        $job->failed($exception);
    }

    public function test_job_has_correct_configuration()
    {
        $job = new UpdateComponentPrices();

        $this->assertEquals(300, $job->timeout);
        $this->assertEquals(3, $job->tries);
    }
}