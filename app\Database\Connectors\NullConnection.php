<?php

namespace App\Database\Connectors;

use Illuminate\Database\Connection;
use PDO;
use PDOException;

class NullConnection extends Connection
{
    public function __construct()
    {
        if (!extension_loaded('pdo_sqlite')) {
            exit('Error: The "pdo_sqlite" extension is not enabled. Please enable it in your php.ini file.');
        }

        try {
            $pdo = new PDO('sqlite::memory:');
            parent::__construct($pdo);
        } catch (PDOException $e) {
            exit('Error: Failed to initialize SQLite connection. ' . $e->getMessage());
        }
    }

    public function select($query, $bindings = [], $useReadPdo = true)
    {
        return [];
    }

    public function table($table, $as = null)
    {
        return new class {
            public function get() { return collect([]); }
            public function first() { return null; }
            public function select() { return $this; }
            public function where() { return $this; }
            public function whereIn() { return $this; }
            public function whereNotIn() { return $this; }
            public function orderBy() { return $this; }
            public function limit() { return $this; }
            public function offset() { return $this; }
        };
    }

    public function getDatabaseName()
    {
        return ':memory:';
    }
}
