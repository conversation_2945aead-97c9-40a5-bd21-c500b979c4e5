<?php

namespace Tests\Performance;

use App\Models\Component;
use App\Models\ComponentCategory;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Tests\TestCase;

class LoadTestingTest extends TestCase
{
    use RefreshDatabase;

    protected array $users;
    protected array $components;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test users for load testing
        $this->users = User::factory()->count(100)->create();
        
        // Create components for testing
        $categories = ComponentCategory::factory()->count(7)->create();
        $this->components = Component::factory()->count(1000)->create([
            'category_id' => function() use ($categories) {
                return $categories->random()->id;
            },
            'is_active' => true,
            'stock' => rand(10, 100),
        ]);
    }

    /** @test */
    public function system_handles_concurrent_user_sessions()
    {
        $startTime = microtime(true);
        $sessionTimes = [];
        
        // Simulate 50 concurrent user sessions
        $processes = [];
        for ($i = 0; $i < 50; $i++) {
            $user = $this->users[array_rand($this->users)];
            $processes[] = $this->simulateUserSession($user);
        }
        
        // Wait for all sessions to complete
        foreach ($processes as $process) {
            $sessionTime = $process->wait();
            $sessionTimes[] = $sessionTime;
        }
        
        $endTime = microtime(true);
        $totalTime = ($endTime - $startTime) * 1000;
        $averageSessionTime = array_sum($sessionTimes) / count($sessionTimes);
        
        // All 50 concurrent sessions should complete under 30 seconds
        $this->assertLessThan(30000, $totalTime, 
            "50 concurrent sessions took {$totalTime}ms, which exceeds 30000ms limit");
        
        // Average session time should be reasonable
        $this->assertLessThan(5000, $averageSessionTime, 
            "Average session time was {$averageSessionTime}ms, which exceeds 5000ms limit");
    }

    /** @test */
    public function database_handles_concurrent_read_operations()
    {
        $startTime = microtime(true);
        $readTimes = [];
        
        // Simulate 100 concurrent read operations
        for ($i = 0; $i < 100; $i++) {
            $readStartTime = microtime(true);
            
            // Mix of different read operations
            switch ($i % 4) {
                case 0:
                    Component::with('category')->where('is_active', true)->limit(20)->get();
                    break;
                case 1:
                    Component::where('stock', '>', 0)->orderBy('price')->limit(50)->get();
                    break;
                case 2:
                    ComponentCategory::with('components')->get();
                    break;
                case 3:
                    Component::where('is_featured', true)->get();
                    break;
            }
            
            $readEndTime = microtime(true);
            $readTime = ($readEndTime - $readStartTime) * 1000;
            $readTimes[] = $readTime;
        }
        
        $endTime = microtime(true);
        $totalTime = ($endTime - $startTime) * 1000;
        $averageReadTime = array_sum($readTimes) / count($readTimes);
        
        // 100 read operations should complete under 5 seconds
        $this->assertLessThan(5000, $totalTime, 
            "100 concurrent reads took {$totalTime}ms, which exceeds 5000ms limit");
        
        // Average read time should be under 50ms
        $this->assertLessThan(50, $averageReadTime, 
            "Average read time was {$averageReadTime}ms, which exceeds 50ms limit");
    }

    /** @test */
    public function system_handles_concurrent_write_operations()
    {
        $startTime = microtime(true);
        $writeTimes = [];
        
        // Simulate 50 concurrent write operations
        for ($i = 0; $i < 50; $i++) {
            $writeStartTime = microtime(true);
            
            // Mix of different write operations
            switch ($i % 3) {
                case 0:
                    // Create new component
                    Component::factory()->create([
                        'category_id' => ComponentCategory::inRandomOrder()->first()->id,
                        'name' => "Load Test Component {$i}",
                        'price' => rand(50, 1000),
                        'stock' => rand(1, 50),
                    ]);
                    break;
                case 1:
                    // Update existing component
                    $component = $this->components[array_rand($this->components)];
                    $component->update(['stock' => $component->stock - 1]);
                    break;
                case 2:
                    // Create user order simulation
                    $user = $this->users[array_rand($this->users)];
                    DB::table('orders')->insert([
                        'user_id' => $user->id,
                        'order_number' => 'LOAD-' . $i,
                        'status' => 'pending',
                        'total_amount' => rand(100, 1000),
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]);
                    break;
            }
            
            $writeEndTime = microtime(true);
            $writeTime = ($writeEndTime - $writeStartTime) * 1000;
            $writeTimes[] = $writeTime;
        }
        
        $endTime = microtime(true);
        $totalTime = ($endTime - $startTime) * 1000;
        $averageWriteTime = array_sum($writeTimes) / count($writeTimes);
        
        // 50 write operations should complete under 10 seconds
        $this->assertLessThan(10000, $totalTime, 
            "50 concurrent writes took {$totalTime}ms, which exceeds 10000ms limit");
        
        // Average write time should be under 200ms
        $this->assertLessThan(200, $averageWriteTime, 
            "Average write time was {$averageWriteTime}ms, which exceeds 200ms limit");
    }

    /** @test */
    public function cache_system_handles_high_load()
    {
        $startTime = microtime(true);
        $cacheTimes = [];
        
        // Simulate 200 cache operations (mix of reads and writes)
        for ($i = 0; $i < 200; $i++) {
            $cacheStartTime = microtime(true);
            
            if ($i % 3 === 0) {
                // Cache write
                Cache::put("load_test_key_{$i}", "value_{$i}", 3600);
            } else {
                // Cache read
                $key = "load_test_key_" . rand(0, $i);
                Cache::get($key, 'default_value');
            }
            
            $cacheEndTime = microtime(true);
            $cacheTime = ($cacheEndTime - $cacheStartTime) * 1000;
            $cacheTimes[] = $cacheTime;
        }
        
        $endTime = microtime(true);
        $totalTime = ($endTime - $startTime) * 1000;
        $averageCacheTime = array_sum($cacheTimes) / count($cacheTimes);
        
        // 200 cache operations should complete under 2 seconds
        $this->assertLessThan(2000, $totalTime, 
            "200 cache operations took {$totalTime}ms, which exceeds 2000ms limit");
        
        // Average cache operation should be under 10ms
        $this->assertLessThan(10, $averageCacheTime, 
            "Average cache operation took {$averageCacheTime}ms, which exceeds 10ms limit");
    }

    /** @test */
    public function api_endpoints_handle_high_request_volume()
    {
        $endpoints = [
            ['GET', '/api/components'],
            ['GET', '/api/categories'],
            ['POST', '/api/cart/add'],
            ['GET', '/api/cart'],
            ['POST', '/api/builds'],
        ];
        
        $requestTimes = [];
        $startTime = microtime(true);
        
        // Make 500 API requests across different endpoints
        for ($i = 0; $i < 500; $i++) {
            $endpoint = $endpoints[array_rand($endpoints)];
            $user = $this->users[array_rand($this->users)];
            
            $requestStartTime = microtime(true);
            
            $response = $this->actingAs($user, 'sanctum');
            
            switch ($endpoint[0]) {
                case 'GET':
                    $response = $response->get($endpoint[1]);
                    break;
                case 'POST':
                    $data = $this->getTestDataForEndpoint($endpoint[1]);
                    $response = $response->post($endpoint[1], $data);
                    break;
            }
            
            $requestEndTime = microtime(true);
            $requestTime = ($requestEndTime - $requestStartTime) * 1000;
            $requestTimes[] = $requestTime;
            
            // Each API request should complete under 500ms
            $this->assertLessThan(500, $requestTime, 
                "API request to {$endpoint[1]} took {$requestTime}ms, which exceeds 500ms limit");
        }
        
        $endTime = microtime(true);
        $totalTime = ($endTime - $startTime) * 1000;
        $averageRequestTime = array_sum($requestTimes) / count($requestTimes);
        
        // 500 API requests should complete under 60 seconds
        $this->assertLessThan(60000, $totalTime, 
            "500 API requests took {$totalTime}ms, which exceeds 60000ms limit");
        
        // Average API response time should be under 200ms
        $this->assertLessThan(200, $averageRequestTime, 
            "Average API response time was {$averageRequestTime}ms, which exceeds 200ms limit");
    }

    /** @test */
    public function system_maintains_performance_under_memory_pressure()
    {
        $initialMemory = memory_get_usage();
        $performanceTimes = [];
        
        // Create memory pressure by loading large datasets
        for ($i = 0; $i < 100; $i++) {
            $startTime = microtime(true);
            
            // Load large dataset
            $components = Component::with(['category', 'reviews'])
                                  ->where('is_active', true)
                                  ->limit(100)
                                  ->get();
            
            // Perform operations on the dataset
            $totalPrice = $components->sum('price');
            $averagePrice = $components->avg('price');
            $groupedByCategory = $components->groupBy('category.name');
            
            $endTime = microtime(true);
            $operationTime = ($endTime - $startTime) * 1000;
            $performanceTimes[] = $operationTime;
            
            $currentMemory = memory_get_usage();
            $memoryIncrease = ($currentMemory - $initialMemory) / 1024 / 1024;
            
            // Performance should not degrade significantly under memory pressure
            $this->assertLessThan(1000, $operationTime, 
                "Operation {$i} took {$operationTime}ms under memory pressure");
            
            // Force garbage collection every 20 iterations
            if ($i % 20 === 0) {
                gc_collect_cycles();
            }
        }
        
        $averagePerformanceTime = array_sum($performanceTimes) / count($performanceTimes);
        
        // Average performance should remain reasonable
        $this->assertLessThan(500, $averagePerformanceTime, 
            "Average performance under memory pressure was {$averagePerformanceTime}ms");
    }

    /** @test */
    public function queue_system_handles_high_job_volume()
    {
        $startTime = microtime(true);
        
        // Dispatch 1000 jobs to test queue performance
        for ($i = 0; $i < 1000; $i++) {
            // Simulate different types of jobs
            switch ($i % 4) {
                case 0:
                    // Email job simulation
                    DB::table('jobs')->insert([
                        'queue' => 'emails',
                        'payload' => json_encode(['job' => 'SendOrderConfirmation', 'data' => ['order_id' => $i]]),
                        'attempts' => 0,
                        'created_at' => now()->timestamp,
                    ]);
                    break;
                case 1:
                    // Price update job simulation
                    DB::table('jobs')->insert([
                        'queue' => 'price-updates',
                        'payload' => json_encode(['job' => 'UpdateComponentPrice', 'data' => ['component_id' => $i]]),
                        'attempts' => 0,
                        'created_at' => now()->timestamp,
                    ]);
                    break;
                case 2:
                    // Inventory sync job simulation
                    DB::table('jobs')->insert([
                        'queue' => 'inventory',
                        'payload' => json_encode(['job' => 'SyncInventory', 'data' => ['batch_id' => $i]]),
                        'attempts' => 0,
                        'created_at' => now()->timestamp,
                    ]);
                    break;
                case 3:
                    // Report generation job simulation
                    DB::table('jobs')->insert([
                        'queue' => 'reports',
                        'payload' => json_encode(['job' => 'GenerateReport', 'data' => ['report_id' => $i]]),
                        'attempts' => 0,
                        'created_at' => now()->timestamp,
                    ]);
                    break;
            }
        }
        
        $endTime = microtime(true);
        $totalTime = ($endTime - $startTime) * 1000;
        
        // Dispatching 1000 jobs should complete under 5 seconds
        $this->assertLessThan(5000, $totalTime, 
            "Dispatching 1000 jobs took {$totalTime}ms, which exceeds 5000ms limit");
        
        // Verify jobs were queued
        $jobCount = DB::table('jobs')->count();
        $this->assertEquals(1000, $jobCount);
    }

    /** @test */
    public function system_recovers_gracefully_from_high_load_spikes()
    {
        $baselineTime = $this->measureBaselinePerformance();
        
        // Create high load spike
        $spikeStartTime = microtime(true);
        
        $processes = [];
        for ($i = 0; $i < 100; $i++) {
            $processes[] = $this->simulateHighLoadOperation();
        }
        
        // Wait for spike to complete
        foreach ($processes as $process) {
            $process->wait();
        }
        
        $spikeEndTime = microtime(true);
        $spikeTime = ($spikeEndTime - $spikeStartTime) * 1000;
        
        // Measure recovery time
        $recoveryStartTime = microtime(true);
        $recoveryTime = $this->measureBaselinePerformance();
        $recoveryEndTime = microtime(true);
        $totalRecoveryTime = ($recoveryEndTime - $recoveryStartTime) * 1000;
        
        // System should handle the spike
        $this->assertLessThan(30000, $spikeTime, 
            "High load spike took {$spikeTime}ms, which exceeds 30000ms limit");
        
        // System should recover to near baseline performance
        $performanceDegradation = (($recoveryTime - $baselineTime) / $baselineTime) * 100;
        $this->assertLessThan(50, $performanceDegradation, 
            "Performance degradation after spike was {$performanceDegradation}%, which exceeds 50% limit");
        
        // Recovery should be fast
        $this->assertLessThan(5000, $totalRecoveryTime, 
            "Recovery took {$totalRecoveryTime}ms, which exceeds 5000ms limit");
    }

    protected function simulateUserSession($user)
    {
        return new class($user, $this->components) {
            private $user;
            private $components;
            
            public function __construct($user, $components) {
                $this->user = $user;
                $this->components = $components;
            }
            
            public function wait() {
                $startTime = microtime(true);
                
                // Simulate typical user session
                // 1. Browse products
                for ($i = 0; $i < 5; $i++) {
                    usleep(rand(50000, 200000)); // 50-200ms
                }
                
                // 2. View product details
                for ($i = 0; $i < 3; $i++) {
                    usleep(rand(100000, 300000)); // 100-300ms
                }
                
                // 3. Add to cart
                usleep(rand(200000, 500000)); // 200-500ms
                
                // 4. Checkout process
                usleep(rand(1000000, 2000000)); // 1-2 seconds
                
                $endTime = microtime(true);
                return ($endTime - $startTime) * 1000;
            }
        };
    }

    protected function simulateHighLoadOperation()
    {
        return new class {
            public function wait() {
                // Simulate CPU-intensive operation
                $data = [];
                for ($i = 0; $i < 10000; $i++) {
                    $data[] = md5(rand());
                }
                
                // Simulate memory-intensive operation
                $largeArray = array_fill(0, 1000, str_repeat('x', 1000));
                
                usleep(rand(100000, 500000)); // 100-500ms
                
                return true;
            }
        };
    }

    protected function measureBaselinePerformance()
    {
        $startTime = microtime(true);
        
        // Standard operations
        Component::where('is_active', true)->limit(20)->get();
        ComponentCategory::with('components')->limit(5)->get();
        User::where('created_at', '>', now()->subDays(30))->count();
        
        $endTime = microtime(true);
        return ($endTime - $startTime) * 1000;
    }

    protected function getTestDataForEndpoint($endpoint)
    {
        switch ($endpoint) {
            case '/api/cart/add':
                return [
                    'component_id' => $this->components[array_rand($this->components)]->id,
                    'quantity' => rand(1, 3)
                ];
            case '/api/builds':
                return [
                    'name' => 'Load Test Build',
                    'components' => [
                        $this->components[array_rand($this->components)]->id,
                        $this->components[array_rand($this->components)]->id,
                    ]
                ];
            default:
                return [];
        }
    }
}