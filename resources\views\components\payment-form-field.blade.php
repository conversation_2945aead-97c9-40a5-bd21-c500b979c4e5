@props([
    'name' => '',
    'label' => '',
    'type' => 'text',
    'value' => '',
    'placeholder' => '',
    'required' => false,
    'min' => null,
    'max' => null,
    'step' => null,
    'options' => [], // For select fields
    'helpText' => '',
    'icon' => null,
    'prefix' => null,
    'suffix' => null
])

<div class="payment-form-field">
    @if($label)
        <x-label for="{{ $name }}" :value="$label . ($required ? ' *' : '')" />
    @endif
    
    <div class="relative mt-1">
        @if($prefix)
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <span class="text-gray-500 text-sm font-medium">{{ $prefix }}</span>
            </div>
        @endif
        
        @if($icon)
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                {!! $icon !!}
            </div>
        @endif
        
        @if($type === 'select')
            <select 
                id="{{ $name }}" 
                name="{{ $name }}"
                class="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 {{ $prefix ? 'pl-12' : ($icon ? 'pl-10' : '') }} {{ $suffix ? 'pr-12' : '' }} {{ $errors->has($name) ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : '' }}"
                {{ $required ? 'required' : '' }}
                {{ $attributes }}
            >
                @foreach($options as $optionValue => $optionLabel)
                    <option value="{{ $optionValue }}" {{ old($name, $value) == $optionValue ? 'selected' : '' }}>
                        {{ $optionLabel }}
                    </option>
                @endforeach
            </select>
        @elseif($type === 'textarea')
            <textarea 
                id="{{ $name }}" 
                name="{{ $name }}"
                placeholder="{{ $placeholder }}"
                class="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 {{ $prefix ? 'pl-12' : ($icon ? 'pl-10' : '') }} {{ $suffix ? 'pr-12' : '' }} {{ $errors->has($name) ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : '' }}"
                {{ $required ? 'required' : '' }}
                {{ $attributes }}
            >{{ old($name, $value) }}</textarea>
        @else
            <input 
                id="{{ $name }}" 
                name="{{ $name }}" 
                type="{{ $type }}"
                value="{{ old($name, $value) }}"
                placeholder="{{ $placeholder }}"
                class="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 {{ $prefix ? 'pl-12' : ($icon ? 'pl-10' : '') }} {{ $suffix ? 'pr-12' : '' }} {{ $errors->has($name) ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : '' }}"
                {{ $required ? 'required' : '' }}
                {{ $min ? "min={$min}" : '' }}
                {{ $max ? "max={$max}" : '' }}
                {{ $step ? "step={$step}" : '' }}
                {{ $attributes }}
            />
        @endif
        
        @if($suffix)
            <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                <span class="text-gray-500 text-sm">{{ $suffix }}</span>
            </div>
        @endif
    </div>
    
    @error($name)
        <x-input-error :messages="$message" class="mt-1" />
    @enderror
    
    @if($helpText && !$errors->has($name))
        <p class="mt-1 text-sm text-gray-600">{{ $helpText }}</p>
    @endif
</div>