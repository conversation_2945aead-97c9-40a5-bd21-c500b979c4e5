@props([
    'gateways' => [],
    'selectedGateway' => null,
    'error' => null
])

<div class="payment-gateway-selector">
    <div class="mb-4">
        <h3 class="text-lg font-semibold text-gray-900 mb-2">Select Payment Method</h3>
        <p class="text-sm text-gray-600">Choose your preferred payment gateway to continue</p>
    </div>
    
    @if($error)
        <div class="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg">
            <div class="flex items-center">
                <svg class="w-5 h-5 text-red-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <span class="text-red-800 text-sm">{{ $error }}</span>
            </div>
        </div>
    @endif
    
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        @forelse($gateways as $gateway)
            <x-payment-gateway-card
                :gateway="$gateway['key']"
                :name="$gateway['name']"
                :description="$gateway['description'] ?? ''"
                :logo="$gateway['logo'] ?? ''"
                :enabled="$gateway['enabled'] ?? true"
                :selected="$selectedGateway === $gateway['key']"
            />
        @empty
            <div class="col-span-full">
                <div class="text-center py-8 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
                    <svg class="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                    </svg>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">No Payment Gateways Available</h3>
                    <p class="text-gray-600">Please contact support or try again later.</p>
                </div>
            </div>
        @endforelse
    </div>
    
    @if(count($gateways) > 0)
        <div class="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <div class="flex items-start">
                <svg class="w-5 h-5 text-blue-400 mt-0.5 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <div class="text-sm text-blue-800">
                    <p class="font-medium mb-1">Secure Payment Processing</p>
                    <p>All payments are processed securely through encrypted connections. Your payment information is never stored on our servers.</p>
                </div>
            </div>
        </div>
    @endif
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const gatewayCards = document.querySelectorAll('.payment-gateway-card input[type="radio"]');
    
    gatewayCards.forEach(function(radio) {
        radio.addEventListener('change', function() {
            // Dispatch custom event when gateway selection changes
            const event = new CustomEvent('gatewayChanged', {
                detail: {
                    gateway: this.value,
                    name: this.closest('.payment-gateway-card').querySelector('.gateway-label h3').textContent
                }
            });
            document.dispatchEvent(event);
        });
    });
    
    // Handle keyboard navigation
    document.addEventListener('keydown', function(e) {
        if (e.key === 'ArrowDown' || e.key === 'ArrowUp') {
            const focusedElement = document.activeElement;
            if (focusedElement.type === 'radio' && focusedElement.name === 'gateway') {
                e.preventDefault();
                const radios = Array.from(gatewayCards).filter(radio => !radio.disabled);
                const currentIndex = radios.indexOf(focusedElement);
                
                let nextIndex;
                if (e.key === 'ArrowDown') {
                    nextIndex = (currentIndex + 1) % radios.length;
                } else {
                    nextIndex = (currentIndex - 1 + radios.length) % radios.length;
                }
                
                radios[nextIndex].focus();
                radios[nextIndex].checked = true;
                radios[nextIndex].dispatchEvent(new Event('change'));
            }
        }
    });
});
</script>

<style>
.payment-gateway-card input[type="radio"]:focus + .gateway-label {
    @apply ring-2 ring-indigo-500 ring-offset-2;
}

.payment-gateway-card input[type="radio"]:checked + .gateway-label .w-2 {
    @apply opacity-100;
}

@media (max-width: 640px) {
    .payment-gateway-selector .grid {
        @apply grid-cols-1;
    }
}
</style>