<?php

namespace Tests\Feature;

use App\Livewire\Admin\OrderManagement;
use App\Models\Component;
use App\Models\ComponentCategory;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Livewire\Livewire;
use Tests\TestCase;

class AdminOrderManagementTest extends TestCase
{
    use RefreshDatabase;

    protected User $adminUser;
    protected User $regularUser;
    protected User $customer;
    protected ComponentCategory $category;
    protected Component $component;

    protected function setUp(): void
    {
        parent::setUp();

        // Create admin user
        $this->adminUser = User::factory()->create([
            'role' => 'admin',
            'email' => '<EMAIL>',
        ]);

        // Create regular user
        $this->regularUser = User::factory()->create([
            'role' => 'user',
            'email' => '<EMAIL>',
        ]);

        // Create customer
        $this->customer = User::factory()->create([
            'role' => 'user',
            'email' => '<EMAIL>',
            'name' => 'John Customer',
        ]);

        // Create component category and component
        $this->category = ComponentCategory::factory()->create([
            'name' => 'CPU',
            'slug' => 'cpu',
        ]);

        $this->component = Component::factory()->create([
            'name' => 'Intel Core i7-12700K',
            'category_id' => $this->category->id,
            'price' => 399.99,
        ]);
    }

    /** @test */
    public function admin_can_access_order_management()
    {
        $this->actingAs($this->adminUser);

        $response = $this->get(route('admin.orders.index'));

        $response->assertStatus(200);
        $response->assertSeeLivewire('admin.order-management');
    }

    /** @test */
    public function regular_user_cannot_access_order_management()
    {
        $this->actingAs($this->regularUser);

        $response = $this->get(route('admin.orders.index'));

        $response->assertStatus(403);
    }

    /** @test */
    public function guest_cannot_access_order_management()
    {
        $response = $this->get(route('admin.orders.index'));

        $response->assertRedirect(route('admin.login'));
    }

    /** @test */
    public function order_management_displays_orders_correctly()
    {
        $this->actingAs($this->adminUser);

        $order = Order::factory()->create([
            'user_id' => $this->customer->id,
            'order_number' => 'PCB20250726001',
            'status' => 'pending',
            'total' => 399.99,
        ]);

        OrderItem::factory()->create([
            'order_id' => $order->id,
            'component_id' => $this->component->id,
            'quantity' => 1,
            'price' => 399.99,
            'name' => $this->component->name,
        ]);

        Livewire::test(OrderManagement::class)
            ->assertSee('PCB20250726001')
            ->assertSee('John Customer')
            ->assertSee('<EMAIL>')
            ->assertSee('$399.99')
            ->assertSee('Pending');
    }

    /** @test */
    public function orders_can_be_searched_by_order_number()
    {
        $this->actingAs($this->adminUser);

        $order1 = Order::factory()->create([
            'user_id' => $this->customer->id,
            'order_number' => 'PCB20250726001',
            'status' => 'pending',
            'total' => 399.99,
        ]);

        $order2 = Order::factory()->create([
            'user_id' => $this->customer->id,
            'order_number' => 'PCB20250726002',
            'status' => 'completed',
            'total' => 599.99,
        ]);

        Livewire::test(OrderManagement::class)
            ->set('search', 'PCB20250726001')
            ->assertSee('PCB20250726001')
            ->assertDontSee('PCB20250726002');
    }

    /** @test */
    public function orders_can_be_searched_by_customer_name()
    {
        $this->actingAs($this->adminUser);

        $customer2 = User::factory()->create([
            'name' => 'Jane Smith',
            'email' => '<EMAIL>',
        ]);

        $order1 = Order::factory()->create([
            'user_id' => $this->customer->id,
            'order_number' => 'PCB20250726001',
        ]);

        $order2 = Order::factory()->create([
            'user_id' => $customer2->id,
            'order_number' => 'PCB20250726002',
        ]);

        Livewire::test(OrderManagement::class)
            ->set('search', 'John Customer')
            ->assertSee('PCB20250726001')
            ->assertDontSee('PCB20250726002');
    }

    /** @test */
    public function orders_can_be_filtered_by_status()
    {
        $this->actingAs($this->adminUser);

        $pendingOrder = Order::factory()->create([
            'user_id' => $this->customer->id,
            'order_number' => 'PCB20250726001',
            'status' => 'pending',
        ]);

        $completedOrder = Order::factory()->create([
            'user_id' => $this->customer->id,
            'order_number' => 'PCB20250726002',
            'status' => 'completed',
        ]);

        Livewire::test(OrderManagement::class)
            ->set('statusFilter', 'pending')
            ->assertSee('PCB20250726001')
            ->assertDontSee('PCB20250726002');
    }

    /** @test */
    public function orders_can_be_filtered_by_date()
    {
        $this->actingAs($this->adminUser);

        $todayOrder = Order::factory()->create([
            'user_id' => $this->customer->id,
            'order_number' => 'PCB20250726001',
            'created_at' => now(),
        ]);

        $yesterdayOrder = Order::factory()->create([
            'user_id' => $this->customer->id,
            'order_number' => 'PCB20250726002',
            'created_at' => now()->subDay(),
        ]);

        Livewire::test(OrderManagement::class)
            ->set('dateFilter', 'today')
            ->assertSee('PCB20250726001')
            ->assertDontSee('PCB20250726002');
    }

    /** @test */
    public function orders_can_be_sorted()
    {
        $this->actingAs($this->adminUser);

        $order1 = Order::factory()->create([
            'user_id' => $this->customer->id,
            'order_number' => 'PCB20250726001',
            'total' => 100.00,
            'created_at' => now()->subHour(),
        ]);

        $order2 = Order::factory()->create([
            'user_id' => $this->customer->id,
            'order_number' => 'PCB20250726002',
            'total' => 200.00,
            'created_at' => now(),
        ]);

        $livewire = Livewire::test(OrderManagement::class)
            ->call('sortBy', 'total');

        $this->assertEquals('total', $livewire->get('sortField'));
        $this->assertEquals('asc', $livewire->get('sortDirection'));
    }

    /** @test */
    public function admin_can_view_order_details()
    {
        $this->actingAs($this->adminUser);

        $order = Order::factory()->create([
            'user_id' => $this->customer->id,
            'order_number' => 'PCB20250726001',
            'status' => 'pending',
            'total' => 399.99,
            'shipping_address' => '123 Main St',
            'shipping_city' => 'Anytown',
            'shipping_state' => 'CA',
            'shipping_zipcode' => '12345',
            'shipping_country' => 'USA',
        ]);

        OrderItem::factory()->create([
            'order_id' => $order->id,
            'component_id' => $this->component->id,
            'quantity' => 1,
            'price' => 399.99,
            'name' => $this->component->name,
        ]);

        Livewire::test(OrderManagement::class)
            ->call('viewOrder', $order->id)
            ->assertSet('showingOrderDetails', true)
            ->assertSet('selectedOrder.id', $order->id)
            ->assertSee('PCB20250726001')
            ->assertSee('John Customer')
            ->assertSee('123 Main St')
            ->assertSee('Intel Core i7-12700K');
    }

    /** @test */
    public function admin_can_update_order_status()
    {
        $this->actingAs($this->adminUser);

        $order = Order::factory()->create([
            'user_id' => $this->customer->id,
            'status' => 'pending',
        ]);

        Livewire::test(OrderManagement::class)
            ->call('viewOrder', $order->id)
            ->set('newStatus', 'processing')
            ->call('updateOrderStatus');

        $this->assertEquals('processing', $order->fresh()->status);
    }

    /** @test */
    public function admin_can_update_tracking_information()
    {
        $this->actingAs($this->adminUser);

        $order = Order::factory()->create([
            'user_id' => $this->customer->id,
        ]);

        Livewire::test(OrderManagement::class)
            ->call('viewOrder', $order->id)
            ->set('trackingNumber', 'TRK123456789')
            ->set('trackingCarrier', 'UPS')
            ->set('trackingUrl', 'https://ups.com/track/TRK123456789')
            ->call('updateTracking');

        $order->refresh();
        $this->assertEquals('TRK123456789', $order->tracking_number);
        $this->assertEquals('UPS', $order->tracking_carrier);
        $this->assertEquals('https://ups.com/track/TRK123456789', $order->tracking_url);
    }

    /** @test */
    public function admin_can_update_order_notes()
    {
        $this->actingAs($this->adminUser);

        $order = Order::factory()->create([
            'user_id' => $this->customer->id,
        ]);

        Livewire::test(OrderManagement::class)
            ->call('viewOrder', $order->id)
            ->set('orderNote', 'Customer requested expedited shipping')
            ->call('updateNote');

        $this->assertEquals('Customer requested expedited shipping', $order->fresh()->notes);
    }

    /** @test */
    public function order_status_validation_works()
    {
        $this->actingAs($this->adminUser);

        $order = Order::factory()->create([
            'user_id' => $this->customer->id,
        ]);

        Livewire::test(OrderManagement::class)
            ->call('viewOrder', $order->id)
            ->set('newStatus', 'invalid_status')
            ->call('updateOrderStatus')
            ->assertHasErrors(['newStatus']);
    }

    /** @test */
    public function tracking_url_validation_works()
    {
        $this->actingAs($this->adminUser);

        $order = Order::factory()->create([
            'user_id' => $this->customer->id,
        ]);

        Livewire::test(OrderManagement::class)
            ->call('viewOrder', $order->id)
            ->set('trackingUrl', 'not-a-valid-url')
            ->call('updateTracking')
            ->assertHasErrors(['trackingUrl']);
    }

    /** @test */
    public function admin_can_close_order_details()
    {
        $this->actingAs($this->adminUser);

        $order = Order::factory()->create([
            'user_id' => $this->customer->id,
        ]);

        Livewire::test(OrderManagement::class)
            ->call('viewOrder', $order->id)
            ->assertSet('showingOrderDetails', true)
            ->call('closeOrderDetails')
            ->assertSet('showingOrderDetails', false)
            ->assertSet('selectedOrder', null);
    }

    /** @test */
    public function order_items_display_correctly_in_details()
    {
        $this->actingAs($this->adminUser);

        $order = Order::factory()->create([
            'user_id' => $this->customer->id,
            'total' => 799.98,
        ]);

        // Create multiple order items
        OrderItem::factory()->create([
            'order_id' => $order->id,
            'component_id' => $this->component->id,
            'quantity' => 2,
            'price' => 399.99,
            'name' => $this->component->name,
        ]);

        Livewire::test(OrderManagement::class)
            ->call('viewOrder', $order->id)
            ->assertSee('Intel Core i7-12700K')
            ->assertSee('$399.99')
            ->assertSee('2')
            ->assertSee('$799.98');
    }

    /** @test */
    public function pagination_works_for_orders()
    {
        $this->actingAs($this->adminUser);

        // Create more orders than the default per page
        Order::factory()->count(20)->create([
            'user_id' => $this->customer->id,
        ]);

        Livewire::test(OrderManagement::class)
            ->set('perPage', 15)
            ->assertSee('Next');
    }

    /** @test */
    public function date_filter_this_week_works()
    {
        $this->actingAs($this->adminUser);

        $thisWeekOrder = Order::factory()->create([
            'user_id' => $this->customer->id,
            'order_number' => 'PCB20250726001',
            'created_at' => now()->startOfWeek()->addDay(),
        ]);

        $lastWeekOrder = Order::factory()->create([
            'user_id' => $this->customer->id,
            'order_number' => 'PCB20250726002',
            'created_at' => now()->subWeek(),
        ]);

        Livewire::test(OrderManagement::class)
            ->set('dateFilter', 'this_week')
            ->assertSee('PCB20250726001')
            ->assertDontSee('PCB20250726002');
    }

    /** @test */
    public function date_filter_this_month_works()
    {
        $this->actingAs($this->adminUser);

        $thisMonthOrder = Order::factory()->create([
            'user_id' => $this->customer->id,
            'order_number' => 'PCB20250726001',
            'created_at' => now()->startOfMonth()->addDay(),
        ]);

        $lastMonthOrder = Order::factory()->create([
            'user_id' => $this->customer->id,
            'order_number' => 'PCB20250726002',
            'created_at' => now()->subMonth(),
        ]);

        Livewire::test(OrderManagement::class)
            ->set('dateFilter', 'this_month')
            ->assertSee('PCB20250726001')
            ->assertDontSee('PCB20250726002');
    }

    /** @test */
    public function order_status_badges_display_correctly()
    {
        $this->actingAs($this->adminUser);

        $pendingOrder = Order::factory()->create([
            'user_id' => $this->customer->id,
            'status' => 'pending',
        ]);

        $processingOrder = Order::factory()->create([
            'user_id' => $this->customer->id,
            'status' => 'processing',
        ]);

        $completedOrder = Order::factory()->create([
            'user_id' => $this->customer->id,
            'status' => 'completed',
        ]);

        $cancelledOrder = Order::factory()->create([
            'user_id' => $this->customer->id,
            'status' => 'canceled',
        ]);

        $refundedOrder = Order::factory()->create([
            'user_id' => $this->customer->id,
            'status' => 'refunded',
        ]);

        $livewire = Livewire::test(OrderManagement::class);

        // Check that different status badges are displayed
        $livewire->assertSeeHtml('bg-yellow-100 text-yellow-800'); // pending
        $livewire->assertSeeHtml('bg-blue-100 text-blue-800'); // processing
        $livewire->assertSeeHtml('bg-green-100 text-green-800'); // completed
        $livewire->assertSeeHtml('bg-red-100 text-red-800'); // cancelled
        $livewire->assertSeeHtml('bg-purple-100 text-purple-800'); // refunded
    }
}