<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Component;
use Illuminate\Http\Request;

class InventoryController extends Controller
{
    /**
     * Show components with low stock.
     */
    public function lowStock()
    {
        $lowStockComponents = Component::where('stock', '<=', 5)
            ->where('is_active', true)
            ->with('category')
            ->orderBy('stock', 'asc')
            ->get();

        return view('admin.inventory.low-stock', compact('lowStockComponents'));
    }

    /**
     * Bulk update stock levels.
     */
    public function bulkStockUpdate(Request $request)
    {
        $request->validate([
            'updates' => 'required|array',
            'updates.*.component_id' => 'required|exists:components,id',
            'updates.*.stock' => 'required|integer|min:0'
        ]);

        foreach ($request->updates as $update) {
            Component::where('id', $update['component_id'])
                ->update(['stock' => $update['stock']]);
        }

        return response()->json(['success' => true, 'message' => 'Stock levels updated successfully']);
    }
}