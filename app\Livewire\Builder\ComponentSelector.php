<?php

namespace App\Livewire\Builder;

use App\Models\Component;
use App\Models\ComponentCategory;
use App\Services\BuilderService;
use Livewire\Component as LivewireComponent;

class ComponentSelector extends LivewireComponent
{
    public $categorySlug;
    public $category;
    public $components = [];
    public $selectedComponentId = null;
    
    // Filters
    public $search = '';
    public $brands = [];
    public $selectedBrands = [];
    public $priceMin;
    public $priceMax;
    public $inStockOnly = false;
    public $sortBy = 'price';
    public $sortDirection = 'asc';
    
    // For compatibility checking
    public $buildComponents = [];
    public $compatibilityIssues = [];
    
    protected $builderService;
    
    protected $queryString = [
        'search' => ['except' => ''],
        'selectedBrands' => ['except' => []],
        'priceMin' => ['except' => ''],
        'priceMax' => ['except' => ''],
        'inStockOnly' => ['except' => false],
        'sortBy' => ['except' => 'price'],
        'sortDirection' => ['except' => 'asc'],
    ];
    
    public function boot(BuilderService $builderService)
    {
        $this->builderService = $builderService;
    }
    
    public function mount($categorySlug, $buildComponents = [], $selectedComponentId = null)
    {
        $this->categorySlug = $categorySlug;
        $this->buildComponents = $buildComponents;
        $this->selectedComponentId = $selectedComponentId;
        
        // Load category
        $this->category = ComponentCategory::where('slug', $categorySlug)->firstOrFail();
        
        // Load components
        $this->loadComponents();
        
        // Extract available brands for filtering
        $this->extractBrands();
        
        // Set price range
        $this->setPriceRange();
    }
    
    public function updatedSearch()
    {
        $this->loadComponents();
    }
    
    public function updatedSelectedBrands()
    {
        $this->loadComponents();
    }
    
    public function updatedPriceMin()
    {
        $this->loadComponents();
    }
    
    public function updatedPriceMax()
    {
        $this->loadComponents();
    }
    
    public function updatedInStockOnly()
    {
        $this->loadComponents();
    }
    
    public function updatedSortBy()
    {
        $this->loadComponents();
    }
    
    public function updatedSortDirection()
    {
        $this->loadComponents();
    }
    
    public function loadComponents()
    {
        $query = Component::where('category_id', $this->category->id)
            ->where('is_active', true);
        
        // Apply search filter
        if (!empty($this->search)) {
            $query->where(function ($q) {
                $q->where('name', 'like', "%{$this->search}%")
                  ->orWhere('brand', 'like', "%{$this->search}%")
                  ->orWhere('model', 'like', "%{$this->search}%")
                  ->orWhere('description', 'like', "%{$this->search}%");
            });
        }
        
        // Apply brand filter
        if (!empty($this->selectedBrands)) {
            $query->whereIn('brand', $this->selectedBrands);
        }
        
        // Apply price range filter
        if (!empty($this->priceMin)) {
            $query->where('price', '>=', $this->priceMin);
        }
        
        if (!empty($this->priceMax)) {
            $query->where('price', '<=', $this->priceMax);
        }
        
        // Apply stock filter
        if ($this->inStockOnly) {
            $query->where('stock', '>', 0);
        }
        
        // Apply sorting
        $query->orderBy($this->sortBy, $this->sortDirection);
        
        $this->components = $query->get();
        
        // Check compatibility for each component
        $this->checkCompatibility();
    }
    
    public function extractBrands()
    {
        $this->brands = Component::where('category_id', $this->category->id)
            ->where('is_active', true)
            ->distinct()
            ->pluck('brand')
            ->filter()
            ->values()
            ->toArray();
    }
    
    public function setPriceRange()
    {
        $priceRange = Component::where('category_id', $this->category->id)
            ->where('is_active', true)
            ->selectRaw('MIN(price) as min_price, MAX(price) as max_price')
            ->first();
        
        if ($priceRange) {
            $this->priceMin = $this->priceMin ?: floor($priceRange->min_price);
            $this->priceMax = $this->priceMax ?: ceil($priceRange->max_price);
        }
    }
    
    public function checkCompatibility()
    {
        // TODO: Implement compatibility checking logic
        // This would check each component against the current build components
        // For now, we'll just mark all as compatible
        
        foreach ($this->components as $component) {
            $component->is_compatible = true;
            $component->compatibility_issues = [];
        }
    }
    
    public function selectComponent($componentId)
    {
        $this->selectedComponentId = $componentId;
        $this->dispatch('componentSelected', $componentId, $this->categorySlug);
    }
    
    public function sortComponents($field)
    {
        if ($this->sortBy === $field) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortBy = $field;
            $this->sortDirection = 'asc';
        }
        
        $this->loadComponents();
    }
    
    public function render()
    {
        return view('livewire.builder.component-selector');
    }
}