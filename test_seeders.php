<?php

/**
 * Test Seeders Script
 * 
 * This script tests individual seeders to ensure they work correctly
 * without the random selection errors.
 */

require_once 'vendor/autoload.php';

use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\DB;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "🧪 Testing Individual Seeders...\n";
echo "================================\n\n";

// Test seeders that might have issues
$testSeeders = [
    'UserSeeder' => 'Creates users first',
    'ComponentCategorySeeder' => 'Creates component categories',
    'ComponentSeeder' => 'Creates components',
    'CartSeeder' => 'Creates carts (potential issue)',
    'OrderSeeder' => 'Creates orders (potential issue)',
    'TransactionSeeder' => 'Creates transactions (potential issue)',
    'CouponSeeder' => 'Creates coupons',
    'CouponUsageSeeder' => 'Creates coupon usage (potential issue)',
];

foreach ($testSeeders as $seederClass => $description) {
    echo "Testing {$seederClass}... ";
    
    try {
        $startTime = microtime(true);
        Artisan::call('db:seed', ['--class' => "Database\\Seeders\\{$seederClass}"]);
        $duration = round((microtime(true) - $startTime) * 1000);
        
        echo "✅ ({$duration}ms)\n";
        echo "   └─ {$description}\n";
        
    } catch (Exception $e) {
        echo "❌ FAILED\n";
        echo "   └─ Error: " . $e->getMessage() . "\n";
        echo "   └─ File: " . $e->getFile() . ":" . $e->getLine() . "\n\n";
        
        // Show more context for debugging
        if (strpos($e->getMessage(), 'items available') !== false) {
            echo "   └─ This appears to be a random selection issue\n";
        }
        
        break; // Stop on first error for debugging
    }
}

echo "\n📊 Current Database Counts:\n";
echo "---------------------------\n";

try {
    $tables = [
        'users' => 'Users',
        'components' => 'Components',
        'component_categories' => 'Component Categories',
        'carts' => 'Carts',
        'cart_items' => 'Cart Items',
        'orders' => 'Orders',
        'order_items' => 'Order Items',
        'transactions' => 'Transactions',
        'coupons' => 'Coupons',
        'coupon_usages' => 'Coupon Usages',
    ];
    
    foreach ($tables as $table => $label) {
        try {
            $count = DB::table($table)->count();
            echo sprintf("%-20s: %d\n", $label, $count);
        } catch (Exception $e) {
            echo sprintf("%-20s: Table not found\n", $label);
        }
    }
    
} catch (Exception $e) {
    echo "Could not retrieve counts: " . $e->getMessage() . "\n";
}

echo "\n✨ Test completed!\n";