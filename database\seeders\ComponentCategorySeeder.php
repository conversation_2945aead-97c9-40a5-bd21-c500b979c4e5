<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\ComponentCategory;

class ComponentCategorySeeder extends Seeder
{
    public function run()
    {
        $categories = [
            ['name' => 'CPU', 'slug' => 'cpu', 'description' => 'Processor', 'icon' => 'cpu', 'display_order' => 1, 'is_required' => true],
            ['name' => 'Motherboard', 'slug' => 'motherboard', 'description' => 'Mainboard', 'icon' => 'motherboard', 'display_order' => 2, 'is_required' => true],
            ['name' => 'RAM', 'slug' => 'ram', 'description' => 'Memory', 'icon' => 'ram', 'display_order' => 3, 'is_required' => true],
            ['name' => 'GPU', 'slug' => 'gpu', 'description' => 'Graphics Card', 'icon' => 'gpu', 'display_order' => 4, 'is_required' => false],
        ];

        foreach ($categories as $cat) {
            ComponentCategory::create($cat);
        }
    }
}
