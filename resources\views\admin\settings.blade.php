@extends('admin.layouts.admin')

@section('title', 'Admin Settings')
@section('page-title', 'Settings')

@section('content')
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
    <!-- General Settings -->
    <div class="bg-white shadow-md rounded-lg overflow-hidden">
        <div class="px-6 py-4 border-b">
            <h2 class="text-xl font-semibold text-gray-800">General Settings</h2>
        </div>

        <form action="{{ route('admin.settings.update') }}" method="POST" class="p-6">
            @method('PUT')
            @csrf
            <div class="space-y-4">
                <div>
                    <label for="site_name" class="block text-sm font-medium text-gray-700 mb-1">Site Name</label>
                    <input type="text" name="site_name" id="site_name" value="{{ old('site_name', $settings->site_name ?? 'Pincodes App') }}"
                        class="w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50 @error('site_name') border-red-500 @enderror">
                    @error('site_name')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="site_description" class="block text-sm font-medium text-gray-700 mb-1">Site Description</label>
                    <textarea name="site_description" id="site_description" rows="3"
                        class="w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50 @error('site_description') border-red-500 @enderror">{{ old('site_description', $settings->site_description ?? 'Find pincodes for locations across India') }}</textarea>
                    @error('site_description')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="contact_email" class="block text-sm font-medium text-gray-700 mb-1">Contact Email</label>
                    <input type="email" name="contact_email" id="contact_email" value="{{ old('contact_email', $settings->contact_email ?? '<EMAIL>') }}"
                        class="w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50 @error('contact_email') border-red-500 @enderror">
                    @error('contact_email')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="items_per_page" class="block text-sm font-medium text-gray-700 mb-1">Items Per Page</label>
                    <select name="items_per_page" id="items_per_page"
                        class="w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50 @error('items_per_page') border-red-500 @enderror">
                        @foreach([10, 25, 50, 100] as $option)
                            <option value="{{ $option }}" {{ old('items_per_page', $settings->items_per_page ?? 25) == $option ? 'selected' : '' }}>{{ $option }}</option>
                        @endforeach
                    </select>
                    @error('items_per_page')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>

            <div class="mt-6">
                <button type="submit" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium">
                    Save General Settings
                </button>
            </div>
        </form>
    </div>

    <!-- API Settings -->
    <div class="bg-white shadow-md rounded-lg overflow-hidden">
        <div class="px-6 py-4 border-b">
            <h2 class="text-xl font-semibold text-gray-800">API Settings</h2>
        </div>

        <form action="{{ route('admin.settings.update') }}" method="POST" class="p-6">
            @method('PUT')
            @csrf
            <div class="space-y-4">
                <div>
                    <label for="api_enabled" class="block text-sm font-medium text-gray-700 mb-1">API Status</label>
                    <select name="api_enabled" id="api_enabled"
                        class="w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50 @error('api_enabled') border-red-500 @enderror">
                        <option value="1" {{ old('api_enabled', $settings->api_enabled ?? '1') == '1' ? 'selected' : '' }}>Enabled</option>
                        <option value="0" {{ old('api_enabled', $settings->api_enabled ?? '1') == '0' ? 'selected' : '' }}>Disabled</option>
                    </select>
                    @error('api_enabled')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="api_key" class="block text-sm font-medium text-gray-700 mb-1">API Key</label>
                    <div class="flex">
                        <input type="text" id="api_key" value="{{ $settings->api_key ?? 'Generate a new API key' }}" readonly
                            class="w-full border-gray-300 rounded-l-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50">
                        <button type="button" id="copyApiKey" class="bg-gray-200 hover:bg-gray-300 text-gray-700 px-4 py-2 border border-l-0 border-gray-300 rounded-r-md text-sm font-medium">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3"></path>
                            </svg>
                        </button>
                    </div>
                </div>

                <div>
                    <label for="rate_limit" class="block text-sm font-medium text-gray-700 mb-1">Rate Limit (requests per minute)</label>
                    <input type="number" name="rate_limit" id="rate_limit" min="1" max="1000" value="{{ old('rate_limit', $settings->rate_limit ?? 60) }}"
                        class="w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50 @error('rate_limit') border-red-500 @enderror">
                    @error('rate_limit')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <button type="button" id="generateApiKey" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-md text-sm font-medium">
                        Generate New API Key
                    </button>
                </div>
            </div>

            <div class="mt-6">
                <button type="submit" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium">
                    Save API Settings
                </button>
            </div>
        </form>
    </div>

    <!-- Email Settings -->
    <div class="bg-white shadow-md rounded-lg overflow-hidden">
        <div class="px-6 py-4 border-b">
            <h2 class="text-xl font-semibold text-gray-800">Email Settings</h2>
        </div>

        <form action="{{ route('admin.settings.update') }}" method="POST" class="p-6">
            @method('PUT')
            @csrf
            <div class="space-y-4">
                <div>
                    <label for="mail_driver" class="block text-sm font-medium text-gray-700 mb-1">Mail Driver</label>
                    <select name="mail_driver" id="mail_driver"
                        class="w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50 @error('mail_driver') border-red-500 @enderror">
                        <option value="smtp" {{ old('mail_driver', $settings->mail_driver ?? 'smtp') == 'smtp' ? 'selected' : '' }}>SMTP</option>
                        <option value="sendmail" {{ old('mail_driver', $settings->mail_driver ?? 'smtp') == 'sendmail' ? 'selected' : '' }}>Sendmail</option>
                        <option value="mailgun" {{ old('mail_driver', $settings->mail_driver ?? 'smtp') == 'mailgun' ? 'selected' : '' }}>Mailgun</option>
                    </select>
                    @error('mail_driver')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="mail_host" class="block text-sm font-medium text-gray-700 mb-1">Mail Host</label>
                    <input type="text" name="mail_host" id="mail_host" value="{{ old('mail_host', $settings->mail_host ?? 'smtp.mailtrap.io') }}"
                        class="w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50 @error('mail_host') border-red-500 @enderror">
                    @error('mail_host')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="mail_port" class="block text-sm font-medium text-gray-700 mb-1">Mail Port</label>
                    <input type="text" name="mail_port" id="mail_port" value="{{ old('mail_port', $settings->mail_port ?? '2525') }}"
                        class="w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50 @error('mail_port') border-red-500 @enderror">
                    @error('mail_port')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="mail_username" class="block text-sm font-medium text-gray-700 mb-1">Mail Username</label>
                    <input type="text" name="mail_username" id="mail_username" value="{{ old('mail_username', $settings->mail_username ?? '') }}"
                        class="w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50 @error('mail_username') border-red-500 @enderror">
                    @error('mail_username')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="mail_password" class="block text-sm font-medium text-gray-700 mb-1">Mail Password</label>
                    <input type="password" name="mail_password" id="mail_password" value="{{ old('mail_password', $settings->mail_password ?? '') }}"
                        class="w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50 @error('mail_password') border-red-500 @enderror">
                    @error('mail_password')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="mail_encryption" class="block text-sm font-medium text-gray-700 mb-1">Mail Encryption</label>
                    <select name="mail_encryption" id="mail_encryption"
                        class="w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50 @error('mail_encryption') border-red-500 @enderror">
                        <option value="tls" {{ old('mail_encryption', $settings->mail_encryption ?? 'tls') == 'tls' ? 'selected' : '' }}>TLS</option>
                        <option value="ssl" {{ old('mail_encryption', $settings->mail_encryption ?? 'tls') == 'ssl' ? 'selected' : '' }}>SSL</option>
                        <option value="" {{ old('mail_encryption', $settings->mail_encryption ?? 'tls') == '' ? 'selected' : '' }}>None</option>
                    </select>
                    @error('mail_encryption')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="mail_from_address" class="block text-sm font-medium text-gray-700 mb-1">From Address</label>
                    <input type="email" name="mail_from_address" id="mail_from_address" value="{{ old('mail_from_address', $settings->mail_from_address ?? '<EMAIL>') }}"
                        class="w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50 @error('mail_from_address') border-red-500 @enderror">
                    @error('mail_from_address')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="mail_from_name" class="block text-sm font-medium text-gray-700 mb-1">From Name</label>
                    <input type="text" name="mail_from_name" id="mail_from_name" value="{{ old('mail_from_name', $settings->mail_from_name ?? 'Pincodes App') }}"
                        class="w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50 @error('mail_from_name') border-red-500 @enderror">
                    @error('mail_from_name')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>

            <div class="mt-6">
                <button type="submit" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium">
                    Save Email Settings
                </button>
            </div>
        </form>
    </div>

    <!-- Backup Settings -->
    <div class="bg-white shadow-md rounded-lg overflow-hidden">
        <div class="px-6 py-4 border-b">
            <h2 class="text-xl font-semibold text-gray-800">Backup & Maintenance</h2>
        </div>

        <div class="p-6">
            <div class="mb-6">
                <h3 class="text-lg font-medium text-gray-900 mb-2">Database Backup</h3>
                <p class="text-gray-600 mb-4">Create a backup of your database. This will download a SQL file that you can use to restore your database.</p>
                <form action="{{ route('admin.backup.database') }}" method="POST">
                    @csrf
                    <button type="submit" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium">
                        <div class="flex items-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                            </svg>
                            Download Database Backup
                        </div>
                    </button>
                </form>
            </div>

            <div class="mb-6">
                <h3 class="text-lg font-medium text-gray-900 mb-2">Clear Cache</h3>
                <p class="text-gray-600 mb-4">Clear the application cache to refresh the system.</p>
                <form action="{{ route('admin.cache.clear') }}" method="POST">
                    @csrf
                    <button type="submit" class="bg-yellow-500 hover:bg-yellow-600 text-white px-4 py-2 rounded-md text-sm font-medium">
                        <div class="flex items-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                            </svg>
                            Clear Cache
                        </div>
                    </button>
                </form>
            </div>

            <div>
                <h3 class="text-lg font-medium text-gray-900 mb-2">Maintenance Mode</h3>
                <p class="text-gray-600 mb-4">Put the application into maintenance mode to perform updates or maintenance.</p>
                <form action="{{ route('admin.settings.maintenance') }}" method="POST">
                    @csrf
                    <button type="submit" class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-md text-sm font-medium">
                        <div class="flex items-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                            {{ app()->isDownForMaintenance() ? 'Exit Maintenance Mode' : 'Enter Maintenance Mode' }}
                        </div>
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const copyApiKeyBtn = document.getElementById('copyApiKey');
        const generateApiKeyBtn = document.getElementById('generateApiKey');
        const apiKeyInput = document.getElementById('api_key');

        copyApiKeyBtn.addEventListener('click', function() {
            apiKeyInput.select();
            document.execCommand('copy');
            
            // Show a temporary "Copied!" message
            const originalText = copyApiKeyBtn.innerHTML;
            copyApiKeyBtn.innerHTML = '<span>Copied!</span>';
            setTimeout(() => {
                copyApiKeyBtn.innerHTML = originalText;
            }, 2000);
        });

        generateApiKeyBtn.addEventListener('click', function() {
            fetch('{{ route("admin.generate.apikey") }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    apiKeyInput.value = data.api_key;
                    
                    // Show a temporary success message
                    const originalText = generateApiKeyBtn.innerHTML;
                    generateApiKeyBtn.innerHTML = '<span>Generated!</span>';
                    setTimeout(() => {
                        generateApiKeyBtn.innerHTML = originalText;
                    }, 2000);
                }
            })
            .catch(error => console.error('Error:', error));
        });
    });
</script>
@endpush
