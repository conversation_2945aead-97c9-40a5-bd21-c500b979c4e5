<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use App\Services\Payment\Exceptions\WebhookVerificationException;
use Symfony\Component\HttpFoundation\Response;

class WebhookSignatureValidation
{
    /**
     * Handle an incoming request.
     * Requirements: 6.1, 6.2, 6.3, 6.4
     */
    public function handle(Request $request, Closure $next): Response
    {
        $gateway = $this->extractGatewayFromPath($request->path());
        
        try {
            if (!$this->validateWebhookSignature($request, $gateway)) {
                $this->logSecurityViolation($request, 'invalid_signature', $gateway);
                
                return response()->json([
                    'error' => 'Invalid webhook signature'
                ], 401);
            }
        } catch (WebhookVerificationException $e) {
            $this->logSecurityViolation($request, 'signature_verification_failed', $gateway, $e->getMessage());
            
            return response()->json([
                'error' => 'Webhook verification failed'
            ], 401);
        } catch (\Exception $e) {
            $this->logSecurityViolation($request, 'verification_error', $gateway, $e->getMessage());
            
            return response()->json([
                'error' => 'Webhook processing error'
            ], 500);
        }
        
        return $next($request);
    }
    
    /**
     * Extract gateway name from request path
     */
    protected function extractGatewayFromPath(string $path): string
    {
        if (str_contains($path, 'razorpay')) {
            return 'razorpay';
        } elseif (str_contains($path, 'payumoney')) {
            return 'payumoney';
        } elseif (str_contains($path, 'cashfree')) {
            return 'cashfree';
        }
        
        return 'unknown';
    }
    
    /**
     * Validate webhook signature based on gateway
     */
    protected function validateWebhookSignature(Request $request, string $gateway): bool
    {
        switch ($gateway) {
            case 'razorpay':
                return $this->validateRazorpaySignature($request);
            case 'payumoney':
                return $this->validatePayUmoneySignature($request);
            case 'cashfree':
                return $this->validateCashfreeSignature($request);
            default:
                throw new WebhookVerificationException("Unknown gateway: {$gateway}");
        }
    }
    
    /**
     * Validate Razorpay webhook signature
     */
    protected function validateRazorpaySignature(Request $request): bool
    {
        $signature = $request->header('X-Razorpay-Signature');
        
        if (!$signature) {
            return false;
        }
        
        $webhookSecret = $this->getGatewayWebhookSecret('razorpay');
        if (!$webhookSecret) {
            throw new WebhookVerificationException('Razorpay webhook secret not configured');
        }
        
        $payload = $request->getContent();
        $expectedSignature = hash_hmac('sha256', $payload, $webhookSecret);
        
        return hash_equals($expectedSignature, $signature);
    }
    
    /**
     * Validate PayUmoney webhook signature
     */
    protected function validatePayUmoneySignature(Request $request): bool
    {
        // PayUmoney uses hash validation instead of signature
        $receivedHash = $request->input('hash');
        
        if (!$receivedHash) {
            return false;
        }
        
        $salt = $this->getGatewayWebhookSecret('payumoney');
        if (!$salt) {
            throw new WebhookVerificationException('PayUmoney salt not configured');
        }
        
        // Build hash string according to PayUmoney documentation
        $hashString = $salt . '|' . $request->input('status') . '|||||||||||' . 
                     $request->input('udf5') . '|' . $request->input('udf4') . '|' . 
                     $request->input('udf3') . '|' . $request->input('udf2') . '|' . 
                     $request->input('udf1') . '|' . $request->input('email') . '|' . 
                     $request->input('firstname') . '|' . $request->input('productinfo') . '|' . 
                     $request->input('amount') . '|' . $request->input('txnid') . '|' . 
                     $request->input('key');
        
        $expectedHash = strtolower(hash('sha512', $hashString));
        
        return hash_equals($expectedHash, strtolower($receivedHash));
    }
    
    /**
     * Validate Cashfree webhook signature
     */
    protected function validateCashfreeSignature(Request $request): bool
    {
        $signature = $request->header('X-Cashfree-Signature');
        
        if (!$signature) {
            return false;
        }
        
        $webhookSecret = $this->getGatewayWebhookSecret('cashfree');
        if (!$webhookSecret) {
            throw new WebhookVerificationException('Cashfree webhook secret not configured');
        }
        
        $payload = $request->getContent();
        $timestamp = $request->header('X-Cashfree-Timestamp');
        
        if (!$timestamp) {
            return false;
        }
        
        // Cashfree signature format: timestamp.payload
        $signedPayload = $timestamp . '.' . $payload;
        $expectedSignature = hash_hmac('sha256', $signedPayload, $webhookSecret);
        
        return hash_equals($expectedSignature, $signature);
    }
    
    /**
     * Get webhook secret for gateway
     */
    protected function getGatewayWebhookSecret(string $gateway): ?string
    {
        $gatewaySettings = \App\Models\GatewaySetting::where('gateway_name', $gateway)
            ->where('is_enabled', true)
            ->first();
        
        if (!$gatewaySettings) {
            return null;
        }
        
        $settings = $gatewaySettings->settings;
        
        return match($gateway) {
            'razorpay' => $settings['webhook_secret'] ?? null,
            'payumoney' => $settings['salt'] ?? null,
            'cashfree' => $settings['webhook_secret'] ?? null,
            default => null
        };
    }
    
    /**
     * Log security violation
     */
    protected function logSecurityViolation(Request $request, string $violationType, string $gateway, ?string $details = null): void
    {
        Log::warning('Webhook security violation detected', [
            'violation_type' => $violationType,
            'gateway' => $gateway,
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'path' => $request->path(),
            'method' => $request->method(),
            'headers' => $this->sanitizeHeaders($request->headers->all()),
            'payload_size' => strlen($request->getContent()),
            'details' => $details,
            'timestamp' => now()->toISOString()
        ]);
        
        // Also log to a separate security log file
        Log::channel('security')->warning('Webhook security violation', [
            'type' => $violationType,
            'gateway' => $gateway,
            'ip' => $request->ip(),
            'timestamp' => now()->toISOString(),
            'details' => $details
        ]);
    }
    
    /**
     * Sanitize headers for logging (remove sensitive data)
     */
    protected function sanitizeHeaders(array $headers): array
    {
        $sensitiveHeaders = [
            'authorization',
            'x-razorpay-signature',
            'x-cashfree-signature',
            'x-payumoney-signature'
        ];
        
        foreach ($sensitiveHeaders as $header) {
            if (isset($headers[$header])) {
                $headers[$header] = ['[REDACTED]'];
            }
        }
        
        return $headers;
    }
}