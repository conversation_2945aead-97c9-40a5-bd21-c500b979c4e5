<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PC Build Shared</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background-color: #10b981;
            color: white;
            padding: 20px;
            text-align: center;
            border-radius: 8px 8px 0 0;
        }
        .content {
            background-color: #f9fafb;
            padding: 20px;
            border: 1px solid #e5e7eb;
        }
        .build-details {
            background-color: white;
            padding: 15px;
            border-radius: 6px;
            margin: 15px 0;
        }
        .component-row {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #e5e7eb;
        }
        .component-row:last-child {
            border-bottom: none;
        }
        .total-row {
            font-weight: bold;
            font-size: 1.1em;
            background-color: #f3f4f6;
            padding: 10px;
            margin-top: 10px;
            text-align: right;
        }
        .message-section {
            background-color: #fef3c7;
            border-left: 4px solid #f59e0b;
            padding: 15px;
            margin: 15px 0;
        }
        .footer {
            background-color: #374151;
            color: white;
            padding: 20px;
            text-align: center;
            border-radius: 0 0 8px 8px;
            font-size: 0.9em;
        }
        .button {
            display: inline-block;
            background-color: #10b981;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 6px;
            margin: 10px 0;
        }
        .build-stats {
            display: flex;
            justify-content: space-around;
            background-color: white;
            padding: 15px;
            border-radius: 6px;
            margin: 15px 0;
        }
        .stat {
            text-align: center;
        }
        .stat-value {
            font-size: 1.5em;
            font-weight: bold;
            color: #10b981;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>PC Build Shared</h1>
        <p>{{ $sharedBy->name }} has shared a PC build with you!</p>
    </div>

    <div class="content">
        <div class="build-details">
            <h2>{{ $build->name }}</h2>
            @if($build->description)
                <p><em>{{ $build->description }}</em></p>
            @endif
            <p><strong>Created by:</strong> {{ $sharedBy->name }}</p>
            <p><strong>Build Date:</strong> {{ $build->created_at->format('F j, Y') }}</p>
        </div>

        @if($message)
        <div class="message-section">
            <h3>Personal Message</h3>
            <p>{{ $message }}</p>
        </div>
        @endif

        <div class="build-stats">
            <div class="stat">
                <div class="stat-value">{{ $components->count() }}</div>
                <div>Components</div>
            </div>
            <div class="stat">
                <div class="stat-value">${{ number_format($build->total_price, 0) }}</div>
                <div>Total Price</div>
            </div>
            <div class="stat">
                <div class="stat-value">{{ $build->is_complete ? 'Complete' : 'In Progress' }}</div>
                <div>Status</div>
            </div>
        </div>

        <div class="build-details">
            <h3>Components</h3>
            @foreach($components as $buildComponent)
                @if($buildComponent->component)
                <div class="component-row">
                    <div>
                        <strong>{{ $buildComponent->component->name }}</strong><br>
                        <small>{{ $buildComponent->component->brand }} {{ $buildComponent->component->model }}</small>
                        @if($buildComponent->quantity > 1)
                            <br><small>Quantity: {{ $buildComponent->quantity }}</small>
                        @endif
                    </div>
                    <div>
                        ${{ number_format($buildComponent->price * $buildComponent->quantity, 2) }}
                    </div>
                </div>
                @endif
            @endforeach
            
            <div class="total-row">
                Total Build Cost: ${{ number_format($build->total_price, 2) }}
            </div>
        </div>

        <div style="text-align: center; margin: 20px 0;">
            <a href="{{ $buildUrl }}" class="button">View Full Build</a>
        </div>

        <div class="build-details">
            <h3>What's Next?</h3>
            <ul>
                <li>Click "View Full Build" to see detailed specifications</li>
                <li>Use the compatibility checker to verify all components work together</li>
                <li>Add components to your cart to purchase this build</li>
                <li>Create your own account to save and modify builds</li>
            </ul>
        </div>
    </div>

    <div class="footer">
        <p>Start building your own PC at <a href="{{ url('/') }}" style="color: #60a5fa;">PC Builder</a></p>
        <p>&copy; {{ date('Y') }} PC Builder. All rights reserved.</p>
    </div>
</body>
</html>