<?php

namespace App\Services;

use App\Models\Component;
use App\Models\PriceHistory;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Exception;

class PriceTrackingService
{
    protected array $priceProviders = [
        'newegg' => 'https://api.newegg.com/marketplace/datafeedmgmt/feeds/item',
        'amazon' => 'https://webservices.amazon.com/paapi5/getitems',
        'bestbuy' => 'https://api.bestbuy.com/v1/products'
    ];

    public function updatePricesForComponent(Component $component): bool
    {
        try {
            $newPrice = $this->fetchPriceFromProviders($component);
            
            if ($newPrice === null) {
                Log::warning("No price found for component: {$component->id}");
                return false;
            }

            $previousPrice = $component->price;
            
            if ($newPrice != $previousPrice) {
                $this->recordPriceChange($component, $newPrice, $previousPrice);
                $component->update(['price' => $newPrice]);
                
                Log::info("Price updated for component {$component->id}: {$previousPrice} -> {$newPrice}");
                return true;
            }
            
            return false;
        } catch (Exception $e) {
            Log::error("Failed to update price for component {$component->id}: " . $e->getMessage());
            return false;
        }
    }

    public function updateAllPrices(): array
    {
        $results = [
            'updated' => 0,
            'failed' => 0,
            'unchanged' => 0
        ];

        Component::where('is_active', true)->chunk(50, function ($components) use (&$results) {
            foreach ($components as $component) {
                try {
                    $updated = $this->updatePricesForComponent($component);
                    if ($updated) {
                        $results['updated']++;
                    } else {
                        $results['unchanged']++;
                    }
                } catch (Exception $e) {
                    $results['failed']++;
                    Log::error("Price update failed for component {$component->id}: " . $e->getMessage());
                }
            }
        });

        return $results;
    }

    public function getPriceHistory(Component $component, int $days = 30): Collection
    {
        return PriceHistory::where('component_id', $component->id)
            ->where('created_at', '>=', now()->subDays($days))
            ->orderBy('created_at', 'desc')
            ->get();
    }

    public function getLowestPrice(Component $component, int $days = 30): ?float
    {
        $history = $this->getPriceHistory($component, $days);
        return $history->min('price');
    }

    public function getHighestPrice(Component $component, int $days = 30): ?float
    {
        $history = $this->getPriceHistory($component, $days);
        return $history->max('price');
    }

    public function getAveragePrice(Component $component, int $days = 30): ?float
    {
        $history = $this->getPriceHistory($component, $days);
        return $history->avg('price');
    }

    public function createPriceAlert(Component $component, float $targetPrice, string $email): bool
    {
        // This would integrate with a notification system
        // For now, we'll just log the alert creation
        Log::info("Price alert created for component {$component->id} at target price {$targetPrice} for {$email}");
        return true;
    }

    protected function fetchPriceFromProviders(Component $component): ?float
    {
        // Mock implementation - in real scenario, this would call actual APIs
        // For demonstration, we'll simulate price fluctuation
        $basePrice = $component->price;
        $fluctuation = rand(-10, 10) / 100; // -10% to +10%
        $newPrice = $basePrice * (1 + $fluctuation);
        
        // Only return if price change is significant (more than 1%)
        if (abs($fluctuation) > 0.01) {
            return round($newPrice, 2);
        }
        
        return null;
    }

    protected function recordPriceChange(Component $component, float $newPrice, float $previousPrice): void
    {
        PriceHistory::create([
            'component_id' => $component->id,
            'price' => $newPrice,
            'previous_price' => $previousPrice,
            'source' => 'automated',
            'metadata' => [
                'change_amount' => $newPrice - $previousPrice,
                'change_percentage' => (($newPrice - $previousPrice) / $previousPrice) * 100,
                'updated_at' => now()->toISOString()
            ]
        ]);
    }

    protected function callNeweggAPI(Component $component): ?float
    {
        try {
            // Mock Newegg API call
            $response = Http::timeout(10)->get($this->priceProviders['newegg'], [
                'item_number' => $component->model,
                'seller_id' => config('services.newegg.seller_id')
            ]);

            if ($response->successful()) {
                $data = $response->json();
                return $data['price'] ?? null;
            }
        } catch (Exception $e) {
            Log::error("Newegg API error for component {$component->id}: " . $e->getMessage());
        }

        return null;
    }

    protected function callAmazonAPI(Component $component): ?float
    {
        try {
            // Mock Amazon API call
            $response = Http::timeout(10)->get($this->priceProviders['amazon'], [
                'ItemIds' => $component->model,
                'Resources' => 'Offers.Listings.Price'
            ]);

            if ($response->successful()) {
                $data = $response->json();
                return $data['ItemsResult']['Items'][0]['Offers']['Listings'][0]['Price']['Amount'] ?? null;
            }
        } catch (Exception $e) {
            Log::error("Amazon API error for component {$component->id}: " . $e->getMessage());
        }

        return null;
    }

    protected function callBestBuyAPI(Component $component): ?float
    {
        try {
            // Mock Best Buy API call
            $response = Http::timeout(10)->get($this->priceProviders['bestbuy'], [
                'format' => 'json',
                'show' => 'salePrice',
                'sku' => $component->model,
                'apikey' => config('services.bestbuy.api_key')
            ]);

            if ($response->successful()) {
                $data = $response->json();
                return $data['products'][0]['salePrice'] ?? null;
            }
        } catch (Exception $e) {
            Log::error("Best Buy API error for component {$component->id}: " . $e->getMessage());
        }

        return null;
    }
}