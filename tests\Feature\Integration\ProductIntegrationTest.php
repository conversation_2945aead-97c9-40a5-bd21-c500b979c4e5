<?php

namespace Tests\Feature\Integration;

use App\Models\Coupon;
use App\Models\Product;
use App\Models\ProductCategory;
use App\Models\ProductReview;
use App\Models\StockMovement;
use App\Models\Transaction;
use App\Models\User;
use App\Services\CouponService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Auth;
use Tests\TestCase;

class ProductIntegrationTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        $this->artisan('migrate');
    }

    /** @test */
    public function it_creates_complete_product_with_category_and_reviews()
    {
        // Create category hierarchy
        $electronics = ProductCategory::create([
            'name' => 'Electronics',
            'description' => 'Electronic devices',
            'is_active' => true,
        ]);

        $smartphones = ProductCategory::create([
            'name' => 'Smartphones',
            'description' => 'Mobile phones',
            'parent_id' => $electronics->id,
            'is_active' => true,
        ]);

        // Create product
        $product = Product::create([
            'name' => 'iPhone 15 Pro',
            'description' => 'Latest iPhone with advanced features',
            'sku' => 'IPH15PRO001',
            'price' => 99999.00,
            'sale_price' => 89999.00,
            'category_id' => $smartphones->id,
            'status' => 'active',
            'in_stock' => true,
            'stock_quantity' => 50,
            'manage_stock' => true,
        ]);

        // Create users and reviews
        $user1 = User::factory()->create();
        $user2 = User::factory()->create();

        $review1 = ProductReview::create([
            'user_id' => $user1->id,
            'product_id' => $product->id,
            'rating' => 5,
            'title' => 'Excellent phone!',
            'comment' => 'This phone is amazing. Great camera quality and performance.',
            'is_approved' => true,
            'is_verified_purchase' => true,
        ]);

        $review2 = ProductReview::create([
            'user_id' => $user2->id,
            'product_id' => $product->id,
            'rating' => 4,
            'title' => 'Good but expensive',
            'comment' => 'Great phone but quite expensive. Worth it for the features.',
            'is_approved' => true,
            'is_verified_purchase' => false,
        ]);

        // Test relationships
        $this->assertEquals('Electronics > Smartphones', $product->getCategoryPath());
        $this->assertEquals(2, $product->getReviewCount());
        $this->assertEquals(4.5, $product->getAverageRating());
        $this->assertTrue($product->hasUserReviewed($user1->id));
        $this->assertFalse($product->hasUserReviewed(999));

        // Test review stats
        $stats = $product->getReviewStats();
        $this->assertEquals(4.5, $stats['average_rating']);
        $this->assertEquals(2, $stats['total_reviews']);
        $this->assertEquals(1, $stats['verified_reviews']);
        $this->assertArrayHasKey('rating_distribution', $stats);
    }

    /** @test */
    public function it_handles_stock_management_with_transactions()
    {
        $product = Product::factory()->create([
            'stock_quantity' => 100,
            'manage_stock' => true,
            'in_stock' => true,
        ]);

        // Simulate sale
        $product->updateStock(10, 'sale');

        $this->assertEquals(90, $product->fresh()->stock_quantity);
        $this->assertTrue($product->fresh()->in_stock);

        // Check stock movement was recorded
        $this->assertDatabaseHas('stock_movements', [
            'product_id' => $product->id,
            'type' => 'sale',
            'quantity_change' => -10,
            'previous_stock' => 100,
            'new_stock' => 90,
        ]);

        // Simulate large sale that depletes stock
        $product->updateStock(90, 'sale');

        $this->assertEquals(0, $product->fresh()->stock_quantity);
        $this->assertFalse($product->fresh()->in_stock);
        $this->assertEquals('Out of Stock', $product->fresh()->getStockStatus());

        // Restock
        $product->updateStock(25, 'restock');

        $this->assertEquals(25, $product->fresh()->stock_quantity);
        $this->assertTrue($product->fresh()->in_stock);
        $this->assertEquals('In Stock', $product->fresh()->getStockStatus());
    }

    /** @test */
    public function it_integrates_coupon_system_with_products()
    {
        $user = User::factory()->create();
        Auth::login($user);

        // Create categories and products
        $electronics = ProductCategory::factory()->create(['name' => 'Electronics']);
        $clothing = ProductCategory::factory()->create(['name' => 'Clothing']);

        $phone = Product::create([
            'name' => 'Smartphone',
            'sku' => 'PHONE001',
            'price' => 50000.00,
            'sale_price' => null,
            'category_id' => $electronics->id,
            'status' => 'active',
            'in_stock' => true,
            'stock_quantity' => 10,
        ]);

        $shirt = Product::create([
            'name' => 'T-Shirt',
            'sku' => 'SHIRT001',
            'price' => 1000.00,
            'sale_price' => null,
            'category_id' => $clothing->id,
            'status' => 'active',
            'in_stock' => true,
            'stock_quantity' => 10,
        ]);

        // Create different types of coupons
        $generalCoupon = Coupon::create([
            'code' => 'GENERAL20',
            'name' => 'General 20% Off',
            'type' => 'percentage',
            'value' => 20,
            'minimum_amount' => null,
            'is_active' => true,
        ]);

        $electronicsCoupon = Coupon::create([
            'code' => 'ELECTRONICS15',
            'name' => 'Electronics 15% Off',
            'type' => 'percentage',
            'value' => 15,
            'applicable_categories' => [$electronics->id],
            'is_active' => true,
        ]);

        $specificProductCoupon = Coupon::create([
            'code' => 'PHONE500',
            'name' => 'Phone ₹500 Off',
            'type' => 'fixed',
            'value' => 500,
            'applicable_products' => [$phone->id],
            'is_active' => true,
        ]);

        $minAmountCoupon = Coupon::create([
            'code' => 'BIG1000',
            'name' => 'Big Order ₹1000 Off',
            'type' => 'fixed',
            'value' => 1000,
            'minimum_amount' => 40000,
            'is_active' => true,
        ]);

        $couponService = new CouponService();

        // Test general coupon on phone
        $cartItems = [['product_id' => $phone->id, 'quantity' => 1]];
        $result = $couponService->validateAndApplyCoupon('GENERAL20', $cartItems);
        $this->assertTrue($result['valid']);
        $this->assertEquals(10000.0, $result['discount']); // 20% of 50000

        // Test electronics-specific coupon on phone
        $result = $couponService->validateAndApplyCoupon('ELECTRONICS15', $cartItems);
        $this->assertTrue($result['valid']);
        $this->assertEquals(7500.0, $result['discount']); // 15% of 50000

        // Test electronics coupon on clothing (should fail)
        $cartItems = [['product_id' => $shirt->id, 'quantity' => 1]];
        $result = $couponService->validateAndApplyCoupon('ELECTRONICS15', $cartItems);
        $this->assertFalse($result['valid']);

        // Test product-specific coupon
        $cartItems = [['product_id' => $phone->id, 'quantity' => 1]];
        $result = $couponService->validateAndApplyCoupon('PHONE500', $cartItems);
        $this->assertTrue($result['valid']);
        $this->assertEquals(500.0, $result['discount']);

        // Test minimum amount coupon (should fail with single shirt - 1000 < 40000)
        $cartItems = [['product_id' => $shirt->id, 'quantity' => 1]];
        $result = $couponService->validateAndApplyCoupon('BIG1000', $cartItems);
        $this->assertFalse($result['valid']);
        $this->assertStringContainsString('Minimum order amount', $result['message']);

        // Test minimum amount coupon with sufficient cart value
        $cartItems = [['product_id' => $phone->id, 'quantity' => 1]]; // 50000 > 40000
        $result = $couponService->validateAndApplyCoupon('BIG1000', $cartItems);
        $this->assertTrue($result['valid']);
        $this->assertEquals(1000.0, $result['discount']);
    }

    /** @test */
    public function it_handles_review_moderation_workflow()
    {
        $product = Product::factory()->create();
        $user = User::factory()->create();

        // Create a review that needs moderation
        $badReview = ProductReview::create([
            'user_id' => $user->id,
            'product_id' => $product->id,
            'rating' => 1,
            'title' => 'Terrible spam',
            'comment' => 'This is fake and terrible spam content.',
            'is_approved' => false,
        ]);

        // Should need moderation
        $this->assertTrue($badReview->needsModeration());

        // Auto-approve should not approve it
        $badReview->autoApprove();
        $this->assertFalse($badReview->fresh()->is_approved);

        // Create a good review
        $goodReview = ProductReview::create([
            'user_id' => User::factory()->create()->id,
            'product_id' => $product->id,
            'rating' => 4,
            'title' => 'Good product',
            'comment' => 'I really like this product. The quality is excellent and delivery was fast. Would recommend to others.',
            'is_approved' => false,
        ]);

        // Should not need moderation
        $this->assertFalse($goodReview->needsModeration());

        // Auto-approve should approve it
        $goodReview->autoApprove();
        $this->assertTrue($goodReview->fresh()->is_approved);

        // Product stats should only include approved reviews
        $this->assertEquals(1, $product->getReviewCount());
        $this->assertEquals(4.0, $product->getAverageRating());
    }

    /** @test */
    public function it_handles_complex_category_hierarchy()
    {
        // Create deep category hierarchy
        $electronics = ProductCategory::create([
            'name' => 'Electronics',
            'is_active' => true,
        ]);

        $mobile = ProductCategory::create([
            'name' => 'Mobile',
            'parent_id' => $electronics->id,
            'is_active' => true,
        ]);

        $smartphones = ProductCategory::create([
            'name' => 'Smartphones',
            'parent_id' => $mobile->id,
            'is_active' => true,
        ]);

        $android = ProductCategory::create([
            'name' => 'Android',
            'parent_id' => $smartphones->id,
            'is_active' => true,
        ]);

        // Create products at different levels
        Product::factory()->create(['category_id' => $electronics->id, 'status' => 'active']);
        Product::factory()->create(['category_id' => $mobile->id, 'status' => 'active']);
        Product::factory()->create(['category_id' => $smartphones->id, 'status' => 'active']);
        Product::factory()->create(['category_id' => $android->id, 'status' => 'active']);

        // Test category relationships
        $this->assertEquals('Electronics', $electronics->getBreadcrumb()[0]['name']);
        $this->assertEquals('Electronics > Mobile > Smartphones > Android', 
            collect($android->getBreadcrumb())->pluck('name')->implode(' > '));

        // Test product counts
        $this->assertEquals(4, $electronics->getTotalProductCount()); // All products
        $this->assertEquals(3, $mobile->getTotalProductCount()); // Mobile and below
        $this->assertEquals(2, $smartphones->getTotalProductCount()); // Smartphones and below
        $this->assertEquals(1, $android->getTotalProductCount()); // Only Android

        // Test ancestors and descendants
        $this->assertCount(3, $android->getAncestors());
        $this->assertCount(3, $electronics->getAllChildren());
    }

    /** @test */
    public function it_handles_coupon_usage_limits()
    {
        $user1 = User::factory()->create();
        $user2 = User::factory()->create();
        $product = Product::factory()->create(['price' => 1000]);

        // Create coupon with usage limits
        $coupon = Coupon::create([
            'code' => 'LIMITED',
            'name' => 'Limited Use Coupon',
            'type' => 'fixed',
            'value' => 100,
            'usage_limit' => 2,
            'usage_limit_per_user' => 1,
            'is_active' => true,
        ]);

        // User 1 uses coupon
        Auth::login($user1);
        $discount1 = $coupon->apply($user1->id, 1000);
        $this->assertEquals(100, $discount1);
        $this->assertEquals(1, $coupon->fresh()->used_count);

        // User 1 tries to use again (should fail)
        $this->assertFalse($coupon->canBeUsedBy($user1->id));

        // User 2 uses coupon
        Auth::login($user2);
        $discount2 = $coupon->apply($user2->id, 1000);
        $this->assertEquals(100, $discount2);
        $this->assertEquals(2, $coupon->fresh()->used_count);

        // Coupon should now be at usage limit
        $user3 = User::factory()->create();
        $this->assertFalse($coupon->fresh()->canBeUsedBy($user3->id));
        $this->assertEquals('Used Up', $coupon->fresh()->status);
    }

    /** @test */
    public function it_handles_product_availability_checks()
    {
        // Managed stock product
        $managedProduct = Product::factory()->create([
            'status' => 'active',
            'manage_stock' => true,
            'stock_quantity' => 10,
            'in_stock' => true,
        ]);

        // Unmanaged stock product
        $unmanagedProduct = Product::factory()->create([
            'status' => 'active',
            'manage_stock' => false,
            'in_stock' => true,
        ]);

        // Inactive product
        $inactiveProduct = Product::factory()->create([
            'status' => 'inactive',
            'manage_stock' => true,
            'stock_quantity' => 10,
            'in_stock' => true,
        ]);

        // Test availability
        $this->assertTrue($managedProduct->isAvailable(5));
        $this->assertFalse($managedProduct->isAvailable(15));
        $this->assertTrue($unmanagedProduct->isAvailable());
        $this->assertFalse($inactiveProduct->isAvailable());

        // Simulate stock depletion
        $managedProduct->updateStock(10, 'sale');
        $this->assertFalse($managedProduct->fresh()->isAvailable());
        $this->assertEquals('Out of Stock', $managedProduct->fresh()->getStockStatus());

        // Restock to low level
        $managedProduct->updateStock(3, 'restock');
        $this->assertTrue($managedProduct->fresh()->isAvailable(2));
        $this->assertEquals('Low Stock', $managedProduct->fresh()->getStockStatus());
    }

    /** @test */
    public function it_integrates_with_transaction_system()
    {
        $product = Product::factory()->create([
            'price' => 1000,
            'stock_quantity' => 50,
            'manage_stock' => true,
        ]);

        // Create transaction
        $transaction = Transaction::factory()->create([
            'product_id' => $product->id,
            'amount' => 1000,
            'quantity' => 2,
            'status' => 'completed',
        ]);

        // Update stock based on transaction
        $product->updateStock($transaction->quantity, 'sale');

        // Verify stock was updated
        $this->assertEquals(48, $product->fresh()->stock_quantity);

        // Verify relationship
        $this->assertEquals($product->id, $transaction->product->id);
        $this->assertCount(1, $product->transactions);
        $this->assertCount(1, $product->stockMovements);
    }

    /** @test */
    public function it_handles_sale_price_calculations()
    {
        $product = Product::factory()->create([
            'price' => 1000.00,
            'sale_price' => 800.00,
        ]);

        // Test price calculations
        $this->assertEquals(800, $product->effective_price);
        $this->assertTrue($product->is_on_sale);
        $this->assertEquals(20.0, $product->discount_percentage);
        $this->assertEquals('₹800.00', $product->formatted_price);
        $this->assertEquals('₹1,000.00', $product->original_price);

        // Test with coupon on sale price
        $user = User::factory()->create();
        Auth::login($user);

        $coupon = Coupon::create([
            'code' => 'EXTRA10',
            'name' => 'Extra 10% Off',
            'type' => 'percentage',
            'value' => 10,
            'minimum_amount' => null,
            'is_active' => true,
        ]);

        $couponService = new CouponService();
        $cartItems = [['product_id' => $product->id, 'quantity' => 1]];
        $result = $couponService->validateAndApplyCoupon('EXTRA10', $cartItems);

        $this->assertTrue($result['valid']);
        $this->assertEquals(800.0, $result['subtotal']); // Uses sale price
        $this->assertEquals(80.0, $result['discount']); // 10% of sale price
        $this->assertEquals(720.0, $result['final_total']);
    }
}