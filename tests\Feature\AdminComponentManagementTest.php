<?php

namespace Tests\Feature;

use App\Livewire\Admin\ComponentForm;
use App\Livewire\Admin\ComponentsList;
use App\Models\Component;
use App\Models\ComponentCategory;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Livewire\Livewire;
use Tests\TestCase;

class AdminComponentManagementTest extends TestCase
{
    use RefreshDatabase;

    protected User $adminUser;
    protected User $regularUser;
    protected ComponentCategory $category;

    protected function setUp(): void
    {
        parent::setUp();

        // Create admin user
        $this->adminUser = User::factory()->create([
            'role' => 'admin',
            'email' => '<EMAIL>',
        ]);

        // Create regular user
        $this->regularUser = User::factory()->create([
            'role' => 'user',
            'email' => '<EMAIL>',
        ]);

        // Create a component category
        $this->category = ComponentCategory::factory()->create([
            'name' => 'CPU',
            'slug' => 'cpu',
        ]);

        Storage::fake('public');
    }

    /** @test */
    public function admin_can_access_components_list()
    {
        $this->actingAs($this->adminUser);

        $response = $this->get(route('admin.components.index'));

        $response->assertStatus(200);
        $response->assertSeeLivewire('admin.components-list');
    }

    /** @test */
    public function regular_user_cannot_access_admin_components()
    {
        $this->actingAs($this->regularUser);

        $response = $this->get(route('admin.components.index'));

        $response->assertStatus(403);
    }

    /** @test */
    public function guest_cannot_access_admin_components()
    {
        $response = $this->get(route('admin.components.index'));

        $response->assertRedirect(route('admin.login'));
    }

    /** @test */
    public function components_list_displays_components_correctly()
    {
        $this->actingAs($this->adminUser);

        $component = Component::factory()->create([
            'name' => 'Intel Core i7-12700K',
            'brand' => 'Intel',
            'model' => 'i7-12700K',
            'price' => 399.99,
            'stock' => 50,
            'category_id' => $this->category->id,
            'is_active' => true,
            'is_featured' => false,
        ]);

        Livewire::test(ComponentsList::class)
            ->assertSee('Intel Core i7-12700K')
            ->assertSee('Intel')
            ->assertSee('i7-12700K')
            ->assertSee('$399.99')
            ->assertSee('50')
            ->assertSee('Active');
    }

    /** @test */
    public function components_list_can_be_searched()
    {
        $this->actingAs($this->adminUser);

        $component1 = Component::factory()->create([
            'name' => 'Intel Core i7-12700K',
            'brand' => 'Intel',
            'category_id' => $this->category->id,
        ]);

        $component2 = Component::factory()->create([
            'name' => 'AMD Ryzen 7 5800X',
            'brand' => 'AMD',
            'category_id' => $this->category->id,
        ]);

        Livewire::test(ComponentsList::class)
            ->set('search', 'Intel')
            ->assertSee('Intel Core i7-12700K')
            ->assertDontSee('AMD Ryzen 7 5800X');
    }

    /** @test */
    public function components_list_can_be_filtered_by_category()
    {
        $this->actingAs($this->adminUser);

        $gpuCategory = ComponentCategory::factory()->create([
            'name' => 'GPU',
            'slug' => 'gpu',
        ]);

        $cpuComponent = Component::factory()->create([
            'name' => 'Intel Core i7-12700K',
            'category_id' => $this->category->id,
        ]);

        $gpuComponent = Component::factory()->create([
            'name' => 'NVIDIA RTX 4080',
            'category_id' => $gpuCategory->id,
        ]);

        Livewire::test(ComponentsList::class)
            ->set('categoryFilter', $this->category->id)
            ->assertSee('Intel Core i7-12700K')
            ->assertDontSee('NVIDIA RTX 4080');
    }

    /** @test */
    public function components_list_can_be_filtered_by_status()
    {
        $this->actingAs($this->adminUser);

        $activeComponent = Component::factory()->create([
            'name' => 'Active Component',
            'category_id' => $this->category->id,
            'is_active' => true,
        ]);

        $inactiveComponent = Component::factory()->create([
            'name' => 'Inactive Component',
            'category_id' => $this->category->id,
            'is_active' => false,
        ]);

        Livewire::test(ComponentsList::class)
            ->set('statusFilter', '1')
            ->assertSee('Active Component')
            ->assertDontSee('Inactive Component');
    }

    /** @test */
    public function components_list_can_be_sorted()
    {
        $this->actingAs($this->adminUser);

        $component1 = Component::factory()->create([
            'name' => 'B Component',
            'price' => 100.00,
            'category_id' => $this->category->id,
        ]);

        $component2 = Component::factory()->create([
            'name' => 'A Component',
            'price' => 200.00,
            'category_id' => $this->category->id,
        ]);

        $livewire = Livewire::test(ComponentsList::class)
            ->call('sortBy', 'name');

        // Check that the sort direction and field are set correctly
        $this->assertEquals('name', $livewire->get('sortBy'));
        // Since default sortBy is 'name' and sortDirection is 'asc', calling sortBy('name') will flip to 'desc'
        $this->assertEquals('desc', $livewire->get('sortDirection'));
    }

    /** @test */
    public function admin_can_toggle_component_status()
    {
        $this->actingAs($this->adminUser);

        $component = Component::factory()->create([
            'category_id' => $this->category->id,
            'is_active' => true,
        ]);

        Livewire::test(ComponentsList::class)
            ->call('toggleStatus', $component->id);

        $this->assertFalse($component->fresh()->is_active);
    }

    /** @test */
    public function admin_can_toggle_component_featured_status()
    {
        $this->actingAs($this->adminUser);

        $component = Component::factory()->create([
            'category_id' => $this->category->id,
            'is_featured' => false,
        ]);

        Livewire::test(ComponentsList::class)
            ->call('toggleFeatured', $component->id);

        $this->assertTrue($component->fresh()->is_featured);
    }

    /** @test */
    public function admin_can_delete_component()
    {
        $this->actingAs($this->adminUser);

        $component = Component::factory()->create([
            'category_id' => $this->category->id,
        ]);

        Livewire::test(ComponentsList::class)
            ->call('deleteComponent', $component->id);

        $this->assertDatabaseMissing('components', ['id' => $component->id]);
    }

    /** @test */
    public function admin_can_perform_bulk_operations()
    {
        $this->actingAs($this->adminUser);

        $component1 = Component::factory()->create([
            'category_id' => $this->category->id,
            'is_active' => true,
        ]);

        $component2 = Component::factory()->create([
            'category_id' => $this->category->id,
            'is_active' => true,
        ]);

        Livewire::test(ComponentsList::class)
            ->set('selectedComponents', [$component1->id, $component2->id])
            ->call('bulkToggleStatus', false);

        $this->assertFalse($component1->fresh()->is_active);
        $this->assertFalse($component2->fresh()->is_active);
    }

    /** @test */
    public function admin_can_bulk_delete_components()
    {
        $this->actingAs($this->adminUser);

        $component1 = Component::factory()->create([
            'category_id' => $this->category->id,
        ]);

        $component2 = Component::factory()->create([
            'category_id' => $this->category->id,
        ]);

        Livewire::test(ComponentsList::class)
            ->set('selectedComponents', [$component1->id, $component2->id])
            ->call('bulkDelete');

        $this->assertDatabaseMissing('components', ['id' => $component1->id]);
        $this->assertDatabaseMissing('components', ['id' => $component2->id]);
    }

    /** @test */
    public function component_form_can_create_new_component()
    {
        $this->actingAs($this->adminUser);

        $componentData = [
            'name' => 'Intel Core i9-13900K',
            'description' => 'High-performance CPU',
            'category_id' => $this->category->id,
            'brand' => 'Intel',
            'model' => 'i9-13900K',
            'price' => 599.99,
            'stock' => 25,
            'is_active' => true,
            'is_featured' => false,
        ];

        Livewire::test(ComponentForm::class)
            ->set($componentData)
            ->call('save');

        $this->assertDatabaseHas('components', [
            'name' => 'Intel Core i9-13900K',
            'brand' => 'Intel',
            'model' => 'i9-13900K',
            'price' => 599.99,
            'stock' => 25,
        ]);
    }

    /** @test */
    public function component_form_validates_required_fields()
    {
        $this->actingAs($this->adminUser);

        Livewire::test(ComponentForm::class)
            ->set('name', '')
            ->set('category_id', '')
            ->set('brand', '')
            ->set('model', '')
            ->set('price', '')
            ->set('stock', '')
            ->call('save')
            ->assertHasErrors([
                'name',
                'category_id',
                'brand',
                'model',
                'price',
                'stock',
            ]);
    }

    /** @test */
    public function component_form_can_edit_existing_component()
    {
        $this->actingAs($this->adminUser);

        $component = Component::factory()->create([
            'name' => 'Original Name',
            'category_id' => $this->category->id,
            'brand' => 'Original Brand',
            'model' => 'Original Model',
            'price' => 100.00,
            'stock' => 10,
        ]);

        Livewire::test(ComponentForm::class, ['componentId' => $component->id])
            ->set('name', 'Updated Name')
            ->set('brand', 'Updated Brand')
            ->set('price', 200.00)
            ->call('save');

        $this->assertDatabaseHas('components', [
            'id' => $component->id,
            'name' => 'Updated Name',
            'brand' => 'Updated Brand',
            'price' => 200.00,
        ]);
    }

    /** @test */
    public function component_form_can_handle_image_upload()
    {
        $this->actingAs($this->adminUser);

        $image = UploadedFile::fake()->image('component.jpg');

        $componentData = [
            'name' => 'Intel Core i7-12700K',
            'category_id' => $this->category->id,
            'brand' => 'Intel',
            'model' => 'i7-12700K',
            'price' => 399.99,
            'stock' => 50,
            'image' => $image,
        ];

        Livewire::test(ComponentForm::class)
            ->set($componentData)
            ->call('save');

        $component = Component::where('name', 'Intel Core i7-12700K')->first();
        $this->assertNotNull($component->image);
        Storage::disk('public')->assertExists($component->image);
    }

    /** @test */
    public function component_form_can_manage_specifications()
    {
        $this->actingAs($this->adminUser);

        $specs = [
            ['key' => 'Cores', 'value' => '12'],
            ['key' => 'Threads', 'value' => '20'],
            ['key' => 'Base Clock', 'value' => '3.6 GHz'],
        ];

        Livewire::test(ComponentForm::class)
            ->set('name', 'Intel Core i7-12700K')
            ->set('category_id', $this->category->id)
            ->set('brand', 'Intel')
            ->set('model', 'i7-12700K')
            ->set('price', 399.99)
            ->set('stock', 50)
            ->set('specs', $specs)
            ->call('save');

        $component = Component::where('name', 'Intel Core i7-12700K')->first();
        $this->assertEquals([
            'Cores' => '12',
            'Threads' => '20',
            'Base Clock' => '3.6 GHz',
        ], $component->specs);
    }

    /** @test */
    public function component_form_can_add_and_remove_specifications()
    {
        $this->actingAs($this->adminUser);

        $livewire = Livewire::test(ComponentForm::class)
            ->call('addSpec')
            ->assertSet('specs.0', ['key' => '', 'value' => ''])
            ->set('specs.0.key', 'Cores')
            ->set('specs.0.value', '8')
            ->call('addSpec')
            ->assertSet('specs.1', ['key' => '', 'value' => ''])
            ->call('removeSpec', 0);

        $specs = $livewire->get('specs');
        $this->assertCount(1, $specs);
    }

    /** @test */
    public function component_form_validates_image_upload()
    {
        $this->actingAs($this->adminUser);

        // Create a file that's too large (over 2MB limit)
        $largeImage = UploadedFile::fake()->image('large.jpg')->size(3000);

        Livewire::test(ComponentForm::class)
            ->set('name', 'Test Component')
            ->set('category_id', $this->category->id)
            ->set('brand', 'Test Brand')
            ->set('model', 'Test Model')
            ->set('price', 100.00)
            ->set('stock', 10)
            ->set('image', $largeImage)
            ->call('save')
            ->assertHasErrors(['image']);
    }

    /** @test */
    public function component_form_can_cancel_and_reset()
    {
        $this->actingAs($this->adminUser);

        Livewire::test(ComponentForm::class)
            ->set('name', 'Test Component')
            ->set('brand', 'Test Brand')
            ->call('cancel')
            ->assertSet('name', '')
            ->assertSet('brand', '');
    }

    /** @test */
    public function components_list_shows_stock_level_warnings()
    {
        $this->actingAs($this->adminUser);

        $lowStockComponent = Component::factory()->create([
            'name' => 'Low Stock Component',
            'category_id' => $this->category->id,
            'stock' => 3,
        ]);

        $mediumStockComponent = Component::factory()->create([
            'name' => 'Medium Stock Component',
            'category_id' => $this->category->id,
            'stock' => 15,
        ]);

        $highStockComponent = Component::factory()->create([
            'name' => 'High Stock Component',
            'category_id' => $this->category->id,
            'stock' => 100,
        ]);

        $response = Livewire::test(ComponentsList::class);

        // Low stock should be red
        $response->assertSeeHtml('text-red-600');
        
        // Medium stock should be yellow
        $response->assertSeeHtml('text-yellow-600');
        
        // High stock should be green
        $response->assertSeeHtml('text-green-600');
    }

    /** @test */
    public function components_list_pagination_works()
    {
        $this->actingAs($this->adminUser);

        // Create more components than the default per page
        Component::factory()->count(20)->create([
            'category_id' => $this->category->id,
        ]);

        Livewire::test(ComponentsList::class)
            ->set('perPage', 15)
            ->assertSee('Next');
    }

    /** @test */
    public function select_all_functionality_works()
    {
        $this->actingAs($this->adminUser);

        $components = Component::factory()->count(3)->create([
            'category_id' => $this->category->id,
        ]);

        $livewire = Livewire::test(ComponentsList::class)
            ->set('selectAll', true);

        $selectedComponents = $livewire->get('selectedComponents');
        $this->assertCount(3, $selectedComponents);
    }
}