<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\CouponUsage;
use App\Models\Coupon;
use App\Models\User;
use App\Models\Order;

class CouponUsageSeeder extends Seeder
{
    public function run(): void
    {
        $coupons = Coupon::where('is_active', true)->get();
        $users = User::all();
        $orders = Order::whereIn('status', [
            Order::STATUS_COMPLETED,
            Order::STATUS_PROCESSING,
            Order::STATUS_SHIPPED,
            Order::STATUS_DELIVERED
        ])->get();

        // Skip if no coupons available
        if ($coupons->isEmpty()) {
            $this->command->warn('No active coupons found. Skipping coupon usage seeding.');
            return;
        }

        // Create coupon usage records for some orders
        foreach ($orders->take(15) as $order) {
            // 30% chance an order used a coupon
            if (rand(1, 100) <= 30) {
                $coupon = $coupons->random();
                $user = $users->find($order->user_id);
                
                // Calculate discount amount based on coupon type
                $discountAmount = $this->calculateDiscount($coupon, $order->subtotal);
                
                // Check if coupon usage limit allows this
                $currentUsage = CouponUsage::where('coupon_id', $coupon->id)->count();
                if ($coupon->usage_limit && $currentUsage >= $coupon->usage_limit) {
                    continue;
                }
                
                // Check user usage limit
                $userUsage = CouponUsage::where('coupon_id', $coupon->id)
                    ->where('user_id', $user->id)
                    ->count();
                if ($coupon->usage_limit_per_user && $userUsage >= $coupon->usage_limit_per_user) {
                    continue;
                }

                CouponUsage::create([
                    'coupon_id' => $coupon->id,
                    'user_id' => $user->id,
                    'order_id' => $order->id,
                    'discount_amount' => $discountAmount,
                ]);

                // Update order with coupon information
                $order->update([
                    'discount' => $discountAmount,
                    'total' => $order->total - $discountAmount,
                ]);

                // Update coupon usage count
                $coupon->increment('used_count');
            }
        }

        // Create some standalone coupon usage records (for testing)
        foreach ($coupons->take(5) as $coupon) {
            $numUsages = rand(1, min(5, $coupon->usage_limit ?? 5));
            
            for ($i = 0; $i < $numUsages; $i++) {
                if ($users->isEmpty()) break;
                
                $user = $users->random();
                $order = $orders->where('user_id', $user->id)->first();
                
                if ($order && !CouponUsage::where('coupon_id', $coupon->id)->where('order_id', $order->id)->exists()) {
                    $discountAmount = $this->calculateDiscount($coupon, $order->subtotal);
                    
                    CouponUsage::create([
                        'coupon_id' => $coupon->id,
                        'user_id' => $user->id,
                        'order_id' => $order->id,
                        'discount_amount' => $discountAmount,
                    ]);
                }
            }
        }
    }

    private function calculateDiscount(Coupon $coupon, float $orderAmount): float
    {
        if ($coupon->type === 'percentage') {
            $discount = ($orderAmount * $coupon->value) / 100;
            
            // Apply maximum discount limit if set
            if ($coupon->max_discount_amount) {
                $discount = min($discount, $coupon->max_discount_amount);
            }
            
            return round($discount, 2);
        } else {
            // Fixed amount discount
            return min($coupon->value, $orderAmount);
        }
    }
}