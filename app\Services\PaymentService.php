<?php

namespace App\Services;

use App\Models\Transaction;
use App\Models\User;
use App\Services\Payment\Exceptions\PaymentGatewayException;
use App\Services\Payment\Exceptions\InvalidPaymentDataException;
use App\Services\Payment\Exceptions\GatewayConfigurationException;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Exception;

class PaymentService
{
    public function __construct(
        private PaymentGatewayFactory $gatewayFactory,
        private TransactionService $transactionService
    ) {}

    /**
     * Initiate a payment process
     *
     * @param array $paymentData
     * @return array
     * @throws InvalidPaymentDataException
     * @throws PaymentGatewayException
     * @throws GatewayConfigurationException
     */
    public function initiatePayment(array $paymentData): array
    {
        $this->validatePaymentData($paymentData);

        $transaction = null;
        
        try {
            // Create transaction record first (without DB transaction)
            $transaction = $this->transactionService->createTransaction([
                'user_id' => $paymentData['user_id'],
                'gateway_name' => $paymentData['gateway'],
                'amount' => $paymentData['amount'],
                'currency' => $paymentData['currency'] ?? 'INR',
                'status' => Transaction::STATUS_PENDING,
                'transaction_id' => $this->generateTransactionId(),
            ]);

            // Get gateway service
            $gateway = $this->gatewayFactory->create($paymentData['gateway']);

            // Prepare gateway payment data
            $gatewayData = $this->prepareGatewayData($transaction, $paymentData);

            // Create payment with gateway
            $gatewayResponse = $gateway->createPayment($gatewayData);

            // Update transaction with gateway response
            $this->transactionService->updateTransaction($transaction->id, [
                'status' => Transaction::STATUS_PROCESSING,
                'payment_details' => array_merge(
                    $transaction->payment_details ?? [],
                    ['gateway_response' => $gatewayResponse]
                )
            ]);

            Log::info('Payment initiated successfully', [
                'transaction_id' => $transaction->transaction_id,
                'gateway' => $paymentData['gateway'],
                'amount' => $paymentData['amount']
            ]);

            return [
                'success' => true,
                'transaction' => $transaction->fresh(),
                'gateway_data' => $gatewayResponse,
                'redirect_url' => $this->getRedirectUrl($transaction, $gatewayResponse)
            ];

        } catch (Exception $e) {
            if ($transaction) {
                $this->transactionService->updateTransaction($transaction->id, [
                    'status' => Transaction::STATUS_FAILED,
                    'failure_reason' => $e->getMessage()
                ]);
            }

            Log::error('Payment initiation failed', [
                'error' => $e->getMessage(),
                'gateway' => $paymentData['gateway'] ?? 'unknown',
                'amount' => $paymentData['amount'] ?? 'unknown'
            ]);

            throw new PaymentGatewayException(
                'Failed to initiate payment: ' . $e->getMessage(),
                'PAYMENT_INITIATION_FAILED',
                ['original_error' => $e->getMessage()]
            );
        }
    }

    /**
     * Process payment verification
     *
     * @param string $transactionId
     * @param array $verificationData
     * @return array
     * @throws PaymentGatewayException
     */
    public function verifyPayment(string $transactionId, array $verificationData): array
    {
        try {
            $transaction = $this->transactionService->getTransactionByTransactionId($transactionId);
            
            if (!$transaction) {
                throw new PaymentGatewayException('Transaction not found', 'TRANSACTION_NOT_FOUND');
            }

            $gateway = $this->gatewayFactory->create($transaction->gateway_name);
            
            $isVerified = $gateway->verifyPayment($transactionId, $verificationData);

            if ($isVerified) {
                $this->transactionService->updateTransaction($transaction->id, [
                    'status' => Transaction::STATUS_COMPLETED,
                    'gateway_transaction_id' => $verificationData['gateway_transaction_id'] ?? null,
                    'payment_details' => array_merge(
                        $transaction->payment_details ?? [],
                        ['verification_data' => $verificationData, 'verified_at' => now()]
                    )
                ]);

                Log::info('Payment verified successfully', [
                    'transaction_id' => $transactionId,
                    'gateway' => $transaction->gateway_name
                ]);

                return [
                    'success' => true,
                    'status' => 'completed',
                    'transaction' => $transaction->fresh(),
                    'message' => 'Payment completed successfully'
                ];
            } else {
                $this->transactionService->updateTransaction($transaction->id, [
                    'status' => Transaction::STATUS_FAILED,
                    'failure_reason' => 'Payment verification failed'
                ]);

                Log::warning('Payment verification failed', [
                    'transaction_id' => $transactionId,
                    'gateway' => $transaction->gateway_name
                ]);

                return [
                    'success' => false,
                    'status' => 'failed',
                    'transaction' => $transaction->fresh(),
                    'message' => 'Payment verification failed'
                ];
            }

        } catch (Exception $e) {
            Log::error('Payment verification error', [
                'transaction_id' => $transactionId,
                'error' => $e->getMessage()
            ]);

            throw new PaymentGatewayException(
                'Payment verification failed: ' . $e->getMessage(),
                'PAYMENT_VERIFICATION_FAILED'
            );
        }
    }

    /**
     * Get payment status
     *
     * @param string $transactionId
     * @return array
     */
    public function getPaymentStatus(string $transactionId): array
    {
        try {
            $transaction = $this->transactionService->getTransactionByTransactionId($transactionId);
            
            if (!$transaction) {
                return [
                    'success' => false,
                    'message' => 'Transaction not found'
                ];
            }

            // For completed or failed transactions, return current status
            if (in_array($transaction->status, [Transaction::STATUS_COMPLETED, Transaction::STATUS_FAILED, Transaction::STATUS_CANCELLED])) {
                return [
                    'success' => true,
                    'status' => $transaction->status,
                    'transaction' => $transaction,
                    'message' => $this->getStatusMessage($transaction->status)
                ];
            }

            // For pending/processing transactions, check with gateway
            try {
                $gateway = $this->gatewayFactory->create($transaction->gateway_name);
                $gatewayStatus = $gateway->getPaymentStatus($transactionId);
                
                // Update transaction if status changed
                if ($gatewayStatus !== $transaction->status) {
                    $this->transactionService->updateTransaction($transaction->id, [
                        'status' => $gatewayStatus
                    ]);
                    $transaction = $transaction->fresh();
                }

                return [
                    'success' => true,
                    'status' => $transaction->status,
                    'transaction' => $transaction,
                    'message' => $this->getStatusMessage($transaction->status)
                ];

            } catch (Exception $e) {
                Log::warning('Failed to get status from gateway', [
                    'transaction_id' => $transactionId,
                    'error' => $e->getMessage()
                ]);

                // Return current transaction status if gateway check fails
                return [
                    'success' => true,
                    'status' => $transaction->status,
                    'transaction' => $transaction,
                    'message' => $this->getStatusMessage($transaction->status)
                ];
            }

        } catch (Exception $e) {
            Log::error('Error getting payment status', [
                'transaction_id' => $transactionId,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => 'Error retrieving payment status'
            ];
        }
    }

    /**
     * Cancel a payment
     *
     * @param string $transactionId
     * @param string $reason
     * @return array
     */
    public function cancelPayment(string $transactionId, string $reason = 'User cancelled'): array
    {
        try {
            $transaction = $this->transactionService->getTransactionByTransactionId($transactionId);
            
            if (!$transaction) {
                return [
                    'success' => false,
                    'message' => 'Transaction not found'
                ];
            }

            if (!in_array($transaction->status, [Transaction::STATUS_PENDING, Transaction::STATUS_PROCESSING])) {
                return [
                    'success' => false,
                    'message' => 'Transaction cannot be cancelled in current status: ' . $transaction->status
                ];
            }

            $this->transactionService->updateTransaction($transaction->id, [
                'status' => Transaction::STATUS_CANCELLED,
                'failure_reason' => $reason
            ]);

            Log::info('Payment cancelled', [
                'transaction_id' => $transactionId,
                'reason' => $reason
            ]);

            return [
                'success' => true,
                'status' => 'cancelled',
                'transaction' => $transaction->fresh(),
                'message' => 'Payment cancelled successfully'
            ];

        } catch (Exception $e) {
            Log::error('Error cancelling payment', [
                'transaction_id' => $transactionId,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => 'Error cancelling payment'
            ];
        }
    }

    /**
     * Get available payment gateways for user
     *
     * @return array
     */
    public function getAvailableGateways(): array
    {
        return $this->gatewayFactory->getAvailableGateways();
    }

    /**
     * Validate payment data
     *
     * @param array $data
     * @throws InvalidPaymentDataException
     */
    private function validatePaymentData(array $data): void
    {
        $required = ['user_id', 'gateway', 'amount'];
        
        foreach ($required as $field) {
            if (!isset($data[$field]) || empty($data[$field])) {
                throw new InvalidPaymentDataException("Missing required field: $field");
            }
        }

        if (!is_numeric($data['amount']) || $data['amount'] <= 0) {
            throw new InvalidPaymentDataException('Amount must be a positive number');
        }

        if (!$this->gatewayFactory->isSupported($data['gateway'])) {
            throw new InvalidPaymentDataException("Unsupported gateway: {$data['gateway']}");
        }

        // Validate user exists
        if (!User::find($data['user_id'])) {
            throw new InvalidPaymentDataException('Invalid user ID');
        }
    }

    /**
     * Prepare data for gateway
     *
     * @param Transaction $transaction
     * @param array $paymentData
     * @return array
     */
    private function prepareGatewayData(Transaction $transaction, array $paymentData): array
    {
        return [
            'transaction_id' => $transaction->transaction_id,
            'amount' => $transaction->amount,
            'currency' => $transaction->currency,
            'user_id' => $transaction->user_id,
            'description' => $paymentData['description'] ?? 'Payment for order',
            'customer_email' => $paymentData['customer_email'] ?? null,
            'customer_phone' => $paymentData['customer_phone'] ?? null,
            'return_url' => $paymentData['return_url'] ?? route('payment.status', $transaction->transaction_id),
            'cancel_url' => $paymentData['cancel_url'] ?? route('payment.cancel', $transaction->transaction_id),
            'webhook_url' => route('webhooks.payment', $transaction->gateway_name),
        ];
    }

    /**
     * Generate unique transaction ID
     *
     * @return string
     */
    private function generateTransactionId(): string
    {
        do {
            $transactionId = 'TXN_' . strtoupper(Str::random(12)) . '_' . time();
        } while (Transaction::where('transaction_id', $transactionId)->exists());

        return $transactionId;
    }

    /**
     * Get redirect URL based on gateway response
     *
     * @param Transaction $transaction
     * @param array $gatewayResponse
     * @return string|null
     */
    private function getRedirectUrl(Transaction $transaction, array $gatewayResponse): ?string
    {
        // Each gateway might have different redirect URL structure
        return $gatewayResponse['redirect_url'] ?? 
               $gatewayResponse['payment_url'] ?? 
               route('payment.process', $transaction->transaction_id);
    }

    /**
     * Get status message for transaction status
     *
     * @param string $status
     * @return string
     */
    private function getStatusMessage(string $status): string
    {
        return match($status) {
            Transaction::STATUS_PENDING => 'Payment is pending',
            Transaction::STATUS_PROCESSING => 'Payment is being processed',
            Transaction::STATUS_COMPLETED => 'Payment completed successfully',
            Transaction::STATUS_FAILED => 'Payment failed',
            Transaction::STATUS_CANCELLED => 'Payment was cancelled',
            default => 'Unknown payment status'
        };
    }
}