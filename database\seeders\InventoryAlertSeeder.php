<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\InventoryAlert;
use App\Models\Component;
use Carbon\Carbon;

class InventoryAlertSeeder extends Seeder
{
    public function run(): void
    {
        $components = Component::all();

        foreach ($components as $component) {
            // Create alerts based on current stock levels
            $this->createStockAlerts($component);
            
            // Create some historical resolved alerts
            $this->createHistoricalAlerts($component);
        }
    }

    private function createStockAlerts(Component $component): void
    {
        $currentStock = $component->stock;
        
        // Low stock alert (threshold: 10)
        if ($currentStock <= 10 && $currentStock > 0) {
            InventoryAlert::create([
                'component_id' => $component->id,
                'type' => 'low_stock',
                'current_stock' => $currentStock,
                'threshold' => 10,
                'is_resolved' => false,
                'metadata' => [
                    'alert_level' => $currentStock <= 5 ? 'critical' : 'warning',
                    'reorder_point' => 20,
                    'suggested_quantity' => 50,
                    'supplier' => $this->getRandomSupplier(),
                    'last_restock_date' => Carbon::now()->subDays(rand(10, 60))->toDateString(),
                ],
            ]);
        }
        
        // Out of stock alert
        if ($currentStock <= 0) {
            InventoryAlert::create([
                'component_id' => $component->id,
                'type' => 'out_of_stock',
                'current_stock' => $currentStock,
                'threshold' => 0,
                'is_resolved' => false,
                'metadata' => [
                    'alert_level' => 'critical',
                    'days_out_of_stock' => rand(1, 15),
                    'lost_sales_estimate' => rand(5, 50),
                    'supplier' => $this->getRandomSupplier(),
                    'expected_restock_date' => Carbon::now()->addDays(rand(3, 21))->toDateString(),
                ],
            ]);
        }
        
        // Overstock alert (threshold: 100)
        if ($currentStock > 100) {
            InventoryAlert::create([
                'component_id' => $component->id,
                'type' => 'overstock',
                'current_stock' => $currentStock,
                'threshold' => 100,
                'is_resolved' => rand(1, 100) <= 30, // 30% chance it's resolved
                'resolved_at' => rand(1, 100) <= 30 ? Carbon::now()->subDays(rand(1, 10)) : null,
                'metadata' => [
                    'alert_level' => 'info',
                    'excess_quantity' => $currentStock - 100,
                    'storage_cost_impact' => rand(100, 1000),
                    'suggested_action' => 'Consider promotional pricing',
                    'seasonal_factor' => $this->getSeasonalFactor(),
                ],
            ]);
        }
    }

    private function createHistoricalAlerts(Component $component): void
    {
        // Create 2-5 historical alerts per component
        $numAlerts = rand(2, 5);
        
        for ($i = 0; $i < $numAlerts; $i++) {
            $alertType = $this->getRandomAlertType();
            $createdAt = Carbon::now()->subDays(rand(30, 180));
            $resolvedAt = $createdAt->copy()->addDays(rand(1, 14));
            
            $threshold = match($alertType) {
                'low_stock' => rand(5, 15),
                'out_of_stock' => 0,
                'overstock' => rand(80, 120),
                'price_change' => 0,
                'supplier_issue' => 0,
                default => 10,
            };
            
            $currentStock = match($alertType) {
                'low_stock' => rand(0, $threshold),
                'out_of_stock' => 0,
                'overstock' => rand($threshold + 10, $threshold + 50),
                default => rand(0, 50),
            };

            InventoryAlert::create([
                'component_id' => $component->id,
                'type' => $alertType,
                'current_stock' => $currentStock,
                'threshold' => $threshold,
                'is_resolved' => true,
                'resolved_at' => $resolvedAt,
                'metadata' => $this->getAlertMetadata($alertType, $currentStock, $threshold),
                'created_at' => $createdAt,
                'updated_at' => $resolvedAt,
            ]);
        }
    }

    private function getRandomAlertType(): string
    {
        $types = [
            'low_stock',
            'out_of_stock',
            'overstock',
            'price_change',
            'supplier_issue',
            'quality_issue',
            'demand_spike',
        ];

        return $types[array_rand($types)];
    }

    private function getAlertMetadata(string $type, int $currentStock, int $threshold): array
    {
        $baseMetadata = [
            'alert_generated_by' => 'system',
            'notification_sent' => true,
            'priority' => $this->getAlertPriority($type),
        ];

        return match($type) {
            'low_stock' => array_merge($baseMetadata, [
                'alert_level' => $currentStock <= 5 ? 'critical' : 'warning',
                'reorder_point' => $threshold + 10,
                'suggested_quantity' => rand(30, 100),
                'supplier' => $this->getRandomSupplier(),
                'lead_time_days' => rand(3, 14),
            ]),
            'out_of_stock' => array_merge($baseMetadata, [
                'alert_level' => 'critical',
                'days_out_of_stock' => rand(1, 30),
                'lost_sales_estimate' => rand(10, 100),
                'backorder_count' => rand(0, 25),
                'supplier' => $this->getRandomSupplier(),
            ]),
            'overstock' => array_merge($baseMetadata, [
                'alert_level' => 'info',
                'excess_quantity' => $currentStock - $threshold,
                'storage_cost_impact' => rand(200, 2000),
                'suggested_action' => $this->getOverstockAction(),
                'seasonal_factor' => $this->getSeasonalFactor(),
            ]),
            'price_change' => array_merge($baseMetadata, [
                'alert_level' => 'info',
                'price_change_percentage' => rand(-30, 30),
                'previous_price' => rand(1000, 50000),
                'new_price' => rand(1000, 50000),
                'reason' => $this->getPriceChangeReason(),
            ]),
            'supplier_issue' => array_merge($baseMetadata, [
                'alert_level' => 'warning',
                'supplier' => $this->getRandomSupplier(),
                'issue_type' => $this->getSupplierIssueType(),
                'estimated_resolution_days' => rand(3, 21),
                'alternative_suppliers' => rand(1, 3),
            ]),
            'quality_issue' => array_merge($baseMetadata, [
                'alert_level' => 'critical',
                'defect_rate' => rand(5, 25) . '%',
                'affected_batch' => 'BATCH_' . rand(1000, 9999),
                'customer_complaints' => rand(1, 10),
                'action_required' => 'Quality review and potential recall',
            ]),
            'demand_spike' => array_merge($baseMetadata, [
                'alert_level' => 'info',
                'demand_increase_percentage' => rand(50, 200),
                'trigger_event' => $this->getDemandTrigger(),
                'projected_duration_days' => rand(7, 30),
                'recommended_stock_increase' => rand(20, 100),
            ]),
            default => $baseMetadata,
        };
    }

    private function getAlertPriority(string $type): string
    {
        return match($type) {
            'out_of_stock', 'quality_issue' => 'high',
            'low_stock', 'supplier_issue' => 'medium',
            'overstock', 'price_change', 'demand_spike' => 'low',
            default => 'medium',
        };
    }

    private function getRandomSupplier(): string
    {
        $suppliers = [
            'TechDistributor Ltd',
            'ComponentHub India',
            'ElectroSupply Co',
            'DigitalParts Inc',
            'HardwareSource',
            'TechMart Wholesale',
            'ComponentDirect',
            'EliteSuppliers',
        ];

        return $suppliers[array_rand($suppliers)];
    }

    private function getSeasonalFactor(): string
    {
        $factors = [
            'Back to school season',
            'Holiday shopping',
            'New product launch',
            'End of financial year',
            'Summer sale period',
            'Festival season',
            'Regular demand',
        ];

        return $factors[array_rand($factors)];
    }

    private function getOverstockAction(): string
    {
        $actions = [
            'Consider promotional pricing',
            'Bundle with other products',
            'Return to supplier',
            'Liquidation sale',
            'Transfer to other location',
            'Hold for seasonal demand',
        ];

        return $actions[array_rand($actions)];
    }

    private function getPriceChangeReason(): string
    {
        $reasons = [
            'Supplier price increase',
            'Currency fluctuation',
            'Market demand change',
            'Competitor pricing',
            'Promotional pricing',
            'Cost optimization',
            'New product version',
        ];

        return $reasons[array_rand($reasons)];
    }

    private function getSupplierIssueType(): string
    {
        $issues = [
            'Delivery delay',
            'Quality problems',
            'Price increase',
            'Stock shortage',
            'Communication issues',
            'Contract dispute',
            'Logistics problems',
        ];

        return $issues[array_rand($issues)];
    }

    private function getDemandTrigger(): string
    {
        $triggers = [
            'New game release',
            'Cryptocurrency mining boom',
            'Work from home trend',
            'Competitor stock out',
            'Influencer recommendation',
            'Seasonal demand',
            'Product review viral',
        ];

        return $triggers[array_rand($triggers)];
    }
}