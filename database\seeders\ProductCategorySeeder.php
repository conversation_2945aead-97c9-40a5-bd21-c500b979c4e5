<?php

namespace Database\Seeders;

use App\Models\ProductCategory;
use Illuminate\Database\Seeder;

class ProductCategorySeeder extends Seeder
{
    public function run(): void
    {
        $categories = [
            [
                'name' => 'Electronics',
                'description' => 'Electronic devices and accessories',
                'children' => [
                    'Smartphones',
                    'Laptops',
                    'Tablets',
                    'Accessories',
                ]
            ],
            [
                'name' => 'Clothing',
                'description' => 'Fashion and apparel',
                'children' => [
                    'Men\'s Clothing',
                    'Women\'s Clothing',
                    'Kids\' Clothing',
                    'Shoes',
                ]
            ],
            [
                'name' => 'Home & Garden',
                'description' => 'Home improvement and garden supplies',
                'children' => [
                    'Furniture',
                    'Kitchen',
                    'Garden Tools',
                    'Decor',
                ]
            ],
            [
                'name' => 'Books',
                'description' => 'Books and educational materials',
                'children' => [
                    'Fiction',
                    'Non-Fiction',
                    'Educational',
                    'Children\'s Books',
                ]
            ],
            [
                'name' => 'Sports & Outdoors',
                'description' => 'Sports equipment and outdoor gear',
                'children' => [
                    'Fitness Equipment',
                    'Outdoor Gear',
                    'Sports Accessories',
                    'Team Sports',
                ]
            ],
        ];

        foreach ($categories as $index => $categoryData) {
            $parent = ProductCategory::create([
                'name' => $categoryData['name'],
                'description' => $categoryData['description'],
                'sort_order' => $index + 1,
                'is_active' => true,
            ]);

            foreach ($categoryData['children'] as $childIndex => $childName) {
                ProductCategory::create([
                    'name' => $childName,
                    'description' => "Products in {$childName} category",
                    'parent_id' => $parent->id,
                    'sort_order' => $childIndex + 1,
                    'is_active' => true,
                ]);
            }
        }
    }
}