<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "Testing cart functionality...\n";

// Create a user and cart
$user = App\Models\User::factory()->create();
$cart = App\Models\Cart::factory()->create(['user_id' => $user->id, 'total' => 0]);

echo "Created cart for user: {$user->name}\n";

// Create some components
$components = App\Models\Component::factory()->count(3)->create();
echo "Created 3 components\n";

// Add components to cart
foreach ($components as $component) {
    $cart->addItem($component, rand(1, 3));
    echo "Added {$component->name} to cart\n";
}

// Refresh cart and display total
$cart->refresh();
echo "Cart total after adding items: \${$cart->total}\n";
echo "Cart has {$cart->items->count()} items\n";

// Test creating order from cart
$orderData = [
    'billing_name' => $user->name,
    'billing_email' => $user->email,
    'billing_phone' => '555-1234',
    'billing_address' => '123 Main St',
    'billing_city' => 'Anytown',
    'billing_state' => 'CA',
    'billing_zipcode' => '12345',
    'billing_country' => 'US',
    'shipping_name' => $user->name,
    'shipping_email' => $user->email,
    'shipping_phone' => '555-1234',
    'shipping_address' => '123 Main St',
    'shipping_city' => 'Anytown',
    'shipping_state' => 'CA',
    'shipping_zipcode' => '12345',
    'shipping_country' => 'US',
    'shipping' => 10.00,
    'tax' => $cart->total * 0.08,
];

$order = App\Models\Order::createFromCart($cart, $orderData);
echo "Created order: {$order->order_number} with total: \${$order->total}\n";
echo "Order has {$order->items->count()} items\n";

// Test payment processing
$paymentData = [
    'payment_method' => App\Models\Payment::METHOD_CREDIT_CARD,
    'card_number' => '****************',
    'card_expiry' => '12/25',
    'card_cvv' => '123',
];

$payment = App\Models\Payment::processPayment($order, $paymentData);
echo "Processed payment: {$payment->transaction_id} for \${$payment->amount}\n";

// Refresh order to see updated status
$order->refresh();
echo "Order status after payment: {$order->status}\n";

echo "Cart functionality test completed!\n";