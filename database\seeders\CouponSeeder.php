<?php

namespace Database\Seeders;

use App\Models\Coupon;
use Illuminate\Database\Seeder;

class CouponSeeder extends Seeder
{
    public function run(): void
    {
        $coupons = [
            [
                'code' => 'WELCOME10',
                'name' => 'Welcome Discount',
                'description' => '10% off for new customers',
                'type' => 'percentage',
                'value' => 10,
                'minimum_amount' => 500,
                'maximum_discount' => 200,
                'usage_limit' => 1000,
                'usage_limit_per_user' => 1,
                'expires_at' => now()->addMonths(6),
                'is_active' => true,
            ],
            [
                'code' => 'SAVE50',
                'name' => 'Fixed Discount',
                'description' => '₹50 off on orders above ₹300',
                'type' => 'fixed',
                'value' => 50,
                'minimum_amount' => 300,
                'usage_limit' => 500,
                'expires_at' => now()->addMonths(3),
                'is_active' => true,
            ],
            [
                'code' => 'BIGDEAL',
                'name' => 'Big Deal',
                'description' => '25% off on all electronics',
                'type' => 'percentage',
                'value' => 25,
                'minimum_amount' => 1000,
                'maximum_discount' => 1000,
                'usage_limit' => 200,
                'usage_limit_per_user' => 2,
                'expires_at' => now()->addMonth(),
                'is_active' => true,
                'applicable_categories' => [1], // Electronics category
            ],
            [
                'code' => 'FREESHIP',
                'name' => 'Free Shipping',
                'description' => '₹100 off (equivalent to free shipping)',
                'type' => 'fixed',
                'value' => 100,
                'minimum_amount' => 999,
                'usage_limit' => null,
                'usage_limit_per_user' => 5,
                'expires_at' => now()->addMonths(12),
                'is_active' => true,
            ],
            [
                'code' => 'EXPIRED',
                'name' => 'Expired Coupon',
                'description' => 'This coupon has expired',
                'type' => 'percentage',
                'value' => 20,
                'expires_at' => now()->subDays(10),
                'is_active' => false,
            ],
        ];

        foreach ($coupons as $couponData) {
            Coupon::create($couponData);
        }

        // Create some additional random coupons
        Coupon::factory(10)->create();
    }
}