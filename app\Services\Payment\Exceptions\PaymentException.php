<?php

namespace App\Services\Payment\Exceptions;

use Exception;

abstract class PaymentException extends Exception
{
    protected string $errorCode;
    protected array $context = [];

    public function __construct(string $message = '', string $errorCode = '', array $context = [], int $code = 0, ?Exception $previous = null)
    {
        parent::__construct($message, $code, $previous);
        $this->errorCode = $errorCode;
        $this->context = $context;
    }

    public function getErrorCode(): string
    {
        return $this->errorCode;
    }

    public function getContext(): array
    {
        return $this->context;
    }

    public function toArray(): array
    {
        return [
            'success' => false,
            'error' => [
                'code' => $this->errorCode,
                'message' => $this->getMessage(),
                'context' => $this->context,
            ],
        ];
    }
}