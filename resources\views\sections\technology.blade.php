<!-- Technology Showcase Section -->
<section class="py-20 relative overflow-hidden">
    <div class="absolute inset-0 overflow-hidden">
        <div class="absolute bottom-40 right-20 w-96 h-96 bg-nexus-primary-500 rounded-full opacity-5 floating" style="animation-delay: 1.5s;"></div>
        <div class="absolute top-40 left-20 w-80 h-80 bg-nexus-accent-500 rounded-full opacity-5 floating" style="animation-delay: 2.5s;"></div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div class="text-center mb-16">
            <h2 class="tech-font text-4xl font-bold mb-4 text-nexus-primary-400">CUTTING-EDGE TECHNOLOGY</h2>
            <p class="text-nexus-gray-400 max-w-3xl mx-auto">Our systems are built with the latest technologies to ensure maximum performance, reliability, and future-proofing.</p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <!-- Technology 1: DDR5 Memory -->
            <div class="tech-card p-6 rounded-xl bg-gradient-to-br from-nexus-dark-800 to-nexus-dark-900 border border-nexus-border-blue-light hover:border-nexus-border-blue-strong transition-all duration-300 hover:shadow-glow-blue group">
                <div class="w-16 h-16 bg-nexus-bg-blue-medium rounded-lg flex items-center justify-center mb-6 group-hover:bg-nexus-bg-blue-medium transition-all duration-300">
                    <svg class="w-10 h-10 text-nexus-primary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z"></path>
                    </svg>
                </div>
                <h3 class="tech-font text-xl font-bold mb-3 text-white">DDR5 Memory</h3>
                <p class="text-nexus-gray-400 mb-4">Next-gen memory with higher bandwidth, improved power efficiency, and enhanced stability for multitasking.</p>
                <div class="text-sm text-nexus-primary-400 tech-font flex items-center">
                    <span>UP TO 6000MHz</span>
                    <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
                    </svg>
                </div>
            </div>

            <!-- Technology 2: PCIe Gen 5 -->
            <div class="tech-card p-6 rounded-xl bg-gradient-to-br from-nexus-dark-800 to-nexus-dark-900 border border-nexus-border-green-light hover:border-nexus-border-green-strong transition-all duration-300 hover:shadow-glow-green group">
                <div class="w-16 h-16 bg-nexus-bg-green-medium rounded-lg flex items-center justify-center mb-6 group-hover:bg-nexus-bg-green-medium transition-all duration-300">
                    <svg class="w-10 h-10 text-nexus-success-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                    </svg>
                </div>
                <h3 class="tech-font text-xl font-bold mb-3 text-white">PCIe Gen 5</h3>
                <p class="text-nexus-gray-400 mb-4">Double the bandwidth of PCIe 4.0, enabling faster data transfer for GPUs, SSDs, and networking components.</p>
                <div class="text-sm text-nexus-success-400 tech-font flex items-center">
                    <span>128 GB/s BANDWIDTH</span>
                    <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
                    </svg>
                </div>
            </div>

            <!-- Technology 3: AI Acceleration -->
            <div class="tech-card p-6 rounded-xl bg-gradient-to-br from-nexus-dark-800 to-nexus-dark-900 border border-nexus-border-purple-light hover:border-nexus-border-purple-strong transition-all duration-300 hover:shadow-glow-purple group">
                <div class="w-16 h-16 bg-nexus-bg-purple-medium rounded-lg flex items-center justify-center mb-6 group-hover:bg-nexus-bg-purple-medium transition-all duration-300">
                    <svg class="w-10 h-10 text-nexus-accent-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                    </svg>
                </div>
                <h3 class="tech-font text-xl font-bold mb-3 text-white">AI Acceleration</h3>
                <p class="text-nexus-gray-400 mb-4">Dedicated AI processing units for faster machine learning tasks, content creation, and real-time image enhancement.</p>
                <div class="text-sm text-nexus-accent-400 tech-font flex items-center">
                    <span>TENSOR CORES</span>
                    <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
                    </svg>
                </div>
            </div>

            <!-- Technology 4: Ray Tracing -->
            <div class="tech-card p-6 rounded-xl bg-gradient-to-br from-nexus-dark-800 to-nexus-dark-900 border border-nexus-border-cyan-light hover:border-nexus-border-cyan-strong transition-all duration-300 hover:shadow-glow-cyan group">
                <div class="w-16 h-16 bg-nexus-bg-cyan-medium rounded-lg flex items-center justify-center mb-6 group-hover:bg-nexus-bg-cyan-medium transition-all duration-300">
                    <svg class="w-10 h-10 text-nexus-secondary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                    </svg>
                </div>
                <h3 class="tech-font text-xl font-bold mb-3 text-white">Ray Tracing</h3>
                <p class="text-nexus-gray-400 mb-4">Advanced lighting simulation technology for photorealistic reflections, shadows, and global illumination in games.</p>
                <div class="text-sm text-nexus-secondary-400 tech-font flex items-center">
                    <span>REAL-TIME RENDERING</span>
                    <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
                    </svg>
                </div>
            </div>
        </div>
    </div>
</section>