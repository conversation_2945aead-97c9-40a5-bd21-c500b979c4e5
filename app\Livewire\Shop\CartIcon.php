<?php

namespace App\Livewire\Shop;

use App\Services\CartService;
use Livewire\Component;

class CartIcon extends Component
{
    public $itemCount = 0;
    public $total = 0;
    public $showDropdown = false;
    public $recentItems = [];
    
    protected $listeners = [
        'cartUpdated' => 'refreshCartData',
        'cartRefreshed' => 'handleCartRefresh',
        'itemAdded' => 'refreshCartData',
        'itemRemoved' => 'refreshCartData',
        'cartCleared' => 'refreshCartData',
    ];
    
    public function mount()
    {
        $this->refreshCartData();
    }
    
    public function refreshCartData()
    {
        $cartService = app(CartService::class);
        $this->itemCount = $cartService->getItemCount();
        $this->total = $cartService->getTotal();
        
        // Get recent items for dropdown preview
        $cart = $cartService->getCart();
        if ($cart) {
            $this->recentItems = $cart->items()
                ->with('component')
                ->latest()
                ->take(3)
                ->get();
        } else {
            $this->recentItems = collect();
        }
    }
    
    public function handleCartRefresh($data)
    {
        $this->itemCount = $data['itemCount'] ?? 0;
        $this->total = $data['total'] ?? 0;
        $this->refreshCartData();
    }
    
    public function toggleDropdown()
    {
        $this->showDropdown = !$this->showDropdown;
        
        if ($this->showDropdown) {
            $this->refreshCartData();
        }
    }
    
    public function hideDropdown()
    {
        $this->showDropdown = false;
    }
    

    
    public function quickRemoveItem($itemId)
    {
        try {
            $cartService = app(CartService::class);
            $cartService->removeFromCart($itemId);
            
            $this->refreshCartData();
            $this->dispatch('cartUpdated');
            $this->dispatch('itemRemoved');
            
            session()->flash('message', 'Item removed from cart!');
        } catch (\Exception $e) {
            session()->flash('error', 'Failed to remove item from cart.');
        }
    }
    
    public function addToCart($componentId, $quantity = 1)
    {
        try {
            $cartService = app(CartService::class);
            $component = \App\Models\Component::findOrFail($componentId);
            
            $cartService->addToCart($component, $quantity);
            
            $this->refreshCartData();
            $this->dispatch('cartUpdated');
            $this->dispatch('itemAdded');
            
            session()->flash('message', 'Item added to cart!');
        } catch (\Exception $e) {
            session()->flash('error', $e->getMessage());
        }
    }
    
    public function render()
    {
        return view('livewire.shop.cart-icon');
    }
}