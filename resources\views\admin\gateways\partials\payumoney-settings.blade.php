<div class="space-y-6">
    <div class="bg-orange-50 border border-orange-200 rounded-lg p-4">
        <div class="flex">
            <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-orange-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                </svg>
            </div>
            <div class="ml-3">
                <h3 class="text-sm font-medium text-orange-800">PayUmoney Configuration</h3>
                <div class="mt-2 text-sm text-orange-700">
                    <p>Get your credentials from the <a href="https://www.payumoney.com/merchant/login" target="_blank" class="underline">PayUmoney Merchant Dashboard</a>.</p>
                    <p class="mt-1">Success URL: <code class="bg-orange-100 px-1 rounded">{{ url('/webhooks/payumoney/success') }}</code></p>
                    <p class="mt-1">Failure URL: <code class="bg-orange-100 px-1 rounded">{{ url('/webhooks/payumoney/failure') }}</code></p>
                </div>
            </div>
        </div>
    </div>

    <!-- Merchant Key -->
    <div>
        <label for="merchant_key" class="block text-sm font-medium text-gray-700 mb-2">
            Merchant Key <span class="text-red-500">*</span>
        </label>
        <input type="text" 
               id="merchant_key" 
               name="settings[merchant_key]" 
               value="{{ old('settings.merchant_key', $settings['merchant_key'] ?? '') }}"
               placeholder="Enter your PayUmoney Merchant Key"
               class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 @error('settings.merchant_key') border-red-500 @enderror"
               required>
        @error('settings.merchant_key')
            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
        @enderror
        <p class="mt-1 text-sm text-gray-500">Your PayUmoney Merchant Key from the dashboard</p>
    </div>

    <!-- Salt -->
    <div>
        <label for="salt" class="block text-sm font-medium text-gray-700 mb-2">
            Salt <span class="text-red-500">*</span>
        </label>
        <div class="relative">
            <input type="password" 
                   id="salt" 
                   name="settings[salt]" 
                   value="{{ old('settings.salt', $settings['salt'] ?? '') }}"
                   placeholder="Enter your PayUmoney Salt"
                   class="w-full px-3 py-2 pr-10 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 @error('settings.salt') border-red-500 @enderror"
                   required>
            <button type="button" 
                    onclick="togglePasswordVisibility('salt')"
                    class="absolute inset-y-0 right-0 pr-3 flex items-center">
                <svg class="h-5 w-5 text-gray-400" id="salt_eye_open" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                </svg>
                <svg class="h-5 w-5 text-gray-400 hidden" id="salt_eye_closed" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                </svg>
            </button>
        </div>
        @error('settings.salt')
            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
        @enderror
        <p class="mt-1 text-sm text-gray-500">Your PayUmoney Salt for hash generation (keep this secure)</p>
    </div>

    <!-- Auth Header -->
    <div>
        <label for="auth_header" class="block text-sm font-medium text-gray-700 mb-2">
            Authorization Header
        </label>
        <div class="relative">
            <input type="password" 
                   id="auth_header" 
                   name="settings[auth_header]" 
                   value="{{ old('settings.auth_header', $settings['auth_header'] ?? '') }}"
                   placeholder="Enter your PayUmoney Authorization Header (optional)"
                   class="w-full px-3 py-2 pr-10 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 @error('settings.auth_header') border-red-500 @enderror">
            <button type="button" 
                    onclick="togglePasswordVisibility('auth_header')"
                    class="absolute inset-y-0 right-0 pr-3 flex items-center">
                <svg class="h-5 w-5 text-gray-400" id="auth_header_eye_open" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                </svg>
                <svg class="h-5 w-5 text-gray-400 hidden" id="auth_header_eye_closed" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                </svg>
            </button>
        </div>
        @error('settings.auth_header')
            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
        @enderror
        <p class="mt-1 text-sm text-gray-500">Optional: Authorization header for API calls (if using API integration)</p>
    </div>

    <!-- Additional Configuration Info -->
    <div class="bg-gray-50 border border-gray-200 rounded-lg p-4">
        <h4 class="text-sm font-medium text-gray-900 mb-2">Important Notes:</h4>
        <ul class="text-sm text-gray-600 space-y-1">
            <li>• PayUmoney uses form POST method for payment processing</li>
            <li>• Hash verification is mandatory for security</li>
            <li>• Configure success and failure URLs in your PayUmoney dashboard</li>
            <li>• Test mode uses the same credentials but different endpoints</li>
        </ul>
    </div>
</div>

<script>
function togglePasswordVisibility(fieldId) {
    const field = document.getElementById(fieldId);
    const eyeOpen = document.getElementById(fieldId + '_eye_open');
    const eyeClosed = document.getElementById(fieldId + '_eye_closed');
    
    if (field.type === 'password') {
        field.type = 'text';
        eyeOpen.classList.add('hidden');
        eyeClosed.classList.remove('hidden');
    } else {
        field.type = 'password';
        eyeOpen.classList.remove('hidden');
        eyeClosed.classList.add('hidden');
    }
}
</script>