<?php

use App\Models\BlogPostTag;
use App\Models\BlogPost;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;

uses(RefreshDatabase::class, WithFaker::class);

// php artisan test tests/Feature/Controllers/Admin/BlogTagControllerTest.php

/*
|--------------------------------------------------------------------------
| Index Tests
|--------------------------------------------------------------------------
*/

test('admin can view blog tags index page', function () {
    $admin = User::factory()->create(['role' => 'admin']);
    $this->actingAs($admin);

    $tags = BlogPostTag::factory()->count(5)->create();

    $response = $this->get(route('admin.blog.tags.index'));

    $response->assertStatus(200);
    $response->assertViewIs('admin.blog.tags.index');
    $response->assertViewHas('tags');
    
    $viewTags = $response->viewData('tags');
    expect($viewTags)->toHaveCount(5);
});

test('blog tags are ordered by name', function () {
    $admin = User::factory()->create(['role' => 'admin']);
    $this->actingAs($admin);

    // Create tags with different names
    $tagC = BlogPostTag::factory()->create(['name' => 'C Tag']);
    $tagA = BlogPostTag::factory()->create(['name' => 'A Tag']);
    $tagB = BlogPostTag::factory()->create(['name' => 'B Tag']);

    $response = $this->get(route('admin.blog.tags.index'));

    $response->assertStatus(200);
    
    $viewTags = $response->viewData('tags');
    expect($viewTags[0]->id)->toBe($tagA->id);
    expect($viewTags[1]->id)->toBe($tagB->id);
    expect($viewTags[2]->id)->toBe($tagC->id);
});

test('blog tags show post count', function () {
    $admin = User::factory()->create(['role' => 'admin']);
    $this->actingAs($admin);

    $tag = BlogPostTag::factory()->create();
    $post = BlogPost::factory()->create();
    $post->tags()->attach($tag->id);

    $response = $this->get(route('admin.blog.tags.index'));

    $response->assertStatus(200);
    
    $viewTags = $response->viewData('tags');
    expect($viewTags->first()->posts_count)->toBe(1);
});

test('non-admin users cannot access blog tags index', function () {
    $user = User::factory()->create(['role' => 'user']);
    $this->actingAs($user);

    $response = $this->get(route('admin.blog.tags.index'));

    $response->assertStatus(403);
});

test('guest users cannot access blog tags index', function () {
    $response = $this->get(route('admin.blog.tags.index'));

    $response->assertRedirect(route('admin.login'));
});

/*
|--------------------------------------------------------------------------
| Create Tests
|--------------------------------------------------------------------------
*/

test('admin can view create blog tag form', function () {
    $admin = User::factory()->create(['role' => 'admin']);
    $this->actingAs($admin);

    $response = $this->get(route('admin.blog.tags.create'));

    $response->assertStatus(200);
    $response->assertViewIs('admin.blog.tags.create');
});

test('admin can create a new blog tag', function () {
    $admin = User::factory()->create(['role' => 'admin']);
    $this->actingAs($admin);

    $tagData = [
        'name' => 'Technology',
        'description' => 'Technology related posts',
    ];

    $response = $this->post(route('admin.blog.tags.store'), $tagData);

    $response->assertRedirect(route('admin.blog.tags.index'));
    $response->assertSessionHas('success', 'Blog tag created successfully.');

    $this->assertDatabaseHas('blog_post_tags', [
        'name' => 'Technology',
        'description' => 'Technology related posts',
    ]);
});

test('blog tag creation uses default description when not provided', function () {
    $admin = User::factory()->create(['role' => 'admin']);
    $this->actingAs($admin);

    $tagData = [
        'name' => 'Default Tag',
    ];

    $response = $this->post(route('admin.blog.tags.store'), $tagData);

    $response->assertRedirect(route('admin.blog.tags.index'));

    $this->assertDatabaseHas('blog_post_tags', [
        'name' => 'Default Tag',
        'description' => null,
    ]);
});

test('blog tag creation validates required fields', function () {
    $admin = User::factory()->create(['role' => 'admin']);
    $this->actingAs($admin);

    $response = $this->post(route('admin.blog.tags.store'), []);

    $response->assertSessionHasErrors(['name']);
});

test('blog tag creation validates name uniqueness', function () {
    $admin = User::factory()->create(['role' => 'admin']);
    $this->actingAs($admin);

    // Create first tag
    BlogPostTag::factory()->create(['name' => 'Technology']);

    // Try to create second tag with same name
    $response = $this->post(route('admin.blog.tags.store'), [
        'name' => 'Technology',
        'description' => 'Duplicate tag',
    ]);

    $response->assertSessionHasErrors(['name']);
});

test('blog tag creation validates name length', function () {
    $admin = User::factory()->create(['role' => 'admin']);
    $this->actingAs($admin);

    $response = $this->post(route('admin.blog.tags.store'), [
        'name' => str_repeat('a', 256), // Exceeds 255 character limit
        'description' => 'Test description',
    ]);

    $response->assertSessionHasErrors(['name']);
});

/*
|--------------------------------------------------------------------------
| Edit Tests
|--------------------------------------------------------------------------
*/

test('admin can view edit blog tag form', function () {
    $admin = User::factory()->create(['role' => 'admin']);
    $this->actingAs($admin);

    $tag = BlogPostTag::factory()->create();

    $response = $this->get(route('admin.blog.tags.edit', $tag));

    $response->assertStatus(200);
    $response->assertViewIs('admin.blog.tags.edit');
    $response->assertViewHas('tag', $tag);
});

test('admin can update blog tag', function () {
    $admin = User::factory()->create(['role' => 'admin']);
    $this->actingAs($admin);

    $tag = BlogPostTag::factory()->create([
        'name' => 'Old Name',
        'description' => 'Old description',
    ]);

    $updateData = [
        'name' => 'Updated Name',
        'description' => 'Updated description',
    ];

    $response = $this->put(route('admin.blog.tags.update', $tag), $updateData);

    $response->assertRedirect(route('admin.blog.tags.index'));
    $response->assertSessionHas('success', 'Blog tag updated successfully.');

    $this->assertDatabaseHas('blog_post_tags', [
        'id' => $tag->id,
        'name' => 'Updated Name',
        'description' => 'Updated description',
    ]);
});

test('blog tag update validates name uniqueness excluding current tag', function () {
    $admin = User::factory()->create(['role' => 'admin']);
    $this->actingAs($admin);

    $tag1 = BlogPostTag::factory()->create(['name' => 'Tag One']);
    $tag2 = BlogPostTag::factory()->create(['name' => 'Tag Two']);

    // Try to update tag2 with tag1's name
    $response = $this->put(route('admin.blog.tags.update', $tag2), [
        'name' => 'Tag One',
        'description' => 'Updated description',
    ]);

    $response->assertSessionHasErrors(['name']);
});

test('blog tag update allows same name for same tag', function () {
    $admin = User::factory()->create(['role' => 'admin']);
    $this->actingAs($admin);

    $tag = BlogPostTag::factory()->create([
        'name' => 'Test Tag',
        'description' => 'Original description',
    ]);

    $response = $this->put(route('admin.blog.tags.update', $tag), [
        'name' => 'Test Tag', // Same name
        'description' => 'Updated description',
    ]);

    $response->assertRedirect(route('admin.blog.tags.index'));
    $response->assertSessionHas('success', 'Blog tag updated successfully.');
});

/*
|--------------------------------------------------------------------------
| Delete Tests
|--------------------------------------------------------------------------
*/

test('admin can delete blog tag without posts', function () {
    $admin = User::factory()->create(['role' => 'admin']);
    $this->actingAs($admin);

    $tag = BlogPostTag::factory()->create();

    $response = $this->delete(route('admin.blog.tags.destroy', $tag));

    $response->assertRedirect(route('admin.blog.tags.index'));
    $response->assertSessionHas('success', 'Blog tag deleted successfully.');

    $this->assertDatabaseMissing('blog_post_tags', [
        'id' => $tag->id,
    ]);
});

test('admin cannot delete blog tag with associated posts', function () {
    $admin = User::factory()->create(['role' => 'admin']);
    $this->actingAs($admin);

    $tag = BlogPostTag::factory()->create();
    $post = BlogPost::factory()->create();
    $post->tags()->attach($tag->id);

    $response = $this->delete(route('admin.blog.tags.destroy', $tag));

    $response->assertRedirect(route('admin.blog.tags.index'));
    $response->assertSessionHas('error', 'Cannot delete tag with associated posts.');

    $this->assertDatabaseHas('blog_post_tags', [
        'id' => $tag->id,
    ]);
});

test('deleting tag with multiple posts is prevented', function () {
    $admin = User::factory()->create(['role' => 'admin']);
    $this->actingAs($admin);

    $tag = BlogPostTag::factory()->create();
    $posts = BlogPost::factory()->count(5)->create();
    
    foreach ($posts as $post) {
        $post->tags()->attach($tag->id);
    }

    $response = $this->delete(route('admin.blog.tags.destroy', $tag));

    $response->assertRedirect(route('admin.blog.tags.index'));
    $response->assertSessionHas('error', 'Cannot delete tag with associated posts.');

    $this->assertDatabaseHas('blog_post_tags', [
        'id' => $tag->id,
    ]);
});

/*
|--------------------------------------------------------------------------
| API Tests
|--------------------------------------------------------------------------
*/

test('api index returns tags for select2', function () {
    $admin = User::factory()->create(['role' => 'admin']);
    $this->actingAs($admin);

    $tags = BlogPostTag::factory()->count(3)->create();

    $response = $this->get(route('admin.blog.tags.api'));

    $response->assertStatus(200);
    $response->assertJsonCount(3);
    
    $responseData = $response->json();
    expect($responseData[0])->toHaveKeys(['id', 'name']);
});

test('api index respects search parameter', function () {
    $admin = User::factory()->create(['role' => 'admin']);
    $this->actingAs($admin);

    BlogPostTag::factory()->create(['name' => 'Technology']);
    BlogPostTag::factory()->create(['name' => 'Design']);
    BlogPostTag::factory()->create(['name' => 'Marketing']);

    $response = $this->get(route('admin.blog.tags.api', ['search' => 'tech']));

    $response->assertStatus(200);
    $response->assertJsonCount(1);
    
    $responseData = $response->json();
    expect($responseData[0]['name'])->toBe('Technology');
});

test('api index returns empty array for no search results', function () {
    $admin = User::factory()->create(['role' => 'admin']);
    $this->actingAs($admin);

    BlogPostTag::factory()->create(['name' => 'Technology']);

    $response = $this->get(route('admin.blog.tags.api', ['search' => 'nonexistent']));

    $response->assertStatus(200);
    $response->assertJsonCount(0);
});

test('api index orders by name', function () {
    $admin = User::factory()->create(['role' => 'admin']);
    $this->actingAs($admin);

    $tagC = BlogPostTag::factory()->create(['name' => 'C Tag']);
    $tagA = BlogPostTag::factory()->create(['name' => 'A Tag']);
    $tagB = BlogPostTag::factory()->create(['name' => 'B Tag']);

    $response = $this->get(route('admin.blog.tags.api'));

    $response->assertStatus(200);
    
    $responseData = $response->json();
    expect($responseData[0]['id'])->toBe($tagA->id);
    expect($responseData[1]['id'])->toBe($tagB->id);
    expect($responseData[2]['id'])->toBe($tagC->id);
});

/*
|--------------------------------------------------------------------------
| Authorization Tests
|--------------------------------------------------------------------------
*/

test('non-admin users cannot access create form', function () {
    $user = User::factory()->create(['role' => 'user']);
    $this->actingAs($user);

    $response = $this->get(route('admin.blog.tags.create'));

    $response->assertStatus(403);
});

test('non-admin users cannot create blog tags', function () {
    $user = User::factory()->create(['role' => 'user']);
    $this->actingAs($user);

    $response = $this->post(route('admin.blog.tags.store'), [
        'name' => 'Test Tag',
    ]);

    $response->assertStatus(403);
});

test('non-admin users cannot access edit form', function () {
    $user = User::factory()->create(['role' => 'user']);
    $this->actingAs($user);

    $tag = BlogPostTag::factory()->create();

    $response = $this->get(route('admin.blog.tags.edit', $tag));

    $response->assertStatus(403);
});

test('non-admin users cannot update blog tags', function () {
    $user = User::factory()->create(['role' => 'user']);
    $this->actingAs($user);

    $tag = BlogPostTag::factory()->create();

    $response = $this->put(route('admin.blog.tags.update', $tag), [
        'name' => 'Updated Tag',
    ]);

    $response->assertStatus(403);
});

test('non-admin users cannot delete blog tags', function () {
    $user = User::factory()->create(['role' => 'user']);
    $this->actingAs($user);

    $tag = BlogPostTag::factory()->create();

    $response = $this->delete(route('admin.blog.tags.destroy', $tag));

    $response->assertStatus(403);
});

test('non-admin users cannot access api index', function () {
    $user = User::factory()->create(['role' => 'user']);
    $this->actingAs($user);

    $response = $this->get(route('admin.blog.tags.api'));

    $response->assertStatus(403);
});

/*
|--------------------------------------------------------------------------
| Edge Cases and Error Handling
|--------------------------------------------------------------------------
*/

test('handles special characters in tag name', function () {
    $admin = User::factory()->create(['role' => 'admin']);
    $this->actingAs($admin);

    $response = $this->post(route('admin.blog.tags.store'), [
        'name' => 'Special Characters: @#$%^&*()',
        'description' => 'Test description',
    ]);

    $response->assertRedirect(route('admin.blog.tags.index'));

    $this->assertDatabaseHas('blog_post_tags', [
        'name' => 'Special Characters: @#$%^&*()',
        'description' => 'Test description',
    ]);
});

test('handles unicode characters in tag name', function () {
    $admin = User::factory()->create(['role' => 'admin']);
    $this->actingAs($admin);

    $response = $this->post(route('admin.blog.tags.store'), [
        'name' => 'Café & Résumé',
        'description' => 'Unicode test',
    ]);

    $response->assertRedirect(route('admin.blog.tags.index'));

    $this->assertDatabaseHas('blog_post_tags', [
        'name' => 'Café & Résumé',
        'description' => 'Unicode test',
    ]);
});

test('handles very long description', function () {
    $admin = User::factory()->create(['role' => 'admin']);
    $this->actingAs($admin);

    $longDescription = str_repeat('This is a very long description. ', 50);

    $response = $this->post(route('admin.blog.tags.store'), [
        'name' => 'Long Description Test',
        'description' => $longDescription,
    ]);

    $response->assertRedirect(route('admin.blog.tags.index'));

    // Just verify the tag was created successfully
    $this->assertDatabaseHas('blog_post_tags', [
        'name' => 'Long Description Test',
    ]);
    
    // Verify the description was stored (check that it's not empty and contains part of the original text)
    $tag = BlogPostTag::where('name', 'Long Description Test')->first();
    expect($tag)->not->toBeNull();
    expect($tag->description)->not->toBeEmpty();
    expect($tag->description)->toContain('This is a very long description');
});

test('handles empty description', function () {
    $admin = User::factory()->create(['role' => 'admin']);
    $this->actingAs($admin);

    $response = $this->post(route('admin.blog.tags.store'), [
        'name' => 'Empty Description Tag',
        'description' => '',
    ]);

    $response->assertRedirect(route('admin.blog.tags.index'));

    $this->assertDatabaseHas('blog_post_tags', [
        'name' => 'Empty Description Tag',
        'description' => null,
    ]);
});

test('handles null description', function () {
    $admin = User::factory()->create(['role' => 'admin']);
    $this->actingAs($admin);

    $response = $this->post(route('admin.blog.tags.store'), [
        'name' => 'Null Description Tag',
    ]);

    $response->assertRedirect(route('admin.blog.tags.index'));

    $this->assertDatabaseHas('blog_post_tags', [
        'name' => 'Null Description Tag',
        'description' => null,
    ]);
}); 