<?php

namespace App\Livewire\Shop;

use App\Models\Component;
use Livewire\Component as LivewireComponent;

class ProductDetail extends LivewireComponent
{
    public $slug;
    public $component;
    public $quantity = 1;
    public $relatedProducts = [];
    
    public function mount($slug)
    {
        $this->slug = $slug;
        $this->loadComponent();
    }
    
    public function loadComponent()
    {
        $this->component = Component::where('slug', $this->slug)
            ->where('is_active', true)
            ->with(['category', 'reviews.user'])
            ->first();
            
        if (!$this->component) {
            return redirect()->route('shop.index');
        }
        
        // Load related products from the same category
        $this->relatedProducts = Component::where('category_id', $this->component->category_id)
            ->where('id', '!=', $this->component->id)
            ->where('is_active', true)
            ->take(4)
            ->get();
    }
    
    public function incrementQuantity()
    {
        if ($this->quantity < $this->component->stock) {
            $this->quantity++;
        }
    }
    
    public function decrementQuantity()
    {
        if ($this->quantity > 1) {
            $this->quantity--;
        }
    }
    
    public function addToCart()
    {
        if (!$this->component || $this->component->stock <= 0) {
            session()->flash('error', 'Product is out of stock.');
            return;
        }
        
        if ($this->quantity > $this->component->stock) {
            session()->flash('error', 'Not enough stock available.');
            return;
        }
        
        // Add to cart using the CartService
        $cartService = app(\App\Services\CartService::class);
        $cartService->addItem($this->component->id, $this->quantity);
        
        $this->dispatch('cartUpdated');
        session()->flash('message', 'Product added to cart successfully!');
    }
    
    public function render()
    {
        return view('livewire.shop.product-detail');
    }
}