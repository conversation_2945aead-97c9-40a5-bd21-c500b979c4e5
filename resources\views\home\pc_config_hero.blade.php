@extends('layouts.app')
@section('content')
    <div class="bg-gray-50">
        <!-- Hero Section -->
        <section class="relative overflow-hidden bg-gradient-to-br from-white to-gray-50 min-h-screen">
            <!-- Background Elements -->
            <div class="absolute inset-0 opacity-30">
                <div
                    class="absolute top-20 left-20 w-72 h-72 bg-purple-200 rounded-full mix-blend-multiply filter blur-xl floating-animation">
                </div>
                <div class="absolute top-40 right-20 w-72 h-72 bg-blue-200 rounded-full mix-blend-multiply filter blur-xl floating-animation"
                    style="animation-delay: 2s;"></div>
            </div>

            <div class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-20 pb-16">
                <div class="grid lg:grid-cols-2 gap-12 items-center min-h-[80vh]">
                    <!-- Left Content -->
                    <div x-data="{ show: false }" x-init="setTimeout(() => show = true, 200)" class="space-y-8">
                        <!-- Headline -->
                        <div x-show="show" x-transition:enter="transition ease-out duration-1000"
                            x-transition:enter-start="opacity-0 transform translate-y-10"
                            x-transition:enter-end="opacity-100 transform translate-y-0">
                            <h1 class="text-5xl lg:text-6xl font-bold text-gray-900 leading-tight">
                                Build Your
                                <span class="gradient-text">Dream PC</span>
                            </h1>
                        </div>

                        <!-- Tagline -->
                        <div x-show="show" x-transition:enter="transition ease-out duration-1000 delay-200"
                            x-transition:enter-start="opacity-0 transform translate-y-10"
                            x-transition:enter-end="opacity-100 transform translate-y-0">
                            <p class="text-xl text-gray-600 max-w-lg">
                                Configure your perfect gaming rig with our intelligent compatibility checker and expert
                                recommendations.
                            </p>
                        </div>

                        <!-- CTA Buttons -->
                        <div x-show="show" x-transition:enter="transition ease-out duration-1000 delay-400"
                            x-transition:enter-start="opacity-0 transform translate-y-10"
                            x-transition:enter-end="opacity-100 transform translate-y-0"
                            class="flex flex-col sm:flex-row gap-4">
                            <button
                                class="inline-flex items-center px-8 py-4 bg-gradient-to-r from-purple-600 to-blue-600 text-white font-semibold rounded-xl hover:from-purple-700 hover:to-blue-700 transition-all duration-300 transform hover:scale-105 pulse-glow">
                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                </svg>
                                Launch Configurator
                            </button>
                            <button
                                class="inline-flex items-center px-8 py-4 bg-white text-gray-700 font-semibold rounded-xl border-2 border-gray-200 hover:border-purple-300 hover:bg-purple-50 transition-all duration-300">
                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z">
                                    </path>
                                </svg>
                                Popular Builds
                            </button>
                        </div>
                    </div>

                    <!-- Right Side - Configuration Preview -->
                    <div x-data="configPreview()" x-init="setTimeout(() => show = true, 600)" class="relative">
                        <div x-show="show" x-transition:enter="transition ease-out duration-1000"
                            x-transition:enter-start="opacity-0 transform translate-x-10"
                            x-transition:enter-end="opacity-100 transform translate-x-0"
                            class="bg-white rounded-2xl shadow-2xl p-8 border border-gray-100">
                            <!-- Header -->
                            <div class="flex items-center justify-between mb-8">
                                <h3 class="text-2xl font-bold text-gray-900">PC Builder</h3>
                                <div class="flex items-center space-x-2">
                                    <div class="w-3 h-3 bg-red-400 rounded-full"></div>
                                    <div class="w-3 h-3 bg-yellow-400 rounded-full"></div>
                                    <div class="w-3 h-3 bg-green-400 rounded-full"></div>
                                </div>
                            </div>

                            <!-- Progress Bar -->
                            <div class="mb-8">
                                <div class="flex justify-between text-sm text-gray-500 mb-2">
                                    <span>Configuration Progress</span>
                                    <span x-text="Math.round((activeStep / 6) * 100) + '%'"></span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2">
                                    <div class="bg-gradient-to-r from-purple-600 to-blue-600 h-2 rounded-full transition-all duration-500"
                                        :style="'width: ' + (activeStep / 6) * 100 + '%'"></div>
                                </div>
                            </div>

                            <!-- Configuration Steps -->
                            <div class="space-y-4">
                                <template x-for="(step, index) in steps" :key="index">
                                    <div @click="setActiveStep(index + 1)" :class="{ 'active': activeStep >= index + 1 }"
                                        class="step-indicator p-4 rounded-lg cursor-pointer">
                                        <div class="flex items-center justify-between">
                                            <div class="flex items-center space-x-3">
                                                <div class="w-8 h-8 rounded-full flex items-center justify-center text-sm font-semibold"
                                                    :class="activeStep >= index + 1 ? 'bg-white text-purple-600' :
                                                        'bg-gray-200 text-gray-600'">
                                                    <span x-text="index + 1"></span>
                                                </div>
                                                <div>
                                                    <h4 class="font-semibold" x-text="step.title"></h4>
                                                    <p class="text-sm opacity-75" x-text="step.description"></p>
                                                </div>
                                            </div>
                                            <div class="text-right">
                                                <span x-show="activeStep > index + 1" class="text-green-500">
                                                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                                        <path fill-rule="evenodd"
                                                            d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                                            clip-rule="evenodd"></path>
                                                    </svg>
                                                </span>
                                                <span x-show="activeStep === index + 1" class="text-purple-500">
                                                    <svg class="w-5 h-5 animate-pulse" fill="currentColor"
                                                        viewBox="0 0 20 20">
                                                        <path fill-rule="evenodd"
                                                            d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z"
                                                            clip-rule="evenodd"></path>
                                                    </svg>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </template>
                            </div>

                            <!-- Auto-advance simulation -->
                            <div class="mt-6 p-4 bg-gray-50 rounded-lg">
                                <div class="flex items-center justify-between">
                                    <span class="text-sm text-gray-600">Auto-advancing demo</span>
                                    <button @click="toggleAutoAdvance()"
                                        class="text-purple-600 hover:text-purple-700 text-sm font-medium">
                                        <span x-text="autoAdvancing ? 'Pause' : 'Play'"></span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Trust Logos Section -->
        <section class="bg-white py-16">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center mb-12">
                    <p class="text-gray-500 text-sm font-medium uppercase tracking-wider">Trusted by thousands of builders
                    </p>
                </div>
                <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-8 items-center justify-items-center">
                    <!-- Trust Logo Placeholders -->
                    <div class="trust-logo w-24 h-12 bg-gray-200 rounded-lg flex items-center justify-center">
                        <span class="text-gray-500 font-semibold text-sm">Intel</span>
                    </div>
                    <div class="trust-logo w-24 h-12 bg-gray-200 rounded-lg flex items-center justify-center">
                        <span class="text-gray-500 font-semibold text-sm">AMD</span>
                    </div>
                    <div class="trust-logo w-24 h-12 bg-gray-200 rounded-lg flex items-center justify-center">
                        <span class="text-gray-500 font-semibold text-sm">NVIDIA</span>
                    </div>
                    <div class="trust-logo w-24 h-12 bg-gray-200 rounded-lg flex items-center justify-center">
                        <span class="text-gray-500 font-semibold text-sm">ASUS</span>
                    </div>
                    <div class="trust-logo w-24 h-12 bg-gray-200 rounded-lg flex items-center justify-center">
                        <span class="text-gray-500 font-semibold text-sm">MSI</span>
                    </div>
                    <div class="trust-logo w-24 h-12 bg-gray-200 rounded-lg flex items-center justify-center">
                        <span class="text-gray-500 font-semibold text-sm">Corsair</span>
                    </div>
                </div>
            </div>
        </section>
    </div>
@endsection
@push('scripts')
    <script>
        function configPreview() {
            return {
                show: false,
                activeStep: 1,
                autoAdvancing: true,
                intervalId: null,
                steps: [{
                        title: 'Select CPU',
                        description: 'Choose your processor'
                    },
                    {
                        title: 'Pick GPU',
                        description: 'Select graphics card'
                    },
                    {
                        title: 'Choose RAM',
                        description: 'Memory configuration'
                    },
                    {
                        title: 'Storage',
                        description: 'SSD/HDD selection'
                    },
                    {
                        title: 'Motherboard',
                        description: 'Compatible board'
                    },
                    {
                        title: 'Power Supply',
                        description: 'PSU requirements'
                    }
                ],

                init() {
                    this.startAutoAdvance();
                },

                setActiveStep(step) {
                    this.activeStep = step;
                },

                startAutoAdvance() {
                    this.intervalId = setInterval(() => {
                        if (this.autoAdvancing) {
                            this.activeStep = this.activeStep >= 6 ? 1 : this.activeStep + 1;
                        }
                    }, 2000);
                },

                toggleAutoAdvance() {
                    this.autoAdvancing = !this.autoAdvancing;
                }
            }
        }
    </script>
@endpush


@push('styles')
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

        body {
            font-family: 'Inter', sans-serif;
        }

        .gradient-text {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .floating-animation {
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {

            0%,
            100% {
                transform: translateY(0px);
            }

            50% {
                transform: translateY(-10px);
            }
        }

        .pulse-glow {
            animation: pulse-glow 2s ease-in-out infinite alternate;
        }

        @keyframes pulse-glow {
            from {
                box-shadow: 0 0 20px rgba(102, 126, 234, 0.3);
            }

            to {
                box-shadow: 0 0 30px rgba(102, 126, 234, 0.6);
            }
        }

        .step-indicator {
            background: linear-gradient(135deg, #f6f8ff 0%, #e8f0ff 100%);
            border: 2px solid #e2e8f0;
            transition: all 0.3s ease;
        }

        .step-indicator.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-color: #667eea;
            color: white;
        }

        .step-indicator:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.2);
        }

        .trust-logo {
            filter: grayscale(100%);
            opacity: 0.6;
            transition: all 0.3s ease;
        }

        .trust-logo:hover {
            filter: grayscale(0%);
            opacity: 1;
        }
    </style>
@endpush
