<?php

namespace App\Jobs;

use App\Mail\BuildShared;
use App\Models\Build;
use App\Models\User;
use App\Services\EmailTrackingService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class SendBuildSharedEmail implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public $tries = 3;

    /**
     * The maximum number of seconds the job can run.
     *
     * @var int
     */
    public $timeout = 60;

    /**
     * Create a new job instance.
     */
    public function __construct(
        public Build $build,
        public User $sharedBy,
        public string $recipientEmail,
        public ?string $message = null
    ) {
        // Set the queue for email jobs
        $this->onQueue('emails');
    }

    /**
     * Execute the job.
     */
    public function handle(EmailTrackingService $emailTrackingService): void
    {
        $emailLog = null;
        
        try {
            // Check if user has build sharing notifications enabled
            if (!$this->sharedBy->getNotificationSetting('build_shared', true)) {
                Log::info('Build shared email skipped - user disabled notifications', [
                    'build_id' => $this->build->id,
                    'shared_by' => $this->sharedBy->id,
                    'recipient_email' => $this->recipientEmail,
                ]);
                return;
            }

            // Validate email address
            if (!filter_var($this->recipientEmail, FILTER_VALIDATE_EMAIL)) {
                Log::warning('Build shared email skipped - invalid recipient email', [
                    'build_id' => $this->build->id,
                    'shared_by' => $this->sharedBy->id,
                    'recipient_email' => $this->recipientEmail,
                ]);
                return;
            }

            // Log the email attempt
            $emailLog = $emailTrackingService->logEmailAttempt(
                'build_shared',
                $this->recipientEmail,
                $this->sharedBy->name . ' shared a PC build with you: ' . $this->build->name,
                $this->build->id,
                Build::class
            );

            // Update attempt count if this is a retry
            if ($this->attempts() > 1) {
                $emailTrackingService->incrementAttempt($emailLog);
            }

            // Send the email
            Mail::to($this->recipientEmail)
                ->send(new BuildShared($this->build, $this->sharedBy, $this->recipientEmail, $this->message));

            // Mark as sent
            $emailTrackingService->markEmailSent($emailLog);

            Log::info('Build shared email sent successfully', [
                'build_id' => $this->build->id,
                'build_name' => $this->build->name,
                'shared_by' => $this->sharedBy->email,
                'recipient_email' => $this->recipientEmail,
                'email_log_id' => $emailLog->id,
            ]);

        } catch (\Exception $e) {
            // Mark as failed if we have an email log
            if ($emailLog) {
                $emailTrackingService->markEmailFailed($emailLog, $e->getMessage(), $this->attempts());
            }

            Log::error('Failed to send build shared email', [
                'build_id' => $this->build->id,
                'shared_by' => $this->sharedBy->id,
                'recipient_email' => $this->recipientEmail,
                'error' => $e->getMessage(),
                'attempt' => $this->attempts(),
                'email_log_id' => $emailLog?->id,
            ]);

            // Re-throw the exception to trigger retry mechanism
            throw $e;
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('Build shared email job failed permanently', [
            'build_id' => $this->build->id,
            'shared_by' => $this->sharedBy->id,
            'recipient_email' => $this->recipientEmail,
            'error' => $exception->getMessage(),
            'attempts' => $this->attempts(),
        ]);
    }

    /**
     * Calculate the number of seconds to wait before retrying the job.
     */
    public function backoff(): array
    {
        return [30, 120, 300]; // 30 seconds, 2 minutes, 5 minutes
    }
}