<div class="relative" x-data="{ open: <?php if ((object) ('showDropdown') instanceof \Livewire\WireDirective) : ?>window.Livewire.find('<?php echo e($__livewire->getId()); ?>').entangle('<?php echo e('showDropdown'->value()); ?>')<?php echo e('showDropdown'->hasModifier('live') ? '.live' : ''); ?><?php else : ?>window.Livewire.find('<?php echo e($__livewire->getId()); ?>').entangle('<?php echo e('showDropdown'); ?>')<?php endif; ?> }">
    <!-- Cart Icon <PERSON> -->
    <button
        @click="open = !open"
        class="relative p-2 text-nexus-gray-700 dark:text-nexus-gray-300 hover:text-nexus-dark-900 dark:hover:text-white hover:bg-nexus-bg-blue-light dark:hover:bg-nexus-bg-blue-light focus:outline-none focus:ring-2 focus:ring-nexus-primary-500 dark:focus:ring-nexus-primary-600 rounded-md transition-all duration-300 nexus-hover-glow"
        aria-label="Shopping cart"
    >
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z" />
        </svg>

        <!-- Item Count Badge -->
        <!--[if BLOCK]><![endif]--><?php if($itemCount > 0): ?>
            <span class="absolute -top-1 -right-1 bg-nexus-primary-600 dark:bg-nexus-primary-500 text-white text-xs font-bold rounded-full h-5 w-5 flex items-center justify-center min-w-[20px] shadow-glow-blue">
                <?php echo e($itemCount > 99 ? '99+' : $itemCount); ?>

            </span>
        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
    </button>
    
    <!-- Dropdown Menu -->
    <div
        x-show="open"
        x-transition:enter="transition ease-out duration-200"
        x-transition:enter-start="opacity-0 scale-95"
        x-transition:enter-end="opacity-100 scale-100"
        x-transition:leave="transition ease-in duration-150"
        x-transition:leave-start="opacity-100 scale-100"
        x-transition:leave-end="opacity-0 scale-95"
        @click.away="open = false"
        class="absolute right-0 mt-2 w-80 bg-white dark:bg-nexus-dark-800 rounded-lg shadow-lg border border-nexus-gray-200 dark:border-nexus-gray-700 z-50 nexus-border-glow"
        style="display: none;"
    >
        <!--[if BLOCK]><![endif]--><?php if($itemCount === 0): ?>
            <!-- Empty Cart State -->
            <div class="p-6 text-center">
                <div class="mx-auto w-12 h-12 bg-nexus-gray-100 dark:bg-nexus-dark-700 rounded-full flex items-center justify-center mb-3">
                    <svg class="w-6 h-6 text-nexus-gray-400 dark:text-nexus-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z" />
                    </svg>
                </div>
                <p class="text-nexus-gray-500 dark:text-nexus-gray-400 text-sm mb-4">Your cart is empty</p>
                <button
                    @click="open = false; window.location.href = '<?php echo e(route('shop.index')); ?>'"
                    class="inline-flex items-center px-4 py-2 nexus-gradient-primary hover:from-nexus-primary-700 hover:to-nexus-secondary-700 text-white text-sm font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-nexus-primary-500 dark:focus:ring-nexus-primary-600 focus:ring-offset-2 transition-all duration-300 glow tech-font"
                >
                    Start Shopping
                </button>
            </div>
        <?php else: ?>
            <!-- Cart Header -->
            <div class="p-4 border-b border-nexus-gray-200 dark:border-nexus-gray-700">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-medium text-nexus-dark-900 dark:text-nexus-gray-100 tech-font">
                        Cart (<?php echo e($itemCount); ?>)
                    </h3>
                    <button
                        @click="open = false"
                        class="text-nexus-gray-400 dark:text-nexus-gray-500 hover:text-nexus-gray-600 dark:hover:text-nexus-gray-300 focus:outline-none transition-colors"
                    >
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
            </div>
            
            <!-- Recent Items Preview -->
            <div class="max-h-64 overflow-y-auto">
                <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $recentItems; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="p-4 border-b border-nexus-gray-100 dark:border-nexus-gray-700 hover:bg-nexus-gray-50 dark:hover:bg-nexus-dark-700 transition-colors" wire:key="preview-<?php echo e($item->id); ?>">
                        <div class="flex items-center space-x-3">
                            <!-- Product Image -->
                            <div class="flex-shrink-0 w-12 h-12 bg-nexus-gray-100 dark:bg-nexus-dark-700 rounded-md overflow-hidden">
                                <!--[if BLOCK]><![endif]--><?php if($item->component->image_url): ?>
                                    <img src="<?php echo e($item->component->image_url); ?>"
                                         alt="<?php echo e($item->component->name); ?>"
                                         class="w-full h-full object-cover">
                                <?php else: ?>
                                    <div class="w-full h-full flex items-center justify-center">
                                        <svg class="w-6 h-6 text-nexus-gray-400 dark:text-nexus-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                        </svg>
                                    </div>
                                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                            </div>

                            <!-- Product Info -->
                            <div class="flex-1 min-w-0">
                                <p class="text-sm font-medium text-nexus-dark-900 dark:text-nexus-gray-100 truncate">
                                    <?php echo e($item->component->name); ?>

                                </p>
                                <div class="flex items-center justify-between mt-1">
                                    <p class="text-xs text-nexus-gray-500 dark:text-nexus-gray-400">
                                        Qty: <?php echo e($item->quantity); ?> × $<?php echo e(number_format($item->price, 2)); ?>

                                    </p>
                                    <p class="text-sm font-medium text-nexus-dark-900 dark:text-nexus-gray-100">
                                        $<?php echo e(number_format($item->price * $item->quantity, 2)); ?>

                                    </p>
                                </div>
                            </div>

                            <!-- Quick Remove -->
                            <button
                                wire:click="quickRemoveItem(<?php echo e($item->id); ?>)"
                                class="text-red-400 hover:text-red-600 dark:text-red-500 dark:hover:text-red-400 focus:outline-none transition-colors"
                                title="Remove item"
                            >
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                </svg>
                            </button>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->

                <!--[if BLOCK]><![endif]--><?php if($recentItems->count() < $itemCount): ?>
                    <div class="p-3 text-center text-sm text-nexus-gray-500 dark:text-nexus-gray-400 bg-nexus-gray-50 dark:bg-nexus-dark-700">
                        <?php echo e($itemCount - $recentItems->count()); ?> more items in cart
                    </div>
                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
            </div>
            
            <!-- Cart Footer -->
            <div class="p-4 border-t border-nexus-gray-200 dark:border-nexus-gray-700 bg-nexus-gray-50 dark:bg-nexus-dark-700">
                <div class="flex items-center justify-between mb-3">
                    <span class="text-sm font-medium text-nexus-dark-900 dark:text-nexus-gray-100 tech-font">Total:</span>
                    <span class="text-lg font-bold text-nexus-dark-900 dark:text-nexus-gray-100 tech-font">$<?php echo e(number_format($total, 2)); ?></span>
                </div>

                <div class="space-y-2">
                    <button
                        @click="open = false; window.location.href = '<?php echo e(route('cart.index')); ?>'"
                        class="w-full px-4 py-2 nexus-gradient-primary hover:from-nexus-primary-700 hover:to-nexus-secondary-700 text-white text-sm font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-nexus-primary-500 dark:focus:ring-nexus-primary-600 focus:ring-offset-2 transition-all duration-300 glow tech-font"
                    >
                        View Cart
                    </button>

                    <button
                        @click="open = false; window.location.href = '/checkout'"
                        class="w-full px-4 py-2 bg-nexus-dark-900 dark:bg-nexus-dark-600 text-white text-sm font-medium rounded-md hover:bg-nexus-dark-800 dark:hover:bg-nexus-dark-500 focus:outline-none focus:ring-2 focus:ring-nexus-dark-500 dark:focus:ring-nexus-dark-400 focus:ring-offset-2 transition-all duration-300 tech-font"
                    >
                        Checkout
                    </button>
                </div>
            </div>
        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
    </div>
</div><?php /**PATH C:\lara\www\pc-builder\resources\views/livewire/shop/cart-icon.blade.php ENDPATH**/ ?>