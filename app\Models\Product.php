<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Str;

class Product extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'slug',
        'description',
        'short_description',
        'sku',
        'price',
        'sale_price',
        'stock_quantity',
        'manage_stock',
        'in_stock',
        'status',
        'type',
        'category',
        'category_id',
        'images',
        'attributes',
        'weight',
        'dimensions',
        'brand',
        'model',
        'sort_order',
        'featured',
        'meta_data'
    ];

    protected $casts = [
        'price' => 'float',
        'sale_price' => 'float',
        'weight' => 'float',
        'stock_quantity' => 'integer',
        'sort_order' => 'integer',
        'manage_stock' => 'boolean',
        'in_stock' => 'boolean',
        'featured' => 'boolean',
        'images' => 'array',
        'attributes' => 'array',
        'dimensions' => 'array',
        'meta_data' => 'array'
    ];

    // Automatically generate slug when creating/updating
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($product) {
            if (empty($product->slug)) {
                $product->slug = Str::slug($product->name);
            }
        });

        static::updating(function ($product) {
            if ($product->isDirty('name') && empty($product->slug)) {
                $product->slug = Str::slug($product->name);
            }
        });
    }

    // Relationships
    public function transactions()
    {
        return $this->hasMany(Transaction::class);
    }

    public function stockMovements()
    {
        return $this->hasMany(StockMovement::class);
    }

    public function category()
    {
        return $this->belongsTo(ProductCategory::class, 'category_id');
    }

    public function reviews()
    {
        return $this->hasMany(ProductReview::class);
    }

    public function approvedReviews()
    {
        return $this->hasMany(ProductReview::class)->approved();
    }

    // Scopes
    public function scopeActive(Builder $query)
    {
        return $query->where('status', 'active');
    }

    public function scopeInStock(Builder $query)
    {
        return $query->where('in_stock', true);
    }

    public function scopeFeatured(Builder $query)
    {
        return $query->where('featured', true);
    }

    public function scopeByCategory(Builder $query, string $category)
    {
        return $query->where('category', $category);
    }

    public function scopeByBrand(Builder $query, string $brand)
    {
        return $query->where('brand', $brand);
    }

    public function scopeOnSale(Builder $query)
    {
        return $query->whereNotNull('sale_price')
                    ->whereColumn('sale_price', '<', 'price');
    }

    // Accessors & Mutators
    public function getEffectivePriceAttribute()
    {
        return $this->sale_price ?? $this->price;
    }

    public function getIsOnSaleAttribute()
    {
        return !is_null($this->sale_price) && $this->sale_price < $this->price;
    }

    public function getDiscountPercentageAttribute()
    {
        if (!$this->is_on_sale) {
            return 0;
        }

        return round((($this->price - $this->sale_price) / $this->price) * 100, 2);
    }

    public function getFormattedPriceAttribute()
    {
        return '₹' . number_format($this->effective_price, 2);
    }

    public function getOriginalPriceAttribute()
    {
        return '₹' . number_format($this->price, 2);
    }

    public function getPrimaryImageAttribute()
    {
        return $this->images[0] ?? '/images/placeholder-product.jpg';
    }

    // Methods
    public function updateStock(int $quantity, string $type = 'sale')
    {
        if (!$this->manage_stock) {
            return true;
        }

        $previousStock = $this->stock_quantity;
        $newQuantity = $type === 'sale' 
            ? $this->stock_quantity - $quantity 
            : $this->stock_quantity + $quantity;

        $this->update([
            'stock_quantity' => max(0, $newQuantity),
            'in_stock' => $newQuantity > 0
        ]);

        // Create stock movement record
        StockMovement::create([
            'product_id' => $this->id,
            'type' => $type,
            'quantity_change' => $type === 'sale' ? -$quantity : $quantity,
            'previous_stock' => $previousStock,
            'new_stock' => max(0, $newQuantity),
            'reason' => ucfirst($type) . ' transaction'
        ]);

        return true;
    }

    public function isAvailable(int $quantity = 1): bool
    {
        if (!$this->manage_stock) {
            return $this->status === 'active' && $this->in_stock;
        }

        return $this->status === 'active' 
            && $this->in_stock 
            && $this->stock_quantity >= $quantity;
    }

    public function getStockStatus(): string
    {
        if (!$this->manage_stock) {
            return $this->in_stock ? 'In Stock' : 'Out of Stock';
        }

        if ($this->stock_quantity <= 0) {
            return 'Out of Stock';
        }

        if ($this->stock_quantity <= 5) {
            return 'Low Stock';
        }

        return 'In Stock';
    }

    // Review-related methods
    public function getAverageRating(): float
    {
        return ProductReview::getAverageRating($this->id);
    }

    public function getReviewCount(): int
    {
        return ProductReview::getReviewCount($this->id);
    }

    public function getReviewStats(): array
    {
        return ProductReview::getProductReviewStats($this->id);
    }

    public function hasUserReviewed(int $userId): bool
    {
        return $this->reviews()->where('user_id', $userId)->exists();
    }

    // Category-related methods
    public function getCategoryPath(): string
    {
        if (!$this->category) {
            return 'Uncategorized';
        }

        $breadcrumb = $this->category->getBreadcrumb();
        return collect($breadcrumb)->pluck('name')->implode(' > ');
    }

    // Coupon-related methods
    public function isEligibleForCoupon(Coupon $coupon): bool
    {
        return $coupon->isApplicableToProducts([$this->id]);
    }
}