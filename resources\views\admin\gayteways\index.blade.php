@extends('layouts.admin')

@section('title', 'Payment Gateways')

@section('content')
<div class="min-h-screen bg-gray-50 py-12">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900">Payment Gateways</h1>
            <p class="text-gray-600">Manage your payment gateway configurations</p>
        </div>

        @if(session('success'))
            <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6">
                {{ session('success') }}
            </div>
        @endif

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            @foreach($gateways as $gateway)
                @php
                    $setting = $settings[$gateway] ?? null;
                    $isEnabled = $setting['is_enabled'] ?? false;
                    $isTestMode = $setting['is_test_mode'] ?? true;
                @endphp
                
                <div class="bg-white rounded-lg shadow-md p-6">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold text-gray-900">{{ ucfirst($gateway) }}</h3>
                        <div class="flex items-center">
                            <span class="text-sm text-gray-500 mr-2">{{ $isEnabled ? 'Enabled' : 'Disabled' }}</span>
                            <button onclick="toggleGateway('{{ $gateway }}', {{ $isEnabled ? 'false' : 'true' }})" 
                                    class="relative inline-flex h-6 w-11 items-center rounded-full transition-colors {{ $isEnabled ? 'bg-indigo-600' : 'bg-gray-200' }}">
                                <span class="inline-block h-4 w-4 transform rounded-full bg-white transition-transform {{ $isEnabled ? 'translate-x-6' : 'translate-x-1' }}"></span>
                            </button>
                        </div>
                    </div>
                    
                    <div class="space-y-2 mb-4">
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600">Mode:</span>
                            <span class="font-medium {{ $isTestMode ? 'text-yellow-600' : 'text-green-600' }}">
                                {{ $isTestMode ? 'Test' : 'Live' }}
                            </span>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600">Status:</span>
                            <span class="font-medium {{ $isEnabled ? 'text-green-600' : 'text-red-600' }}">
                                {{ $isEnabled ? 'Active' : 'Inactive' }}
                            </span>
                        </div>
                    </div>
                    
                    <a href="{{ route('admin.gateways.show', $gateway) }}" 
                       class="block w-full bg-indigo-600 text-white text-center py-2 px-4 rounded-md hover:bg-indigo-700 transition duration-200">
                        Configure
                    </a>
                </div>
            @endforeach
        </div>
    </div>
</div>

<script>
async function toggleGateway(gateway, enabled) {
    try {
        const response = await fetch(`/admin/gateways/${gateway}/toggle`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            },
            body: JSON.stringify({ enabled: enabled })
        });
        
        if (response.ok) {
            location.reload();
        } else {
            alert('Failed to toggle gateway');
        }
    } catch (error) {
        alert('Error: ' + error.message);
    }
}
</script>
@endsection
