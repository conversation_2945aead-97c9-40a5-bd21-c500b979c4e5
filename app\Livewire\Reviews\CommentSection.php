<?php

namespace App\Livewire\Reviews;

use App\Models\Component;
use App\Models\Review;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\ValidationException;
use Livewire\Component as LivewireComponent;
use Livewire\WithPagination;

class CommentSection extends LivewireComponent
{
    use WithPagination;

    public Component $component;
    public $showReviewForm = false;
    public $rating = 5;
    public $title = '';
    public $comment = '';
    public $sortBy = 'newest';
    public $filterByRating = 'all';

    protected $rules = [
        'rating' => 'required|integer|min:1|max:5',
        'title' => 'nullable|string|max:255',
        'comment' => 'nullable|string|max:2000',
    ];

    protected $messages = [
        'rating.required' => 'Please select a rating.',
        'rating.min' => 'Rating must be at least 1 star.',
        'rating.max' => 'Rating cannot exceed 5 stars.',
        'title.max' => 'Title cannot exceed 255 characters.',
        'comment.max' => 'Comment cannot exceed 2000 characters.',
    ];

    public function mount(Component $component)
    {
        $this->component = $component;
    }

    public function toggleReviewForm()
    {
        if (!Auth::check()) {
            return redirect()->route('login');
        }

        $this->showReviewForm = !$this->showReviewForm;
        
        if ($this->showReviewForm) {
            $this->resetForm();
        }
    }

    public function submitReview()
    {
        if (!Auth::check()) {
            return redirect()->route('login');
        }

        $user = Auth::user();

        // Check if user can review this component
        if (!$this->component->canBeReviewedBy($user)) {
            throw ValidationException::withMessages([
                'general' => 'You can only review components you have purchased and not already reviewed.'
            ]);
        }

        $this->validate();

        // Create the review
        $review = Review::create([
            'user_id' => $user->id,
            'component_id' => $this->component->id,
            'rating' => $this->rating,
            'title' => $this->title,
            'comment' => $this->comment,
            'is_approved' => false,
        ]);

        // Auto-approve if it passes moderation
        $review->autoApprove();

        $this->resetForm();
        $this->showReviewForm = false;

        $this->dispatch('review-submitted');
        session()->flash('success', 'Your review has been submitted and will be visible after moderation.');
    }

    public function updatedSortBy()
    {
        $this->resetPage();
    }

    public function updatedFilterByRating()
    {
        $this->resetPage();
    }

    public function getReviewsProperty()
    {
        $query = $this->component->reviews()
            ->with('user')
            ->approved();

        // Apply rating filter
        if ($this->filterByRating !== 'all') {
            $query->where('rating', $this->filterByRating);
        }

        // Apply sorting
        switch ($this->sortBy) {
            case 'oldest':
                $query->orderBy('created_at', 'asc');
                break;
            case 'highest_rated':
                $query->orderBy('rating', 'desc')->orderBy('created_at', 'desc');
                break;
            case 'lowest_rated':
                $query->orderBy('rating', 'asc')->orderBy('created_at', 'desc');
                break;
            case 'newest':
            default:
                $query->orderBy('created_at', 'desc');
                break;
        }

        return $query->paginate(10);
    }

    public function getUserCanReviewProperty()
    {
        if (!Auth::check()) {
            return false;
        }

        return $this->component->canBeReviewedBy(Auth::user());
    }

    public function getUserHasReviewedProperty()
    {
        if (!Auth::check()) {
            return false;
        }

        return $this->component->reviews()
            ->where('user_id', Auth::id())
            ->exists();
    }

    private function resetForm()
    {
        $this->rating = 5;
        $this->title = '';
        $this->comment = '';
        $this->resetErrorBag();
    }

    public function render()
    {
        return view('livewire.reviews.comment-section', [
            'reviews' => $this->reviews,
            'reviewStats' => $this->component->review_stats,
            'userCanReview' => $this->userCanReview,
            'userHasReviewed' => $this->userHasReviewed,
        ]);
    }
}