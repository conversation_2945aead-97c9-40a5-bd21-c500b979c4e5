<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

class WebhookRateLimit
{
    /**
     * Handle an incoming request.
     * Requirements: 6.1, 6.2, 6.3, 6.4
     */
    public function handle(Request $request, Closure $next, int $maxAttempts = 60, int $decayMinutes = 1): Response
    {
        $key = $this->resolveRequestSignature($request);
        $maxAttempts = $maxAttempts ?: 60;
        $decayMinutes = $decayMinutes ?: 1;
        
        if ($this->tooManyAttempts($key, $maxAttempts)) {
            $this->logSecurityViolation($request, 'rate_limit_exceeded');
            
            return response()->json([
                'error' => 'Too many webhook requests',
                'retry_after' => $this->availableIn($key)
            ], 429);
        }
        
        $this->hit($key, $decayMinutes * 60);
        
        $response = $next($request);
        
        return $this->addHeaders(
            $response,
            $maxAttempts,
            $this->calculateRemainingAttempts($key, $maxAttempts)
        );
    }
    
    /**
     * Resolve request signature for rate limiting
     */
    protected function resolveRequestSignature(Request $request): string
    {
        $gateway = $this->extractGatewayFromPath($request->path());
        $ip = $request->ip();
        
        // Use IP + gateway combination for rate limiting
        return 'webhook_rate_limit:' . $gateway . ':' . $ip;
    }
    
    /**
     * Extract gateway name from request path
     */
    protected function extractGatewayFromPath(string $path): string
    {
        if (str_contains($path, 'razorpay')) {
            return 'razorpay';
        } elseif (str_contains($path, 'payumoney')) {
            return 'payumoney';
        } elseif (str_contains($path, 'cashfree')) {
            return 'cashfree';
        }
        
        return 'unknown';
    }
    
    /**
     * Determine if the given key has been "accessed" too many times
     */
    protected function tooManyAttempts(string $key, int $maxAttempts): bool
    {
        return Cache::get($key, 0) >= $maxAttempts;
    }
    
    /**
     * Increment the counter for a given key for a given decay time
     */
    protected function hit(string $key, int $decaySeconds = 60): int
    {
        $current = Cache::get($key, 0);
        $new = $current + 1;
        
        Cache::put($key, $new, $decaySeconds);
        
        return $new;
    }
    
    /**
     * Calculate the number of remaining attempts
     */
    protected function calculateRemainingAttempts(string $key, int $maxAttempts): int
    {
        return max(0, $maxAttempts - Cache::get($key, 0));
    }
    
    /**
     * Get the number of seconds until the "key" is accessible again
     */
    protected function availableIn(string $key): int
    {
        $ttl = Cache::getStore()->getRedis()->ttl(Cache::getStore()->getPrefix() . $key);
        return max(0, $ttl);
    }
    
    /**
     * Add the limit header information to the given response
     */
    protected function addHeaders(Response $response, int $maxAttempts, int $remainingAttempts): Response
    {
        $response->headers->add([
            'X-RateLimit-Limit' => $maxAttempts,
            'X-RateLimit-Remaining' => $remainingAttempts,
        ]);
        
        return $response;
    }
    
    /**
     * Log security violation
     */
    protected function logSecurityViolation(Request $request, string $violationType): void
    {
        Log::warning('Webhook security violation detected', [
            'violation_type' => $violationType,
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'path' => $request->path(),
            'method' => $request->method(),
            'headers' => $this->sanitizeHeaders($request->headers->all()),
            'timestamp' => now()->toISOString()
        ]);
    }
    
    /**
     * Sanitize headers for logging (remove sensitive data)
     */
    protected function sanitizeHeaders(array $headers): array
    {
        $sensitiveHeaders = [
            'authorization',
            'x-razorpay-signature',
            'x-cashfree-signature',
            'x-payumoney-signature'
        ];
        
        foreach ($sensitiveHeaders as $header) {
            if (isset($headers[$header])) {
                $headers[$header] = ['[REDACTED]'];
            }
        }
        
        return $headers;
    }
}