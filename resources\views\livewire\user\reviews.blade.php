<div>
    <h2 class="text-xl font-bold mb-4">My Reviews</h2>
    @if ($reviews->isEmpty())
        <div class="text-gray-500">You have not written any reviews yet.</div>
    @else
        <ul class="divide-y divide-gray-200">
            @foreach ($reviews as $review)
                <li class="py-4">
                    <div class="flex justify-between items-center">
                        <div>
                            <div class="font-semibold">{{ $review->component->name ?? 'Component' }}</div>
                            <div class="text-xs text-gray-400">{{ $review->created_at->format('Y-m-d') }}</div>
                        </div>
                        <div class="flex gap-2">
                            <button wire:click="startEdit({{ $review->id }})" class="text-blue-600 underline">Edit</button>
                            <button wire:click="deleteReview({{ $review->id }})" class="text-red-600 underline" onclick="return confirm('Delete this review?')">Delete</button>
                        </div>
                    </div>
                    @if ($editingReview && $editingReview->id === $review->id)
                        <form wire:submit.prevent="saveEdit" class="mt-2">
                            <textarea wire:model.defer="editContent" class="w-full border rounded p-2" rows="3"></textarea>
                            @error('editContent') <span class="text-red-600 text-xs">{{ $message }}</span> @enderror
                            <div class="mt-2 flex gap-2">
                                <button type="submit" class="bg-blue-600 text-white px-3 py-1 rounded">Save</button>
                                <button type="button" wire:click="cancelEdit" class="bg-gray-300 px-3 py-1 rounded">Cancel</button>
                            </div>
                        </form>
                    @else
                        <div class="mt-2">{{ $review->content }}</div>
                    @endif
                </li>
            @endforeach
        </ul>
    @endif
</div> 