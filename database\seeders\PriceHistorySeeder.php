<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\PriceHistory;
use App\Models\Component;
use Carbon\Carbon;

class PriceHistorySeeder extends Seeder
{
    public function run(): void
    {
        $components = Component::all();

        foreach ($components as $component) {
            $currentPrice = $component->price;
            $startDate = Carbon::now()->subMonths(6);
            
            // Create price history entries for the last 6 months
            for ($i = 0; $i < 20; $i++) {
                $date = $startDate->copy()->addDays($i * 9); // Every 9 days
                
                // Generate price variations (±5% to ±15%)
                $variation = rand(-15, 15) / 100;
                $newPrice = $currentPrice * (1 + $variation);
                $newPrice = max($newPrice, $currentPrice * 0.7); // Don't go below 70% of original
                $newPrice = min($newPrice, $currentPrice * 1.3); // Don't go above 130% of original
                
                $previousPrice = $i === 0 ? null : $currentPrice;
                
                PriceHistory::create([
                    'component_id' => $component->id,
                    'price' => round($newPrice, 2),
                    'previous_price' => $previousPrice,
                    'source' => $this->getRandomSource(),
                    'metadata' => [
                        'reason' => $this->getPriceChangeReason($variation),
                        'market_trend' => $variation > 0 ? 'up' : 'down',
                        'supplier' => $this->getRandomSupplier(),
                        'stock_level' => rand(0, 100),
                    ],
                    'created_at' => $date,
                    'updated_at' => $date,
                ]);
                
                $currentPrice = $newPrice;
            }
            
            // Update component with latest price
            $component->update(['price' => $currentPrice]);
        }
    }

    private function getRandomSource(): string
    {
        $sources = [
            'manual_update',
            'supplier_api',
            'market_analysis',
            'competitor_pricing',
            'bulk_import',
            'promotional_pricing',
            'seasonal_adjustment',
        ];

        return $sources[array_rand($sources)];
    }

    private function getPriceChangeReason(float $variation): string
    {
        if ($variation > 0.05) {
            $reasons = [
                'Supply shortage',
                'Increased demand',
                'New product launch',
                'Currency fluctuation',
                'Import duty increase',
                'Supplier price hike',
            ];
        } elseif ($variation < -0.05) {
            $reasons = [
                'Bulk discount',
                'Clearance sale',
                'New competitor',
                'Overstock situation',
                'Seasonal discount',
                'Promotional offer',
            ];
        } else {
            $reasons = [
                'Regular price adjustment',
                'Market stabilization',
                'Minor cost change',
                'Routine update',
            ];
        }

        return $reasons[array_rand($reasons)];
    }

    private function getRandomSupplier(): string
    {
        $suppliers = [
            'TechDistributor Ltd',
            'ComponentHub India',
            'ElectroSupply Co',
            'DigitalParts Inc',
            'HardwareSource',
            'TechMart Wholesale',
            'ComponentDirect',
            'EliteSuppliers',
        ];

        return $suppliers[array_rand($suppliers)];
    }
}