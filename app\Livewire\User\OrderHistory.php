<?php

namespace App\Livewire\User;

use Livewire\Component;
use Livewire\WithPagination;
use Illuminate\Support\Facades\Auth;
use App\Models\Order;

class OrderHistory extends Component
{
    use WithPagination;
    
    public $selectedOrder = null;
    public $search = '';
    public $statusFilter = '';
    public $sortBy = 'created_at';
    public $sortDirection = 'desc';
    
    protected $queryString = [
        'search' => ['except' => ''],
        'statusFilter' => ['except' => ''],
        'sortBy' => ['except' => 'created_at'],
        'sortDirection' => ['except' => 'desc'],
    ];

    public function updatingSearch()
    {
        $this->resetPage();
    }

    public function updatingStatusFilter()
    {
        $this->resetPage();
    }

    public function selectOrder($orderId)
    {
        $order = Order::where('user_id', Auth::id())
            ->with(['items.component', 'payment'])
            ->find($orderId);
            
        $this->selectedOrder = $order;
    }

    public function clearSelectedOrder()
    {
        $this->selectedOrder = null;
    }

    public function sortBy($field)
    {
        if ($this->sortBy === $field) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortBy = $field;
            $this->sortDirection = 'asc';
        }
        $this->resetPage();
    }

    public function reorderItems($orderId)
    {
        $order = Order::where('user_id', Auth::id())->find($orderId);
        
        if (!$order) {
            session()->flash('error', 'Order not found.');
            return;
        }

        // Add order items to cart
        $cartService = app(\App\Services\CartService::class);
        
        try {
            foreach ($order->items as $item) {
                if ($item->component && $item->component->stock_quantity > 0) {
                    $cartService->addToCart($item->component, $item->quantity, Auth::user());
                }
            }
            
            session()->flash('message', 'Items added to cart successfully!');
            return redirect()->route('cart.index');
            
        } catch (\Exception $e) {
            session()->flash('error', 'Failed to add items to cart: ' . $e->getMessage());
        }
    }

    public function downloadInvoice($orderId)
    {
        $order = Order::where('user_id', Auth::id())->find($orderId);
        
        if (!$order) {
            session()->flash('error', 'Order not found.');
            return;
        }

        // Generate and download invoice (placeholder for now)
        $this->dispatch('notify', 'Invoice download feature coming soon!');
        session()->flash('message', 'Invoice download feature coming soon!');
    }

    public function trackOrder($orderId)
    {
        $order = Order::where('user_id', Auth::id())->find($orderId);
        
        if (!$order || !$order->tracking_url) {
            session()->flash('error', 'Tracking information not available.');
            return;
        }

        return redirect()->away($order->tracking_url);
    }

    public function render()
    {
        if (!Auth::check()) {
            return view('livewire.user.order-history', ['orders' => collect()]);
        }

        $query = Order::where('user_id', Auth::id())
            ->with(['items.component', 'payment']);

        // Apply search filter
        if (!empty($this->search)) {
            $query->where(function ($q) {
                $q->where('order_number', 'like', '%' . $this->search . '%')
                  ->orWhereHas('items', function ($itemQuery) {
                      $itemQuery->where('name', 'like', '%' . $this->search . '%');
                  });
            });
        }

        // Apply status filter
        if (!empty($this->statusFilter)) {
            $query->where('status', $this->statusFilter);
        }

        // Apply sorting
        $query->orderBy($this->sortBy, $this->sortDirection);

        $orders = $query->paginate(10);

        return view('livewire.user.order-history', compact('orders'));
    }
} 