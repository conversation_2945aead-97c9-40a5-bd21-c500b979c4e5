<?php

namespace Tests\Unit;

use App\Models\Build;
use App\Models\BuildComponent;
use App\Models\Component;
use App\Models\ComponentCategory;
use App\Models\User;
use App\Services\BuilderService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class BuildPersistenceAndSharingTest extends TestCase
{
    use RefreshDatabase;

    protected BuilderService $builderService;
    protected User $user;
    protected User $otherUser;
    protected Build $build;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->builderService = app(BuilderService::class);
        $this->user = User::factory()->create();
        $this->otherUser = User::factory()->create();
        
        // Create test data
        $category = ComponentCategory::factory()->create(['name' => 'CPU', 'slug' => 'cpu']);
        $component = Component::factory()->create(['category_id' => $category->id]);
        
        $this->build = Build::factory()->create([
            'user_id' => $this->user->id,
            'name' => 'Test Build',
            'description' => 'Test Description',
            'is_public' => false,
        ]);
        
        BuildComponent::factory()->create([
            'build_id' => $this->build->id,
            'component_id' => $component->id,
            'category_id' => $category->id,
        ]);
    }

    public function test_save_build_updates_metadata()
    {
        $updateData = [
            'name' => 'Updated Build Name',
            'description' => 'Updated Description',
            'is_public' => true,
        ];

        $savedBuild = $this->builderService->saveBuild($this->build, $updateData);

        $this->assertEquals('Updated Build Name', $savedBuild->name);
        $this->assertEquals('Updated Description', $savedBuild->description);
        $this->assertTrue($savedBuild->is_public);
    }

    public function test_save_build_assigns_user_if_none_exists()
    {
        $build = Build::factory()->create(['user_id' => null]);
        
        $savedBuild = $this->builderService->saveBuild($build, [], $this->user);
        
        $this->assertEquals($this->user->id, $savedBuild->user_id);
    }

    public function test_save_build_recalculates_totals()
    {
        $originalPrice = $this->build->total_price;
        
        $savedBuild = $this->builderService->saveBuild($this->build, []);
        
        // The build should have recalculated its total price
        $this->assertNotNull($savedBuild->total_price);
    }

    public function test_create_shareable_build_creates_copy()
    {
        $shareData = [
            'name' => 'Shared Build',
            'description' => 'Shared Description',
            'is_public' => true,
        ];

        $shareableBuild = $this->builderService->createShareableBuild($this->build, $shareData);

        $this->assertNotEquals($this->build->id, $shareableBuild->id);
        $this->assertEquals('Shared Build', $shareableBuild->name);
        $this->assertEquals('Shared Description', $shareableBuild->description);
        $this->assertTrue($shareableBuild->is_public);
        $this->assertEquals($this->build->user_id, $shareableBuild->user_id);
    }

    public function test_create_shareable_build_copies_components()
    {
        $shareableBuild = $this->builderService->createShareableBuild($this->build);

        $originalComponents = $this->build->components()->count();
        $sharedComponents = $shareableBuild->components()->count();

        $this->assertEquals($originalComponents, $sharedComponents);
        $this->assertGreaterThan(0, $sharedComponents);
    }

    public function test_toggle_build_visibility_changes_public_status()
    {
        $originalStatus = $this->build->is_public;
        
        $toggledBuild = $this->builderService->toggleBuildVisibility($this->build);
        
        $this->assertEquals(!$originalStatus, $toggledBuild->is_public);
    }

    public function test_generate_shareable_url_requires_public_build()
    {
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Build must be public to generate shareable URL');
        
        $this->builderService->generateShareableUrl($this->build);
    }

    public function test_generate_shareable_url_returns_valid_url()
    {
        $this->build->is_public = true;
        $this->build->save();
        
        $url = $this->builderService->generateShareableUrl($this->build);
        
        $this->assertStringContainsString(route('builder.show', ['id' => $this->build->id]), $url);
    }

    public function test_generate_private_share_url_creates_token()
    {
        $this->assertNull($this->build->share_token);
        
        $url = $this->builderService->generatePrivateShareUrl($this->build);
        
        $this->build->refresh();
        $this->assertNotNull($this->build->share_token);
        $this->assertEquals(32, strlen($this->build->share_token));
        $this->assertStringContainsString($this->build->share_token, $url);
    }

    public function test_generate_private_share_url_reuses_existing_token()
    {
        $existingToken = 'existing_token_1234567890123456';
        $this->build->share_token = $existingToken;
        $this->build->save();
        
        $url = $this->builderService->generatePrivateShareUrl($this->build);
        
        $this->build->refresh();
        $this->assertEquals($existingToken, $this->build->share_token);
        $this->assertStringContainsString($existingToken, $url);
    }

    public function test_get_user_builds_returns_user_builds_only()
    {
        // Create builds for different users
        Build::factory()->create(['user_id' => $this->otherUser->id]);
        Build::factory()->create(['user_id' => $this->user->id]);
        
        $userBuilds = $this->builderService->getUserBuilds($this->user);
        
        $this->assertGreaterThan(0, $userBuilds->count());
        foreach ($userBuilds as $build) {
            $this->assertEquals($this->user->id, $build->user_id);
        }
    }

    public function test_get_user_builds_excludes_private_when_requested()
    {
        Build::factory()->create(['user_id' => $this->user->id, 'is_public' => true]);
        Build::factory()->create(['user_id' => $this->user->id, 'is_public' => false]);
        
        $publicBuilds = $this->builderService->getUserBuilds($this->user, false);
        
        foreach ($publicBuilds as $build) {
            $this->assertTrue($build->is_public);
        }
    }

    public function test_get_public_builds_returns_only_public_builds()
    {
        Build::factory()->create(['is_public' => true]);
        Build::factory()->create(['is_public' => false]);
        
        $publicBuilds = $this->builderService->getPublicBuilds();
        
        foreach ($publicBuilds as $build) {
            $this->assertTrue($build->is_public);
        }
    }

    public function test_get_public_builds_applies_filters()
    {
        Build::factory()->create(['is_public' => true, 'is_complete' => true, 'total_price' => 1000]);
        Build::factory()->create(['is_public' => true, 'is_complete' => false, 'total_price' => 2000]);
        
        $filters = [
            'complete_only' => true,
            'price_max' => 1500,
        ];
        
        $filteredBuilds = $this->builderService->getPublicBuilds(20, $filters);
        
        foreach ($filteredBuilds as $build) {
            $this->assertTrue($build->is_complete);
            $this->assertLessThanOrEqual(1500, $build->total_price);
        }
    }

    public function test_clone_build_creates_new_build_for_user()
    {
        $this->build->is_public = true;
        $this->build->save();
        
        $clonedBuild = $this->builderService->cloneBuild($this->build, $this->otherUser);
        
        $this->assertNotEquals($this->build->id, $clonedBuild->id);
        $this->assertEquals($this->otherUser->id, $clonedBuild->user_id);
        $this->assertStringContainsString('Clone of', $clonedBuild->name);
        $this->assertFalse($clonedBuild->is_public);
        $this->assertNull($clonedBuild->share_token);
    }

    public function test_clone_build_copies_components()
    {
        $this->build->is_public = true;
        $this->build->save();
        
        $clonedBuild = $this->builderService->cloneBuild($this->build, $this->otherUser);
        
        $originalComponents = $this->build->components()->count();
        $clonedComponents = $clonedBuild->components()->count();
        
        $this->assertEquals($originalComponents, $clonedComponents);
    }

    public function test_clone_build_fails_for_private_build_not_owned_by_user()
    {
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Cannot clone private build that does not belong to user');
        
        $this->builderService->cloneBuild($this->build, $this->otherUser);
    }

    public function test_clone_build_allows_owner_to_clone_private_build()
    {
        $clonedBuild = $this->builderService->cloneBuild($this->build, $this->user);
        
        $this->assertNotEquals($this->build->id, $clonedBuild->id);
        $this->assertEquals($this->user->id, $clonedBuild->user_id);
    }

    public function test_delete_build_removes_build_and_components()
    {
        $buildId = $this->build->id;
        $componentCount = $this->build->components()->count();
        
        $this->assertGreaterThan(0, $componentCount);
        
        $result = $this->builderService->deleteBuild($this->build, $this->user);
        
        $this->assertTrue($result);
        $this->assertDatabaseMissing('builds', ['id' => $buildId]);
        $this->assertDatabaseMissing('build_components', ['build_id' => $buildId]);
    }

    public function test_delete_build_fails_for_non_owner()
    {
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('User does not own this build');
        
        $this->builderService->deleteBuild($this->build, $this->otherUser);
    }

    public function test_get_build_by_id_returns_public_build()
    {
        $this->build->is_public = true;
        $this->build->save();
        
        $retrievedBuild = $this->builderService->getBuildById($this->build->id);
        
        $this->assertNotNull($retrievedBuild);
        $this->assertEquals($this->build->id, $retrievedBuild->id);
    }

    public function test_get_build_by_id_returns_owned_private_build()
    {
        $retrievedBuild = $this->builderService->getBuildById($this->build->id, $this->user);
        
        $this->assertNotNull($retrievedBuild);
        $this->assertEquals($this->build->id, $retrievedBuild->id);
    }

    public function test_get_build_by_id_returns_null_for_unauthorized_private_build()
    {
        $retrievedBuild = $this->builderService->getBuildById($this->build->id, $this->otherUser);
        
        $this->assertNull($retrievedBuild);
    }

    public function test_get_build_by_id_returns_build_with_valid_share_token()
    {
        $this->build->share_token = 'valid_token_12345678901234567890';
        $this->build->save();
        
        $retrievedBuild = $this->builderService->getBuildById(
            $this->build->id, 
            null, 
            'valid_token_12345678901234567890'
        );
        
        $this->assertNotNull($retrievedBuild);
        $this->assertEquals($this->build->id, $retrievedBuild->id);
    }

    public function test_get_build_by_id_returns_null_with_invalid_share_token()
    {
        $this->build->share_token = 'valid_token_12345678901234567890';
        $this->build->save();
        
        $retrievedBuild = $this->builderService->getBuildById(
            $this->build->id, 
            null, 
            'invalid_token'
        );
        
        $this->assertNull($retrievedBuild);
    }

    public function test_get_build_by_id_returns_null_for_nonexistent_build()
    {
        $retrievedBuild = $this->builderService->getBuildById(99999);
        
        $this->assertNull($retrievedBuild);
    }
}