<?php

namespace Tests\Unit\Models;

use App\Models\Component;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Review;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ComponentReviewTest extends TestCase
{
    use RefreshDatabase;

    public function test_component_has_many_reviews()
    {
        $component = Component::factory()->create();
        
        Review::factory()->count(3)->create(['component_id' => $component->id]);

        $this->assertCount(3, $component->reviews);
        $this->assertInstanceOf(Review::class, $component->reviews->first());
    }

    public function test_approved_reviews_returns_only_approved_reviews()
    {
        $component = Component::factory()->create();
        
        Review::factory()->approved()->count(2)->create(['component_id' => $component->id]);
        Review::factory()->pending()->count(1)->create(['component_id' => $component->id]);

        $approvedReviews = $component->approvedReviews;
        
        $this->assertCount(2, $approvedReviews);
        $this->assertTrue($approvedReviews->every(fn($review) => $review->is_approved));
    }

    public function test_average_rating_attribute_returns_correct_value()
    {
        $component = Component::factory()->create();
        
        Review::factory()->approved()->rating(5)->create(['component_id' => $component->id]);
        Review::factory()->approved()->rating(3)->create(['component_id' => $component->id]);
        Review::factory()->pending()->rating(1)->create(['component_id' => $component->id]); // Should be ignored

        $this->assertEquals(4.0, $component->average_rating);
    }

    public function test_review_count_attribute_returns_correct_count()
    {
        $component = Component::factory()->create();
        
        Review::factory()->approved()->count(3)->create(['component_id' => $component->id]);
        Review::factory()->pending()->count(2)->create(['component_id' => $component->id]); // Should be ignored

        $this->assertEquals(3, $component->review_count);
    }

    public function test_rating_distribution_attribute_returns_correct_distribution()
    {
        $component = Component::factory()->create();
        
        Review::factory()->approved()->rating(5)->count(2)->create(['component_id' => $component->id]);
        Review::factory()->approved()->rating(4)->create(['component_id' => $component->id]);
        Review::factory()->approved()->rating(3)->create(['component_id' => $component->id]);

        $distribution = $component->rating_distribution;
        
        $this->assertEquals([
            1 => 0,
            2 => 0,
            3 => 1,
            4 => 1,
            5 => 2,
        ], $distribution);
    }

    public function test_review_stats_attribute_returns_comprehensive_data()
    {
        $component = Component::factory()->create();
        $user = User::factory()->create();
        
        // Create a completed order for verified purchase
        $order = Order::factory()->completed()->create([
            'user_id' => $user->id,
        ]);
        
        OrderItem::factory()->create([
            'order_id' => $order->id,
            'component_id' => $component->id,
        ]);
        
        Review::factory()->approved()->rating(5)->create([
            'component_id' => $component->id,
            'user_id' => $user->id,
        ]);
        Review::factory()->approved()->rating(4)->create(['component_id' => $component->id]);

        $stats = $component->review_stats;
        
        $this->assertArrayHasKey('average_rating', $stats);
        $this->assertArrayHasKey('total_reviews', $stats);
        $this->assertArrayHasKey('rating_distribution', $stats);
        $this->assertArrayHasKey('verified_purchases', $stats);
        
        $this->assertEquals(4.5, $stats['average_rating']);
        $this->assertEquals(2, $stats['total_reviews']);
        $this->assertEquals(1, $stats['verified_purchases']);
    }

    public function test_can_be_reviewed_by_returns_true_for_eligible_user()
    {
        $user = User::factory()->create();
        $component = Component::factory()->create();
        
        // Create a completed order
        $order = Order::factory()->completed()->create([
            'user_id' => $user->id,
        ]);
        
        OrderItem::factory()->create([
            'order_id' => $order->id,
            'component_id' => $component->id,
        ]);

        $this->assertTrue($component->canBeReviewedBy($user));
    }

    public function test_can_be_reviewed_by_returns_false_for_user_who_already_reviewed()
    {
        $user = User::factory()->create();
        $component = Component::factory()->create();
        
        // Create a completed order
        $order = Order::factory()->completed()->create([
            'user_id' => $user->id,
        ]);
        
        OrderItem::factory()->create([
            'order_id' => $order->id,
            'component_id' => $component->id,
        ]);

        // User already reviewed this component
        Review::factory()->create([
            'user_id' => $user->id,
            'component_id' => $component->id,
        ]);

        $this->assertFalse($component->canBeReviewedBy($user));
    }

    public function test_can_be_reviewed_by_returns_false_for_user_without_purchase()
    {
        $user = User::factory()->create();
        $component = Component::factory()->create();

        $this->assertFalse($component->canBeReviewedBy($user));
    }

    public function test_can_be_reviewed_by_returns_false_for_non_completed_order()
    {
        $user = User::factory()->create();
        $component = Component::factory()->create();
        
        // Create a non-completed order
        $order = Order::factory()->create([
            'user_id' => $user->id,
            'status' => 'processing',
        ]);
        
        OrderItem::factory()->create([
            'order_id' => $order->id,
            'component_id' => $component->id,
        ]);

        $this->assertFalse($component->canBeReviewedBy($user));
    }
}