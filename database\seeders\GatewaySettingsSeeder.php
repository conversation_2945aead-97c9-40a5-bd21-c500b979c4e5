<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\GatewaySetting;

class GatewaySettingsSeeder extends Seeder
{
    public function run(): void
    {
        $gateways = [
            [
                'gateway_name' => 'razorpay',
                'is_enabled' => true,
                'is_test_mode' => true,
                'settings' => [
                    'key_id' => 'rzp_test_xxxxxxxxxxxxxxxx',
                    'key_secret' => 'xxxxxxxxxxxxxxxxxxxxxxxx',
                    'theme_color' => '#3399cc'
                ]
            ],
            [
                'gateway_name' => 'payumoney',
                'is_enabled' => true,
                'is_test_mode' => true,
                'settings' => [
                    'merchant_key' => 'xxxxxxxx',
                    'salt' => 'xxxxxxxxxxxxxxxx'
                ]
            ],
            [
                'gateway_name' => 'cashfree',
                'is_enabled' => true,
                'is_test_mode' => true,
                'settings' => [
                    'app_id' => 'xxxxxxxxxxxxxxxxxxxxxxxx',
                    'secret_key' => 'xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx'
                ]
            ]
        ];

        foreach ($gateways as $gateway) {
            GatewaySetting::updateOrCreate(
                ['gateway_name' => $gateway['gateway_name']],
                $gateway
            );
        }
    }
}