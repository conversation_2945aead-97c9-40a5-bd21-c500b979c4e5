<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use App\Services\PaymentGatewayFactory;

class PaymentRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $gatewayFactory = app(PaymentGatewayFactory::class);
        $availableGateways = collect($gatewayFactory->getAvailableGateways())->pluck('name')->toArray();

        return [
            'gateway' => [
                'required',
                'string',
                Rule::in($availableGateways)
            ],
            'amount' => [
                'required',
                'numeric',
                'min:1',
                'max:999999.99',
                'regex:/^\d+(\.\d{1,2})?$/' // Ensures max 2 decimal places
            ],
            'currency' => [
                'required',
                'string',
                Rule::in(['INR', 'USD', 'EUR'])
            ],
            'description' => [
                'nullable',
                'string',
                'max:255',
                'regex:/^[a-zA-Z0-9\s\-_.,#()]+$/' // Alphanumeric with common symbols
            ]
        ];
    }

    /**
     * Get custom validation messages.
     */
    public function messages(): array
    {
        return [
            'gateway.required' => 'Please select a payment gateway.',
            'gateway.in' => 'The selected payment gateway is not available.',
            'amount.required' => 'Payment amount is required.',
            'amount.numeric' => 'Payment amount must be a valid number.',
            'amount.min' => 'Minimum payment amount is ₹1.00.',
            'amount.max' => 'Maximum payment amount is ₹999,999.99.',
            'amount.regex' => 'Payment amount can have maximum 2 decimal places.',
            'currency.required' => 'Currency is required.',
            'currency.in' => 'The selected currency is not supported.',
            'description.max' => 'Description cannot exceed 255 characters.',
            'description.regex' => 'Description contains invalid characters.'
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'gateway' => 'payment gateway',
            'amount' => 'payment amount',
            'currency' => 'currency',
            'description' => 'payment description'
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // Additional validation for amount based on currency
            if ($this->currency === 'USD' && $this->amount && $this->amount < 0.50) {
                $validator->errors()->add('amount', 'Minimum amount for USD is $0.50.');
            }
            
            if ($this->currency === 'EUR' && $this->amount && $this->amount < 0.50) {
                $validator->errors()->add('amount', 'Minimum amount for EUR is €0.50.');
            }

            // Validate gateway availability
            if ($this->gateway) {
                $gatewayFactory = app(PaymentGatewayFactory::class);
                $availableGateways = collect($gatewayFactory->getAvailableGateways());
                $selectedGateway = $availableGateways->firstWhere('name', $this->gateway);
                
                if (!$selectedGateway || !$selectedGateway['is_enabled']) {
                    $validator->errors()->add('gateway', 'The selected payment gateway is currently unavailable.');
                }
            }
        });
    }

    /**
     * Handle a failed validation attempt.
     */
    protected function failedValidation(\Illuminate\Contracts\Validation\Validator $validator): void
    {
        if ($this->wantsJson()) {
            $response = response()->json([
                'success' => false,
                'message' => 'The given data was invalid.',
                'errors' => $validator->errors()->toArray()
            ], 422);
            
            throw new \Illuminate\Http\Exceptions\HttpResponseException($response);
        }

        parent::failedValidation($validator);
    }
}