<?php

namespace App\Traits;

trait InstallationChecks
{
    protected function checkDatabaseConnection(array $config): bool
    {
        try {
            $pdo = new \PDO(
                "mysql:host={$config['host']};port={$config['port']}",
                $config['username'],
                $config['password']
            );
            $pdo->setAttribute(\PDO::ATTR_ERRMODE, \PDO::ERRMODE_EXCEPTION);
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }

    protected function checkDirectoryPermissions(): array
    {
        $directories = [
            storage_path(),
            storage_path('app'),
            storage_path('framework'),
            storage_path('logs'),
            base_path('bootstrap/cache')
        ];

        $results = [];
        foreach ($directories as $directory) {
            $results[$directory] = is_writable($directory);
        }

        return $results;
    }

    protected function validateEmailConfig(array $config): bool
    {
        return !empty($config['mail_host']) && 
               !empty($config['mail_port']) && 
               !empty($config['mail_username']) && 
               !empty($config['mail_password']);
    }
} 