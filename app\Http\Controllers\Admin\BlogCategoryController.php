<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\BlogPostCategory;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class BlogCategoryController extends Controller
{
    /**
     * Display a listing of the blog categories.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $categories = BlogPostCategory::withCount('posts')
            ->orderBy('display_order')
            ->paginate(20);

        return view('admin.blog.categories.index', compact('categories'));
    }

    /**
     * Show the form for creating a new blog category.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        return view('admin.blog.categories.create');
    }

    /**
     * Store a newly created blog category in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255|unique:blog_post_categories,name',
            'description' => 'nullable|string',
            'display_order' => 'nullable|integer|min:0',
        ]);

        BlogPostCategory::create([
            'name' => $request->name,
            'slug' => Str::slug($request->name),
            'description' => $request->description,
            'display_order' => $request->display_order ?? 0,
        ]);

        return redirect()->route('admin.blog.categories.index')
            ->with('success', 'Blog category created successfully.');
    }

    /**
     * Show the form for editing the specified blog category.
     *
     * @param  \App\Models\BlogPostCategory  $category
     * @return \Illuminate\Http\Response
     */
    public function edit(BlogPostCategory $category)
    {
        return view('admin.blog.categories.edit', compact('category'));
    }

    /**
     * Update the specified blog category in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\BlogPostCategory  $category
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, BlogPostCategory $category)
    {
        $request->validate([
            'name' => 'required|string|max:255|unique:blog_post_categories,name,' . $category->id,
            'description' => 'nullable|string',
            'display_order' => 'nullable|integer|min:0',
        ]);

        $category->update([
            'name' => $request->name,
            'slug' => Str::slug($request->name),
            'description' => $request->description,
            'display_order' => $request->display_order ?? 0,
        ]);

        return redirect()->route('admin.blog.categories.index')
            ->with('success', 'Blog category updated successfully.');
    }

    /**
     * Remove the specified blog category from storage.
     *
     * @param  \App\Models\BlogPostCategory  $category
     * @return \Illuminate\Http\Response
     */
    public function destroy(BlogPostCategory $category)
    {
        // Check if category has posts
        if ($category->posts()->count() > 0) {
            return redirect()->route('admin.blog.categories.index')
                ->with('error', 'Cannot delete category with associated posts.');
        }

        $category->delete();

        return redirect()->route('admin.blog.categories.index')
            ->with('success', 'Blog category deleted successfully.');
    }

    /**
     * API endpoint to get all categories (for select2 or similar)
     *
     * @return \Illuminate\Http\Response
     */
    public function apiIndex(Request $request)
    {
        $search = $request->get('search');
        $categories = BlogPostCategory::when($search, function ($query) use ($search) {
                return $query->where('name', 'like', "%{$search}%");
            })
            ->orderBy('display_order')
            ->orderBy('name')
            ->get(['id', 'name']);

        return response()->json($categories);
    }
}