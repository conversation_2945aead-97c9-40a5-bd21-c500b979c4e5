<?php

/**
 * Fix Seeder Data Script
 * 
 * This script ensures there's enough base data before running
 * seeders that depend on random selections.
 */

require_once 'vendor/autoload.php';

use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\DB;
use App\Models\Component;
use App\Models\ComponentCategory;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "🔧 Fixing Seeder Data Dependencies...\n";
echo "=====================================\n\n";

// Step 1: Ensure we have component categories
echo "1. Checking component categories... ";
$categoryCount = DB::table('component_categories')->count();
if ($categoryCount < 4) {
    echo "Missing categories, creating...\n";
    try {
        Artisan::call('db:seed', ['--class' => 'Database\\Seeders\\ComponentCategorySeeder']);
        echo "   ✅ Component categories created\n";
    } catch (Exception $e) {
        echo "   ❌ Failed: " . $e->getMessage() . "\n";
    }
} else {
    echo "✅ ({$categoryCount} found)\n";
}

// Step 2: Ensure we have enough components
echo "2. Checking components... ";
$componentCount = DB::table('components')->count();
if ($componentCount < 5) {
    echo "Not enough components, creating more...\n";
    
    // Create additional components programmatically
    $categories = ComponentCategory::all();
    
    if ($categories->count() > 0) {
        $additionalComponents = [
            [
                'name' => 'AMD Ryzen 5 5600X',
                'slug' => 'amd-ryzen-5-5600x',
                'description' => '6-core AMD processor',
                'category_id' => $categories->where('slug', 'cpu')->first()->id ?? $categories->first()->id,
                'brand' => 'AMD',
                'model' => 'Ryzen 5 5600X',
                'price' => 280.00,
                'stock' => 15,
                'image' => 'amd-ryzen5.jpg',
                'specs' => json_encode([
                    'socket' => 'AM4',
                    'power_consumption' => '65W',
                    'cores' => 6,
                    'threads' => 12,
                ]),
                'is_featured' => false,
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'MSI B450 TOMAHAWK',
                'slug' => 'msi-b450-tomahawk',
                'description' => 'AMD B450 Motherboard',
                'category_id' => $categories->where('slug', 'motherboard')->first()->id ?? $categories->first()->id,
                'brand' => 'MSI',
                'model' => 'B450 TOMAHAWK',
                'price' => 150.00,
                'stock' => 8,
                'image' => 'msi-b450.jpg',
                'specs' => json_encode([
                    'socket' => 'AM4',
                    'ram_slots' => 4,
                    'max_ram' => '128GB',
                ]),
                'is_featured' => true,
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Corsair Vengeance LPX 16GB',
                'slug' => 'corsair-vengeance-lpx-16gb',
                'description' => 'DDR4 3200MHz RAM',
                'category_id' => $categories->where('slug', 'ram')->first()->id ?? $categories->first()->id,
                'brand' => 'Corsair',
                'model' => 'Vengeance LPX',
                'price' => 120.00,
                'stock' => 20,
                'image' => 'corsair-ram.jpg',
                'specs' => json_encode([
                    'capacity' => '16GB',
                    'speed' => '3200MHz',
                    'type' => 'DDR4',
                ]),
                'is_featured' => false,
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'NVIDIA RTX 3060',
                'slug' => 'nvidia-rtx-3060',
                'description' => 'Mid-range graphics card',
                'category_id' => $categories->where('slug', 'gpu')->first()->id ?? $categories->first()->id,
                'brand' => 'NVIDIA',
                'model' => 'RTX 3060',
                'price' => 450.00,
                'stock' => 12,
                'image' => 'nvidia-rtx3060.jpg',
                'specs' => json_encode([
                    'memory' => '12GB GDDR6',
                    'power_consumption' => '170W',
                    'cuda_cores' => 3584,
                ]),
                'is_featured' => true,
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'AMD RX 6600 XT',
                'slug' => 'amd-rx-6600-xt',
                'description' => 'AMD graphics card',
                'category_id' => $categories->where('slug', 'gpu')->first()->id ?? $categories->first()->id,
                'brand' => 'AMD',
                'model' => 'RX 6600 XT',
                'price' => 380.00,
                'stock' => 10,
                'image' => 'amd-rx6600xt.jpg',
                'specs' => json_encode([
                    'memory' => '8GB GDDR6',
                    'power_consumption' => '160W',
                    'stream_processors' => 2048,
                ]),
                'is_featured' => false,
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ];
        
        DB::table('components')->insert($additionalComponents);
        echo "   ✅ Additional components created\n";
    }
} else {
    echo "✅ ({$componentCount} found)\n";
}

// Step 3: Verify active components
echo "3. Checking active components... ";
$activeCount = Component::where('is_active', true)->count();
echo "✅ ({$activeCount} active components)\n";

// Step 4: Run the basic seeders first
echo "4. Running prerequisite seeders...\n";

$basicSeeders = [
    'UserSeeder' => 'Users',
    'SettingsSeeder' => 'Settings',
    'GatewaySettingsSeeder' => 'Gateway Settings',
];

foreach ($basicSeeders as $seeder => $name) {
    echo "   Running {$name}... ";
    try {
        Artisan::call('db:seed', ['--class' => "Database\\Seeders\\{$seeder}"]);
        echo "✅\n";
    } catch (Exception $e) {
        echo "❌ " . $e->getMessage() . "\n";
    }
}

echo "\n✨ Data preparation completed!\n";
echo "\nNow you can safely run:\n";
echo "- php artisan db:seed (for all seeders)\n";
echo "- php run_seeders.php (for detailed progress)\n";
echo "- php test_seeders.php (for testing individual seeders)\n";