<!-- User Sidebar -->
<script>
    function sidebarTabs() {
        return {
            activeTab: 'account',
            setActiveTab(tab) {
                this.activeTab = tab;
            }
        }
    }
</script>

<style>
    .hide-scrollbar {
        -ms-overflow-style: none;  /* IE and Edge */
        scrollbar-width: none;  /* Firefox */
    }
    .hide-scrollbar::-webkit-scrollbar {
        display: none; /* Chrome, Safari, Opera */
    }
</style>
<div class="sticky top-0 flex flex-col h-screen bg-white dark:bg-bg-dark border-r border-border-light dark:border-border-dark overflow-hidden">
    <!-- Sidebar Header -->
    <div class="sticky top-0 z-10 bg-gradient-to-r from-primary-light to-blue-600 dark:from-primary-dark dark:to-blue-800 flex items-center justify-between px-4 py-5 shadow-sm">
        <div class="flex items-center space-x-3">
            <div class="h-10 w-10 bg-white dark:bg-gray-800 rounded-lg flex items-center justify-center shadow-md">
                <span class="text-primary-light dark:text-primary-dark font-bold text-lg">{{ substr(config('app.name', 'App'), 0, 1) }}</span>
            </div>
            <span class="text-lg font-bold text-white">{{ config('app.name', 'Dashboard') }}</span>
        </div>
        <!-- Close button for mobile -->
        <button @click="sidebarOpen = false" class="lg:hidden text-white hover:text-gray-200 focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50 rounded transition-colors duration-200">
            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
        </button>
    </div>

    <!-- Navigation Links -->
    <nav class="flex-1 px-4 py-6 space-y-1 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-gray-700 scrollbar-track-gray-100 dark:scrollbar-track-gray-800">
        <div class="mb-2 px-4">
            <h3 class="text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider">Main</h3>
        </div>
        
        <!-- Dashboard -->
        <a href="{{ route('dashboard') }}" 
           class="flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-all duration-200 {{ request()->routeIs('dashboard') ? 'bg-blue-50 dark:bg-blue-900/20 text-primary-light dark:text-primary-dark shadow-sm' : 'text-text-primary-light dark:text-text-primary-dark hover:bg-gray-50 dark:hover:bg-gray-800 hover:text-primary-light dark:hover:text-primary-dark hover:translate-x-1' }}">
            <svg class="mr-3 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
            </svg>
            <span>Dashboard</span>
        </a>

        <!-- Tabbed Menu Section -->
        <div x-data="sidebarTabs()" class="mt-4">
            <div class="border-b border-gray-200 dark:border-gray-700">
                <nav class="flex space-x-2 overflow-x-auto hide-scrollbar" aria-label="Sidebar Tabs">
                    <button 
                        @click="setActiveTab('account')" 
                        :class="activeTab === 'account' ? 'border-indigo-500 text-indigo-600 dark:text-indigo-400' : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-indigo-600 dark:hover:text-indigo-400 hover:border-indigo-300 dark:hover:border-indigo-700'"
                        class="py-2 px-3 border-b-2 text-xs font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 flex items-center space-x-1"
                        :aria-selected="activeTab === 'account'"
                        role="tab">
                        <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5.121 17.804A13.937 13.937 0 0112 15c2.5 0 4.847.655 6.879 1.804M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                        <span>Account</span>
                    </button>
                    <button 
                        @click="setActiveTab('orders')" 
                        :class="activeTab === 'orders' ? 'border-indigo-500 text-indigo-600 dark:text-indigo-400' : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-indigo-600 dark:hover:text-indigo-400 hover:border-indigo-300 dark:hover:border-indigo-700'"
                        class="py-2 px-3 border-b-2 text-xs font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 flex items-center space-x-1"
                        :aria-selected="activeTab === 'orders'"
                        role="tab">
                        <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
                        </svg>
                        <span>Orders</span>
                    </button>
                    <button 
                        @click="setActiveTab('builds')" 
                        :class="activeTab === 'builds' ? 'border-indigo-500 text-indigo-600 dark:text-indigo-400' : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-indigo-600 dark:hover:text-indigo-400 hover:border-indigo-300 dark:hover:border-indigo-700'"
                        class="py-2 px-3 border-b-2 text-xs font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 flex items-center space-x-1"
                        :aria-selected="activeTab === 'builds'"
                        role="tab">
                        <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                        </svg>
                        <span>Builds</span>
                    </button>
                    <button 
                        @click="setActiveTab('notifications')" 
                        :class="activeTab === 'notifications' ? 'border-indigo-500 text-indigo-600 dark:text-indigo-400' : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-indigo-600 dark:hover:text-indigo-400 hover:border-indigo-300 dark:hover:border-indigo-700'"
                        class="py-2 px-3 border-b-2 text-xs font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 flex items-center space-x-1"
                        :aria-selected="activeTab === 'notifications'"
                        role="tab">
                        <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V4a2 2 0 10-4 0v1.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"></path>
                        </svg>
                        <span>Notifications</span>
                    </button>
                    <button 
                        @click="setActiveTab('reviews')" 
                        :class="activeTab === 'reviews' ? 'border-indigo-500 text-indigo-600 dark:text-indigo-400' : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-indigo-600 dark:hover:text-indigo-400 hover:border-indigo-300 dark:hover:border-indigo-700'"
                        class="py-2 px-3 border-b-2 text-xs font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 flex items-center space-x-1"
                        :aria-selected="activeTab === 'reviews'"
                        role="tab">
                        <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l2.036 6.29a1 1 0 00.95.69h6.631c.969 0 1.371 1.24.588 1.81l-5.37 3.905a1 1 0 00-.364 1.118l2.036 6.29c.3.921-.755 1.688-1.54 1.118l-5.37-3.905a1 1 0 00-1.176 0l-5.37 3.905c-.784.57-1.838-.197-1.54-1.118l2.036-6.29a1 1 0 00-.364-1.118l-5.37-3.905c-.783-.57-.38-1.81.588-1.81h6.631a1 1 0 00.95-.69l2.036-6.29z"></path>
                        </svg>
                        <span>Reviews</span>
                    </button>
                </nav>
            </div>
            
            <!-- Tab Content -->
            <div class="pt-3 pb-1">
                <!-- Account Tab Content -->
                <div x-show="activeTab === 'account'" class="space-y-1">
                    <a href="{{ route('dashboard') }}#account" class="flex items-center px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 text-text-primary-light dark:text-text-primary-dark hover:bg-gray-50 dark:hover:bg-gray-800 hover:text-primary-light dark:hover:text-primary-dark hover:translate-x-1">
                        <svg class="mr-3 h-4 w-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                        </svg>
                        <span>Profile Settings</span>
                    </a>
                    <a href="{{ route('dashboard') }}#account" class="flex items-center px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 text-text-primary-light dark:text-text-primary-dark hover:bg-gray-50 dark:hover:bg-gray-800 hover:text-primary-light dark:hover:text-primary-dark hover:translate-x-1">
                        <svg class="mr-3 h-4 w-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z"></path>
                        </svg>
                        <span>Security</span>
                    </a>
                    <a href="{{ route('dashboard') }}#account" class="flex items-center px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 text-text-primary-light dark:text-text-primary-dark hover:bg-gray-50 dark:hover:bg-gray-800 hover:text-primary-light dark:hover:text-primary-dark hover:translate-x-1">
                        <svg class="mr-3 h-4 w-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                        <span>Preferences</span>
                    </a>
                </div>
                <!-- Orders Tab Content -->
                <div x-show="activeTab === 'orders'" class="space-y-1">
                    <a href="{{ route('orders.index') }}" class="flex items-center px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 text-text-primary-light dark:text-text-primary-dark hover:bg-gray-50 dark:hover:bg-gray-800 hover:text-primary-light dark:hover:text-primary-dark hover:translate-x-1">
                        <svg class="mr-3 h-4 w-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                        </svg>
                        <span>Recent Orders</span>
                    </a>
                    <a href="{{ route('orders.index') }}?status=completed" class="flex items-center px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 text-text-primary-light dark:text-text-primary-dark hover:bg-gray-50 dark:hover:bg-gray-800 hover:text-primary-light dark:hover:text-primary-dark hover:translate-x-1">
                        <svg class="mr-3 h-4 w-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <span>Completed</span>
                    </a>
                    <a href="{{ route('orders.index') }}?status=pending" class="flex items-center px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 text-text-primary-light dark:text-text-primary-dark hover:bg-gray-50 dark:hover:bg-gray-800 hover:text-primary-light dark:hover:text-primary-dark hover:translate-x-1">
                        <svg class="mr-3 h-4 w-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <span>Pending</span>
                    </a>
                    <!-- Download Invoices -->
                    <a href="{{ route('orders.index') }}#invoices" class="flex items-center px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 text-text-primary-light dark:text-text-primary-dark hover:bg-gray-50 dark:hover:bg-gray-800 hover:text-primary-light dark:hover:text-primary-dark hover:translate-x-1">
                        <svg class="mr-3 h-4 w-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v8m0 0v-8m0 8H8m4 0h4" />
                        </svg>
                        <span>Download Invoices</span>
                    </a>
                </div>
                <!-- Builds Tab Content -->
                <div x-show="activeTab === 'builds'" class="space-y-1">
                    <a href="{{ route('builder.index') }}" class="flex items-center px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 text-text-primary-light dark:text-text-primary-dark hover:bg-gray-50 dark:hover:bg-gray-800 hover:text-primary-light dark:hover:text-primary-dark hover:translate-x-1">
                        <svg class="mr-3 h-4 w-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        <span>Create New Build</span>
                    </a>
                    <a href="{{ route('dashboard') }}#builds" class="flex items-center px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 text-text-primary-light dark:text-text-primary-dark hover:bg-gray-50 dark:hover:bg-gray-800 hover:text-primary-light dark:hover:text-primary-dark hover:translate-x-1">
                        <svg class="mr-3 h-4 w-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7v8a2 2 0 002 2h6M8 7V5a2 2 0 012-2h4.586a1 1 0 01.707.293l4.414 4.414a1 1 0 01.293.707V15a2 2 0 01-2 2h-2M8 7H6a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2v-2"></path>
                        </svg>
                        <span>Saved Builds</span>
                    </a>
                    <a href="{{ route('dashboard') }}#builds" class="flex items-center px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 text-text-primary-light dark:text-text-primary-dark hover:bg-gray-50 dark:hover:bg-gray-800 hover:text-primary-light dark:hover:text-primary-dark hover:translate-x-1">
                        <svg class="mr-3 h-4 w-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.368 2.684 3 3 0 00-5.368-2.684z"></path>
                        </svg>
                        <span>Shared Builds</span>
                    </a>
                </div>
                <!-- Notifications Tab Content -->
                <div x-show="activeTab === 'notifications'" class="space-y-1">
                    <a href="{{ route('dashboard') }}#notifications" class="flex items-center px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 text-text-primary-light dark:text-text-primary-dark hover:bg-gray-50 dark:hover:bg-gray-800 hover:text-primary-light dark:hover:text-primary-dark hover:translate-x-1">
                        <svg class="mr-3 h-4 w-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V4a2 2 0 10-4 0v1.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"></path>
                        </svg>
                        <span>All Notifications</span>
                    </a>
                </div>
                <!-- Reviews Tab Content -->
                <div x-show="activeTab === 'reviews'" class="space-y-1">
                    <a href="{{ route('dashboard') }}#reviews" class="flex items-center px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 text-text-primary-light dark:text-text-primary-dark hover:bg-gray-50 dark:hover:bg-gray-800 hover:text-primary-light dark:hover:text-primary-dark hover:translate-x-1">
                        <svg class="mr-3 h-4 w-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l2.036 6.29a1 1 0 00.95.69h6.631c.969 0 1.371 1.24.588 1.81l-5.37 3.905a1 1 0 00-.364 1.118l2.036 6.29c.3.921-.755 1.688-1.54 1.118l-5.37-3.905a1 1 0 00-1.176 0l-5.37 3.905c-.784.57-1.838-.197-1.54-1.118l2.036-6.29a1 1 0 00-.364-1.118l-5.37-3.905c-.783-.57-.38-1.81.588-1.81h6.631a1 1 0 00.95-.69l2.036-6.29z"></path>
                        </svg>
                        <span>My Reviews</span>
                    </a>
                </div>
            </div>
        </div>

        <!-- Notifications -->
        <a href="#" 
           class="flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-all duration-200 text-text-primary-light dark:text-text-primary-dark hover:bg-gray-50 dark:hover:bg-gray-800 hover:text-primary-light dark:hover:text-primary-dark hover:translate-x-1 mt-3">
            <svg class="mr-3 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V4a2 2 0 10-4 0v1.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"></path>
            </svg>
            <span>Notifications</span>
            <span class="ml-auto bg-red-100 text-red-600 dark:bg-red-900 dark:text-red-300 text-xs font-medium px-2 py-0.5 rounded-full">5</span>
        </a>

        <!-- Divider with heading -->
        <div class="my-4 border-t border-border-light dark:border-border-dark pt-4">
            <h3 class="px-4 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider mb-2">Support</h3>
        </div>

        <!-- Help & Support -->
        <a href="#" 
           class="flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-all duration-200 text-text-primary-light dark:text-text-primary-dark hover:bg-gray-50 dark:hover:bg-gray-800 hover:text-primary-light dark:hover:text-primary-dark hover:translate-x-1">
            <svg class="mr-3 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <span>Help Center</span>
        </a>
        
        <!-- Documentation -->
        <a href="#" 
           class="flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-all duration-200 text-text-primary-light dark:text-text-primary-dark hover:bg-gray-50 dark:hover:bg-gray-800 hover:text-primary-light dark:hover:text-primary-dark hover:translate-x-1">
            <svg class="mr-3 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
            </svg>
            <span>Documentation</span>
        </a>

    </nav>
    
    <!-- User Profile Section at Bottom -->
    <div class="sticky bottom-0 bg-white dark:bg-bg-dark border-t border-border-light dark:border-border-dark p-4">
        <div class="flex items-center space-x-3 mb-3">
            <div class="h-10 w-10 rounded-full bg-gradient-to-r from-primary-light to-blue-600 dark:from-primary-dark dark:to-blue-800 flex items-center justify-center text-white shadow-md">
                {{ substr(auth()->user()->name ?? 'U', 0, 1) }}
            </div>
            <div class="flex-1 min-w-0">
                <p class="text-sm font-medium text-text-primary-light dark:text-text-primary-dark truncate">{{ auth()->user()->name ?? 'User' }}</p>
                <p class="text-xs text-text-secondary-light dark:text-text-secondary-dark truncate">{{ auth()->user()->email ?? '<EMAIL>' }}</p>
            </div>
        </div>
        
        <!-- Logout Button -->
        <form method="POST" action="{{ route('logout') }}">
            @csrf
            <button type="submit" 
                    class="flex w-full items-center justify-center px-4 py-2 text-sm font-medium text-white bg-gradient-to-r from-red-500 to-red-600 dark:from-red-600 dark:to-red-700 rounded-lg hover:from-red-600 hover:to-red-700 dark:hover:from-red-700 dark:hover:to-red-800 transition-all duration-200 shadow-sm hover:shadow">
                <svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                </svg>
                Sign Out
            </button>
        </form>
    </div>
</div>