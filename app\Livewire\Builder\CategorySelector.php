<?php

namespace App\Livewire\Builder;

use App\Models\ComponentCategory;
use App\Services\BuilderService;
use Livewire\Component;

class CategorySelector extends Component
{
    public $categories = [];
    public $selectedCategorySlug = null;
    public $buildId;
    
    protected $listeners = [
        'selectCategory' => 'selectCategory',
        'componentAdded' => 'refreshCategories',
        'componentRemoved' => 'refreshCategories'
    ];
    
    public function mount($buildId = null)
    {
        $this->buildId = $buildId;
        $this->loadCategories();
    }
    
    public function loadCategories()
    {
        $builderService = app(BuilderService::class);
        $this->categories = $builderService->getComponentCategories();
        
        // If we have a build ID, mark categories that already have components
        if ($this->buildId) {
            $build = \App\Models\Build::with('components')->find($this->buildId);
            
            if ($build) {
                $selectedCategoryIds = $build->components->pluck('category_id')->toArray();
                
                foreach ($this->categories as $key => $category) {
                    $this->categories[$key]['has_component'] = in_array($category->id, $selectedCategoryIds);
                }
            }
        }
    }
    
    public function refreshCategories()
    {
        $this->loadCategories();
    }
    
    public function selectCategory($categorySlug)
    {
        $this->selectedCategorySlug = $categorySlug;
        $this->dispatch('categorySelected', $categorySlug);
    }
    
    public function render()
    {
        return view('livewire.builder.category-selector');
    }
}