@extends('layouts.app')

@section('content')
<div class="max-w-7xl mx-auto py-10 sm:px-6 lg:px-8">
    <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg">
        <div class="p-6 sm:px-20 bg-white border-b border-gray-200">
            <div class="flex justify-between items-center">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">Order #{{ $order->order_number }}</h1>
                    <p class="text-sm text-gray-500">Placed on {{ $order->created_at->format('F j, Y \a\t g:i A') }}</p>
                </div>
                <div>
                    <a href="{{ route('account.orders') }}" class="inline-flex items-center px-4 py-2 bg-gray-200 border border-transparent rounded-md font-semibold text-xs text-gray-700 uppercase tracking-widest hover:bg-gray-300 active:bg-gray-300 focus:outline-none focus:border-gray-300 focus:ring focus:ring-gray-200 disabled:opacity-25 transition">
                        <svg class="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                        </svg>
                        Back to Orders
                    </a>
                </div>
            </div>
        </div>

        <div class="p-6 sm:px-20 bg-white border-b border-gray-200">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <!-- Order Status -->
                <div class="md:col-span-3">
                    <div class="flex items-center justify-between">
                        <h2 class="text-lg font-medium text-gray-900">Status</h2>
                        <span class="px-3 py-1 inline-flex text-sm leading-5 font-semibold rounded-full bg-{{ $order->status === 'completed' ? 'green' : ($order->status === 'cancelled' ? 'red' : 'yellow') }}-100 text-{{ $order->status === 'completed' ? 'green' : ($order->status === 'cancelled' ? 'red' : 'yellow') }}-800">
                            {{ ucfirst($order->status) }}
                        </span>
                    </div>
                    @if($order->status === 'pending')
                        <div class="mt-2 flex justify-end">
                            <form method="POST" action="{{ route('orders.cancel', $order->id) }}" class="inline">
                                @csrf
                                @method('PUT')
                                <button type="submit" class="inline-flex items-center px-4 py-2 bg-red-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-red-700 active:bg-red-700 focus:outline-none focus:border-red-700 focus:ring focus:ring-red-200 disabled:opacity-25 transition" onclick="return confirm('Are you sure you want to cancel this order?');">
                                    Cancel Order
                                </button>
                            </form>
                        </div>
                    @endif
                </div>

                <!-- Shipping Information -->
                <div class="bg-gray-50 rounded-lg p-4">
                    <h2 class="text-lg font-medium text-gray-900 mb-2">Shipping Information</h2>
                    <div class="text-sm text-gray-600">
                        <p class="font-medium">{{ $order->shipping_name }}</p>
                        <p>{{ $order->shipping_address }}</p>
                        <p>{{ $order->shipping_city }}, {{ $order->shipping_state }} {{ $order->shipping_zip }}</p>
                        <p>{{ $order->shipping_country }}</p>
                        <p class="mt-2">{{ $order->shipping_phone }}</p>
                        <p>{{ $order->shipping_email }}</p>
                    </div>
                </div>

                <!-- Billing Information -->
                <div class="bg-gray-50 rounded-lg p-4">
                    <h2 class="text-lg font-medium text-gray-900 mb-2">Billing Information</h2>
                    <div class="text-sm text-gray-600">
                        <p class="font-medium">{{ $order->billing_name }}</p>
                        <p>{{ $order->billing_address }}</p>
                        <p>{{ $order->billing_city }}, {{ $order->billing_state }} {{ $order->billing_zip }}</p>
                        <p>{{ $order->billing_country }}</p>
                        <p class="mt-2">{{ $order->billing_phone }}</p>
                        <p>{{ $order->billing_email }}</p>
                    </div>
                </div>

                <!-- Payment Information -->
                <div class="bg-gray-50 rounded-lg p-4">
                    <h2 class="text-lg font-medium text-gray-900 mb-2">Payment Information</h2>
                    <div class="text-sm text-gray-600">
                        <p><span class="font-medium">Method:</span> Credit Card</p>
                        <p><span class="font-medium">Card:</span> •••• •••• •••• {{ substr($order->card_last_four ?? '0000', -4) }}</p>
                        <p><span class="font-medium">Payment Status:</span> {{ ucfirst($order->payment_status ?? 'Completed') }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Order Items -->
        <div class="p-6 sm:px-20 bg-white border-b border-gray-200">
            <h2 class="text-lg font-medium text-gray-900 mb-4">Order Items</h2>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Product</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Price</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quantity</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        @foreach($order->items as $item)
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        @if($item->component && $item->component->image_url)
                                            <div class="flex-shrink-0 h-10 w-10">
                                                <img class="h-10 w-10 rounded-md object-cover" src="{{ $item->component->image_url }}" alt="{{ $item->name }}">
                                            </div>
                                        @endif
                                        <div class="ml-4">
                                            <div class="text-sm font-medium text-gray-900">{{ $item->name }}</div>
                                            @if($item->component)
                                                <div class="text-sm text-gray-500">{{ $item->component->category->name }}</div>
                                            @endif
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${{ number_format($item->price, 2) }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ $item->quantity }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${{ number_format($item->price * $item->quantity, 2) }}</td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Order Summary -->
        <div class="p-6 sm:px-20 bg-white">
            <div class="flex justify-end">
                <div class="w-full md:w-1/3">
                    <div class="border-t border-gray-200 py-4">
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600">Subtotal</span>
                            <span class="text-gray-900 font-medium">${{ number_format($order->subtotal, 2) }}</span>
                        </div>
                        <div class="flex justify-between text-sm mt-2">
                            <span class="text-gray-600">Shipping</span>
                            <span class="text-gray-900 font-medium">${{ number_format($order->shipping_cost, 2) }}</span>
                        </div>
                        <div class="flex justify-between text-sm mt-2">
                            <span class="text-gray-600">Tax</span>
                            <span class="text-gray-900 font-medium">${{ number_format($order->tax, 2) }}</span>
                        </div>
                        @if($order->discount_amount > 0)
                        <div class="flex justify-between text-sm mt-2">
                            <span class="text-gray-600">Discount</span>
                            <span class="text-green-600 font-medium">-${{ number_format($order->discount_amount, 2) }}</span>
                        </div>
                        @endif
                        @if($order->coupon_code)
                        <div class="flex justify-between text-sm mt-2">
                            <span class="text-gray-600">Coupon</span>
                            <span class="text-green-600 font-medium">{{ $order->coupon_code }}</span>
                        </div>
                        @endif
                        <div class="flex justify-between text-base mt-4 border-t border-gray-200 pt-4">
                            <span class="text-gray-900 font-medium">Total</span>
                            <span class="text-gray-900 font-bold">${{ number_format($order->total_amount, 2) }}</span>
                        </div>
                    </div>
                </div>
            </div>
            @if($order->status !== 'cancelled' && $order->status !== 'completed')
                <div class="mt-6 flex justify-between">
                    <a href="{{ route('orders.track', $order->id) }}" class="inline-flex items-center px-4 py-2 bg-indigo-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-indigo-700 active:bg-indigo-700 focus:outline-none focus:border-indigo-700 focus:ring focus:ring-indigo-200 disabled:opacity-25 transition">
                        <svg class="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
                        </svg>
                        Track Order
                    </a>
                </div>
            @endif
        </div>
    </div>
</div>
@endsection