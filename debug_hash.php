<?php

$hashString = 'test_salt_key|success|||||||||<EMAIL>|Customer|Payment|100.00|TXN_test123|test_merchant_key';
echo 'Hash string: ' . $hashString . PHP_EOL;
echo 'Hash: ' . strtolower(hash('sha512', $hashString)) . PHP_EOL;

// Test the reverse hash for verification
$data = [
    'status' => 'success',
    'key' => 'test_merchant_key',
    'txnid' => 'TXN_test123',
    'amount' => '100.00',
    'productinfo' => 'Payment',
    'firstname' => 'Customer',
    'email' => '<EMAIL>',
    'udf1' => '',
    'udf2' => '',
    'udf3' => '',
    'udf4' => '',
    'udf5' => '',
];

$verifyHashString = 'test_salt_key|success|||||||||<EMAIL>|Customer|Payment|100.00|TXN_test123|test_merchant_key';
echo 'Verify hash string: ' . $verifyHashString . PHP_EOL;
echo 'Verify hash: ' . strtolower(hash('sha512', $verifyHashString)) . PHP_EOL;
echo 'Match: ' . ($hashString === $verifyHashString ? 'YES' : 'NO') . PHP_EOL;