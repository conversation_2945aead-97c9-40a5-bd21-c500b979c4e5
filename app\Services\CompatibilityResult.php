<?php

namespace App\Services;

use Illuminate\Support\Collection;

/**
 * Compatibility result class for component compatibility checks.
 */
class CompatibilityResult
{
    protected array $data;
    
    public function __construct(array $data)
    {
        $this->data = $data;
    }
    
    /**
     * Check if components are compatible.
     */
    public function isCompatible(): bool
    {
        return $this->data['compatible'] ?? false;
    }
    
    /**
     * Get compatibility issues.
     */
    public function getIssues(): array
    {
        return $this->data['issues'] ?? [];
    }
    
    /**
     * Get compatibility warnings.
     */
    public function getWarnings(): array
    {
        return $this->data['warnings'] ?? [];
    }
    
    /**
     * Get components that were checked.
     */
    public function getComponents(): Collection
    {
        return $this->data['components'] ?? collect();
    }
    
    /**
     * Check if there are any errors.
     */
    public function hasErrors(): bool
    {
        return !empty($this->getIssues());
    }
    
    /**
     * Check if there are any warnings.
     */
    public function hasWarnings(): bool
    {
        return !empty($this->getWarnings());
    }
    
    /**
     * Get the first error message.
     */
    public function getFirstError(): ?string
    {
        $issues = $this->getIssues();
        return !empty($issues) ? $issues[0]['message'] : null;
    }
    
    /**
     * Get all error messages.
     */
    public function getErrorMessages(): array
    {
        return array_map(function ($issue) {
            return $issue['message'];
        }, $this->getIssues());
    }
    
    /**
     * Get all warning messages.
     */
    public function getWarningMessages(): array
    {
        return array_map(function ($warning) {
            return $warning['message'];
        }, $this->getWarnings());
    }
    
    /**
     * Convert to array for JSON serialization.
     */
    public function toArray(): array
    {
        return [
            'compatible' => $this->isCompatible(),
            'has_errors' => $this->hasErrors(),
            'has_warnings' => $this->hasWarnings(),
            'issues' => $this->getIssues(),
            'warnings' => $this->getWarnings(),
            'error_messages' => $this->getErrorMessages(),
            'warning_messages' => $this->getWarningMessages(),
        ];
    }
}

/**
 * Validation result class for complete build validation.
 */
class ValidationResult
{
    protected array $data;
    
    public function __construct(array $data)
    {
        $this->data = $data;
    }
    
    /**
     * Check if build is valid.
     */
    public function isValid(): bool
    {
        return $this->data['valid'] ?? false;
    }
    
    /**
     * Get compatibility result.
     */
    public function getCompatibilityResult(): CompatibilityResult
    {
        return $this->data['compatibility_result'];
    }
    
    /**
     * Get total power consumption.
     */
    public function getPowerConsumption(): int
    {
        return $this->data['power_consumption'] ?? 0;
    }
    
    /**
     * Get PSU capacity.
     */
    public function getPsuCapacity(): ?int
    {
        return $this->data['psu_capacity'];
    }
    
    /**
     * Get power-related issues.
     */
    public function getPowerIssues(): array
    {
        return $this->data['power_issues'] ?? [];
    }
    
    /**
     * Check if there are power issues.
     */
    public function hasPowerIssues(): bool
    {
        return !empty($this->getPowerIssues());
    }
    
    /**
     * Get power efficiency percentage.
     */
    public function getPowerEfficiency(): ?float
    {
        $psuCapacity = $this->getPsuCapacity();
        if (!$psuCapacity) {
            return null;
        }
        
        return ($this->getPowerConsumption() / $psuCapacity) * 100;
    }
    
    /**
     * Get power recommendation.
     */
    public function getPowerRecommendation(): string
    {
        $efficiency = $this->getPowerEfficiency();
        
        if (!$efficiency) {
            return 'unknown';
        }
        
        if ($efficiency > 90) {
            return 'insufficient';
        } elseif ($efficiency > 80) {
            return 'tight';
        } elseif ($efficiency < 50) {
            return 'oversized';
        }
        
        return 'sufficient';
    }
    
    /**
     * Get all validation issues (compatibility + power).
     */
    public function getAllIssues(): array
    {
        $issues = $this->getCompatibilityResult()->getIssues();
        return array_merge($issues, $this->getPowerIssues());
    }
    
    /**
     * Get all validation warnings.
     */
    public function getAllWarnings(): array
    {
        return $this->getCompatibilityResult()->getWarnings();
    }
    
    /**
     * Convert to array for JSON serialization.
     */
    public function toArray(): array
    {
        return [
            'valid' => $this->isValid(),
            'power_consumption' => $this->getPowerConsumption(),
            'psu_capacity' => $this->getPsuCapacity(),
            'power_efficiency' => $this->getPowerEfficiency(),
            'power_recommendation' => $this->getPowerRecommendation(),
            'has_power_issues' => $this->hasPowerIssues(),
            'power_issues' => $this->getPowerIssues(),
            'compatibility' => $this->getCompatibilityResult()->toArray(),
            'all_issues' => $this->getAllIssues(),
            'all_warnings' => $this->getAllWarnings(),
        ];
    }
}

/**
 * Build operation result class.
 */
class BuildOperationResult
{
    protected array $data;
    
    public function __construct(array $data)
    {
        $this->data = $data;
    }
    
    /**
     * Check if operation was successful.
     */
    public function isSuccess(): bool
    {
        return $this->data['success'] ?? false;
    }
    
    /**
     * Get operation message.
     */
    public function getMessage(): string
    {
        return $this->data['message'] ?? '';
    }
    
    /**
     * Get operation warnings.
     */
    public function getWarnings(): array
    {
        return $this->data['warnings'] ?? [];
    }
    
    /**
     * Check if there are warnings.
     */
    public function hasWarnings(): bool
    {
        return !empty($this->getWarnings());
    }
    
    /**
     * Get the build component (for add operations).
     */
    public function getBuildComponent()
    {
        return $this->data['buildComponent'] ?? null;
    }
    
    /**
     * Get validation result (if available).
     */
    public function getValidationResult(): ?ValidationResult
    {
        return $this->data['validationResult'] ?? null;
    }
    
    /**
     * Convert to array for JSON serialization.
     */
    public function toArray(): array
    {
        $result = [
            'success' => $this->isSuccess(),
            'message' => $this->getMessage(),
            'has_warnings' => $this->hasWarnings(),
            'warnings' => $this->getWarnings(),
        ];
        
        if ($this->getBuildComponent()) {
            $result['build_component'] = $this->getBuildComponent();
        }
        
        if ($this->getValidationResult()) {
            $result['validation'] = $this->getValidationResult()->toArray();
        }
        
        return $result;
    }
}