<?php

namespace App\Http\Controllers;

use App\Models\Order;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class OrderController extends Controller
{
    /**
     * Display a listing of the user's orders.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {        
        $orders = Order::where('user_id', Auth::id())
            ->orderBy('created_at', 'desc')
            ->paginate(10);
        
        return view('orders.index', ['orders' => $orders]);
    }
    
    /**
     * Display the specified order.
     *
     * @param int $id
     * @return \Illuminate\View\View
     */
    public function show($id)
    {        
        $order = Order::where('id', $id)
            ->where('user_id', Auth::id())
            ->with('items')
            ->firstOrFail();
        
        return view('orders.show', ['order' => $order]);
    }
    
    /**
     * Cancel the specified order.
     *
     * @param int $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function cancel($id)
    {        
        $order = Order::where('id', $id)
            ->where('user_id', Auth::id())
            ->where('status', 'pending')
            ->firstOrFail();
        
        $order->status = 'cancelled';
        $order->save();
        
        return redirect()->route('orders.show', $order->id)
            ->with('message', 'Order cancelled successfully.');
    }
    
    /**
     * Track the specified order.
     *
     * @param int $id
     * @return \Illuminate\View\View
     */
    public function track($id)
    {        
        $order = Order::where('id', $id)
            ->where('user_id', Auth::id())
            ->firstOrFail();
        
        // In a real application, you would fetch tracking information from a shipping API
        $trackingInfo = [
            'status' => $order->status,
            'estimated_delivery' => now()->addDays(5)->format('Y-m-d'),
            'current_location' => 'Distribution Center',
            'history' => [
                [
                    'status' => 'Order Placed',
                    'date' => $order->created_at->format('Y-m-d H:i:s'),
                    'location' => 'Online'
                ],
                [
                    'status' => 'Processing',
                    'date' => $order->created_at->addHours(2)->format('Y-m-d H:i:s'),
                    'location' => 'Warehouse'
                ],
                [
                    'status' => 'Shipped',
                    'date' => $order->created_at->addDays(1)->format('Y-m-d H:i:s'),
                    'location' => 'Shipping Center'
                ]
            ]
        ];
        
        return view('orders.track', [
            'order' => $order,
            'trackingInfo' => $trackingInfo
        ]);
    }

    /**
     * Show order confirmation page (public route for guests).
     *
     * @param string $orderNumber
     * @return \Illuminate\View\View
     */
    public function confirmation($orderNumber)
    {
        $order = Order::where('order_number', $orderNumber)
            ->with('items.component')
            ->firstOrFail();

        return view('orders.confirmation', compact('order'));
    }

    /**
     * Display user's orders (account page).
     *
     * @return \Illuminate\View\View
     */
    public function userOrders()
    {
        $orders = Order::where('user_id', Auth::id())
            ->orderBy('created_at', 'desc')
            ->paginate(10);
        
        return view('account.orders', compact('orders'));
    }

    /**
     * Display specific order for user (account page).
     *
     * @param int $id
     * @return \Illuminate\View\View
     */
    public function userOrder($id)
    {
        $order = Order::where('id', $id)
            ->where('user_id', Auth::id())
            ->with('items.component')
            ->firstOrFail();
        
        return view('account.order', compact('order'));
    }
}