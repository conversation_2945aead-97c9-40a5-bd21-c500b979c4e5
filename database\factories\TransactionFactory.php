<?php

namespace Database\Factories;

use App\Models\Transaction;
use App\Models\User;
use App\Models\Product;
use Illuminate\Database\Eloquent\Factories\Factory;

class TransactionFactory extends Factory
{
    protected $model = Transaction::class;

    public function definition(): array
    {
        return [
            'user_id' => User::factory(),
            'product_id' => Product::factory(),
            'quantity' => $this->faker->numberBetween(1, 3),
            'gateway_name' => $this->faker->randomElement(['razorpay', 'payumoney', 'cashfree']),
            'amount' => $this->faker->randomFloat(2, 10, 1000),
            'currency' => 'INR',
            'status' => Transaction::STATUS_PENDING,
            'transaction_id' => 'TXN_' . strtoupper($this->faker->bothify('??########')) . '_' . time(),
            'gateway_transaction_id' => null,
            'payment_details' => [],
            'webhook_verified' => false,
            'failure_reason' => null,
        ];
    }

    public function completed(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => Transaction::STATUS_COMPLETED,
            'gateway_transaction_id' => $this->faker->bothify('pay_??########'),
            'webhook_verified' => true,
            'payment_details' => [
                'gateway_response' => [
                    'id' => $this->faker->bothify('order_??########'),
                    'status' => 'captured'
                ],
                'verified_at' => now()->toISOString()
            ]
        ]);
    }

    public function failed(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => Transaction::STATUS_FAILED,
            'failure_reason' => $this->faker->randomElement([
                'Payment declined by bank',
                'Insufficient funds',
                'Card expired',
                'Invalid card details',
                'Transaction timeout'
            ]),
            'payment_details' => [
                'error_code' => $this->faker->bothify('ERR_###'),
                'error_message' => 'Payment processing failed'
            ]
        ]);
    }

    public function processing(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => Transaction::STATUS_PROCESSING,
            'payment_details' => [
                'gateway_response' => [
                    'id' => $this->faker->bothify('order_??########'),
                    'status' => 'created'
                ]
            ]
        ]);
    }

    public function cancelled(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => Transaction::STATUS_CANCELLED,
            'failure_reason' => 'User cancelled payment'
        ]);
    }

    public function razorpay(): static
    {
        return $this->state(fn (array $attributes) => [
            'gateway_name' => 'razorpay',
            'gateway_transaction_id' => $this->faker->bothify('pay_??########'),
            'payment_details' => [
                'gateway_response' => [
                    'id' => $this->faker->bothify('order_??########'),
                    'amount' => ($attributes['amount'] ?? 100) * 100, // Razorpay uses paise
                    'currency' => 'INR'
                ]
            ]
        ]);
    }

    public function payumoney(): static
    {
        return $this->state(fn (array $attributes) => [
            'gateway_name' => 'payumoney',
            'gateway_transaction_id' => $this->faker->bothify('payumoney_??########'),
            'payment_details' => [
                'gateway_response' => [
                    'mihpayid' => $this->faker->bothify('??########'),
                    'status' => 'success'
                ]
            ]
        ]);
    }

    public function cashfree(): static
    {
        return $this->state(fn (array $attributes) => [
            'gateway_name' => 'cashfree',
            'gateway_transaction_id' => $this->faker->bothify('cf_payment_??########'),
            'payment_details' => [
                'gateway_response' => [
                    'payment_session_id' => $this->faker->bothify('session_??########'),
                    'order_id' => $attributes['transaction_id'] ?? $this->faker->bothify('TXN_??########')
                ]
            ]
        ]);
    }

    public function withUser(User $user): static
    {
        return $this->state(fn (array $attributes) => [
            'user_id' => $user->id
        ]);
    }

    public function withAmount(float $amount): static
    {
        return $this->state(fn (array $attributes) => [
            'amount' => $amount
        ]);
    }

    public function withGateway(string $gateway): static
    {
        return $this->state(fn (array $attributes) => [
            'gateway_name' => $gateway
        ]);
    }
}