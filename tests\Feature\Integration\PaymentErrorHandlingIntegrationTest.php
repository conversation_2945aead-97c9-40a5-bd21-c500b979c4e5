<?php

namespace Tests\Feature\Integration;

use Tests\TestCase;
use App\Models\User;
use App\Models\Transaction;
use App\Models\GatewaySetting;
use App\Services\Payment\Gateways\RazorpayService;
use App\Services\Payment\Gateways\PayUmoneyService;
use App\Services\Payment\Gateways\CashfreeService;
use App\Services\Payment\Exceptions\GatewayConfigurationException;
use App\Services\Payment\Exceptions\InvalidPaymentDataException;
use App\Services\Payment\Exceptions\PaymentGatewayException;
use App\Services\Payment\Exceptions\WebhookVerificationException;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Mockery;
use Razorpay\Api\Api;
use Razorpay\Api\Errors\BadRequestError;
use Razorpay\Api\Errors\ServerError;

class PaymentErrorHandlingIntegrationTest extends TestCase
{
    use DatabaseTransactions;

    protected User $user;
    protected array $gatewaySettings;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test user
        $this->user = User::factory()->create([
            'email' => '<EMAIL>'
        ]);

        // Create gateway settings for all three gateways
        $this->gatewaySettings = [
            GatewaySetting::GATEWAY_RAZORPAY => GatewaySetting::create([
                'gateway_name' => GatewaySetting::GATEWAY_RAZORPAY,
                'is_enabled' => true,
                'is_test_mode' => true,
                'settings' => [
                    'key_id' => 'rzp_test_1234567890',
                    'key_secret' => 'test_secret_key',
                    'webhook_secret' => 'test_webhook_secret'
                ]
            ]),
            GatewaySetting::GATEWAY_PAYUMONEY => GatewaySetting::create([
                'gateway_name' => GatewaySetting::GATEWAY_PAYUMONEY,
                'is_enabled' => true,
                'is_test_mode' => true,
                'settings' => [
                    'merchant_key' => 'test_merchant_key',
                    'salt' => 'test_salt_key',
                    'auth_header' => 'test_auth_header'
                ]
            ]),
            GatewaySetting::GATEWAY_CASHFREE => GatewaySetting::create([
                'gateway_name' => GatewaySetting::GATEWAY_CASHFREE,
                'is_enabled' => true,
                'is_test_mode' => true,
                'settings' => [
                    'app_id' => 'test_app_id',
                    'secret_key' => 'test_secret_key',
                    'client_id' => 'test_client_id',
                    'client_secret' => 'test_client_secret'
                ]
            ])
        ];
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /**
     * Test configuration error handling for all gateways
     */
    public function test_configuration_error_handling()
    {
        // Test missing gateway configuration
        $this->gatewaySettings[GatewaySetting::GATEWAY_RAZORPAY]->delete();
        
        $this->expectException(GatewayConfigurationException::class);
        $this->expectExceptionMessage('Razorpay gateway configuration not found');
        
        new RazorpayService();
    }

    public function test_missing_credentials_error_handling()
    {
        // Test missing Razorpay credentials
        $this->gatewaySettings[GatewaySetting::GATEWAY_RAZORPAY]->update([
            'settings' => [
                'key_id' => '',
                'key_secret' => '',
                'webhook_secret' => 'test_webhook_secret'
            ]
        ]);

        $this->expectException(GatewayConfigurationException::class);
        $this->expectExceptionMessage('Razorpay API credentials not configured');
        
        new RazorpayService();
    }

    public function test_payumoney_missing_credentials()
    {
        $this->gatewaySettings[GatewaySetting::GATEWAY_PAYUMONEY]->update([
            'settings' => [
                'merchant_key' => '',
                'salt' => '',
                'auth_header' => 'test_auth_header'
            ]
        ]);

        $this->expectException(GatewayConfigurationException::class);
        $this->expectExceptionMessage('PayUmoney API credentials not configured');
        
        new PayUmoneyService();
    }

    public function test_cashfree_missing_credentials()
    {
        $this->gatewaySettings[GatewaySetting::GATEWAY_CASHFREE]->update([
            'settings' => [
                'app_id' => '',
                'secret_key' => '',
                'client_id' => 'test_client_id',
                'client_secret' => 'test_client_secret'
            ]
        ]);

        $this->expectException(GatewayConfigurationException::class);
        $this->expectExceptionMessage('Cashfree API credentials not configured');
        
        new CashfreeService();
    }

    /**
     * Test payment data validation errors
     */
    public function test_payment_data_validation_errors()
    {
        $services = [
            'razorpay' => new RazorpayService(),
            'payumoney' => new PayUmoneyService(),
            'cashfree' => new CashfreeService()
        ];

        foreach ($services as $gatewayName => $service) {
            // Test missing amount
            try {
                $service->createPayment([
                    'currency' => 'INR',
                    'transaction_id' => 'test_txn',
                    'user_id' => $this->user->id
                ]);
                $this->fail("Expected InvalidPaymentDataException for missing amount in {$gatewayName}");
            } catch (InvalidPaymentDataException $e) {
                $this->assertStringContainsString('amount', $e->getMessage());
                $this->assertEquals('MISSING_REQUIRED_FIELD', $e->getErrorCode());
            }

            // Test invalid amount
            try {
                $service->createPayment([
                    'amount' => -100,
                    'currency' => 'INR',
                    'transaction_id' => 'test_txn',
                    'user_id' => $this->user->id
                ]);
                $this->fail("Expected InvalidPaymentDataException for invalid amount in {$gatewayName}");
            } catch (InvalidPaymentDataException $e) {
                $this->assertStringContainsString('positive number', $e->getMessage());
                $this->assertEquals('INVALID_AMOUNT', $e->getErrorCode());
            }

            // Test invalid currency (for Razorpay and Cashfree)
            if (in_array($gatewayName, ['razorpay', 'cashfree'])) {
                try {
                    $service->createPayment([
                        'amount' => 100,
                        'currency' => 'INVALID',
                        'transaction_id' => 'test_txn',
                        'user_id' => $this->user->id
                    ]);
                    $this->fail("Expected InvalidPaymentDataException for invalid currency in {$gatewayName}");
                } catch (InvalidPaymentDataException $e) {
                    $this->assertStringContainsString('3-character code', $e->getMessage());
                    $this->assertEquals('INVALID_CURRENCY', $e->getErrorCode());
                }
            }
        }
    }

    /**
     * Test Razorpay API error handling
     */
    public function test_razorpay_api_error_handling()
    {
        $transaction = Transaction::create([
            'user_id' => $this->user->id,
            'gateway_name' => GatewaySetting::GATEWAY_RAZORPAY,
            'amount' => 100.00,
            'currency' => 'INR',
            'status' => Transaction::STATUS_PENDING,
            'transaction_id' => 'TXN_' . uniqid(),
        ]);

        // Test BadRequestError
        $mockApi = Mockery::mock(Api::class);
        $mockOrder = Mockery::mock();
        
        $mockOrder->shouldReceive('create')
            ->once()
            ->andThrow(new BadRequestError('Invalid amount', 'BAD_REQUEST_ERROR', 400));

        $mockApi->order = $mockOrder;

        $service = new RazorpayService();
        $reflection = new \ReflectionClass($service);
        $razorpayProperty = $reflection->getProperty('razorpay');
        $razorpayProperty->setAccessible(true);
        $razorpayProperty->setValue($service, $mockApi);

        try {
            $service->createPayment([
                'amount' => 100.00,
                'currency' => 'INR',
                'user_id' => $this->user->id,
                'transaction_id' => $transaction->transaction_id,
            ]);
            $this->fail('Expected InvalidPaymentDataException');
        } catch (InvalidPaymentDataException $e) {
            $this->assertStringContainsString('Invalid amount', $e->getMessage());
            $this->assertEquals('RAZORPAY_BAD_REQUEST', $e->getErrorCode());
            $this->assertArrayHasKey('razorpay_error', $e->getContext());
        }

        // Test ServerError
        $mockOrder->shouldReceive('create')
            ->once()
            ->andThrow(new ServerError('Server error', 'SERVER_ERROR', 500));

        try {
            $service->createPayment([
                'amount' => 100.00,
                'currency' => 'INR',
                'user_id' => $this->user->id,
                'transaction_id' => $transaction->transaction_id,
            ]);
            $this->fail('Expected PaymentGatewayException');
        } catch (PaymentGatewayException $e) {
            $this->assertStringContainsString('Server error', $e->getMessage());
            $this->assertEquals('RAZORPAY_SERVER_ERROR', $e->getErrorCode());
        }

        // Test generic Exception
        $mockOrder->shouldReceive('create')
            ->once()
            ->andThrow(new \Exception('Generic error'));

        try {
            $service->createPayment([
                'amount' => 100.00,
                'currency' => 'INR',
                'user_id' => $this->user->id,
                'transaction_id' => $transaction->transaction_id,
            ]);
            $this->fail('Expected PaymentGatewayException');
        } catch (PaymentGatewayException $e) {
            $this->assertStringContainsString('Generic error', $e->getMessage());
            $this->assertEquals('RAZORPAY_PAYMENT_FAILED', $e->getErrorCode());
        }
    }

    /**
     * Test PayUmoney error handling
     */
    public function test_payumoney_error_handling()
    {
        $service = new PayUmoneyService();

        // Test missing transaction
        try {
            $service->createPayment([
                'amount' => 100.00,
                'transaction_id' => 'nonexistent_transaction',
            ]);
            $this->fail('Expected PaymentGatewayException');
        } catch (PaymentGatewayException $e) {
            $this->assertStringContainsString('Transaction not found', $e->getMessage());
            $this->assertEquals('PAYUMONEY_PAYMENT_FAILED', $e->getErrorCode());
        }

        // Test payment verification with missing transaction
        $result = $service->verifyPayment('nonexistent_transaction', [
            'status' => 'success',
            'txnid' => 'nonexistent_transaction',
            'amount' => '100.00',
            'hash' => 'test_hash'
        ]);

        $this->assertFalse($result);

        // Test payment verification with invalid hash
        $transaction = Transaction::create([
            'user_id' => $this->user->id,
            'gateway_name' => GatewaySetting::GATEWAY_PAYUMONEY,
            'amount' => 100.00,
            'currency' => 'INR',
            'status' => Transaction::STATUS_PENDING,
            'transaction_id' => 'TXN_' . uniqid(),
        ]);

        $result = $service->verifyPayment($transaction->transaction_id, [
            'status' => 'success',
            'txnid' => $transaction->transaction_id,
            'amount' => '100.00',
            'productinfo' => 'Payment',
            'firstname' => 'Customer',
            'email' => '<EMAIL>',
            'key' => 'test_merchant_key',
            'hash' => 'invalid_hash',
            'udf1' => '',
            'udf2' => '',
            'udf3' => '',
            'udf4' => '',
            'udf5' => '',
        ]);

        $this->assertFalse($result);

        // Verify transaction was updated to failed due to hash verification failure
        $transaction->refresh();
        $this->assertEquals(Transaction::STATUS_FAILED, $transaction->status);
        $this->assertEquals('Hash verification failed', $transaction->failure_reason);
    }

    /**
     * Test Cashfree API error handling
     */
    public function test_cashfree_api_error_handling()
    {
        $transaction = Transaction::create([
            'user_id' => $this->user->id,
            'gateway_name' => GatewaySetting::GATEWAY_CASHFREE,
            'amount' => 100.00,
            'currency' => 'INR',
            'status' => Transaction::STATUS_PENDING,
            'transaction_id' => 'TXN_' . uniqid(),
        ]);

        $service = new CashfreeService();

        // Test API error response
        Http::fake([
            'https://sandbox.cashfree.com/pg/orders' => Http::response([
                'message' => 'Invalid request',
                'code' => 'invalid_request_error'
            ], 400)
        ]);

        try {
            $service->createPayment([
                'amount' => 100.00,
                'currency' => 'INR',
                'transaction_id' => $transaction->transaction_id,
            ]);
            $this->fail('Expected PaymentGatewayException');
        } catch (PaymentGatewayException $e) {
            $this->assertStringContainsString('Invalid request', $e->getMessage());
            $this->assertEquals('CASHFREE_SESSION_FAILED', $e->getErrorCode());
        }

        // Test HTTP exception
        Http::fake([
            'https://sandbox.cashfree.com/pg/orders' => function () {
                throw new \Exception('Network error');
            }
        ]);

        try {
            $service->createPayment([
                'amount' => 100.00,
                'currency' => 'INR',
                'transaction_id' => $transaction->transaction_id,
            ]);
            $this->fail('Expected PaymentGatewayException');
        } catch (PaymentGatewayException $e) {
            $this->assertStringContainsString('Network error', $e->getMessage());
            $this->assertEquals('CASHFREE_SESSION_FAILED', $e->getErrorCode());
        }

        // Test missing transaction
        try {
            $service->createPayment([
                'amount' => 100.00,
                'currency' => 'INR',
                'transaction_id' => 'nonexistent_transaction',
            ]);
            $this->fail('Expected InvalidPaymentDataException');
        } catch (InvalidPaymentDataException $e) {
            $this->assertStringContainsString('Transaction not found', $e->getMessage());
            $this->assertEquals('TRANSACTION_NOT_FOUND', $e->getErrorCode());
        }
    }

    /**
     * Test payment verification error handling
     */
    public function test_payment_verification_error_handling()
    {
        // Test Razorpay verification with missing fields
        $transaction = Transaction::create([
            'user_id' => $this->user->id,
            'gateway_name' => GatewaySetting::GATEWAY_RAZORPAY,
            'amount' => 100.00,
            'currency' => 'INR',
            'status' => Transaction::STATUS_PENDING,
            'transaction_id' => 'TXN_RZP_' . uniqid(),
        ]);

        $razorpayService = new RazorpayService();

        $result = $razorpayService->verifyPayment($transaction->transaction_id, [
            'razorpay_order_id' => 'order_test123',
            // Missing razorpay_payment_id and razorpay_signature
        ]);

        $this->assertFalse($result);

        // Test Razorpay verification with API exception
        $mockApi = Mockery::mock(Api::class);
        $mockUtility = Mockery::mock();
        
        $mockUtility->shouldReceive('verifyPaymentSignature')
            ->once()
            ->andThrow(new \Exception('Signature verification failed'));

        $mockApi->utility = $mockUtility;

        $reflection = new \ReflectionClass($razorpayService);
        $razorpayProperty = $reflection->getProperty('razorpay');
        $razorpayProperty->setAccessible(true);
        $razorpayProperty->setValue($razorpayService, $mockApi);

        $result = $razorpayService->verifyPayment($transaction->transaction_id, [
            'razorpay_order_id' => 'order_test123',
            'razorpay_payment_id' => 'pay_test123',
            'razorpay_signature' => 'test_signature'
        ]);

        $this->assertFalse($result);
    }

    /**
     * Test webhook verification error handling
     */
    public function test_webhook_verification_error_handling()
    {
        // Test Razorpay webhook with missing secret
        $this->gatewaySettings[GatewaySetting::GATEWAY_RAZORPAY]->update([
            'settings' => [
                'key_id' => 'rzp_test_1234567890',
                'key_secret' => 'test_secret_key',
                // webhook_secret is missing
            ]
        ]);

        $razorpayService = new RazorpayService();

        $_SERVER['HTTP_X_RAZORPAY_SIGNATURE'] = 'test_signature';

        try {
            $razorpayService->handleWebhook(['event' => 'payment.captured', 'payload' => []]);
            $this->fail('Expected WebhookVerificationException');
        } catch (WebhookVerificationException $e) {
            $this->assertStringContainsString('Webhook secret not configured', $e->getMessage());
            $this->assertEquals('WEBHOOK_SECRET_MISSING', $e->getErrorCode());
        }

        // Test Cashfree webhook with missing signature
        $cashfreeService = new CashfreeService();

        if (isset($_SERVER['HTTP_X_WEBHOOK_SIGNATURE'])) {
            unset($_SERVER['HTTP_X_WEBHOOK_SIGNATURE']);
        }

        try {
            $cashfreeService->handleWebhook(['type' => 'PAYMENT_SUCCESS_WEBHOOK', 'data' => []]);
            $this->fail('Expected WebhookVerificationException');
        } catch (WebhookVerificationException $e) {
            $this->assertStringContainsString('Webhook signature missing', $e->getMessage());
            $this->assertEquals('WEBHOOK_SIGNATURE_MISSING', $e->getErrorCode());
        }

        // Test Cashfree webhook with missing timestamp
        $_SERVER['HTTP_X_WEBHOOK_SIGNATURE'] = 'test_signature';
        
        if (isset($_SERVER['HTTP_X_WEBHOOK_TIMESTAMP'])) {
            unset($_SERVER['HTTP_X_WEBHOOK_TIMESTAMP']);
        }

        try {
            $cashfreeService->handleWebhook(['type' => 'PAYMENT_SUCCESS_WEBHOOK', 'data' => []]);
            $this->fail('Expected WebhookVerificationException');
        } catch (WebhookVerificationException $e) {
            $this->assertStringContainsString('Webhook timestamp missing', $e->getMessage());
            $this->assertEquals('WEBHOOK_TIMESTAMP_MISSING', $e->getErrorCode());
        }
    }

    /**
     * Test payment status retrieval error handling
     */
    public function test_payment_status_retrieval_error_handling()
    {
        // Test Razorpay status retrieval with API error
        $transaction = Transaction::create([
            'user_id' => $this->user->id,
            'gateway_name' => GatewaySetting::GATEWAY_RAZORPAY,
            'amount' => 100.00,
            'currency' => 'INR',
            'status' => Transaction::STATUS_PENDING,
            'transaction_id' => 'TXN_' . uniqid(),
            'gateway_transaction_id' => 'pay_test123'
        ]);

        $mockApi = Mockery::mock(Api::class);
        $mockPayment = Mockery::mock();
        
        $mockPayment->shouldReceive('fetch')
            ->once()
            ->andThrow(new \Exception('API Error'));

        $mockApi->payment = $mockPayment;

        $razorpayService = new RazorpayService();
        $reflection = new \ReflectionClass($razorpayService);
        $razorpayProperty = $reflection->getProperty('razorpay');
        $razorpayProperty->setAccessible(true);
        $razorpayProperty->setValue($razorpayService, $mockApi);

        $status = $razorpayService->getPaymentStatus($transaction->transaction_id);
        
        $this->assertEquals(Transaction::STATUS_PENDING, $status);

        // Test Cashfree status retrieval with API error
        $cashfreeTransaction = Transaction::create([
            'user_id' => $this->user->id,
            'gateway_name' => GatewaySetting::GATEWAY_CASHFREE,
            'amount' => 100.00,
            'currency' => 'INR',
            'status' => Transaction::STATUS_PENDING,
            'transaction_id' => 'TXN_CF_' . uniqid(),
        ]);

        Http::fake([
            "https://sandbox.cashfree.com/pg/orders/{$cashfreeTransaction->transaction_id}" => Http::response([], 404)
        ]);

        $cashfreeService = new CashfreeService();
        $status = $cashfreeService->getPaymentStatus($cashfreeTransaction->transaction_id);
        
        $this->assertEquals(Transaction::STATUS_PENDING, $status);
    }

    /**
     * Test edge cases and boundary conditions
     */
    public function test_edge_cases_and_boundary_conditions()
    {
        // Test very large amount
        $transaction = Transaction::create([
            'user_id' => $this->user->id,
            'gateway_name' => GatewaySetting::GATEWAY_RAZORPAY,
            'amount' => 999999.99,
            'currency' => 'INR',
            'status' => Transaction::STATUS_PENDING,
            'transaction_id' => 'TXN_LARGE_' . uniqid(),
        ]);

        $mockApi = Mockery::mock(Api::class);
        $mockOrder = Mockery::mock();
        
        $mockOrder->shouldReceive('create')
            ->once()
            ->with([
                'amount' => 99999999, // 999999.99 * 100
                'currency' => 'INR',
                'receipt' => $transaction->transaction_id,
                'notes' => [
                    'user_id' => $this->user->id,
                    'transaction_id' => $transaction->transaction_id,
                ]
            ])
            ->andReturn([
                'id' => 'order_large_test',
                'amount' => 99999999,
                'currency' => 'INR',
                'status' => 'created'
            ]);

        $mockApi->order = $mockOrder;

        $razorpayService = new RazorpayService();
        $reflection = new \ReflectionClass($razorpayService);
        $razorpayProperty = $reflection->getProperty('razorpay');
        $razorpayProperty->setAccessible(true);
        $razorpayProperty->setValue($razorpayService, $mockApi);

        $result = $razorpayService->createPayment([
            'amount' => 999999.99,
            'currency' => 'INR',
            'user_id' => $this->user->id,
            'transaction_id' => $transaction->transaction_id,
        ]);

        $this->assertTrue($result['success']);
        $this->assertEquals('order_large_test', $result['gateway_order_id']);

        // Test very small amount
        $smallTransaction = Transaction::create([
            'user_id' => $this->user->id,
            'gateway_name' => GatewaySetting::GATEWAY_RAZORPAY,
            'amount' => 0.01,
            'currency' => 'INR',
            'status' => Transaction::STATUS_PENDING,
            'transaction_id' => 'TXN_SMALL_' . uniqid(),
        ]);

        $mockOrder->shouldReceive('create')
            ->once()
            ->with([
                'amount' => 1, // 0.01 * 100
                'currency' => 'INR',
                'receipt' => $smallTransaction->transaction_id,
                'notes' => [
                    'user_id' => $this->user->id,
                    'transaction_id' => $smallTransaction->transaction_id,
                ]
            ])
            ->andReturn([
                'id' => 'order_small_test',
                'amount' => 1,
                'currency' => 'INR',
                'status' => 'created'
            ]);

        $result = $razorpayService->createPayment([
            'amount' => 0.01,
            'currency' => 'INR',
            'user_id' => $this->user->id,
            'transaction_id' => $smallTransaction->transaction_id,
        ]);

        $this->assertTrue($result['success']);
        $this->assertEquals('order_small_test', $result['gateway_order_id']);
    }

    /**
     * Test concurrent error scenarios
     */
    public function test_concurrent_error_scenarios()
    {
        // Create multiple transactions that will fail
        $transactions = [];
        for ($i = 0; $i < 3; $i++) {
            $transactions[] = Transaction::create([
                'user_id' => $this->user->id,
                'gateway_name' => GatewaySetting::GATEWAY_RAZORPAY,
                'amount' => 100.00 + $i,
                'currency' => 'INR',
                'status' => Transaction::STATUS_PENDING,
                'transaction_id' => 'TXN_FAIL_' . $i . '_' . uniqid(),
            ]);
        }

        $mockApi = Mockery::mock(Api::class);
        $mockOrder = Mockery::mock();
        
        // Mock all calls to throw exceptions
        $mockOrder->shouldReceive('create')
            ->times(3)
            ->andThrow(new BadRequestError('Invalid amount', 'BAD_REQUEST_ERROR', 400));

        $mockApi->order = $mockOrder;

        $razorpayService = new RazorpayService();
        $reflection = new \ReflectionClass($razorpayService);
        $razorpayProperty = $reflection->getProperty('razorpay');
        $razorpayProperty->setAccessible(true);
        $razorpayProperty->setValue($razorpayService, $mockApi);

        // Process all payments and expect all to fail
        $exceptions = [];
        foreach ($transactions as $transaction) {
            try {
                $razorpayService->createPayment([
                    'amount' => $transaction->amount,
                    'currency' => 'INR',
                    'user_id' => $this->user->id,
                    'transaction_id' => $transaction->transaction_id,
                ]);
                $this->fail('Expected InvalidPaymentDataException');
            } catch (InvalidPaymentDataException $e) {
                $exceptions[] = $e;
                $this->assertStringContainsString('Invalid amount', $e->getMessage());
                $this->assertEquals('RAZORPAY_BAD_REQUEST', $e->getErrorCode());
            }
        }

        // Verify all exceptions were caught
        $this->assertCount(3, $exceptions);
    }

    /**
     * Test error logging integration
     */
    public function test_error_logging_integration()
    {
        Log::shouldReceive('error')
            ->once()
            ->with('Razorpay payment creation failed', Mockery::type('array'));

        $transaction = Transaction::create([
            'user_id' => $this->user->id,
            'gateway_name' => GatewaySetting::GATEWAY_RAZORPAY,
            'amount' => 100.00,
            'currency' => 'INR',
            'status' => Transaction::STATUS_PENDING,
            'transaction_id' => 'TXN_LOG_' . uniqid(),
        ]);

        $mockApi = Mockery::mock(Api::class);
        $mockOrder = Mockery::mock();
        
        $mockOrder->shouldReceive('create')
            ->once()
            ->andThrow(new \Exception('Test error for logging'));

        $mockApi->order = $mockOrder;

        $razorpayService = new RazorpayService();
        $reflection = new \ReflectionClass($razorpayService);
        $razorpayProperty = $reflection->getProperty('razorpay');
        $razorpayProperty->setAccessible(true);
        $razorpayProperty->setValue($razorpayService, $mockApi);

        try {
            $razorpayService->createPayment([
                'amount' => 100.00,
                'currency' => 'INR',
                'user_id' => $this->user->id,
                'transaction_id' => $transaction->transaction_id,
            ]);
            $this->fail('Expected PaymentGatewayException');
        } catch (PaymentGatewayException $e) {
            // Exception was expected and logged
            $this->assertStringContainsString('Test error for logging', $e->getMessage());
        }
    }
}