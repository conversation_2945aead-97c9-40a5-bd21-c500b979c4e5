<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Laravel Installer - System Requirements</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <style>
        /* Base Styles */
        .gradient-background {
            background: linear-gradient(135deg, #EBF4FF 0%, #E6FFFA 100%);
            min-height: 100vh;
        }

        /* Animation Keyframes */
        @keyframes slideIn {
            from { opacity: 0; transform: translateY(-20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes scaleIn {
            from { transform: scale(0.95); opacity: 0; }
            to { transform: scale(1); opacity: 1; }
        }

        /* Component Styles */
        .installer-card {
            animation: scaleIn 0.5s ease-out;
            background: white;
            border-radius: 1rem;
            box-shadow: 0 10px 25px -5px rgba(59, 130, 246, 0.1), 0 8px 10px -6px rgba(59, 130, 246, 0.1);
        }

        .installer-header {
            background: linear-gradient(135deg, #4F46E5 0%, #3B82F6 100%);
            border-radius: 1rem 1rem 0 0;
            padding: 2rem;
        }

        .requirement-item {
            animation: fadeIn 0.5s ease-out forwards;
            opacity: 0;
        }

        .requirement-item:nth-child(1) { animation-delay: 0.1s; }
        .requirement-item:nth-child(2) { animation-delay: 0.2s; }
        .requirement-item:nth-child(3) { animation-delay: 0.3s; }
        .requirement-item:nth-child(4) { animation-delay: 0.4s; }

        .hover-scale {
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }

        .hover-scale:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 6px -1px rgba(59, 130, 246, 0.2), 0 2px 4px -1px rgba(59, 130, 246, 0.1);
        }

        .progress-bar {
            display: flex;
            margin: 2rem 0;
            justify-content: space-between;
            position: relative;
        }

        .progress-bar::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 2px;
            background: #E5E7EB;
            transform: translateY(-50%);
            z-index: 0;
        }

        .progress-step {
            width: 2.5rem;
            height: 2.5rem;
            border-radius: 50%;
            background: white;
            border: 2px solid #E5E7EB;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            z-index: 1;
            transition: all 0.3s ease;
        }

        .progress-step.active {
            border-color: #3B82F6;
            background: #3B82F6;
            color: white;
        }

        .progress-step.completed {
            border-color: #10B981;
            background: #10B981;
            color: white;
        }

        .check-animation {
            animation: checkmark 0.5s ease-out forwards;
        }

        @keyframes checkmark {
            0% { transform: scale(0); opacity: 0; }
            50% { transform: scale(1.2); }
            100% { transform: scale(1); opacity: 1; }
        }
    </style>
</head>

<body class="gradient-background">
    <div class="min-h-screen flex items-center justify-center p-4">
        <div class="max-w-4xl w-full">
            <!-- Progress Bar -->
            <div class="progress-bar mb-8">
                <div class="progress-step completed">1</div>
                <div class="progress-step active">2</div>
                <div class="progress-step">3</div>
                <div class="progress-step">4</div>
                <div class="progress-step">5</div>
            </div>

            <div class="installer-card">
                <!-- Header -->
                <div class="installer-header text-center">
                    <div class="flex justify-center mb-6">
                        <i data-lucide="check-circle-2" class="h-16 w-16 text-white opacity-90"></i>
                    </div>
                    <h1 class="text-4xl font-bold text-white mb-2">System Requirements</h1>
                    <p class="text-blue-100 text-lg">Checking your server compatibility</p>
                </div>

                <!-- Content -->
                <div class="p-8">
                    <div class="space-y-8">
                        <!-- PHP Version -->
                        <div class="requirement-item bg-white rounded-xl border border-gray-200 overflow-hidden">
                            <div class="p-6 bg-gray-50 border-b border-gray-200">
                                <h2 class="text-xl font-semibold text-gray-900 flex items-center">
                                    <i data-lucide="cpu" class="h-6 w-6 mr-3 text-blue-600"></i>
                                    PHP Version
                                </h2>
                            </div>
                            <div class="p-6">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <p class="text-gray-700 font-medium">Current Version: PHP {{ phpversion() }}</p>
                                        <p class="text-sm text-gray-500 mt-1">Required: PHP >= 8.1</p>
                                    </div>
                                    @if ($results['php'])
                                        <div class="flex items-center text-green-500">
                                            <i data-lucide="check-circle" class="h-8 w-8 check-animation"></i>
                                        </div>
                                    @else
                                        <div class="flex items-center text-red-500">
                                            <i data-lucide="x-circle" class="h-8 w-8 check-animation"></i>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>

                        <!-- PHP Extensions -->
                        <div class="requirement-item bg-white rounded-xl border border-gray-200 overflow-hidden">
                            <div class="p-6 bg-gray-50 border-b border-gray-200">
                                <h2 class="text-xl font-semibold text-gray-900 flex items-center">
                                    <i data-lucide="puzzle" class="h-6 w-6 mr-3 text-blue-600"></i>
                                    Required Extensions
                                </h2>
                            </div>
                            <div class="p-6">
                                <div class="space-y-4">
                                    @foreach ($results['extensions'] as $extension => $installed)
                                        <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                                            <div>
                                                <p class="font-medium text-gray-900">{{ $extension }}</p>
                                                <p class="text-sm text-gray-500 mt-1">PHP Extension</p>
                                            </div>
                                            @if ($installed)
                                                <div class="flex items-center text-green-500">
                                                    <i data-lucide="check-circle" class="h-6 w-6 check-animation"></i>
                                                </div>
                                            @else
                                                <div class="flex items-center text-red-500">
                                                    <i data-lucide="x-circle" class="h-6 w-6 check-animation"></i>
                                                </div>
                                            @endif
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        </div>

                        <!-- Directory Permissions -->
                        <div class="requirement-item bg-white rounded-xl border border-gray-200 overflow-hidden">
                            <div class="p-6 bg-gray-50 border-b border-gray-200">
                                <h2 class="text-xl font-semibold text-gray-900 flex items-center">
                                    <i data-lucide="folder" class="h-6 w-6 mr-3 text-blue-600"></i>
                                    Directory Permissions
                                </h2>
                            </div>
                            <div class="p-6">
                                <div class="space-y-4">
                                    @foreach ($results['permissions'] as $directory => $isWritable)
                                        <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                                            <div>
                                                <p class="font-medium text-gray-900">{{ $directory }}</p>
                                                <p class="text-sm text-gray-500 mt-1">Write Permission Required</p>
                                            </div>
                                            @if ($isWritable)
                                                <div class="flex items-center text-green-500">
                                                    <i data-lucide="check-circle" class="h-6 w-6 check-animation"></i>
                                                </div>
                                            @else
                                                <div class="flex items-center text-red-500">
                                                    <i data-lucide="x-circle" class="h-6 w-6 check-animation"></i>
                                                </div>
                                            @endif
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        </div>

                        @if (!$canProceed)
                            <!-- Error Message -->
                            <div class="bg-red-50 border border-red-200 rounded-xl p-6">
                                <div class="flex items-start">
                                    <i data-lucide="alert-triangle" class="h-6 w-6 text-red-500 mt-0.5 mr-3"></i>
                                    <div>
                                        <h3 class="text-red-800 font-medium">Requirements Not Met</h3>
                                        <ul class="mt-2 text-sm text-red-700 space-y-1">
                                            @if (!$results['php'])
                                                <li>• Upgrade PHP to version 8.1 or higher</li>
                                            @endif
                                            @foreach ($results['extensions'] as $extension => $installed)
                                                @if (!$installed)
                                                    <li>• Install PHP extension: {{ $extension }}</li>
                                                @endif
                                            @endforeach
                                            @foreach ($results['permissions'] as $directory => $isWritable)
                                                @if (!$isWritable)
                                                    <li>• Grant write permission to: {{ $directory }}</li>
                                                @endif
                                            @endforeach
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        @endif

                        <!-- Action Buttons -->
                        <div class="flex space-x-4">
                            <a href="{{ route('installer.welcome') }}"
                                class="flex-1 flex justify-center items-center py-3 px-4 rounded-lg text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-all duration-200">
                                <i data-lucide="arrow-left" class="h-5 w-5 mr-2"></i>
                                Back
                            </a>

                            @if ($canProceed)
                                <a href="{{ route('installer.license') }}"
                                    class="hover-scale flex-1 flex justify-center items-center py-3 px-4 rounded-lg text-sm font-medium text-white bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200 shadow-lg">
                                    <span>Continue to License</span>
                                    <i data-lucide="arrow-right" class="h-5 w-5 ml-2"></i>
                                </a>
                            @else
                                <button disabled
                                    class="flex-1 flex justify-center items-center py-3 px-4 rounded-lg text-sm font-medium text-gray-400 bg-gray-100 cursor-not-allowed">
                                    <span>Continue to License</span>
                                    <i data-lucide="arrow-right" class="h-5 w-5 ml-2"></i>
                                </button>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Initialize Lucide icons
        lucide.createIcons();
    </script>
</body>

</html>
