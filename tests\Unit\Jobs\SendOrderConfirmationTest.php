<?php

namespace Tests\Unit\Jobs;

use App\Jobs\SendOrderConfirmation;
use App\Mail\OrderConfirmation;
use App\Models\EmailLog;
use App\Models\Order;
use App\Models\User;
use App\Services\EmailTrackingService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Queue;
use Tests\TestCase;

class SendOrderConfirmationTest extends TestCase
{
    use RefreshDatabase;

    public function test_job_sends_order_confirmation_email()
    {
        Mail::fake();
        
        $user = User::factory()->create([
            'notification_settings' => ['order_confirmation' => true],
        ]);
        $order = Order::factory()->create(['user_id' => $user->id]);

        $job = new SendOrderConfirmation($order);
        $job->handle(new EmailTrackingService());

        Mail::assertQueued(OrderConfirmation::class, function ($mail) use ($order) {
            return $mail->order->id === $order->id;
        });
    }

    public function test_job_skips_email_when_user_disabled_notifications()
    {
        Mail::fake();
        
        $user = User::factory()->create([
            'notification_settings' => ['order_confirmation' => false],
        ]);
        $order = Order::factory()->create(['user_id' => $user->id]);

        $job = new SendOrderConfirmation($order);
        $job->handle(new EmailTrackingService());

        Mail::assertNotQueued(OrderConfirmation::class);
    }

    public function test_job_creates_email_log_entry()
    {
        Mail::fake();
        
        $user = User::factory()->create([
            'notification_settings' => ['order_confirmation' => true],
        ]);
        $order = Order::factory()->create([
            'user_id' => $user->id,
            'order_number' => 'PCB123456789',
        ]);

        $job = new SendOrderConfirmation($order);
        $job->handle(new EmailTrackingService());

        $this->assertDatabaseHas('email_logs', [
            'type' => 'order_confirmation',
            'recipient' => $user->email,
            'subject' => 'Order Confirmation - PCB123456789',
            'status' => 'sent',
            'related_id' => $order->id,
            'related_type' => Order::class,
        ]);
    }

    public function test_job_handles_email_failure()
    {
        Mail::shouldReceive('to')->andThrow(new \Exception('SMTP Error'));
        
        $user = User::factory()->create([
            'notification_settings' => ['order_confirmation' => true],
        ]);
        $order = Order::factory()->create(['user_id' => $user->id]);

        $job = new SendOrderConfirmation($order);
        
        $this->expectException(\Exception::class);
        $job->handle(new EmailTrackingService());

        $this->assertDatabaseHas('email_logs', [
            'type' => 'order_confirmation',
            'status' => 'failed',
            'error_message' => 'SMTP Error',
        ]);
    }

    public function test_job_is_queueable()
    {
        Queue::fake();
        
        $user = User::factory()->create();
        $order = Order::factory()->create(['user_id' => $user->id]);

        SendOrderConfirmation::dispatch($order);

        Queue::assertPushed(SendOrderConfirmation::class, function ($job) use ($order) {
            return $job->order->id === $order->id;
        });
    }

    public function test_job_has_correct_retry_configuration()
    {
        $user = User::factory()->create();
        $order = Order::factory()->create(['user_id' => $user->id]);

        $job = new SendOrderConfirmation($order);

        $this->assertEquals(3, $job->tries);
        $this->assertEquals(60, $job->timeout);
        $this->assertEquals([30, 120, 300], $job->backoff());
    }

    public function test_job_logs_retry_attempts()
    {
        Mail::fake();
        
        $user = User::factory()->create([
            'notification_settings' => ['order_confirmation' => true],
        ]);
        $order = Order::factory()->create(['user_id' => $user->id]);

        // Mock the attempts method to return 2 (simulating a retry)
        $job = new class($order) extends SendOrderConfirmation {
            public function attempts(): int
            {
                return 2;
            }
        };

        $job->handle(new EmailTrackingService());

        // Should create an email log with attempt count > 1
        $this->assertDatabaseHas('email_logs', [
            'type' => 'order_confirmation',
            'related_id' => $order->id,
            'attempts' => 2,
        ]);
    }
}