<?php

/**
 * Test Order Seeder Script
 * 
 * This script specifically tests the OrderSeeder to ensure it works
 * with the correct database schema.
 */

require_once 'vendor/autoload.php';

use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "🧪 Testing Order Seeder...\n";
echo "==========================\n\n";

// Check database connection
try {
    DB::connection()->getPdo();
    echo "✅ Database connection successful\n";
} catch (Exception $e) {
    echo "❌ Database connection failed: " . $e->getMessage() . "\n";
    exit(1);
}

// Check if orders table exists and show its structure
echo "\n📋 Checking orders table structure...\n";
if (Schema::hasTable('orders')) {
    $columns = Schema::getColumnListing('orders');
    echo "✅ Orders table exists with columns:\n";
    foreach ($columns as $column) {
        echo "   - {$column}\n";
    }
} else {
    echo "❌ Orders table does not exist\n";
    exit(1);
}

// Check prerequisites
echo "\n🔍 Checking prerequisites...\n";

$userCount = DB::table('users')->count();
echo "Users: {$userCount}\n";

$componentCount = DB::table('components')->count();
echo "Components: {$componentCount}\n";

$buildCount = DB::table('builds')->count();
echo "Builds: {$buildCount}\n";

if ($userCount === 0) {
    echo "⚠️  No users found, running UserSeeder first...\n";
    try {
        Artisan::call('db:seed', ['--class' => 'Database\\Seeders\\UserSeeder']);
        echo "✅ UserSeeder completed\n";
    } catch (Exception $e) {
        echo "❌ UserSeeder failed: " . $e->getMessage() . "\n";
    }
}

if ($componentCount === 0) {
    echo "⚠️  No components found, running ComponentSeeder first...\n";
    try {
        Artisan::call('db:seed', ['--class' => 'Database\\Seeders\\ComponentCategorySeeder']);
        Artisan::call('db:seed', ['--class' => 'Database\\Seeders\\ComponentSeeder']);
        echo "✅ Component seeders completed\n";
    } catch (Exception $e) {
        echo "❌ Component seeders failed: " . $e->getMessage() . "\n";
    }
}

// Test OrderSeeder
echo "\n🚀 Running OrderSeeder...\n";
try {
    $startTime = microtime(true);
    Artisan::call('db:seed', ['--class' => 'Database\\Seeders\\OrderSeeder']);
    $duration = round((microtime(true) - $startTime) * 1000);
    
    echo "✅ OrderSeeder completed successfully ({$duration}ms)\n";
    
    // Show results
    $orderCount = DB::table('orders')->count();
    $orderItemCount = DB::table('order_items')->count();
    
    echo "\n📊 Results:\n";
    echo "Orders created: {$orderCount}\n";
    echo "Order items created: {$orderItemCount}\n";
    
    // Show sample order
    $sampleOrder = DB::table('orders')->first();
    if ($sampleOrder) {
        echo "\n📄 Sample order:\n";
        echo "Order Number: {$sampleOrder->order_number}\n";
        echo "Status: {$sampleOrder->status}\n";
        echo "Total: ₹{$sampleOrder->total}\n";
        echo "Customer: {$sampleOrder->billing_name}\n";
    }
    
} catch (Exception $e) {
    echo "❌ OrderSeeder failed: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . ":" . $e->getLine() . "\n";
    
    // Show more details for SQL errors
    if ($e instanceof \Illuminate\Database\QueryException) {
        echo "SQL Error: " . $e->getMessage() . "\n";
    }
}

echo "\n✨ Test completed!\n";