<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up()
    {
        Schema::create('blog_post_tags', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('slug')->unique();
            $table->text('description')->nullable();
            $table->timestamps();
        });

        Schema::create('blog_post_tag', function (Blueprint $table) {
            $table->unsignedBigInteger('blog_post_id');
            $table->unsignedBigInteger('blog_post_tag_id');
            $table->timestamps();

            $table->primary(['blog_post_id', 'blog_post_tag_id']);

            $table->foreign('blog_post_id')
                ->references('id')
                ->on('blog_posts')
                ->onUpdate('cascade')
                ->onDelete('cascade');

            $table->foreign('blog_post_tag_id')
                ->references('id')
                ->on('blog_post_tags')
                ->onUpdate('cascade')
                ->onDelete('cascade');
        });
    }

    public function down()
    {
        Schema::dropIfExists('blog_post_tag');
        Schema::dropIfExists('blog_post_tags');
    }
};
