<?php

namespace App\Livewire\Builder;

use App\Models\Build;
use App\Models\BuildComponent;
use App\Models\ComponentCategory;
use App\Services\BuilderService;
use Livewire\Component;

class BuildSummary extends Component
{
    public $buildId;
    public $build;
    public $buildComponents = [];
    public $categories = [];
    public $totalPrice = 0;
    public $isComplete = false;
    public $compatibilityIssues = [];
    
    protected $listeners = [
        'buildUpdated' => 'loadBuild',
        'componentAdded' => 'loadBuild',
        'componentRemoved' => 'loadBuild'
    ];
    
    public function mount($buildId = null)
    {
        $this->buildId = $buildId;
        $this->loadBuild();
    }
    
    public function loadBuild()
    {
        if (!$this->buildId) {
            return;
        }
        
        $this->build = Build::with(['components.component', 'components.category'])->find($this->buildId);
        
        if (!$this->build) {
            return;
        }
        
        $this->buildComponents = $this->build->components;
        $this->totalPrice = $this->build->total_price;
        $this->isComplete = $this->build->is_complete;
        $this->compatibilityIssues = $this->build->compatibility_issues ?? [];
        
        // Get all categories for display
        $this->categories = ComponentCategory::orderBy('display_order')->get();
        
        // Dispatch compatibility check event (Livewire v3 syntax)
        $this->dispatch('checkCompatibility', $this->buildComponents->pluck('component_id')->toArray());
    }
    
    public function removeComponent($buildComponentId)
    {
        $buildComponent = BuildComponent::find($buildComponentId);
        
        if ($buildComponent && $buildComponent->build_id == $this->buildId) {
            $buildComponent->delete();
            
            // Update build total price and completion status
            $builderService = app(BuilderService::class);
            $builderService->updateBuild($this->buildId, [
                'recalculate_price' => true,
                'check_completion' => true,
                'check_compatibility' => true
            ]);
            
            $this->loadBuild();
            $this->dispatch('componentRemoved', $buildComponentId);
        }
    }
    
    public function saveBuild()
    {
        if (!$this->build) {
            return;
        }
        
        $this->validate([
            'build.name' => 'required|string|max:255',
            'build.description' => 'nullable|string|max:1000',
            'build.is_public' => 'boolean',
        ]);
        
        $this->build->save();
        
        $this->dispatch('buildSaved', $this->buildId);
        session()->flash('message', 'Build saved successfully!');
    }
    
    public function render()
    {
        return view('livewire.builder.build-summary');
    }
}