@extends('layouts.app')
@section('content')
    <div class="bg-gray-900 text-white min-h-screen">
        <!-- Hero Section -->
        <section class="relative h-screen flex items-center overflow-hidden">
            <!-- Parallax Background Layers -->
            <div class="parallax-layer" data-speed="0.1">
                <div class="absolute inset-0 bg-gradient-to-br from-gray-900 via-purple-900/20 to-gray-900"></div>
            </div>

            <div class="parallax-layer hexagon-pattern" data-speed="0.3"></div>

            <div class="parallax-layer" data-speed="0.5">
                <div class="absolute inset-0 bg-gradient-to-tr from-transparent via-pink-500/5 to-cyan-500/5"></div>
            </div>

            <!-- Gaming Particles -->
            <div class="parallax-layer" data-speed="0.7" id="particles-container"></div>

            <!-- Main Content Grid -->
            <div class="relative z-10 container mx-auto px-6 lg:px-12 grid lg:grid-cols-2 gap-12 items-center h-full">
                <!-- Left Content -->
                <div class="space-y-8">
                    <!-- Main Headline -->
                    <h1 class="text-5xl lg:text-7xl font-black gamer-text glitch-effect" data-text="Level Up Your Game">
                        <span class="gaming-gradient bg-clip-text text-transparent">
                            Level Up Your Game
                        </span>
                    </h1>

                    <!-- Subheadline -->
                    <h2 class="text-3xl lg:text-5xl font-bold gamer-text">
                        <span class="text-white">with</span>
                        <span class="text-pink-400"> Custom Rigs</span>
                    </h2>

                    <!-- Description -->
                    <p class="text-lg lg:text-xl text-gray-300 max-w-lg leading-relaxed">
                        Dominate the battlefield with precision-engineered gaming rigs.
                        Ultra-high performance meets cutting-edge RGB aesthetics.
                    </p>

                    <!-- Stats -->
                    <div class="grid grid-cols-3 gap-6 py-6">
                        <div class="text-center">
                            <div class="text-2xl font-bold text-pink-400 mb-1">240+</div>
                            <div class="text-sm text-gray-400 uppercase tracking-wider">FPS Ready</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-cyan-400 mb-1">4K</div>
                            <div class="text-sm text-gray-400 uppercase tracking-wider">Gaming</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-green-400 mb-1">VR</div>
                            <div class="text-sm text-gray-400 uppercase tracking-wider">Ready</div>
                        </div>
                    </div>

                    <!-- CTA Button -->
                    <div class="pt-4">
                        <button
                            class="group relative px-8 py-4 bg-gradient-to-r from-pink-500 to-cyan-500 rounded-lg font-bold text-lg uppercase tracking-wider transition-all duration-300 hover:scale-105 hover:shadow-2xl neon-glow">
                            <span class="relative z-10">Build Now</span>
                            <div
                                class="absolute inset-0 bg-gradient-to-r from-pink-600 to-cyan-600 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                            </div>
                        </button>
                    </div>
                </div>

                <!-- Right Content - Gaming PC -->
                <div class="flex justify-center lg:justify-end">
                    <div class="relative">
                        <!-- PC Case with Tilt Effect -->
                        <div class="pc-case-container" data-tilt data-tilt-max="15" data-tilt-speed="1000"
                            data-tilt-perspective="1000">
                            <div class="pc-case w-80 h-96 lg:w-96 lg:h-[28rem] rounded-lg relative transform-gpu">
                                <!-- RGB Strips -->
                                <div class="rgb-strip top-4 left-4 right-4"></div>
                                <div class="rgb-strip bottom-4 left-4 right-4"></div>
                                <div class="rgb-strip top-4 bottom-4 left-4 w-1"></div>
                                <div class="rgb-strip top-4 bottom-4 right-4 w-1"></div>

                                <!-- Power Button -->
                                <div class="power-button top-6 right-6"></div>

                                <!-- Front Panel -->
                                <div class="absolute inset-4 bg-gray-800 rounded-lg border border-gray-600">
                                    <!-- Mesh Pattern -->
                                    <div class="absolute inset-4 bg-gray-700 rounded opacity-50"
                                        style="background-image: repeating-linear-gradient(45deg, transparent, transparent 2px, rgba(255,255,255,0.1) 2px, rgba(255,255,255,0.1) 4px);">
                                    </div>

                                    <!-- RGB Glow Areas -->
                                    <div
                                        class="absolute top-8 left-8 right-8 h-16 bg-gradient-to-r from-pink-500/30 to-cyan-500/30 rounded blur-sm">
                                    </div>
                                    <div
                                        class="absolute bottom-8 left-8 right-8 h-16 bg-gradient-to-r from-purple-500/30 to-green-500/30 rounded blur-sm">
                                    </div>

                                    <!-- Ventilation Grilles -->
                                    <div
                                        class="absolute top-20 left-8 right-8 h-8 bg-gray-900 rounded grid grid-cols-12 gap-1 p-1">
                                        <div class="bg-gray-600 rounded-full"></div>
                                        <div class="bg-gray-600 rounded-full"></div>
                                        <div class="bg-gray-600 rounded-full"></div>
                                        <div class="bg-gray-600 rounded-full"></div>
                                        <div class="bg-gray-600 rounded-full"></div>
                                        <div class="bg-gray-600 rounded-full"></div>
                                        <div class="bg-gray-600 rounded-full"></div>
                                        <div class="bg-gray-600 rounded-full"></div>
                                        <div class="bg-gray-600 rounded-full"></div>
                                        <div class="bg-gray-600 rounded-full"></div>
                                        <div class="bg-gray-600 rounded-full"></div>
                                        <div class="bg-gray-600 rounded-full"></div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Floating Gaming Icons -->
                        <div
                            class="absolute -top-4 -left-4 w-8 h-8 bg-pink-500 rounded-full flex items-center justify-center animate-bounce">
                            <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M10 2L3 7v11a2 2 0 002 2h10a2 2 0 002-2V7l-7-5z" />
                            </svg>
                        </div>

                        <div
                            class="absolute -bottom-4 -right-4 w-8 h-8 bg-cyan-500 rounded-full flex items-center justify-center animate-pulse">
                            <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path
                                    d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z" />
                            </svg>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Scroll Indicator -->
            <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 text-center">
                <div class="text-sm text-gray-400 mb-2 uppercase tracking-wider">Scroll to Explore</div>
                <div class="w-px h-12 bg-gradient-to-b from-pink-500 to-transparent mx-auto animate-pulse"></div>
            </div>
        </section>

        <!-- Additional Section for Scroll Effect -->
        <section class="h-screen bg-gradient-to-br from-gray-900 to-purple-900 flex items-center justify-center">
            <div class="text-center">
                <h2 class="text-4xl font-bold text-white mb-4">Ready to Dominate?</h2>
                <p class="text-xl text-gray-300">Custom gaming rigs built for champions</p>
            </div>
        </section>
    </div>
@endsection
@push('styles')
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Rajdhani:wght@300;400;500;600;700&family=Orbitron:wght@400;700;900&display=swap');

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Rajdhani', sans-serif;
            overflow-x: hidden;
            background: #0a0a0a;
        }

        .gamer-text {
            font-family: 'Orbitron', monospace;
            text-transform: uppercase;
            letter-spacing: 2px;
            text-shadow: 0 0 20px rgba(255, 0, 102, 0.5), 0 0 40px rgba(0, 255, 255, 0.3);
        }

        .neon-glow {
            box-shadow:
                0 0 20px rgba(255, 0, 102, 0.4),
                0 0 40px rgba(0, 255, 255, 0.3),
                inset 0 0 20px rgba(255, 255, 255, 0.1);
        }

        .gaming-gradient {
            background: linear-gradient(135deg,
                    #ff0066 0%,
                    #ff6600 25%,
                    #00ff99 50%,
                    #0066ff 75%,
                    #9900ff 100%);
            background-size: 300% 300%;
            animation: gradientShift 4s ease infinite;
        }

        @keyframes gradientShift {
            0% {
                background-position: 0% 50%;
            }

            50% {
                background-position: 100% 50%;
            }

            100% {
                background-position: 0% 50%;
            }
        }

        .hexagon-pattern {
            background-image:
                radial-gradient(circle at 1px 1px, rgba(255, 0, 102, 0.15) 1px, transparent 0);
            background-size: 20px 20px;
            animation: patternMove 20s linear infinite;
        }

        @keyframes patternMove {
            0% {
                background-position: 0 0;
            }

            100% {
                background-position: 20px 20px;
            }
        }

        .parallax-layer {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            will-change: transform;
        }

        .gaming-particle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: linear-gradient(45deg, #ff0066, #00ffff);
            border-radius: 50%;
            animation: floatParticle 6s ease-in-out infinite;
        }

        @keyframes floatParticle {

            0%,
            100% {
                transform: translateY(0px) rotate(0deg) scale(1);
                opacity: 0.7;
            }

            33% {
                transform: translateY(-30px) rotate(120deg) scale(1.2);
                opacity: 1;
            }

            66% {
                transform: translateY(20px) rotate(240deg) scale(0.8);
                opacity: 0.5;
            }
        }

        .glitch-effect {
            position: relative;
            animation: glitch 3s infinite;
        }

        .glitch-effect::before,
        .glitch-effect::after {
            content: attr(data-text);
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: transparent;
        }

        .glitch-effect::before {
            animation: glitch-1 0.5s infinite;
            color: #ff0066;
            z-index: -1;
        }

        .glitch-effect::after {
            animation: glitch-2 0.5s infinite;
            color: #00ffff;
            z-index: -2;
        }

        @keyframes glitch {

            0%,
            100% {
                transform: translate(0);
            }

            20% {
                transform: translate(-2px, 2px);
            }

            40% {
                transform: translate(-2px, -2px);
            }

            60% {
                transform: translate(2px, 2px);
            }

            80% {
                transform: translate(2px, -2px);
            }
        }

        @keyframes glitch-1 {

            0%,
            100% {
                transform: translate(0);
            }

            20% {
                transform: translate(-2px, 2px);
            }

            40% {
                transform: translate(-2px, -2px);
            }

            60% {
                transform: translate(2px, 2px);
            }

            80% {
                transform: translate(2px, -2px);
            }
        }

        @keyframes glitch-2 {

            0%,
            100% {
                transform: translate(0);
            }

            20% {
                transform: translate(2px, -2px);
            }

            40% {
                transform: translate(2px, 2px);
            }

            60% {
                transform: translate(-2px, -2px);
            }

            80% {
                transform: translate(-2px, 2px);
            }
        }

        .pc-case {
            background: linear-gradient(135deg,
                    #1a1a1a 0%,
                    #2d2d2d 25%,
                    #1a1a1a 50%,
                    #404040 75%,
                    #1a1a1a 100%);
            border: 2px solid #333;
            position: relative;
            overflow: hidden;
        }

        .pc-case::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg,
                    transparent 40%,
                    rgba(255, 0, 102, 0.1) 50%,
                    transparent 60%);
            animation: shine 3s ease-in-out infinite;
        }

        @keyframes shine {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }

        .rgb-strip {
            position: absolute;
            height: 4px;
            background: linear-gradient(90deg,
                    #ff0066, #ff6600, #00ff99, #0066ff, #9900ff, #ff0066);
            background-size: 200% 100%;
            animation: rgbFlow 2s linear infinite;
        }

        @keyframes rgbFlow {
            0% {
                background-position: 0% 0%;
            }

            100% {
                background-position: 200% 0%;
            }
        }

        .power-button {
            position: absolute;
            width: 12px;
            height: 12px;
            background: radial-gradient(circle, #00ff99, #00cc77);
            border-radius: 50%;
            animation: pulse 2s ease-in-out infinite;
        }

        @keyframes pulse {

            0%,
            100% {
                box-shadow: 0 0 10px rgba(0, 255, 153, 0.5);
                transform: scale(1);
            }

            50% {
                box-shadow: 0 0 20px rgba(0, 255, 153, 0.8);
                transform: scale(1.1);
            }
        }
    </style>
@endpush
@push('scripts')
    <script>
        // Initialize Vanilla Tilt
        VanillaTilt.init(document.querySelector('.pc-case-container'), {
            max: 15,
            speed: 1000,
            glare: true,
            "max-glare": 0.2,
            gyroscope: true
        });

        // Create gaming particles
        function createGamingParticles() {
            const container = document.getElementById('particles-container');
            const particleCount = 30;

            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                particle.className = 'gaming-particle';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.top = Math.random() * 100 + '%';
                particle.style.animationDelay = Math.random() * 6 + 's';
                particle.style.animationDuration = (Math.random() * 4 + 3) + 's';

                // Random colors
                const colors = [
                    'linear-gradient(45deg, #ff0066, #ff6600)',
                    'linear-gradient(45deg, #00ff99, #0066ff)',
                    'linear-gradient(45deg, #9900ff, #ff0066)',
                    'linear-gradient(45deg, #00ffff, #ff00ff)'
                ];
                particle.style.background = colors[Math.floor(Math.random() * colors.length)];

                container.appendChild(particle);
            }
        }

        // Parallax scroll effect
        function initParallax() {
            const parallaxLayers = document.querySelectorAll('.parallax-layer');

            function updateParallax() {
                const scrolled = window.pageYOffset;
                const rate = scrolled * -0.5;

                parallaxLayers.forEach(layer => {
                    const speed = layer.dataset.speed;
                    const yPos = -(scrolled * speed);
                    layer.style.transform = `translateY(${yPos}px)`;
                });
            }

            window.addEventListener('scroll', updateParallax);
            window.addEventListener('resize', updateParallax);
        }

        // Mouse movement effects
        function initMouseEffects() {
            const hero = document.querySelector('section');

            hero.addEventListener('mousemove', (e) => {
                const {
                    clientX,
                    clientY
                } = e;
                const {
                    innerWidth,
                    innerHeight
                } = window;

                const xPos = (clientX / innerWidth) * 100;
                const yPos = (clientY / innerHeight) * 100;

                // Update particle positions slightly based on mouse
                const particles = document.querySelectorAll('.gaming-particle');
                particles.forEach((particle, index) => {
                    const factor = (index % 3 + 1) * 0.5;
                    particle.style.transform =
                        `translate(${xPos * factor * 0.1}px, ${yPos * factor * 0.1}px)`;
                });
            });
        }

        // Initialize everything
        document.addEventListener('DOMContentLoaded', () => {
            createGamingParticles();
            initParallax();
            initMouseEffects();
        });

        // CTA Button interaction
        document.querySelector('button').addEventListener('click', () => {
            // Add click animation
            const button = document.querySelector('button');
            button.style.transform = 'scale(0.95)';
            setTimeout(() => {
                button.style.transform = 'scale(1.05)';
                setTimeout(() => {
                    button.style.transform = 'scale(1)';
                }, 100);
            }, 100);
        });
    </script>
@endpush
