<?php

namespace App\Livewire\Admin;

use App\Models\Order;
use Livewire\Component as LivewireComponent;
use Livewire\WithPagination;

class OrderManagement extends LivewireComponent
{
    use WithPagination;

    public $search = '';
    public $statusFilter = '';
    public $dateFilter = '';
    public $sortField = 'created_at';
    public $sortDirection = 'desc';
    public $perPage = 15;
    
    public $selectedOrder = null;
    public $showingOrderDetails = false;
    public $newStatus = '';
    public $trackingNumber = '';
    public $trackingCarrier = '';
    public $trackingUrl = '';
    public $orderNote = '';

    protected $queryString = [
        'search' => ['except' => ''],
        'statusFilter' => ['except' => ''],
        'dateFilter' => ['except' => ''],
        'sortField' => ['except' => 'created_at'],
        'sortDirection' => ['except' => 'desc'],
    ];

    public function updatingSearch()
    {
        $this->resetPage();
    }

    public function updatingStatusFilter()
    {
        $this->resetPage();
    }

    public function updatingDateFilter()
    {
        $this->resetPage();
    }

    public function sortBy($field)
    {
        if ($this->sortField === $field) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortDirection = 'asc';
        }
        
        $this->sortField = $field;
    }

    public function viewOrder($orderId)
    {
        $this->selectedOrder = Order::with(['user', 'items.component'])->findOrFail($orderId);
        $this->newStatus = $this->selectedOrder->status;
        $this->trackingNumber = $this->selectedOrder->tracking_number ?? '';
        $this->trackingCarrier = $this->selectedOrder->tracking_carrier ?? '';
        $this->trackingUrl = $this->selectedOrder->tracking_url ?? '';
        $this->orderNote = $this->selectedOrder->notes ?? '';
        $this->showingOrderDetails = true;
    }

    public function closeOrderDetails()
    {
        $this->showingOrderDetails = false;
        $this->selectedOrder = null;
        $this->resetErrorBag();
    }

    public function updateOrderStatus()
    {
        $this->validate([
            'newStatus' => 'required|in:pending,processing,completed,cancelled,refunded',
        ]);

        $this->selectedOrder->update([
            'status' => $this->newStatus,
        ]);

        session()->flash('message', 'Order status updated successfully.');
    }

    public function updateTracking()
    {
        $this->validate([
            'trackingNumber' => 'nullable|string|max:100',
            'trackingCarrier' => 'nullable|string|max:100',
            'trackingUrl' => 'nullable|url|max:255',
        ]);

        $this->selectedOrder->update([
            'tracking_number' => $this->trackingNumber,
            'tracking_carrier' => $this->trackingCarrier,
            'tracking_url' => $this->trackingUrl,
        ]);

        session()->flash('message', 'Tracking information updated successfully.');
    }

    public function updateNote()
    {
        $this->validate([
            'orderNote' => 'nullable|string',
        ]);

        $this->selectedOrder->update([
            'notes' => $this->orderNote,
        ]);

        session()->flash('message', 'Order note updated successfully.');
    }

    public function getOrdersProperty()
    {
        return $this->getOrdersQueryProperty()->paginate($this->perPage);
    }

    public function render()
    {
        return view('livewire.admin.order-management', [
            'orders' => $this->orders,
        ]);
    }

    private function getOrdersQueryProperty()
    {
        $query = Order::query()->with('user');

        if ($this->search) {
            $query->where(function ($q) {
                $q->where('order_number', 'like', '%' . $this->search . '%')
                  ->orWhereHas('user', function ($userQuery) {
                      $userQuery->where('name', 'like', '%' . $this->search . '%')
                                ->orWhere('email', 'like', '%' . $this->search . '%');
                  });
            });
        }

        if ($this->statusFilter) {
            $query->where('status', $this->statusFilter);
        }

        if ($this->dateFilter) {
            if ($this->dateFilter === 'today') {
                $query->whereDate('created_at', today());
            } elseif ($this->dateFilter === 'yesterday') {
                $query->whereDate('created_at', today()->subDay());
            } elseif ($this->dateFilter === 'this_week') {
                $query->whereBetween('created_at', [now()->startOfWeek(), now()->endOfWeek()]);
            } elseif ($this->dateFilter === 'this_month') {
                $query->whereMonth('created_at', now()->month)
                      ->whereYear('created_at', now()->year);
            } elseif ($this->dateFilter === 'last_month') {
                $lastMonth = now()->subMonth();
                $query->whereMonth('created_at', $lastMonth->month)
                      ->whereYear('created_at', $lastMonth->year);
            }
        }

        return $query->orderBy($this->sortField, $this->sortDirection);
    }
}