<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON> Installer - Welcome</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <style>
        /* Base Styles */
        .gradient-background {
            background: linear-gradient(135deg, #EBF4FF 0%, #E6FFFA 100%);
            min-height: 100vh;
        }

        /* Animation Keyframes */
        @keyframes slideIn {
            from { opacity: 0; transform: translateY(-20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes scaleIn {
            from { transform: scale(0.95); opacity: 0; }
            to { transform: scale(1); opacity: 1; }
        }

        /* Component Styles */
        .installer-card {
            animation: scaleIn 0.5s ease-out;
            background: white;
            border-radius: 1rem;
            box-shadow: 0 10px 25px -5px rgba(59, 130, 246, 0.1), 0 8px 10px -6px rgba(59, 130, 246, 0.1);
        }

        .installer-header {
            background: linear-gradient(135deg, #4F46E5 0%, #3B82F6 100%);
            border-radius: 1rem 1rem 0 0;
            padding: 2rem;
        }

        .step-item {
            animation: fadeIn 0.5s ease-out forwards;
            opacity: 0;
        }

        .step-item:nth-child(1) { animation-delay: 0.2s; }
        .step-item:nth-child(2) { animation-delay: 0.3s; }
        .step-item:nth-child(3) { animation-delay: 0.4s; }
        .step-item:nth-child(4) { animation-delay: 0.5s; }
        .step-item:nth-child(5) { animation-delay: 0.6s; }

        .hover-scale {
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }

        .hover-scale:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 6px -1px rgba(59, 130, 246, 0.2), 0 2px 4px -1px rgba(59, 130, 246, 0.1);
        }

        .progress-bar {
            display: flex;
            margin: 2rem 0;
            justify-content: space-between;
            position: relative;
        }

        .progress-bar::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 2px;
            background: #E5E7EB;
            transform: translateY(-50%);
            z-index: 0;
        }

        .progress-step {
            width: 2.5rem;
            height: 2.5rem;
            border-radius: 50%;
            background: white;
            border: 2px solid #E5E7EB;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            z-index: 1;
            transition: all 0.3s ease;
        }

        .progress-step.active {
            border-color: #3B82F6;
            background: #3B82F6;
            color: white;
        }

        .progress-step.completed {
            border-color: #10B981;
            background: #10B981;
            color: white;
        }
    </style>
</head>

<body class="gradient-background">
    <div class="min-h-screen flex items-center justify-center p-4">
        <div class="max-w-4xl w-full">
            <!-- Progress Bar -->
            <div class="progress-bar mb-8">
                <div class="progress-step active">1</div>
                <div class="progress-step">2</div>
                <div class="progress-step">3</div>
                <div class="progress-step">4</div>
                <div class="progress-step">5</div>
            </div>

            <div class="installer-card">
                <!-- Header -->
                <div class="installer-header text-center">
                    <div class="flex justify-center mb-6">
                        <i data-lucide="boxes" class="h-16 w-16 text-white opacity-90"></i>
                    </div>
                    <h1 class="text-4xl font-bold text-white mb-2">Welcome to Installation</h1>
                    <p class="text-blue-100 text-lg">Let's set up your Forum Script</p>
                </div>

                <!-- Content -->
                <div class="p-8">
                    <div class="space-y-8">
                        <!-- Introduction -->
                        <div class="text-center max-w-2xl mx-auto">
                            <p class="text-gray-600 text-lg leading-relaxed">
                                This wizard will guide you through the installation process. We'll check your server requirements,
                                configure your database, and set up your admin account.
                            </p>
                        </div>

                        <!-- Installation Steps -->
                        <div class="bg-blue-50 rounded-xl p-6">
                            <h2 class="text-xl font-semibold text-blue-800 flex items-center mb-6">
                                <i data-lucide="list-checks" class="w-6 h-6 mr-2"></i>
                                Installation Process
                            </h2>
                            <div class="space-y-4">
                                <div class="step-item flex items-center p-3 bg-white rounded-lg shadow-sm">
                                    <div class="flex-shrink-0 w-10 h-10 flex items-center justify-center bg-blue-100 rounded-full mr-4">
                                        <span class="text-blue-600 font-semibold">1</span>
                                    </div>
                                    <div>
                                        <h3 class="font-medium text-gray-900">Server Requirements</h3>
                                        <p class="text-sm text-gray-500">Verify your server meets all requirements</p>
                                    </div>
                                </div>

                                <div class="step-item flex items-center p-3 bg-white rounded-lg shadow-sm">
                                    <div class="flex-shrink-0 w-10 h-10 flex items-center justify-center bg-blue-100 rounded-full mr-4">
                                        <span class="text-blue-600 font-semibold">2</span>
                                    </div>
                                    <div>
                                        <h3 class="font-medium text-gray-900">License Verification</h3>
                                        <p class="text-sm text-gray-500">Activate your forum script license</p>
                                    </div>
                                </div>

                                <div class="step-item flex items-center p-3 bg-white rounded-lg shadow-sm">
                                    <div class="flex-shrink-0 w-10 h-10 flex items-center justify-center bg-blue-100 rounded-full mr-4">
                                        <span class="text-blue-600 font-semibold">3</span>
                                    </div>
                                    <div>
                                        <h3 class="font-medium text-gray-900">Email Configuration</h3>
                                        <p class="text-sm text-gray-500">Set up your email settings</p>
                                    </div>
                                </div>

                                <div class="step-item flex items-center p-3 bg-white rounded-lg shadow-sm">
                                    <div class="flex-shrink-0 w-10 h-10 flex items-center justify-center bg-blue-100 rounded-full mr-4">
                                        <span class="text-blue-600 font-semibold">4</span>
                                    </div>
                                    <div>
                                        <h3 class="font-medium text-gray-900">Database Setup</h3>
                                        <p class="text-sm text-gray-500">Configure your database connection</p>
                                    </div>
                                </div>

                                <div class="step-item flex items-center p-3 bg-white rounded-lg shadow-sm">
                                    <div class="flex-shrink-0 w-10 h-10 flex items-center justify-center bg-blue-100 rounded-full mr-4">
                                        <span class="text-blue-600 font-semibold">5</span>
                                    </div>
                                    <div>
                                        <h3 class="font-medium text-gray-900">Admin Account</h3>
                                        <p class="text-sm text-gray-500">Create your administrator account</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Info Box -->
                        <div class="bg-yellow-50 border border-yellow-200 rounded-xl p-4">
                            <div class="flex items-start">
                                <i data-lucide="info" class="w-5 h-5 text-yellow-600 mt-0.5 mr-3"></i>
                                <div class="text-sm text-yellow-700">
                                    <p class="font-medium mb-1">Before you begin:</p>
                                    <ul class="list-disc pl-5 space-y-1">
                                        <li>Ensure you have your license key ready</li>
                                        <li>Prepare your database credentials</li>
                                        <li>Make sure your server meets all requirements</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <!-- Action Button -->
                        <div class="flex justify-end">
                            <a href="{{ route('installer.requirements') }}"
                                class="hover-scale inline-flex items-center px-6 py-3 rounded-lg text-white bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 transition-all duration-200 shadow-lg group">
                                <span class="text-sm font-medium">Begin Installation</span>
                                <i data-lucide="arrow-right" class="ml-2 w-5 h-5 transition-transform duration-200 group-hover:translate-x-1"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Initialize Lucide icons
        lucide.createIcons();
    </script>
</body>

</html>
