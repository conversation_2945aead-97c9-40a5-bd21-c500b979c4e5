<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up()
    {
        if (!Schema::hasTable('blog_post_tag')) {
            Schema::create('blog_post_tag', function (Blueprint $table) {
                $table->unsignedBigInteger('blog_post_id');
                $table->unsignedBigInteger('blog_post_tag_id');
                $table->timestamps();

                $table->primary(['blog_post_id', 'blog_post_tag_id']);

                $table->foreign('blog_post_id')
                    ->references('id')
                    ->on('blog_posts')
                    ->onDelete('cascade');

                $table->foreign('blog_post_tag_id')
                    ->references('id')
                    ->on('blog_post_tags')
                    ->onDelete('cascade');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('blog_post_tag');
    }
};
