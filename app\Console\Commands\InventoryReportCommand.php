<?php

namespace App\Console\Commands;

use App\Services\InventoryService;
use Illuminate\Console\Command;

class InventoryReportCommand extends Command
{
    protected $signature = 'inventory:report {--days=30 : Number of days for analytics}';
    protected $description = 'Generate inventory report and analytics';

    public function handle(InventoryService $inventoryService): int
    {
        $days = (int) $this->option('days');
        
        $this->info("Generating inventory report for the last {$days} days...");
        $this->newLine();

        // Get basic inventory report
        $report = $inventoryService->getInventoryReport();
        
        $this->info('📊 INVENTORY OVERVIEW');
        $this->table(
            ['Metric', 'Count', 'Value'],
            [
                ['Total Components', $report['total_components'], ''],
                ['In Stock (Healthy)', $report['in_stock'], ''],
                ['Low Stock (Warning)', $report['low_stock'], ''],
                ['Out of Stock (Critical)', $report['out_of_stock'], ''],
                ['Total Inventory Value', '', '$' . number_format($report['total_inventory_value'], 2)],
            ]
        );

        // Get analytics
        $analytics = $inventoryService->getInventoryAnalytics($days);
        
        $this->newLine();
        $this->info('📈 ANALYTICS (Last ' . $days . ' days)');
        $this->table(
            ['Metric', 'Value'],
            [
                ['Total Stock Movements', $analytics['total_movements']],
                ['Total Alerts Generated', $analytics['total_alerts']],
                ['Alert Resolution Rate', $analytics['alert_resolution_rate'] . '%'],
            ]
        );

        // Show movement breakdown
        if (!empty($analytics['movements_by_type'])) {
            $this->newLine();
            $this->info('📦 STOCK MOVEMENTS BY TYPE');
            $movementData = [];
            foreach ($analytics['movements_by_type'] as $type => $count) {
                $movementData[] = [ucfirst(str_replace('_', ' ', $type)), $count];
            }
            $this->table(['Type', 'Count'], $movementData);
        }

        // Show alert breakdown
        if (!empty($analytics['alerts_by_type'])) {
            $this->newLine();
            $this->info('🚨 ALERTS BY TYPE');
            $alertData = [];
            foreach ($analytics['alerts_by_type'] as $type => $count) {
                $alertData[] = [ucfirst(str_replace('_', ' ', $type)), $count];
            }
            $this->table(['Type', 'Count'], $alertData);
        }

        // Show current alerts
        $activeAlerts = $inventoryService->getInventoryAlerts(true);
        if ($activeAlerts->count() > 0) {
            $this->newLine();
            $this->warn('⚠️  ACTIVE ALERTS (' . $activeAlerts->count() . ')');
            $alertData = [];
            foreach ($activeAlerts->take(10) as $alert) {
                $alertData[] = [
                    $alert->component->name ?? 'Unknown',
                    ucfirst(str_replace('_', ' ', $alert->type)),
                    $alert->current_stock,
                    $alert->created_at->diffForHumans()
                ];
            }
            $this->table(['Component', 'Alert Type', 'Stock', 'Created'], $alertData);
            
            if ($activeAlerts->count() > 10) {
                $this->info('... and ' . ($activeAlerts->count() - 10) . ' more alerts');
            }
        } else {
            $this->newLine();
            $this->info('✅ No active alerts');
        }

        // Show low stock components
        $lowStockComponents = $inventoryService->getLowStockComponents();
        if ($lowStockComponents->count() > 0) {
            $this->newLine();
            $this->warn('📉 LOW STOCK COMPONENTS (' . $lowStockComponents->count() . ')');
            $lowStockData = [];
            foreach ($lowStockComponents->take(10) as $component) {
                $lowStockData[] = [
                    $component->name,
                    $component->category->name ?? 'Unknown',
                    $component->stock,
                    '$' . number_format($component->price, 2)
                ];
            }
            $this->table(['Component', 'Category', 'Stock', 'Price'], $lowStockData);
            
            if ($lowStockComponents->count() > 10) {
                $this->info('... and ' . ($lowStockComponents->count() - 10) . ' more components');
            }
        }

        $this->newLine();
        $this->info('✅ Inventory report generated successfully!');
        
        return Command::SUCCESS;
    }
}