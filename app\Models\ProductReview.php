<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;

class ProductReview extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'product_id',
        'rating',
        'title',
        'comment',
        'is_approved',
        'is_verified_purchase',
        'images',
    ];

    protected $casts = [
        'rating' => 'integer',
        'is_approved' => 'boolean',
        'is_verified_purchase' => 'boolean',
        'images' => 'array',
    ];

    /**
     * Get the user that owns the review.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the product that the review is for.
     */
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * Scope to get only approved reviews.
     */
    public function scopeApproved(Builder $query): Builder
    {
        return $query->where('is_approved', true);
    }

    /**
     * Scope to get reviews pending approval.
     */
    public function scopePending(Builder $query): Builder
    {
        return $query->where('is_approved', false);
    }

    /**
     * Scope to get reviews for a specific product.
     */
    public function scopeForProduct(Builder $query, int $productId): Builder
    {
        return $query->where('product_id', $productId);
    }

    /**
     * Scope to get reviews by rating.
     */
    public function scopeByRating(Builder $query, int $rating): Builder
    {
        return $query->where('rating', $rating);
    }

    /**
     * Scope to get verified purchase reviews.
     */
    public function scopeVerifiedPurchase(Builder $query): Builder
    {
        return $query->where('is_verified_purchase', true);
    }

    /**
     * Get validation rules for review creation.
     */
    public static function validationRules(int $userId, int $productId): array
    {
        return [
            'rating' => ['required', 'integer', 'min:1', 'max:5'],
            'title' => ['nullable', 'string', 'max:255'],
            'comment' => ['nullable', 'string', 'max:2000'],
            'images' => ['nullable', 'array', 'max:5'],
            'images.*' => ['image', 'mimes:jpeg,png,jpg,gif', 'max:2048'],
            'user_id' => [
                'required',
                'exists:users,id',
                Rule::unique('product_reviews')->where(function ($query) use ($userId, $productId) {
                    return $query->where('user_id', $userId)
                                ->where('product_id', $productId);
                }),
            ],
            'product_id' => ['required', 'exists:products,id'],
        ];
    }

    /**
     * Check if review content needs moderation.
     */
    public function needsModeration(): bool
    {
        $flaggedWords = [
            'spam', 'fake', 'scam', 'terrible', 'worst', 'hate',
            'stupid', 'garbage', 'trash', 'useless', 'crap', 'awful',
            'horrible', 'disgusting', 'pathetic', 'worthless'
        ];

        $content = strtolower($this->title . ' ' . $this->comment);
        
        foreach ($flaggedWords as $word) {
            if (str_contains($content, $word)) {
                return true;
            }
        }

        // Flag reviews that are too short or too long
        if (strlen($this->comment) < 10 || strlen($this->comment) > 2000) {
            return true;
        }

        // Flag reviews with extreme ratings without sufficient comment
        if (($this->rating <= 2 || $this->rating >= 5) && strlen($this->comment) < 50) {
            return true;
        }

        // Flag reviews with excessive capitalization
        if (strlen($this->comment) > 0 && strlen(preg_replace('/[^A-Z]/', '', $this->comment)) / strlen($this->comment) > 0.5) {
            return true;
        }

        return false;
    }

    /**
     * Auto-approve review if it passes moderation checks.
     */
    public function autoApprove(): void
    {
        if (!$this->needsModeration()) {
            $this->update(['is_approved' => true]);
        }
    }

    /**
     * Check if user has purchased the product (verified purchase).
     */
    public function checkVerifiedPurchase(): bool
    {
        $hasPurchased = DB::table('order_items')
            ->join('orders', 'order_items.order_id', '=', 'orders.id')
            ->where('orders.user_id', $this->user_id)
            ->where('order_items.product_id', $this->product_id)
            ->where('orders.status', 'completed')
            ->exists();

        if ($hasPurchased && !$this->is_verified_purchase) {
            $this->update(['is_verified_purchase' => true]);
        }

        return $hasPurchased;
    }

    /**
     * Get average rating for a product.
     */
    public static function getAverageRating(int $productId): float
    {
        return static::approved()
            ->forProduct($productId)
            ->avg('rating') ?? 0.0;
    }

    /**
     * Get rating distribution for a product.
     */
    public static function getRatingDistribution(int $productId): array
    {
        $distribution = static::approved()
            ->forProduct($productId)
            ->select('rating', DB::raw('count(*) as count'))
            ->groupBy('rating')
            ->pluck('count', 'rating')
            ->toArray();

        // Fill in missing ratings with 0
        for ($i = 1; $i <= 5; $i++) {
            if (!isset($distribution[$i])) {
                $distribution[$i] = 0;
            }
        }

        ksort($distribution);
        return $distribution;
    }

    /**
     * Get total review count for a product.
     */
    public static function getReviewCount(int $productId): int
    {
        return static::approved()
            ->forProduct($productId)
            ->count();
    }

    /**
     * Get verified purchase review count for a product.
     */
    public static function getVerifiedReviewCount(int $productId): int
    {
        return static::approved()
            ->forProduct($productId)
            ->verifiedPurchase()
            ->count();
    }

    /**
     * Get helpful review statistics for a product.
     */
    public static function getProductReviewStats(int $productId): array
    {
        $reviews = static::approved()->forProduct($productId);
        
        return [
            'average_rating' => round(static::getAverageRating($productId), 1),
            'total_reviews' => static::getReviewCount($productId),
            'verified_reviews' => static::getVerifiedReviewCount($productId),
            'rating_distribution' => static::getRatingDistribution($productId),
            'recent_reviews' => $reviews->latest()->limit(5)->with('user')->get(),
        ];
    }

    /**
     * Get reviews with pagination and filters.
     */
    public static function getProductReviews(int $productId, array $filters = [], int $perPage = 10)
    {
        $query = static::approved()
            ->forProduct($productId)
            ->with(['user'])
            ->latest();

        // Apply filters
        if (!empty($filters['rating'])) {
            $query->byRating($filters['rating']);
        }

        if (!empty($filters['verified_only'])) {
            $query->verifiedPurchase();
        }

        if (!empty($filters['with_images'])) {
            $query->whereNotNull('images');
        }

        return $query->paginate($perPage);
    }

    /**
     * Get review summary for display.
     */
    public function getSummary(): array
    {
        return [
            'id' => $this->id,
            'rating' => $this->rating,
            'title' => $this->title,
            'comment' => $this->comment,
            'user_name' => $this->user->name,
            'is_verified_purchase' => $this->is_verified_purchase,
            'created_at' => $this->created_at->format('M d, Y'),
            'images' => $this->images ?? [],
        ];
    }
}