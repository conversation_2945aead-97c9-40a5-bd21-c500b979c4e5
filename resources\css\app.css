@import './nexus-theme.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

[x-cloak] {
    display: none !important;
}

/* Prevent flash of unstyled content for Alpine.js components */
[x-data] {
    opacity: 0;
    transition: opacity 0.2s ease-in-out;
}

[x-data].alpine-ready {
    opacity: 1;
}

/* Specific fix for navigation menu */
nav[x-data] {
    opacity: 1; /* Navigation should always be visible */
}

/* Fix for mobile menu transitions */
[x-show] {
    transition: all 0.3s ease-in-out;
}

/* Fix for fixed navigation overlapping content */
body {
    padding-top: 4rem; /* 64px - height of navigation bar */
}

/* Ensure main content doesn't overlap with fixed nav */
main, .main-content, #app {
    margin-top: 0;
    padding-top: 0;
}

/* Additional spacing for pages that need it */
.page-content {
    padding-top: 1rem;
}

/* Fix for checkout and other full-height pages */
.checkout-page, .full-height-page {
    min-height: calc(100vh - 4rem);
    padding-top: 0.5rem;
}

/* Specific fix for Livewire components */
[wire\:id] {
    position: relative;
    z-index: 1;
}

/* Ensure dropdowns and modals appear above fixed nav */
.dropdown-menu, .modal, [x-show] {
    z-index: 60;
}

/* Fix for any content that might still overlap */
.container, .max-w-7xl, .max-w-6xl, .max-w-5xl, .max-w-4xl {
    position: relative;
    z-index: 1;
}
