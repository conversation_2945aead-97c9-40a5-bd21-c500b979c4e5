<?php

namespace Tests\Unit;

use App\Models\Order;
use App\Models\Payment;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class PaymentFactoryTest extends TestCase
{
    use RefreshDatabase;

    #[\PHPUnit\Framework\Attributes\Test]
    public function it_can_create_a_payment_with_factory()
    {
        // First create an order manually
        $order = Order::factory()->create();
        
        // Then create a payment for that order
        $payment = Payment::factory()
            ->state([
                'order_id' => $order->id
            ])
            ->create();
        
        $this->assertInstanceOf(Payment::class, $payment);
        $this->assertEquals($order->id, $payment->order_id);
        $this->assertNotNull($payment->transaction_id);
        $this->assertNotNull($payment->amount);
        $this->assertNotNull($payment->status);
        $this->assertIsArray($payment->payment_data);
    }
    
    #[\PHPUnit\Framework\Attributes\Test]
    public function it_can_create_a_completed_payment()
    {
        $order = Order::factory()->create();
        
        $payment = Payment::factory()
            ->state([
                'order_id' => $order->id
            ])
            ->completed()
            ->create();
        
        $this->assertEquals(Payment::STATUS_COMPLETED, $payment->status);
    }
    
    #[\PHPUnit\Framework\Attributes\Test]
    public function it_can_create_a_failed_payment()
    {
        $order = Order::factory()->create();
        
        $payment = Payment::factory()
            ->state([
                'order_id' => $order->id
            ])
            ->failed()
            ->create();
        
        $this->assertEquals(Payment::STATUS_FAILED, $payment->status);
        $this->assertArrayHasKey('error_code', $payment->payment_data);
        $this->assertArrayHasKey('error_message', $payment->payment_data);
    }
}