<?php

namespace Tests\Unit\Jobs;

use App\Jobs\SendBuildSharedEmail;
use App\Mail\BuildShared;
use App\Models\Build;
use App\Models\EmailLog;
use App\Models\User;
use App\Services\EmailTrackingService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Queue;
use Tests\TestCase;

class SendBuildSharedEmailTest extends TestCase
{
    use RefreshDatabase;

    public function test_job_sends_build_shared_email()
    {
        Mail::fake();
        
        $sharedBy = User::factory()->create([
            'name' => '<PERSON> Doe',
            'notification_settings' => ['build_shared' => true],
        ]);
        $build = Build::factory()->create([
            'user_id' => $sharedBy->id,
            'name' => 'Gaming Build',
            'share_token' => 'abc123',
        ]);

        $job = new SendBuildSharedEmail($build, $sharedBy, '<EMAIL>', 'Check this out!');
        $job->handle(new EmailTrackingService());

        Mail::assertQueued(BuildShared::class, function ($mail) use ($build, $sharedBy) {
            return $mail->build->id === $build->id && 
                   $mail->sharedBy->id === $sharedBy->id &&
                   $mail->recipientEmail === '<EMAIL>' &&
                   $mail->message === 'Check this out!';
        });
    }

    public function test_job_skips_email_when_user_disabled_notifications()
    {
        Mail::fake();
        
        $sharedBy = User::factory()->create([
            'notification_settings' => ['build_shared' => false],
        ]);
        $build = Build::factory()->create(['user_id' => $sharedBy->id]);

        $job = new SendBuildSharedEmail($build, $sharedBy, '<EMAIL>');
        $job->handle(new EmailTrackingService());

        Mail::assertNotQueued(BuildShared::class);
    }

    public function test_job_skips_email_for_invalid_recipient_email()
    {
        Mail::fake();
        
        $sharedBy = User::factory()->create([
            'notification_settings' => ['build_shared' => true],
        ]);
        $build = Build::factory()->create(['user_id' => $sharedBy->id]);

        $job = new SendBuildSharedEmail($build, $sharedBy, 'invalid-email');
        $job->handle(new EmailTrackingService());

        Mail::assertNotQueued(BuildShared::class);
    }

    public function test_job_creates_email_log_entry()
    {
        Mail::fake();
        
        $sharedBy = User::factory()->create([
            'name' => 'John Doe',
            'notification_settings' => ['build_shared' => true],
        ]);
        $build = Build::factory()->create([
            'user_id' => $sharedBy->id,
            'name' => 'Gaming Build',
            'share_token' => 'abc123',
        ]);

        $job = new SendBuildSharedEmail($build, $sharedBy, '<EMAIL>');
        $job->handle(new EmailTrackingService());

        $this->assertDatabaseHas('email_logs', [
            'type' => 'build_shared',
            'recipient' => '<EMAIL>',
            'subject' => 'John Doe shared a PC build with you: Gaming Build',
            'status' => 'sent',
            'related_id' => $build->id,
            'related_type' => Build::class,
        ]);
    }

    public function test_job_handles_email_failure()
    {
        Mail::shouldReceive('to')->andThrow(new \Exception('SMTP Error'));
        
        $sharedBy = User::factory()->create([
            'notification_settings' => ['build_shared' => true],
        ]);
        $build = Build::factory()->create([
            'user_id' => $sharedBy->id,
            'share_token' => 'abc123',
        ]);

        $job = new SendBuildSharedEmail($build, $sharedBy, '<EMAIL>');
        
        $this->expectException(\Exception::class);
        $job->handle(new EmailTrackingService());

        $this->assertDatabaseHas('email_logs', [
            'type' => 'build_shared',
            'status' => 'failed',
            'error_message' => 'SMTP Error',
        ]);
    }

    public function test_job_is_queueable()
    {
        Queue::fake();
        
        $sharedBy = User::factory()->create();
        $build = Build::factory()->create(['user_id' => $sharedBy->id]);

        SendBuildSharedEmail::dispatch($build, $sharedBy, '<EMAIL>', 'Test message');

        Queue::assertPushed(SendBuildSharedEmail::class, function ($job) use ($build, $sharedBy) {
            return $job->build->id === $build->id && 
                   $job->sharedBy->id === $sharedBy->id &&
                   $job->recipientEmail === '<EMAIL>' &&
                   $job->message === 'Test message';
        });
    }

    public function test_job_has_correct_retry_configuration()
    {
        $sharedBy = User::factory()->create();
        $build = Build::factory()->create(['user_id' => $sharedBy->id]);

        $job = new SendBuildSharedEmail($build, $sharedBy, '<EMAIL>');

        $this->assertEquals(3, $job->tries);
        $this->assertEquals(60, $job->timeout);
        $this->assertEquals([30, 120, 300], $job->backoff());
    }

    public function test_job_handles_null_message()
    {
        Mail::fake();
        
        $sharedBy = User::factory()->create([
            'notification_settings' => ['build_shared' => true],
        ]);
        $build = Build::factory()->create([
            'user_id' => $sharedBy->id,
            'share_token' => 'abc123',
        ]);

        $job = new SendBuildSharedEmail($build, $sharedBy, '<EMAIL>');
        $job->handle(new EmailTrackingService());

        Mail::assertQueued(BuildShared::class, function ($mail) {
            return $mail->message === null;
        });
    }
}