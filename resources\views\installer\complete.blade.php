<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON> Installer - Installation Complete</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <style>
        /* Base Styles */
        .gradient-background {
            background: linear-gradient(135deg, #EBF4FF 0%, #E6FFFA 100%);
            min-height: 100vh;
        }

        /* Animation Keyframes */
        @keyframes slideIn {
            from { opacity: 0; transform: translateY(-20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes scaleIn {
            from { transform: scale(0.95); opacity: 0; }
            to { transform: scale(1); opacity: 1; }
        }

        @keyframes checkmark {
            0% { transform: scale(0); opacity: 0; }
            50% { transform: scale(1.2); opacity: 0.8; }
            100% { transform: scale(1); opacity: 1; }
        }

        /* Component Styles */
        .installer-card {
            animation: scaleIn 0.5s ease-out;
            background: white;
            border-radius: 1rem;
            box-shadow: 0 10px 25px -5px rgba(59, 130, 246, 0.1), 0 8px 10px -6px rgba(59, 130, 246, 0.1);
        }

        .installer-header {
            background: linear-gradient(135deg, #4F46E5 0%, #3B82F6 100%);
            border-radius: 1rem 1rem 0 0;
            padding: 2rem;
        }

        .success-checkmark {
            animation: checkmark 0.8s cubic-bezier(0.65, 0, 0.45, 1) forwards;
            transform-origin: center;
        }

        .hover-scale {
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }

        .hover-scale:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 6px -1px rgba(59, 130, 246, 0.2), 0 2px 4px -1px rgba(59, 130, 246, 0.1);
        }

        .progress-bar {
            display: flex;
            margin: 2rem 0;
            justify-content: space-between;
            position: relative;
        }

        .progress-bar::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 2px;
            background: #E5E7EB;
            transform: translateY(-50%);
            z-index: 0;
        }

        .progress-step {
            width: 2.5rem;
            height: 2.5rem;
            border-radius: 50%;
            background: white;
            border: 2px solid #E5E7EB;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            z-index: 1;
            transition: all 0.3s ease;
        }

        .progress-step.completed {
            border-color: #10B981;
            background: #10B981;
            color: white;
        }

        .info-card {
            animation: fadeIn 0.5s ease-out forwards;
            opacity: 0;
            animation-delay: 0.3s;
        }

        .info-card:nth-child(2) { animation-delay: 0.4s; }
        .info-card:nth-child(3) { animation-delay: 0.5s; }
    </style>
</head>

<body class="gradient-background">
    <div class="min-h-screen flex items-center justify-center p-4">
        <div class="max-w-4xl w-full">
            <!-- Progress Bar -->
            <div class="progress-bar mb-8">
                <div class="progress-step completed">1</div>
                <div class="progress-step completed">2</div>
                <div class="progress-step completed">3</div>
                <div class="progress-step completed">4</div>
                <div class="progress-step completed">5</div>
                <div class="progress-step completed">6</div>
            </div>

            <div class="installer-card">
                <!-- Header -->
                <div class="installer-header text-center">
                    <div class="flex justify-center mb-6">
                        <div class="success-checkmark">
                            <i data-lucide="check-circle" class="h-20 w-20 text-white"></i>
                        </div>
                    </div>
                    <h1 class="text-4xl font-bold text-white mb-2">Installation Complete!</h1>
                    <p class="text-blue-100 text-lg">Your application has been successfully installed</p>
                </div>

                <!-- Content -->
                <div class="p-8">
                    <div class="space-y-8">
                        <!-- Success Message -->
                        <div class="text-center max-w-2xl mx-auto">
                            <p class="text-gray-600 text-lg leading-relaxed">
                                Congratulations! Your application has been successfully installed and configured.
                                You can now start using your new application.
                            </p>
                        </div>

                        <!-- Important Information Cards -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- Admin Login Info -->
                            <div class="info-card bg-blue-50 border border-blue-200 rounded-xl p-6">
                                <div class="flex items-start">
                                    <i data-lucide="user" class="h-6 w-6 text-blue-500 mt-1"></i>
                                    <div class="ml-4">
                                        <h3 class="text-blue-900 font-semibold text-lg">Admin Login</h3>
                                        <p class="mt-2 text-blue-700">
                                            You can now log in to your admin dashboard using the credentials you provided during installation.
                                        </p>
                                    </div>
                                </div>
                            </div>

                            <!-- Environment Info -->
                            <div class="info-card bg-green-50 border border-green-200 rounded-xl p-6">
                                <div class="flex items-start">
                                    <i data-lucide="settings" class="h-6 w-6 text-green-500 mt-1"></i>
                                    <div class="ml-4">
                                        <h3 class="text-green-900 font-semibold text-lg">Environment</h3>
                                        <p class="mt-2 text-green-700">
                                            Your environment has been configured with the settings you specified.
                                            The .env file has been updated accordingly.
                                        </p>
                                    </div>
                                </div>
                            </div>

                            <!-- Next Steps -->
                            <div class="info-card bg-purple-50 border border-purple-200 rounded-xl p-6 md:col-span-2">
                                <div class="flex items-start">
                                    <i data-lucide="list-checks" class="h-6 w-6 text-purple-500 mt-1"></i>
                                    <div class="ml-4">
                                        <h3 class="text-purple-900 font-semibold text-lg">Next Steps</h3>
                                        <ul class="mt-2 text-purple-700 space-y-2">
                                            <li class="flex items-center">
                                                <i data-lucide="check" class="h-4 w-4 mr-2"></i>
                                                Log in to your admin dashboard
                                            </li>
                                            <li class="flex items-center">
                                                <i data-lucide="check" class="h-4 w-4 mr-2"></i>
                                                Configure your site settings
                                            </li>
                                            <li class="flex items-center">
                                                <i data-lucide="check" class="h-4 w-4 mr-2"></i>
                                                Start adding content to your site
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Action Button -->
                        <div class="flex justify-center">
                            <a href="{{ route('home') }}"
                                class="hover-scale inline-flex justify-center items-center py-3 px-6 rounded-lg text-base font-medium text-white bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200 shadow-lg">
                                <span>Visit Your Website...</span>
                                <i data-lucide="arrow-right" class="h-5 w-5 ml-2"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Initialize Lucide icons
        lucide.createIcons();
    </script>
</body>
</html>
