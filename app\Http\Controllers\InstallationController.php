<?php

namespace App\Http\Controllers;

use App\Mail\TestConfigMail;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;
use Dotenv\Dotenv;
use PDO;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Config;
use Exception;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Mail;
use App\Traits\InstallationChecks;
use App\Services\ScriptActivationService;

class InstallationController extends Controller
{
    use InstallationChecks;

    protected $requirements = [
        'php' => '8.2.0',
        'extensions' => [
            'BCMath',
            'Ctype',
            'Fileinfo',
            'pdo_sqlite',
            'JSON',
            'Mbstring',
            'OpenSSL',
            'PDO',
            'Tokenizer',
            'XML',
            'Zip',
            'PDO',
            'Intl',
            'Mbstring',
            'DOM'
        ],
        'permissions' => [
            'storage/framework/' => '775',
            'storage/logs/' => '775',
            'bootstrap/cache/' => '775'
        ]
    ];

    public function __construct()
    {
        $installedFile = storage_path('installed');
        if (file_exists($installedFile) && !app()->runningInConsole()) {
            abort(404);
        }
    }

    public function showWelcome()
    {
        return view('installer.welcome');
    }

    public function checkRequirements()
    {
        $results = [];

        // Check PHP version
        $results['php'] = version_compare(PHP_VERSION, $this->requirements['php'], '>=');

        // Check extensions
        foreach ($this->requirements['extensions'] as $extension) {
            $results['extensions'][$extension] = extension_loaded($extension);
        }

        // Check directory permissions
        foreach ($this->requirements['permissions'] as $directory => $permission) {
            $results['permissions'][$directory] = $this->checkPermission(base_path($directory));
        }

        $canProceed = !in_array(false, array_merge(
            [$results['php']],
            $results['extensions'],
            $results['permissions']
        ));

        return view('installer.requirements', compact('results', 'canProceed'));
    }

    public function showDatabaseConfig()
    {
        return view('installer.database');
    }

    public function saveDatabaseConfig(Request $request)
    {
        try {
            // Set default connection to null during installation
            config(['database.default' => 'null']);

            logger('Starting database configuration...');

            $envFile = file_get_contents(base_path('.env.example'));
            logger('Env file content: ' . $envFile);

            $envFile = str_replace(
                [
                    'DB_CONNECTION=sqlite',
                    'DB_HOST=127.0.0.1',
                    'DB_PORT=3306',
                    'DB_DATABASE=laravel',
                    'DB_USERNAME=root',
                    'DB_PASSWORD=',
                    'APP_URL=http://localhost',
                    'APP_TIMEZONE=UTC'
                ],
                [
                    'DB_CONNECTION=mysql',
                    'DB_HOST=' . $request->db_host,
                    'DB_PORT=' . $request->db_port,
                    'DB_DATABASE=' . $request->db_name,
                    'DB_USERNAME=' . $request->db_user,
                    'DB_PASSWORD=' . $request->db_password,
                    'APP_URL=' . $request->app_url,
                    'APP_TIMEZONE=' . $request->app_timezone
                ],
                $envFile
            );

            logger('Updated env content: ' . $envFile);
            File::put(base_path('.env'), $envFile);
            logger('Env file written');

            $newKey = 'base64:' . base64_encode(random_bytes(32));
            logger('Generated key: ' . $newKey);

            // Read the entire .env file content
            $envContent = File::get(base_path('.env'));

            // Use preg_replace to completely replace the APP_KEY line
            $updatedEnvContent = preg_replace(
                '/^APP_KEY=.*$/m',
                'APP_KEY=' . $newKey,
                $envContent
            );

            // Write the updated content back to the .env file
            File::put(base_path('.env'), $updatedEnvContent);
            // logger('Key written to env');

            Artisan::call('config:clear');
            logger('Config cleared');

            Artisan::call('cache:clear');
            logger('Cache cleared');

            // Test the actual database connection using the new credentials
            try {
                $testConnection = [
                    'driver' => 'mysql',
                    'host' => $request->db_host,
                    'port' => $request->db_port,
                    'database' => $request->db_name,
                    'username' => $request->db_user,
                    'password' => $request->db_password,
                    'charset' => 'utf8mb4',
                    'collation' => 'utf8mb4_unicode_ci',
                ];

                $pdo = new PDO(
                    "mysql:host={$request->db_host};port={$request->db_port};dbname={$request->db_name}",
                    $request->db_user,
                    $request->db_password
                );

                if ($pdo) {
                    logger('Redirecting to migrations');
                    session()->flash('status', 'success');
                    return response()->redirectTo(route('installer.migrations', [], false));
                }

                // logger('DB connection successful');
                // return redirect()->route('installer.migrations');
            } catch (\PDOException $e) {
                logger('DB connection failed: ' . $e->getMessage());
                return back()->withErrors(['database' => 'Could not connect to database: ' . $e->getMessage()]);
            }
        } catch (Exception $e) {
            logger('Error occurred: ' . $e->getMessage());
            logger('Stack trace: ' . $e->getTraceAsString());
            return back()->withErrors(['database' => 'Configuration error: ' . $e->getMessage()]);
        }
    }

    public function runMigrations()
    {
        try {
            logger('Starting migrations...');

            // Reconfigure database connection with values from .env
            $dbConfig = [
                'driver' => env('DB_CONNECTION'),
                'host' => env('DB_HOST'),
                'port' => env('DB_PORT'),
                'database' => env('DB_DATABASE'),
                'username' => env('DB_USERNAME'),
                'password' => env('DB_PASSWORD'),
            ];

            config(['database.default' => env('DB_CONNECTION')]);
            config(['database.connections.' . env('DB_CONNECTION') => $dbConfig]);

            logger('Clearing config/cache...');
            Artisan::call('config:clear');
            Artisan::call('cache:clear');

            // Group 1: Core system tables (no foreign key dependencies)
            $coreTables = [
                // 'database/migrations/0001_01_01_000000_create_users_table.php',
                // 'database/migrations/0001_01_01_000001_create_cache_table.php',
                // 'database/migrations/0001_01_01_000002_create_jobs_table.php',
                // 'database/migrations/2024_12_02_012230_create_personal_access_tokens_table.php',
                // 'database/migrations/2024_12_18_154018_create_settings_table.php',
                // 'database/migrations/2024_12_19_075905_create_activity_log_table.php',
                // 'database/migrations/2024_12_19_075906_add_event_column_to_activity_log_table.php',
                // 'database/migrations/2024_12_19_075907_add_batch_uuid_column_to_activity_log_table.php',
                // 'database/migrations/2024_12_19_156465_create_themes_table.php',
                // 'database/migrations/2024_12_20_135643_create_pages_table.php',
                // 'database/migrations/2025_03_03_151614_create_adblock_logs_table.php',
            ];

            // Group 2: Tables with user dependencies only
            $userDependentTables = [
                // 'database/migrations/2024_03_20_create_memberships_table.php',
                // 'database/migrations/2024_12_07_133834_create_categories_table.php',
                // 'database/migrations/2024_12_09_104717_create_conversation_table.php',
                // 'database/migrations/2024_12_15_144525_create_blog_posts_table.php',
                // 'database/migrations/2024_12_02_014546_create_ads_table.php',
                // 'database/migrations/2024_12_09_092445_create_friend_requests_table.php',
                // 'database/migrations/2024_12_25_144618_create_reputation_logs_table.php',
                // 'database/migrations/2025_02_27_223533_create_followers_table.php'
            ];

            // Group 3: Tables with category dependencies
            $categoryDependentTables = [
                // 'database/migrations/2024_12_07_133825_create_topics_table.php',
                // 'database/migrations/2025_03_02_084582_add_spam_columns_to_topics_table.php',
                // 'database/migrations/2024_12_20_141841_create_category_subscriptions_table.php'
            ];

            // Group 4: Tables with multiple dependencies
            $complexDependencyTables = [
                // 'database/migrations/2025_03_01_011022_create_blog_post_tags_table.php',
                // 'database/migrations/2024_03_20_create_payments_table.php',
                // 'database/migrations/2024_12_07_133806_create_forum_posts_table.php',
                // 'database/migrations/2025_03_02_148754_add_spam_columns_to_forum_posts_table.php',
                // 'database/migrations/2024_12_07_164959_create_user_activities_table.php',
                // 'database/migrations/2024_12_08_062021_create_post_likes_table.php',
                // 'database/migrations/2024_12_09_092621_create_messages_table.php',
                // 'database/migrations/2024_12_11_130604_create_post_votes.php',
                // 'database/migrations/2024_12_11_171242_create_forum_post_mention_table.php',
                // 'database/migrations/2025_03_03_001920_create_notifications_table.php',
                // 'database/migrations/2024_12_12_202134_create_system_management_table.php',
                // 'database/migrations/2024_12_15_144538_create_comments_table.php',
                // 'database/migrations/2024_12_02_014607_create_ad_placements_table.php',
                // 'database/migrations/2025_01_04_100630_create_topic_subscriptions_table.php'
            ];

            // Group 5: Additional settings and modifications
            // $additionalMigrations = [
            //     'database/migrations/2024_12_18_201158_add_adblock_logging_setting.php'
            // ];

            // Run migrations in sequence
            $migrationGroups = [
                $coreTables,
                $userDependentTables,
                $categoryDependentTables,
                $complexDependencyTables,
                // $additionalMigrations
            ];

            foreach ($migrationGroups as $group) {
                foreach ($group as $migration) {
                    Artisan::call('migrate', [
                        '--path' => $migration,
                        '--force' => true
                    ]);
                    logger('Ran migration: ' . $migration);
                }
            }

            // Run any remaining migrations
            Artisan::call('migrate', [
                '--force' => true
            ]);

            // Run seeders if they exist
            if (File::exists(database_path('seeders/DatabaseSeeder.php'))) {
                logger('Running seeders...');
                Artisan::call('db:seed', ['--force' => true]);
                logger('Seeder output: ' . Artisan::output());
            }

            // logger('Creating storage link...');
            // Artisan::call('storage:link');

            logger('Database Migration completed');
            return redirect()->route('installer.create-admin');

        } catch (Exception $e) {
            logger('Migration failed: ' . $e->getMessage());
            logger('Stack trace: ' . $e->getTraceAsString());
            return back()->withErrors(['migration' => 'Migration failed: ' . $e->getMessage()]);
        }
    }

    public function showAdminCreation()
    {
        return view('installer.admin-creation');
    }

    public function createAdmin(Request $request)
    {
        try {
            // Set up database configuration before creating admin
            $dbConfig = [
                'driver' => env('DB_CONNECTION', 'mysql'),
                'host' => env('DB_HOST'),
                'port' => env('DB_PORT'),
                'database' => env('DB_DATABASE'),
                'username' => env('DB_USERNAME'),
                'password' => env('DB_PASSWORD'),
                'charset' => 'utf8mb4',
                'collation' => 'utf8mb4_unicode_ci',
                'prefix' => '',
                'strict' => true,
                'engine' => null,
            ];

            // Configure the database connection
            config(['database.default' => env('DB_CONNECTION', 'mysql')]);
            config(['database.connections.mysql' => $dbConfig]);

            $validated = $request->validate([
                'name' => 'required|string|max:255',
                'email' => 'required|string|email|max:255|unique:users',
                'password' => 'required|string|min:8|confirmed',
            ]);

            $admin = DB::table('users')->insert([
                'name' => $validated['name'],
                'user_name' => $this->generateUserName($validated['name']),
                'email' => $validated['email'],
                'password' => bcrypt($validated['password']),
                'role' => 'admin',
                // 'super_admin' => 1,
                'created_at' => now(),
                'updated_at' => now(),
                'email_verified_at' => now(),
            ]);

            if ($admin) {
                logger('Admin user created successfully');
                return redirect()->route('installer.complete');
            }

            return back()->withErrors(['admin' => 'Failed to create admin user']);
        } catch (Exception $e) {
            logger('Admin creation failed: ' . $e->getMessage());
            logger('Stack trace: ' . $e->getTraceAsString());
            return back()->withErrors(['admin' => 'Admin creation failed: ' . $e->getMessage()]);
        }
    }

    function generateUserName($name)
    {
        // Convert name to lowercase
        $userName = strtolower($name);

        // Replace spaces and special characters with hyphens
        $userName = preg_replace('/[^a-z0-9]+/', '-', $userName);

        // Trim hyphens from the beginning and end
        $userName = trim($userName, '-');

        // Append a random number for uniqueness
        $userName .= '-' . rand(1000, 9999);

        return $userName;
    }


    public function complete()
    {
        try {
            $installedFile = storage_path('installed');
            file_put_contents($installedFile, date('Y-m-d H:i:s'));

            return view('installer.complete');
        } catch (Exception $e) {
            return back()->withErrors(['error' => 'Failed to complete installation: ' . $e->getMessage()]);
        }
    }

    protected function checkPermission($path)
    {
        if (!file_exists($path)) {
            try {
                mkdir($path, 0755, true);
            } catch (Exception $e) {
                return false;
            }
        }

        return is_writable($path);
    }

    public function testConnection(Request $request)
    {
        $request->validate([
            'db_host' => 'required|string',
            'db_port' => 'required|integer',
            'db_name' => 'required|string',
            'db_user' => 'required|string',
            'db_password' => 'nullable|string'
        ]);

        $config = [
            'host' => $request->db_host,
            'port' => $request->db_port,
            'username' => $request->db_user,
            'password' => $request->db_password ?? ''
        ];

        $success = $this->checkDatabaseConnection($config);

        return response()->json([
            'success' => $success,
            'message' => $success ? 'Connection successful!' : 'Connection failed'
        ]);
    }

    /**
     * Show email configuration form
     */
    public function showEmailConfig()
    {
        return view('installer.email-config');
    }

    /**
     * Test email configuration with basic Laravel Mail facade
     */
    public function testEmailConfig(Request $request)
    {
        try {
            // Validate the request
            $validated = $request->validate([
                'mail_driver' => 'required|string',
                'mail_host' => 'required|string',
                'mail_port' => 'required|numeric',
                'mail_username' => 'required|string',
                'mail_password' => 'required|string',
                'mail_encryption' => 'required|string',
                'mail_from_address' => 'required|email',
                'mail_from_name' => 'required|string',
            ]);

            // Temporarily set mail config
            config([
                'mail.default' => $validated['mail_driver'],
                'mail.mailers.smtp.host' => $validated['mail_host'],
                'mail.mailers.smtp.port' => $validated['mail_port'],
                'mail.mailers.smtp.username' => $validated['mail_username'],
                'mail.mailers.smtp.password' => $validated['mail_password'],
                'mail.mailers.smtp.encryption' => $validated['mail_encryption'],
                'mail.from.address' => $validated['mail_from_address'],
                'mail.from.name' => $validated['mail_from_name'],
            ]);

            // Test the email configuration
            Mail::to($validated['mail_from_address'])->send(new TestConfigMail());

            return response()->json([
                'success' => true,
                'message' => 'Email configuration test successful!'
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Email configuration test failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Save email configuration
     */
    public function saveEmailConfig(Request $request)
    {
        try {
            // Validate the request
            $validated = $request->validate([
                'mail_driver' => 'required|string',
                'mail_host' => 'required|string',
                'mail_port' => 'required|numeric',
                'mail_username' => 'required|string',
                'mail_password' => 'required|string',
                'mail_encryption' => 'required|string',
                'mail_from_address' => 'required|email',
                'mail_from_name' => 'required|string',
            ]);

            // Update environment file
            $envData = [
                'MAIL_MAILER' => $validated['mail_driver'],
                'MAIL_HOST' => $validated['mail_host'],
                'MAIL_PORT' => $validated['mail_port'],
                'MAIL_USERNAME' => $validated['mail_username'],
                'MAIL_PASSWORD' => $validated['mail_password'],
                'MAIL_ENCRYPTION' => $validated['mail_encryption'],
                'MAIL_FROM_ADDRESS' => $validated['mail_from_address'],
                'MAIL_FROM_NAME' => '"'.$validated['mail_from_name'].'"',
            ];

            $this->updateEnvironmentFile($envData);

            // Update mail config
            config([
                'mail.default' => $validated['mail_driver'],
                'mail.mailers.smtp.host' => $validated['mail_host'],
                'mail.mailers.smtp.port' => $validated['mail_port'],
                'mail.mailers.smtp.username' => $validated['mail_username'],
                'mail.mailers.smtp.password' => $validated['mail_password'],
                'mail.mailers.smtp.encryption' => $validated['mail_encryption'],
                'mail.from.address' => $validated['mail_from_address'],
                'mail.from.name' => $validated['mail_from_name'],
            ]);

            return redirect()->route('installer.database')
                            ->with('success', 'Email configuration saved successfully!');
        } catch (Exception $e) {
            return back()->withInput()
                        ->withErrors(['error' => 'Failed to save email configuration: ' . $e->getMessage()]);
        }
    }

    public function testEmailConfiguration(Request $request)
    {
        logger('Starting email test...');
        logger('Test email address: ' . $request->test_email);

        try {
            // Validate email
            $validated = $request->validate([
                'test_email' => 'required|email'
            ]);

            logger('Current mail configuration:', [
                'mailer' => config('mail.mailer'),
                'host' => config('mail.host'),
                'port' => config('mail.port'),
                'from_address' => config('mail.from.address'),
                'encryption' => config('mail.encryption')
            ]);

            // Send test email
            logger('Attempting to send test email...');
            Mail::raw('This is a test email from your application installation.', function ($message) use ($request) {
                $message->to($request->test_email)
                    ->subject('Email Configuration Test');
            });
            logger('Test email sent successfully');

            return response()->json([
                'success' => true,
                'message' => 'Test email sent successfully!'
            ]);

        } catch (Exception $e) {
            logger('Failed to send test email: ' . $e->getMessage());
            logger('Stack trace: ' . $e->getTraceAsString());

            return response()->json([
                'success' => false,
                'message' => 'Failed to send test email: ' . $e->getMessage()
            ], 500);
        }
    }

    public function requirements()
    {
        $results = [
            'php' => version_compare(phpversion(), $this->requirements['php'], '>='),
            'extensions' => [],
            'permissions' => []
        ];

        // Check PHP extensions
        foreach ($this->requirements['extensions'] as $extension) {
            $results['extensions'][$extension] = extension_loaded($extension);
        }

        // Check directory permissions
        foreach ($this->requirements['permissions'] as $directory => $permission) {
            $results['permissions'][$directory] = $this->checkPermission(base_path($directory));
        }

        $canProceed = $results['php'] &&
            !in_array(false, $results['extensions']) &&
            !in_array(false, $results['permissions']);

        return view('installer.requirements', compact('results', 'canProceed'));
    }

    public function showLicense()
    {
        return view('installer.license');
    }

    public function activateLicense(Request $request)
    {
        // @dd($request->all());
        $request->validate([
            // 'license_key' => 'required|string',
            'purchase_code' => 'nullable|string'
        ]);

        $activationService = app(ScriptActivationService::class);
        $result = $activationService->activateLicense(
            // $request->license_key,
            $request->purchase_code
        );
        if (env('APP_DEMO') == 'true') {
            //ByPass Activation
            return redirect()->route('installer.email-config')
                ->with('success', 'License activated successfully');
        } else if ($result['status'] === 'active') {
            // Update .env file with license key
            $this->updateEnvironmentFile([
                'SCRIPT_PURCHASE_CODE' => $request->purchase_code
            ]);

            return redirect()->route('installer.email-config')
                ->with('success', 'License activated successfully');
        }

        return back()->withErrors([
            'license' => $result['message'] ?? 'License activation failed'
        ]);
    }

    private function updateEnvironmentFile($data)
    {
        $envFile = file_get_contents(base_path('.env'));

        foreach ($data as $key => $value) {
            if (strpos($envFile, $key) !== false) {
                $envFile = preg_replace(
                    "/^{$key}=.*/m",
                    "{$key}={$value}",
                    $envFile
                );
            } else {
                $envFile .= "\n{$key}={$value}";
            }
        }

        file_put_contents(base_path('.env'), $envFile);
    }
}