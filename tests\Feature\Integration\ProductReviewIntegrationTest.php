<?php

namespace Tests\Feature\Integration;

use App\Models\Product;
use App\Models\ProductReview;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\DB;
use Tests\TestCase;

class ProductReviewIntegrationTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        $this->artisan('migrate');
    }

    /** @test */
    public function it_handles_complete_review_lifecycle()
    {
        $product = Product::factory()->create();
        $user = User::factory()->create();

        // Create initial review
        $review = ProductReview::create([
            'user_id' => $user->id,
            'product_id' => $product->id,
            'rating' => 4,
            'title' => 'Good product',
            'comment' => 'This product works well and meets my expectations. Good quality overall.',
            'is_approved' => false,
        ]);

        // Should auto-approve clean content
        $review->autoApprove();
        $this->assertTrue($review->fresh()->is_approved);

        // Check verified purchase status
        $this->assertFalse($review->checkVerifiedPurchase());

        // Simulate purchase by creating order records
        DB::table('orders')->insert([
            'id' => 1,
            'user_id' => $user->id,
            'order_number' => 'ORD-001',
            'status' => 'completed',
            'total' => 1000,
            'subtotal' => 1000,
            'tax' => 0,
            'shipping' => 0,
            'discount' => 0,
            'billing_name' => 'Test User',
            'billing_email' => $user->email,
            'billing_address' => '123 Test St',
            'billing_city' => 'Test City',
            'billing_state' => 'Test State',
            'billing_zipcode' => '12345',
            'billing_country' => 'Test Country',
            'shipping_name' => 'Test User',
            'shipping_email' => $user->email,
            'shipping_address' => '123 Test St',
            'shipping_city' => 'Test City',
            'shipping_state' => 'Test State',
            'shipping_zipcode' => '12345',
            'shipping_country' => 'Test Country',
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        DB::table('order_items')->insert([
            'order_id' => 1,
            'product_id' => $product->id,
            'name' => $product->name,
            'quantity' => 1,
            'price' => 1000,
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        // Now check should return true and update review
        $this->assertTrue($review->checkVerifiedPurchase());
        $this->assertTrue($review->fresh()->is_verified_purchase);

        // Test product stats
        $stats = $product->getReviewStats();
        $this->assertEquals(4.0, $stats['average_rating']);
        $this->assertEquals(1, $stats['total_reviews']);
        $this->assertEquals(1, $stats['verified_reviews']);
    }

    /** @test */
    public function it_handles_multiple_reviews_and_statistics()
    {
        $product = Product::factory()->create();
        $users = User::factory()->count(5)->create();

        // Create reviews with different ratings
        $ratings = [5, 4, 4, 3, 2];
        foreach ($users as $index => $user) {
            ProductReview::create([
                'user_id' => $user->id,
                'product_id' => $product->id,
                'rating' => $ratings[$index],
                'title' => "Review {$index}",
                'comment' => 'This is a detailed review comment that is long enough to pass moderation.',
                'is_approved' => true,
                'is_verified_purchase' => $index < 3, // First 3 are verified
            ]);
        }

        // Test statistics
        $this->assertEquals(3.6, $product->getAverageRating()); // (5+4+4+3+2)/5
        $this->assertEquals(5, $product->getReviewCount());

        $stats = $product->getReviewStats();
        $this->assertEquals(3.6, $stats['average_rating']);
        $this->assertEquals(5, $stats['total_reviews']);
        $this->assertEquals(3, $stats['verified_reviews']);

        // Test rating distribution
        $distribution = $stats['rating_distribution'];
        $this->assertEquals(0, $distribution[1]);
        $this->assertEquals(1, $distribution[2]);
        $this->assertEquals(1, $distribution[3]);
        $this->assertEquals(2, $distribution[4]);
        $this->assertEquals(1, $distribution[5]);

        // Test recent reviews
        $this->assertCount(5, $stats['recent_reviews']);
    }

    /** @test */
    public function it_handles_review_filtering_and_pagination()
    {
        $product = Product::factory()->create();
        $users = User::factory()->count(10)->create();

        // Create reviews with different characteristics
        foreach ($users as $index => $user) {
            ProductReview::create([
                'user_id' => $user->id,
                'product_id' => $product->id,
                'rating' => ($index % 5) + 1, // Ratings 1-5
                'title' => "Review {$index}",
                'comment' => 'This is a review comment that meets the minimum length requirements.',
                'is_approved' => true,
                'is_verified_purchase' => $index < 5, // First 5 are verified
                'images' => $index < 3 ? ['image1.jpg'] : null, // First 3 have images
            ]);
        }

        // Test filtering by rating
        $fiveStarReviews = ProductReview::getProductReviews($product->id, ['rating' => 5]);
        $this->assertEquals(2, $fiveStarReviews->total()); // Reviews 4 and 9

        // Test verified only filter
        $verifiedReviews = ProductReview::getProductReviews($product->id, ['verified_only' => true]);
        $this->assertEquals(5, $verifiedReviews->total());

        // Test with images filter
        $reviewsWithImages = ProductReview::getProductReviews($product->id, ['with_images' => true]);
        $this->assertEquals(3, $reviewsWithImages->total());

        // Test combined filters
        $verifiedFiveStars = ProductReview::getProductReviews($product->id, [
            'rating' => 5,
            'verified_only' => true
        ]);
        $this->assertEquals(1, $verifiedFiveStars->total()); // Only review 4
    }

    /** @test */
    public function it_prevents_duplicate_reviews_per_user()
    {
        $product = Product::factory()->create();
        $user = User::factory()->create();

        // Create first review
        $review1 = ProductReview::create([
            'user_id' => $user->id,
            'product_id' => $product->id,
            'rating' => 4,
            'title' => 'First review',
            'comment' => 'This is my first review of this product.',
        ]);

        $this->assertDatabaseHas('product_reviews', [
            'user_id' => $user->id,
            'product_id' => $product->id,
            'title' => 'First review',
        ]);

        // Attempt to create second review should fail due to unique constraint
        $this->expectException(\Illuminate\Database\QueryException::class);
        
        ProductReview::create([
            'user_id' => $user->id,
            'product_id' => $product->id,
            'rating' => 5,
            'title' => 'Second review',
            'comment' => 'This is my second review attempt.',
        ]);
    }

    /** @test */
    public function it_handles_review_moderation_edge_cases()
    {
        $product = Product::factory()->create();
        $user = User::factory()->create();

        // Test various moderation scenarios
        $testCases = [
            [
                'title' => 'Short',
                'comment' => 'Bad',
                'rating' => 3,
                'should_moderate' => true,
                'reason' => 'too short'
            ],
            [
                'title' => 'Extreme rating',
                'comment' => 'Not good',
                'rating' => 1,
                'should_moderate' => true,
                'reason' => 'extreme rating with short comment'
            ],
            [
                'title' => 'CAPS REVIEW',
                'comment' => 'THIS IS ALL CAPS AND LOOKS SUSPICIOUS',
                'rating' => 3,
                'should_moderate' => true,
                'reason' => 'excessive capitalization'
            ],
            [
                'title' => 'Flagged words',
                'comment' => 'This product is spam and fake garbage',
                'rating' => 3,
                'should_moderate' => true,
                'reason' => 'contains flagged words'
            ],
            [
                'title' => 'Good review',
                'comment' => 'This is a well-written review that provides helpful information about the product quality and performance.',
                'rating' => 4,
                'should_moderate' => false,
                'reason' => 'clean content'
            ],
        ];

        foreach ($testCases as $index => $testCase) {
            $review = ProductReview::create([
                'user_id' => User::factory()->create()->id,
                'product_id' => $product->id,
                'rating' => $testCase['rating'],
                'title' => $testCase['title'],
                'comment' => $testCase['comment'],
                'is_approved' => false,
            ]);

            $needsModeration = $review->needsModeration();
            $this->assertEquals(
                $testCase['should_moderate'], 
                $needsModeration,
                "Test case {$index} ({$testCase['reason']}) failed"
            );

            // Test auto-approval
            $review->autoApprove();
            $this->assertEquals(
                !$testCase['should_moderate'],
                $review->fresh()->is_approved,
                "Auto-approval test case {$index} ({$testCase['reason']}) failed"
            );
        }
    }

    /** @test */
    public function it_handles_review_images()
    {
        $product = Product::factory()->create();
        $user = User::factory()->create();

        $review = ProductReview::create([
            'user_id' => $user->id,
            'product_id' => $product->id,
            'rating' => 5,
            'title' => 'Great product with photos',
            'comment' => 'Here are some photos of the product in use.',
            'images' => ['review1.jpg', 'review2.jpg', 'review3.jpg'],
            'is_approved' => true,
        ]);

        $summary = $review->getSummary();
        $this->assertCount(3, $summary['images']);
        $this->assertContains('review1.jpg', $summary['images']);

        // Test filtering reviews with images
        $reviewsWithImages = ProductReview::getProductReviews($product->id, ['with_images' => true]);
        $this->assertEquals(1, $reviewsWithImages->total());
    }

    /** @test */
    public function it_handles_cross_product_review_queries()
    {
        $product1 = Product::factory()->create(['name' => 'Product 1']);
        $product2 = Product::factory()->create(['name' => 'Product 2']);
        $user = User::factory()->create();

        // Create reviews for both products
        ProductReview::create([
            'user_id' => $user->id,
            'product_id' => $product1->id,
            'rating' => 5,
            'title' => 'Love product 1',
            'comment' => 'This is an excellent product with great features.',
            'is_approved' => true,
        ]);

        ProductReview::create([
            'user_id' => User::factory()->create()->id,
            'product_id' => $product2->id,
            'rating' => 3,
            'title' => 'Okay product 2',
            'comment' => 'This product is okay but could be better.',
            'is_approved' => true,
        ]);

        // Test product-specific queries
        $this->assertEquals(1, $product1->getReviewCount());
        $this->assertEquals(1, $product2->getReviewCount());
        $this->assertEquals(5.0, $product1->getAverageRating());
        $this->assertEquals(3.0, $product2->getAverageRating());

        // Test user review status
        $this->assertTrue($product1->hasUserReviewed($user->id));
        $this->assertFalse($product2->hasUserReviewed($user->id));

        // Test scoped queries
        $product1Reviews = ProductReview::forProduct($product1->id)->get();
        $product2Reviews = ProductReview::forProduct($product2->id)->get();

        $this->assertCount(1, $product1Reviews);
        $this->assertCount(1, $product2Reviews);
        $this->assertEquals('Love product 1', $product1Reviews->first()->title);
        $this->assertEquals('Okay product 2', $product2Reviews->first()->title);
    }

    /** @test */
    public function it_handles_review_validation_rules()
    {
        $user = User::factory()->create();
        $product = Product::factory()->create();

        $rules = ProductReview::validationRules($user->id, $product->id);

        // Test that all required fields are present
        $requiredFields = ['rating', 'user_id', 'product_id'];
        foreach ($requiredFields as $field) {
            $this->assertArrayHasKey($field, $rules);
            $this->assertContains('required', $rules[$field]);
        }

        // Test rating constraints
        $this->assertContains('min:1', $rules['rating']);
        $this->assertContains('max:5', $rules['rating']);

        // Test string length constraints
        $this->assertContains('max:255', $rules['title']);
        $this->assertContains('max:2000', $rules['comment']);

        // Test image constraints
        $this->assertArrayHasKey('images', $rules);
        $this->assertArrayHasKey('images.*', $rules);
        $this->assertContains('max:5', $rules['images']); // Max 5 images
        $this->assertContains('image', $rules['images.*']);
        $this->assertContains('max:2048', $rules['images.*']); // Max 2MB per image
    }

    /** @test */
    public function it_handles_bulk_review_operations()
    {
        $product = Product::factory()->create();
        $users = User::factory()->count(100)->create();

        // Create many reviews
        foreach ($users as $index => $user) {
            ProductReview::create([
                'user_id' => $user->id,
                'product_id' => $product->id,
                'rating' => ($index % 5) + 1,
                'title' => "Bulk review {$index}",
                'comment' => 'This is a bulk review comment that meets minimum length requirements for testing.',
                'is_approved' => $index % 3 === 0, // Every 3rd review is approved
            ]);
        }

        // Test bulk statistics
        $approvedCount = ProductReview::approved()->forProduct($product->id)->count();
        $pendingCount = ProductReview::pending()->forProduct($product->id)->count();
        $totalCount = ProductReview::forProduct($product->id)->count();

        $this->assertEquals(34, $approvedCount); // 100/3 rounded up
        $this->assertEquals(66, $pendingCount);
        $this->assertEquals(100, $totalCount);

        // Test performance with pagination
        $paginatedReviews = ProductReview::getProductReviews($product->id, [], 20);
        $this->assertEquals(20, $paginatedReviews->perPage());
        $this->assertLessThanOrEqual(20, $paginatedReviews->count());

        // Test rating distribution with large dataset
        $distribution = ProductReview::getRatingDistribution($product->id);
        $this->assertIsArray($distribution);
        $this->assertArrayHasKey(1, $distribution);
        $this->assertArrayHasKey(5, $distribution);
    }
}