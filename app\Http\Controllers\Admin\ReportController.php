<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Order;
use App\Models\Component;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class ReportController extends Controller
{
    /**
     * Generate orders report.
     */
    public function orders(Request $request)
    {
        $dateFrom = $request->get('date_from', now()->subDays(30)->format('Y-m-d'));
        $dateTo = $request->get('date_to', now()->format('Y-m-d'));
        $status = $request->get('status', 'all');

        $query = Order::whereBetween('created_at', [$dateFrom, $dateTo]);
        
        if ($status !== 'all') {
            $query->where('status', $status);
        }

        $orders = $query->get();
        $totalOrders = $orders->count();
        $totalRevenue = $orders->sum('total');

        $ordersByStatus = Order::whereBetween('created_at', [$dateFrom, $dateTo])
            ->select('status', DB::raw('COUNT(*) as count'))
            ->groupBy('status')
            ->get();

        $dailyStats = Order::whereBetween('created_at', [$dateFrom, $dateTo])
            ->select(
                DB::raw('DATE(created_at) as date'),
                DB::raw('COUNT(*) as order_count'),
                DB::raw('SUM(total) as revenue')
            )
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        return response()->json([
            'total_orders' => $totalOrders,
            'total_revenue' => $totalRevenue,
            'orders_by_status' => $ordersByStatus,
            'daily_stats' => $dailyStats
        ]);
    }

    /**
     * Generate custom report.
     */
    public function custom(Request $request)
    {
        $request->validate([
            'report_type' => 'required|string',
            'date_from' => 'required|date',
            'date_to' => 'required|date',
            'categories' => 'array'
        ]);

        $reportType = $request->report_type;
        $dateFrom = $request->date_from;
        $dateTo = $request->date_to;
        $categories = $request->categories ?? [];

        $reportData = [];
        $summary = [];
        $charts = [];

        switch ($reportType) {
            case 'sales_by_category':
                $query = DB::table('order_items')
                    ->join('orders', 'order_items.order_id', '=', 'orders.id')
                    ->join('components', 'order_items.component_id', '=', 'components.id')
                    ->join('component_categories', 'components.category_id', '=', 'component_categories.id')
                    ->whereBetween('orders.created_at', [$dateFrom, $dateTo])
                    ->where('orders.status', '!=', 'canceled');

                if (!empty($categories)) {
                    $query->whereIn('component_categories.id', $categories);
                }

                $reportData = $query->select(
                    'component_categories.name as category',
                    DB::raw('SUM(order_items.quantity * order_items.price) as revenue'),
                    DB::raw('SUM(order_items.quantity) as units_sold')
                )
                ->groupBy('component_categories.id', 'component_categories.name')
                ->orderBy('revenue', 'desc')
                ->get();

                $summary = [
                    'total_revenue' => $reportData->sum('revenue'),
                    'total_units' => $reportData->sum('units_sold'),
                    'categories_count' => $reportData->count()
                ];

                $charts = [
                    'revenue_by_category' => $reportData->map(function ($item) {
                        return ['label' => $item->category, 'value' => $item->revenue];
                    })
                ];
                break;

            default:
                return response()->json(['error' => 'Invalid report type'], 400);
        }

        return response()->json([
            'report_data' => $reportData,
            'summary' => $summary,
            'charts' => $charts
        ]);
    }
}