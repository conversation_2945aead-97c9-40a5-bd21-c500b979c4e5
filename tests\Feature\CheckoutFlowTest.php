<?php

namespace Tests\Feature;

use App\Livewire\Shop\Checkout;
use App\Models\Cart;
use App\Models\CartItem;
use App\Models\Component;
use App\Models\ComponentCategory;
use App\Models\Order;
use App\Models\Payment;
use App\Models\User;
use App\Services\CartService;
use App\Services\CheckoutService;
use App\Services\PaymentGatewayService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Session;
use Livewire\Livewire;
use Tests\TestCase;

class CheckoutFlowTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;
    protected Cart $cart;
    protected Component $component;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test data
        $this->user = User::factory()->create([
            'name' => 'John Doe',
            'email' => '<EMAIL>',
        ]);

        $category = ComponentCategory::factory()->create(['name' => 'CPU']);
        $this->component = Component::factory()->create([
            'name' => 'Intel Core i7-12700K',
            'category_id' => $category->id,
            'price' => 79.99,
            'stock' => 10,
            'is_active' => true,
        ]);

        // Create cart with items
        $this->cart = Cart::factory()->create(['user_id' => $this->user->id]);
        CartItem::factory()->create([
            'cart_id' => $this->cart->id,
            'component_id' => $this->component->id,
            'quantity' => 1,
            'price' => $this->component->price,
        ]);
        $this->cart->updateTotal();
    }

    public function test_it_redirects_to_cart_if_cart_is_empty()
    {
        $this->cart->clear();

        $this->actingAs($this->user);

        Livewire::test(Checkout::class)
            ->assertRedirect(route('shop.cart'));
    }

    public function test_it_pre_fills_user_information_when_logged_in()
    {
        $this->actingAs($this->user);

        Livewire::test(Checkout::class)
            ->assertSet('shipping.name', $this->user->name)
            ->assertSet('shipping.email', $this->user->email)
            ->assertSet('billing.name', $this->user->name)
            ->assertSet('billing.email', $this->user->email);
    }

    public function test_it_displays_cart_items_in_order_summary()
    {
        $this->actingAs($this->user);

        Livewire::test(Checkout::class)
            ->assertSee($this->component->name)
            ->assertSee('$' . number_format($this->component->price, 2));
    }

    public function test_it_validates_shipping_information_on_step_1()
    {
        $this->actingAs($this->user);

        Livewire::test(Checkout::class)
            ->set('shipping.name', '')
            ->set('shipping.email', 'invalid-email')
            ->set('shipping.phone', '')
            ->set('shipping.address', '')
            ->set('shipping.city', '')
            ->set('shipping.state', '')
            ->set('shipping.zipcode', '')
            ->call('nextStep')
            ->assertHasErrors([
                'shipping.name',
                'shipping.email',
                'shipping.phone',
                'shipping.address',
                'shipping.city',
                'shipping.state',
                'shipping.zipcode',
            ])
            ->assertSet('currentStep', 1);
    }

    public function test_it_advances_to_step_2_with_valid_shipping_information()
    {
        $this->actingAs($this->user);

        Livewire::test(Checkout::class)
            ->set('shipping', [
                'name' => 'John Doe',
                'email' => '<EMAIL>',
                'phone' => '555-1234',
                'address' => '123 Main St',
                'city' => 'Anytown',
                'state' => 'CA',
                'zipcode' => '12345',
                'country' => 'US',
            ])
            ->call('nextStep')
            ->assertHasNoErrors()
            ->assertSet('currentStep', 2);
    }

    public function test_it_processes_successful_credit_card_order()
    {
        $this->actingAs($this->user);

        $this->navigateToPaymentStep()
            ->set('payment', [
                'payment_method' => 'credit_card',
                'card_number' => '****************', // Valid test card
                'card_expiry' => '12/25',
                'card_cvv' => '123',
                'card_name' => 'John Doe',
            ])
            ->call('processOrder')
            ->assertHasNoErrors()
            ->assertSet('orderComplete', true);

        // Verify order was created
        $this->assertDatabaseHas('orders', [
            'user_id' => $this->user->id,
            'status' => 'processing',
            'payment_status' => 'paid',
        ]);

        // Verify payment was processed
        $this->assertDatabaseHas('payments', [
            'payment_method' => 'credit_card',
            'status' => 'completed',
        ]);

        // Verify cart was cleared
        $this->assertEquals(0, $this->cart->fresh()->items()->count());
    }

    public function test_it_calculates_order_totals_correctly()
    {
        $this->actingAs($this->user);

        $component = Livewire::test(Checkout::class)
            ->set('shipping', [
                'name' => 'John Doe',
                'email' => '<EMAIL>',
                'phone' => '555-1234',
                'address' => '123 Main St',
                'city' => 'Anytown',
                'state' => 'CA',
                'zipcode' => '12345',
                'country' => 'US',
            ])
            ->call('nextStep')
            ->call('nextStep')
            ->call('loadShippingOptions')
            ->set('selectedShippingMethod', 'standard')
            ->call('calculateTotals');

        $totals = $component->get('orderTotals');
        $subtotal = $this->cart->total;
        $expectedTax = round($subtotal * 0.08, 2);
        $expectedShipping = 9.99; // Standard shipping for orders under $100
        $expectedTotal = $subtotal + $expectedTax + $expectedShipping;

        $this->assertEquals($subtotal, $totals['subtotal']);
        $this->assertEquals($expectedTax, $totals['tax']);
        $this->assertEquals($expectedShipping, $totals['shipping']);
        $this->assertEquals($expectedTotal, $totals['total']);
    }

    protected function navigateToPaymentStep()
    {
        return Livewire::test(Checkout::class)
            ->set('shipping', [
                'name' => 'John Doe',
                'email' => '<EMAIL>',
                'phone' => '555-1234',
                'address' => '123 Main St',
                'city' => 'Anytown',
                'state' => 'CA',
                'zipcode' => '12345',
                'country' => 'US',
            ])
            ->call('nextStep')
            ->call('nextStep')
            ->set('selectedShippingMethod', 'standard')
            ->call('nextStep')
            ->assertSet('currentStep', 4);
    }
}