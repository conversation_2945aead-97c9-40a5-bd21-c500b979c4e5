<?php

namespace Tests\Feature\Api;

use App\Models\Component;
use App\Models\ComponentCategory;
use App\Models\User;
use App\Models\Cart;
use App\Models\CartItem;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Lara<PERSON>\Sanctum\Sanctum;
use Tests\TestCase;

class CartApiTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;
    protected ComponentCategory $category;
    protected Component $component;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->user = User::factory()->create();
        
        $this->category = ComponentCategory::factory()->create([
            'name' => 'CPU',
            'slug' => 'cpu'
        ]);
        
        $this->component = Component::factory()->create([
            'category_id' => $this->category->id,
            'price' => 299.99,
            'stock' => 10,
            'is_active' => true
        ]);
    }

    public function test_authenticated_user_can_view_empty_cart()
    {
        Sanctum::actingAs($this->user);

        $response = $this->getJson('/api/v1/cart');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    'items',
                    'total',
                    'item_count'
                ]
            ])
            ->assertJson([
                'data' => [
                    'items' => [],
                    'total' => 0,
                    'item_count' => 0
                ]
            ]);
    }

    public function test_authenticated_user_can_add_item_to_cart()
    {
        Sanctum::actingAs($this->user);

        $response = $this->postJson('/api/v1/cart', [
            'component_id' => $this->component->id,
            'quantity' => 2
        ]);

        $response->assertStatus(201)
            ->assertJsonStructure([
                'data' => [
                    'id',
                    'component_id',
                    'quantity',
                    'component'
                ],
                'message'
            ]);

        $this->assertDatabaseHas('cart_items', [
            'component_id' => $this->component->id,
            'quantity' => 2
        ]);
    }

    public function test_cannot_add_more_items_than_stock()
    {
        Sanctum::actingAs($this->user);

        $response = $this->postJson('/api/v1/cart', [
            'component_id' => $this->component->id,
            'quantity' => 11 // More than max allowed (10)
        ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['quantity']);
            
        // Now test with quantity more than stock but within max allowed
        $this->component->update(['stock' => 5]);
        
        $response = $this->postJson('/api/v1/cart', [
            'component_id' => $this->component->id,
            'quantity' => 8 // More than stock (5) but less than max (10)
        ]);

        $response->assertStatus(422)
            ->assertJson([
                'message' => 'Insufficient stock available',
                'available_stock' => 5
            ]);
    }

    public function test_authenticated_user_can_update_cart_item_quantity()
    {
        Sanctum::actingAs($this->user);

        // First add item to cart
        $cart = Cart::factory()->create(['user_id' => $this->user->id]);
        $cartItem = CartItem::factory()->create([
            'cart_id' => $cart->id,
            'component_id' => $this->component->id,
            'quantity' => 1
        ]);

        $response = $this->putJson("/api/v1/cart/items/{$cartItem->id}", [
            'quantity' => 3
        ]);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    'id',
                    'quantity',
                    'component'
                ],
                'message'
            ]);

        $this->assertDatabaseHas('cart_items', [
            'id' => $cartItem->id,
            'quantity' => 3
        ]);
    }

    public function test_authenticated_user_can_remove_item_from_cart()
    {
        Sanctum::actingAs($this->user);

        // First add item to cart
        $cart = Cart::factory()->create(['user_id' => $this->user->id]);
        $cartItem = CartItem::factory()->create([
            'cart_id' => $cart->id,
            'component_id' => $this->component->id,
            'quantity' => 1
        ]);

        $response = $this->deleteJson("/api/v1/cart/items/{$cartItem->id}");

        $response->assertStatus(200)
            ->assertJson([
                'message' => 'Item removed from cart successfully'
            ]);

        $this->assertDatabaseMissing('cart_items', [
            'id' => $cartItem->id
        ]);
    }

    public function test_authenticated_user_can_clear_entire_cart()
    {
        Sanctum::actingAs($this->user);

        // Add multiple items to cart
        $cart = Cart::factory()->create(['user_id' => $this->user->id]);
        
        // Create cart item manually
        CartItem::create([
            'cart_id' => $cart->id,
            'component_id' => $this->component->id,
            'quantity' => 3,
            'price' => $this->component->price
        ]);
        
        // Create additional components and add them to cart
        $component2 = Component::factory()->create([
            'category_id' => $this->category->id,
            'price' => 199.99,
            'stock' => 5
        ]);
        
        $component3 = Component::factory()->create([
            'category_id' => $this->category->id,
            'price' => 149.99,
            'stock' => 8
        ]);
        
        CartItem::create([
            'cart_id' => $cart->id,
            'component_id' => $component2->id,
            'quantity' => 1,
            'price' => $component2->price
        ]);
        
        CartItem::create([
            'cart_id' => $cart->id,
            'component_id' => $component3->id,
            'quantity' => 2,
            'price' => $component3->price
        ]);

        $response = $this->deleteJson('/api/v1/cart');

        $response->assertStatus(200)
            ->assertJson([
                'message' => 'Cart cleared successfully'
            ]);

        $this->assertEquals(0, $cart->fresh()->items()->count());
    }

    public function test_authenticated_user_can_get_cart_summary()
    {
        Sanctum::actingAs($this->user);

        // Add item to cart
        $cart = Cart::factory()->create(['user_id' => $this->user->id]);
        
        // Create cart item manually instead of using factory
        CartItem::create([
            'cart_id' => $cart->id,
            'component_id' => $this->component->id,
            'quantity' => 2,
            'price' => $this->component->price
        ]);

        $response = $this->getJson('/api/v1/cart/summary');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    'subtotal',
                    'shipping',
                    'tax',
                    'total',
                    'item_count',
                    'free_shipping_threshold',
                    'free_shipping_eligible'
                ]
            ]);
    }

    public function test_unauthenticated_user_cannot_access_cart()
    {
        $response = $this->getJson('/api/v1/cart');
        $response->assertStatus(401);

        $response = $this->postJson('/api/v1/cart', [
            'component_id' => $this->component->id
        ]);
        $response->assertStatus(401);
    }

    public function test_validates_add_to_cart_request()
    {
        Sanctum::actingAs($this->user);

        // Missing component_id
        $response = $this->postJson('/api/v1/cart', [
            'quantity' => 1
        ]);
        $response->assertStatus(422);

        // Invalid component_id
        $response = $this->postJson('/api/v1/cart', [
            'component_id' => 99999,
            'quantity' => 1
        ]);
        $response->assertStatus(422);

        // Invalid quantity
        $response = $this->postJson('/api/v1/cart', [
            'component_id' => $this->component->id,
            'quantity' => 0
        ]);
        $response->assertStatus(422);
    }

    public function test_validates_update_cart_item_request()
    {
        Sanctum::actingAs($this->user);

        $cart = Cart::factory()->create(['user_id' => $this->user->id]);
        $cartItem = CartItem::factory()->create([
            'cart_id' => $cart->id,
            'component_id' => $this->component->id,
            'quantity' => 1
        ]);

        // Missing quantity
        $response = $this->putJson("/api/v1/cart/items/{$cartItem->id}", []);
        $response->assertStatus(422);

        // Invalid quantity
        $response = $this->putJson("/api/v1/cart/items/{$cartItem->id}", [
            'quantity' => 0
        ]);
        $response->assertStatus(422);
    }

    public function test_cannot_update_non_existent_cart_item()
    {
        Sanctum::actingAs($this->user);

        $response = $this->putJson('/api/v1/cart/items/99999', [
            'quantity' => 2
        ]);

        $response->assertStatus(404);
    }

    public function test_cannot_remove_non_existent_cart_item()
    {
        Sanctum::actingAs($this->user);

        $response = $this->deleteJson('/api/v1/cart/items/99999');

        $response->assertStatus(404);
    }
}