<?php

namespace App\Livewire\Shop;

use App\Models\CartItem;
use App\Services\CartService;
use Livewire\Component;

class Cart extends Component
{
    public $cart;
    public $cartItems = [];
    public $itemCount = 0;
    public $subtotal = 0;
    public $tax = 0;
    public $shipping = 0;
    public $total = 0;
    public $taxRate = 0.08;
    
    protected $listeners = [
        'cartUpdated' => 'refreshCart',
        'itemAdded' => 'refreshCart',
        'itemRemoved' => 'refreshCart',
    ];
    
    public function mount()
    {
        $this->refreshCart();
    }
    
    public function refreshCart()
    {
        $cartService = app(CartService::class);
        $this->cart = $cartService->getCart();
        
        if ($this->cart) {
            $this->cartItems = $this->cart->items()->with('component')->get();
            $this->calculateTotals();
        } else {
            $this->resetCartData();
        }
        
        // dispatch event to update cart icon
        $this->dispatch('cartRefreshed', [
            'itemCount' => $this->itemCount,
            'total' => $this->total
        ]);
    }
    
    protected function resetCartData()
    {
        $this->cartItems = collect();
        $this->itemCount = 0;
        $this->subtotal = 0;
        $this->tax = 0;
        $this->shipping = 0;
        $this->total = 0;
    }
    
    protected function calculateTotals()
    {
        $cartService = app(CartService::class);
        $this->itemCount = $cartService->getItemCount();
        
        // If cart is empty, set all totals to 0
        if ($this->itemCount === 0) {
            $this->subtotal = 0;
            $this->tax = 0;
            $this->shipping = 0;
            $this->total = 0;
            return;
        }
        
        $totals = $cartService->getCartTotals(null, $this->taxRate);
        $this->subtotal = $totals['subtotal'];
        $this->tax = $totals['tax'];
        $this->shipping = $totals['shipping'];
        $this->total = $totals['total'];
    }
    
    public function updateQuantity($itemId, $quantity)
    {
        try {
            $cartService = app(CartService::class);
            
            if ($quantity <= 0) {
                $this->removeItem($itemId);
                return;
            }
            
            $result = $cartService->updateItemQuantity($itemId, $quantity);
            
            if ($result) {
                $this->refreshCart();
                $this->dispatch('cartUpdated');
                session()->flash('cart_message', 'Cart updated successfully!');
            } else {
                session()->flash('cart_error', 'Failed to update cart item.');
            }
        } catch (\InvalidArgumentException $e) {
            session()->flash('cart_error', $e->getMessage());
        }
    }
    
    public function incrementQuantity($itemId)
    {
        $item = $this->cartItems->firstWhere('id', $itemId);
        
        if (!$item) {
            return;
        }
        
        // Check stock availability
        if ($item->quantity >= $item->component->stock) {
            session()->flash('cart_error', 'Not enough stock available.');
            return;
        }
        
        $this->updateQuantity($itemId, $item->quantity + 1);
    }
    
    public function decrementQuantity($itemId)
    {
        $item = $this->cartItems->firstWhere('id', $itemId);
        
        if (!$item) {
            return;
        }
        
        if ($item->quantity <= 1) {
            $this->removeItem($itemId);
            return;
        }
        
        $this->updateQuantity($itemId, $item->quantity - 1);
    }
    
    public function removeItem($itemId)
    {
        try {
            $cartService = app(CartService::class);
            $result = $cartService->removeFromCart($itemId);
            
            if ($result) {
                $this->refreshCart();
                $this->dispatch('cartUpdated');
                $this->dispatch('itemRemoved');
                session()->flash('cart_message', 'Item removed from cart!');
            } else {
                session()->flash('cart_error', 'Failed to remove item from cart.');
            }
        } catch (\Exception $e) {
            session()->flash('cart_error', 'An error occurred while removing the item.');
        }
    }
    
    public function clearCart()
    {
        try {
            $cartService = app(CartService::class);
            $cartService->clearCart();
            
            $this->refreshCart();
            $this->dispatch('cartUpdated');
            $this->dispatch('cartCleared');
            session()->flash('cart_message', 'Cart cleared successfully!');
        } catch (\Exception $e) {
            session()->flash('cart_error', 'Failed to clear cart.');
        }
    }
    
    public function validateStock()
    {
        $cartService = app(CartService::class);
        $issues = $cartService->validateCartStock();
        
        if (!empty($issues)) {
            $fixed = $cartService->fixCartStockIssues();
            $this->refreshCart();
            
            $message = 'Some items in your cart had stock issues and were adjusted or removed.';
            session()->flash('cart_message', $message);
        }
    }
    
    public function updatePrices()
    {
        $cartService = app(CartService::class);
        $updated = $cartService->updateCartPrices();
        
        if (!empty($updated)) {
            $this->refreshCart();
            $message = 'Cart prices have been updated to reflect current pricing.';
            session()->flash('cart_message', $message);
        }
    }
    
    public function proceedToCheckout()
    {
        if (!auth()->check()) {
            return redirect()->route('login', ['redirect' => 'checkout']);
        }
        
        if ($this->itemCount === 0) {
            session()->flash('cart_error', 'Your cart is empty.');
            return;
        }
        
        // Validate stock before checkout
        $this->validateStock();
        
        // Update prices to current values
        $this->updatePrices();
        
        return redirect()->to('/checkout');
    }
    
    public function continueShopping()
    {
        return redirect()->route('shop.index');
    }
    
    public function render()
    {
        return view('livewire.shop.cart');
    }
}