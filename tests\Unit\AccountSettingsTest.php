<?php

namespace Tests\Unit;

use App\Livewire\User\AccountSettings;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Livewire\Livewire;
use Tests\TestCase;

class AccountSettingsTest extends TestCase
{
    use RefreshDatabase;

    public function test_update_profile_validation()
    {
        $user = User::factory()->create();
        $this->actingAs($user);
        Livewire::test(AccountSettings::class)
            ->set('name', '')
            ->set('email', 'not-an-email')
            ->call('updateProfile')
            ->assertHasErrors(['name', 'email']);
    }

    public function test_change_password_validation()
    {
        $user = User::factory()->create(['password' => bcrypt('oldpassword')]);
        $this->actingAs($user);
        Livewire::test(AccountSettings::class)
            ->set('current_password', '')
            ->set('new_password', 'short')
            ->set('new_password_confirmation', 'notmatch')
            ->call('changePassword')
            ->assertHasErrors(['current_password', 'new_password']);
    }
} 