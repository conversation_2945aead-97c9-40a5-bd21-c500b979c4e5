<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\User;
use App\Models\Component;
use App\Models\Build;

class OrderSeeder extends Seeder
{
    public function run(): void
    {
        $users = User::all();
        
        // Get components, fallback to all components if active scope doesn't work
        $components = Component::where('is_active', true)->get();
        if ($components->isEmpty()) {
            $components = Component::all();
        }
        
        // Skip if no components available
        if ($components->isEmpty()) {
            $this->command->warn('No components found. Skipping order seeding.');
            return;
        }
        
        $builds = Build::take(3)->get();

        foreach ($users as $user) {
            // Create 1-3 orders per user
            $numOrders = rand(1, 3);
            
            for ($i = 0; $i < $numOrders; $i++) {
                $status = $this->getRandomStatus();
                $orderNumber = Order::generateOrderNumber();
                
                $order = Order::create([
                    'user_id' => $user->id,
                    'order_number' => $orderNumber,
                    'status' => $status,
                    'subtotal' => 0,
                    'tax' => 0,
                    'shipping' => 50.00,
                    'discount' => 0,
                    'total' => 0,
                    'billing_name' => $user->name,
                    'billing_email' => $user->email,
                    'billing_phone' => '+91' . rand(7000000000, 9999999999),
                    'billing_address' => $this->getRandomAddress(),
                    'billing_city' => $this->getRandomCity(),
                    'billing_state' => $this->getRandomState(),
                    'billing_zipcode' => rand(100000, 999999),
                    'billing_country' => 'India',
                    'shipping_name' => $user->name,
                    'shipping_email' => $user->email,
                    'shipping_phone' => '+91' . rand(7000000000, 9999999999),
                    'shipping_address' => $this->getRandomAddress(),
                    'shipping_city' => $this->getRandomCity(),
                    'shipping_state' => $this->getRandomState(),
                    'shipping_zipcode' => rand(100000, 999999),
                    'shipping_country' => 'India',
                    'payment_status' => $status === Order::STATUS_COMPLETED ? 'completed' : 'pending',
                ]);

                // Add order items
                $maxItems = min(5, $components->count());
                $numItems = rand(1, $maxItems);
                $selectedComponents = $components->random($numItems);
                $subtotal = 0;

                foreach ($selectedComponents as $component) {
                    $quantity = rand(1, 2);
                    $price = $component->price;
                    $itemTotal = $price * $quantity;
                    $subtotal += $itemTotal;

                    OrderItem::create([
                        'order_id' => $order->id,
                        'component_id' => $component->id,
                        'quantity' => $quantity,
                        'price' => $price,
                        'name' => $component->name,
                        'options' => json_encode([
                            'brand' => $component->brand,
                            'model' => $component->model,
                        ]),
                    ]);
                }

                // Update order totals
                $tax = $subtotal * 0.18; // 18% GST
                $total = $subtotal + $tax + $order->shipping;

                $order->update([
                    'subtotal' => $subtotal,
                    'tax' => $tax,
                    'total' => $total,
                ]);
            }
        }
    }

    private function getRandomStatus(): string
    {
        $statuses = [
            Order::STATUS_PENDING,
            Order::STATUS_PROCESSING,
            Order::STATUS_SHIPPED,
            Order::STATUS_DELIVERED,
            Order::STATUS_COMPLETED,
            Order::STATUS_CANCELLED,
        ];

        return $statuses[array_rand($statuses)];
    }

    private function getRandomAddress(): string
    {
        $addresses = [
            '123 MG Road',
            '456 Brigade Road',
            '789 Commercial Street',
            '321 Residency Road',
            '654 Koramangala',
            '987 Indiranagar',
            '147 Whitefield',
            '258 Electronic City',
        ];

        return $addresses[array_rand($addresses)];
    }

    private function getRandomCity(): string
    {
        $cities = [
            'Bangalore', 'Mumbai', 'Delhi', 'Chennai', 'Hyderabad',
            'Pune', 'Kolkata', 'Ahmedabad', 'Jaipur', 'Lucknow'
        ];

        return $cities[array_rand($cities)];
    }

    private function getRandomState(): string
    {
        $states = [
            'Karnataka', 'Maharashtra', 'Delhi', 'Tamil Nadu', 'Telangana',
            'Maharashtra', 'West Bengal', 'Gujarat', 'Rajasthan', 'Uttar Pradesh'
        ];

        return $states[array_rand($states)];
    }


}