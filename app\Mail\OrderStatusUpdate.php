<?php

namespace App\Mail;

use App\Models\Order;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class OrderStatusUpdate extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    /**
     * Create a new message instance.
     */
    public function __construct(
        public Order $order,
        public string $previousStatus,
        public ?string $updateMessage = null
    ) {
        //
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        $statusText = ucfirst($this->order->status);
        
        return new Envelope(
            subject: "Order {$statusText} - {$this->order->order_number}",
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'emails.order-status-update',
            with: [
                'order' => $this->order,
                'user' => $this->order->user,
                'previousStatus' => $this->previousStatus,
                'currentStatus' => $this->order->status,
                'updateMessage' => $this->updateMessage,
                'trackingInfo' => $this->getTrackingInfo(),
            ],
        );
    }

    /**
     * Get tracking information if available.
     */
    private function getTrackingInfo(): ?array
    {
        if ($this->order->tracking_number && $this->order->tracking_carrier) {
            return [
                'number' => $this->order->tracking_number,
                'carrier' => $this->order->tracking_carrier,
                'url' => $this->order->tracking_url,
            ];
        }

        return null;
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}