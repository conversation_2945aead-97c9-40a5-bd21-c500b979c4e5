@extends('layouts.app')
@section('content')
    <div class="bg-slate-900 text-white overflow-x-hidden">
        <!-- Hero Section -->
        <section class="relative min-h-screen flex items-center justify-center cyber-grid">
            <!-- Animated Background Elements -->
            <div class="absolute inset-0 overflow-hidden">
                <div class="absolute top-20 left-20 w-96 h-96 bg-blue-500 rounded-full opacity-10 floating"></div>
                <div class="absolute bottom-20 right-20 w-80 h-80 bg-cyan-500 rounded-full opacity-10 floating"
                    style="animation-delay: 2s;"></div>
                <div
                    class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[600px] h-[600px] border border-blue-500 rounded-full opacity-20 pulse-ring">
                </div>
            </div>

            <div class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
                <!-- Main Content -->
                <div class="text-center mb-16">
                    <!-- Headline -->
                    <h1 class="tech-font text-6xl md:text-8xl font-black mb-6 neon-text" id="headline">
                        <span class="bg-gradient-to-r from-blue-400 via-cyan-400 to-blue-600 bg-clip-text text-transparent">
                            NEXUS
                        </span>
                        <span class="text-white">PC</span>
                    </h1>

                    <!-- Dynamic Tagline -->
                    <div class="h-16 flex items-center justify-center mb-8">
                        <p class="tech-font text-2xl md:text-3xl font-bold text-blue-400" id="tagline">
                            Silent. Powerful. Smart.
                        </p>
                    </div>

                    <!-- Performance Stats -->
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12 max-w-4xl mx-auto">
                        <!-- FPS Benchmark -->
                        <div class="stat-card p-6 rounded-xl glow">
                            <div class="flex items-center justify-between mb-3">
                                <h3 class="tech-font text-sm font-semibold text-blue-400">FPS BENCHMARK</h3>
                                <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                            </div>
                            <div class="text-3xl font-bold text-white mb-2">
                                <span id="fps-counter">0</span>
                                <span class="text-lg text-gray-400">FPS</span>
                            </div>
                            <p class="text-xs text-gray-400">4K Ultra Settings</p>
                        </div>

                        <!-- Thermal Output -->
                        <div class="stat-card p-6 rounded-xl glow">
                            <div class="flex items-center justify-between mb-3">
                                <h3 class="tech-font text-sm font-semibold text-blue-400">THERMAL OUTPUT</h3>
                                <div class="w-2 h-2 bg-blue-400 rounded-full animate-pulse"></div>
                            </div>
                            <div class="text-3xl font-bold text-white mb-2">
                                <span id="temp-counter">0</span>
                                <span class="text-lg text-gray-400">°C</span>
                            </div>
                            <p class="text-xs text-gray-400">Under Load</p>
                        </div>

                        <!-- Watts Saved -->
                        <div class="stat-card p-6 rounded-xl glow">
                            <div class="flex items-center justify-between mb-3">
                                <h3 class="tech-font text-sm font-semibold text-blue-400">EFFICIENCY</h3>
                                <div class="w-2 h-2 bg-yellow-400 rounded-full animate-pulse"></div>
                            </div>
                            <div class="text-3xl font-bold text-white mb-2">
                                <span id="watts-counter">0</span>
                                <span class="text-lg text-gray-400">W</span>
                            </div>
                            <p class="text-xs text-gray-400">Power Saved</p>
                        </div>
                    </div>
                </div>

                <!-- Component Showcase -->
                <div class="max-w-5xl mx-auto">
                    <h2 class="tech-font text-2xl font-bold text-center mb-8 text-blue-400">COMPONENT PERFORMANCE</h2>

                    <div class="grid grid-cols-2 md:grid-cols-4 gap-8 justify-items-center">
                        <!-- CPU -->
                        <div class="component-icon cursor-pointer" data-tooltip="cpu">
                            <div
                                class="w-20 h-20 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-lg flex items-center justify-center relative">
                                <svg class="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 24 24">
                                    <path
                                        d="M4 4h4v4H4V4zm6 0h4v4h-4V4zm6 0h4v4h-4V4zM4 10h4v4H4v-4zm6 0h4v4h-4v-4zm6 0h4v4h-4v-4zM4 16h4v4H4v-4zm6 0h4v4h-4v-4zm6 0h4v4h-4v-4z" />
                                </svg>
                                <div
                                    class="absolute inset-0 rounded-lg border-2 border-blue-400 opacity-0 hover:opacity-100 transition-opacity duration-300">
                                </div>
                            </div>
                            <p class="tech-font text-sm mt-3 text-center text-gray-400">CPU</p>
                        </div>

                        <!-- GPU -->
                        <div class="component-icon cursor-pointer" data-tooltip="gpu">
                            <div
                                class="w-20 h-20 bg-gradient-to-br from-green-500 to-teal-500 rounded-lg flex items-center justify-center relative">
                                <svg class="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 24 24">
                                    <path
                                        d="M21 16.5c0 .38-.21.71-.53.88l-7.9 4.44c-.16.12-.36.18-.57.18-.21 0-.41-.06-.57-.18l-7.9-4.44A.991.991 0 0 1 3 16.5v-9c0-.38.21-.71.53-.88l7.9-4.44c.16-.12.36-.18.57-.18.21 0 .41.06.57.18l7.9 4.44c.**********.53.88v9z" />
                                </svg>
                                <div
                                    class="absolute inset-0 rounded-lg border-2 border-green-400 opacity-0 hover:opacity-100 transition-opacity duration-300">
                                </div>
                            </div>
                            <p class="tech-font text-sm mt-3 text-center text-gray-400">GPU</p>
                        </div>

                        <!-- RAM -->
                        <div class="component-icon cursor-pointer" data-tooltip="ram">
                            <div
                                class="w-20 h-20 bg-gradient-to-br from-purple-500 to-pink-500 rounded-lg flex items-center justify-center relative">
                                <svg class="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M6 7h12v2H6V7zm0 4h12v2H6v-2zm0 4h12v2H6v-2z" />
                                </svg>
                                <div
                                    class="absolute inset-0 rounded-lg border-2 border-purple-400 opacity-0 hover:opacity-100 transition-opacity duration-300">
                                </div>
                            </div>
                            <p class="tech-font text-sm mt-3 text-center text-gray-400">RAM</p>
                        </div>

                        <!-- Storage -->
                        <div class="component-icon cursor-pointer" data-tooltip="storage">
                            <div
                                class="w-20 h-20 bg-gradient-to-br from-orange-500 to-red-500 rounded-lg flex items-center justify-center relative">
                                <svg class="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 24 24">
                                    <path
                                        d="M6 2c-1.1 0-1.99.9-1.99 2L4 20c0 1.1.89 2 2 2h10v-2H6V4h7v5h5v1.67l2-2V8l-6-6H6z" />
                                </svg>
                                <div
                                    class="absolute inset-0 rounded-lg border-2 border-orange-400 opacity-0 hover:opacity-100 transition-opacity duration-300">
                                </div>
                            </div>
                            <p class="tech-font text-sm mt-3 text-center text-gray-400">SSD</p>
                        </div>
                    </div>
                </div>

                <!-- CTA Button -->
                <div class="text-center mt-16">
                    <button
                        class="px-12 py-4 bg-gradient-to-r from-blue-600 to-cyan-600 rounded-lg font-semibold tech-font text-lg hover:from-blue-700 hover:to-cyan-700 transition-all duration-300 transform hover:scale-105 glow">
                        CONFIGURE YOUR BUILD
                    </button>
                </div>
            </div>

            <!-- Tooltips -->
            <div id="tooltip" class="tooltip"></div>
        </section>
    </div>
@endsection
@push('styles')
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Inter:wght@300;400;500;600&display=swap');

        .tech-font {
            font-family: 'Orbitron', monospace;
        }

        .content-font {
            font-family: 'Inter', sans-serif;
        }

        .cyber-grid {
            background-image:
                linear-gradient(rgba(59, 130, 246, 0.1) 1px, transparent 1px),
                linear-gradient(90deg, rgba(59, 130, 246, 0.1) 1px, transparent 1px);
            background-size: 20px 20px;
            animation: grid-move 20s linear infinite;
        }

        @keyframes grid-move {
            0% {
                background-position: 0 0;
            }

            100% {
                background-position: 20px 20px;
            }
        }

        .glow {
            box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
        }

        .neon-text {
            text-shadow: 0 0 10px rgba(59, 130, 246, 0.5);
        }

        .component-icon {
            transition: all 0.3s ease;
            position: relative;
        }

        .component-icon:hover {
            transform: scale(1.1);
            filter: drop-shadow(0 0 15px rgba(59, 130, 246, 0.6));
        }

        .tooltip {
            position: absolute;
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 12px 16px;
            border-radius: 8px;
            font-size: 12px;
            border: 1px solid rgba(59, 130, 246, 0.3);
            opacity: 0;
            pointer-events: none;
            transform: translateY(10px);
            transition: all 0.3s ease;
            z-index: 1000;
            min-width: 200px;
        }

        .tooltip.show {
            opacity: 1;
            transform: translateY(0);
        }

        .stat-card {
            background: linear-gradient(135deg, rgba(15, 23, 42, 0.8) 0%, rgba(30, 41, 59, 0.8) 100%);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(59, 130, 246, 0.2);
        }

        .pulse-ring {
            animation: pulse-ring 2s infinite;
        }

        @keyframes pulse-ring {
            0% {
                transform: scale(0.8);
                opacity: 1;
            }

            100% {
                transform: scale(1.2);
                opacity: 0;
            }
        }

        .floating {
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {

            0%,
            100% {
                transform: translateY(0px);
            }

            50% {
                transform: translateY(-20px);
            }
        }
    </style>
@endpush
@push('scripts')
    <script>
        // Initialize GSAP animations
        gsap.registerPlugin();

        // Animated counter for performance stats
        function animateCounter(id, target, duration = 2000, suffix = '') {
            const element = document.getElementById(id);
            const increment = target / (duration / 16);
            let current = 0;

            const timer = setInterval(() => {
                current += increment;
                if (current >= target) {
                    current = target;
                    clearInterval(timer);
                }
                element.textContent = Math.floor(current) + suffix;
            }, 16);
        }

        // Rotating taglines
        const taglines = [
            "Silent. Powerful. Smart.",
            "Performance Redefined.",
            "Future-Ready Computing.",
            "Precision Engineering.",
            "Next-Gen Performance."
        ];

        let currentTagline = 0;

        function rotateTagline() {
            const taglineEl = document.getElementById('tagline');

            gsap.to(taglineEl, {
                opacity: 0,
                y: -20,
                duration: 0.5,
                ease: "power2.out",
                onComplete: () => {
                    currentTagline = (currentTagline + 1) % taglines.length;
                    taglineEl.textContent = taglines[currentTagline];
                    gsap.to(taglineEl, {
                        opacity: 1,
                        y: 0,
                        duration: 0.5,
                        ease: "power2.out"
                    });
                }
            });
        }

        // Component tooltip data
        const tooltipData = {
            cpu: {
                title: "Intel Core i9-13900KS",
                specs: [
                    "24 Cores, 32 Threads",
                    "Base: 3.2GHz, Boost: 6.0GHz",
                    "Performance: 98,500 PassMark",
                    "TDP: 150W"
                ]
            },
            gpu: {
                title: "NVIDIA RTX 4090",
                specs: [
                    "16,384 CUDA Cores",
                    "24GB GDDR6X Memory",
                    "4K Gaming: 120+ FPS",
                    "Ray Tracing: Ultimate"
                ]
            },
            ram: {
                title: "DDR5-6000 32GB",
                specs: [
                    "32GB (2x16GB) Kit",
                    "Speed: 6000 MT/s",
                    "Latency: CL30",
                    "RGB Lighting"
                ]
            },
            storage: {
                title: "Samsung 990 PRO 2TB",
                specs: [
                    "PCIe 4.0 NVMe SSD",
                    "Read: 7,450 MB/s",
                    "Write: 6,900 MB/s",
                    "Endurance: 1,200 TBW"
                ]
            }
        };

        // Tooltip functionality
        const tooltip = document.getElementById('tooltip');
        const componentIcons = document.querySelectorAll('.component-icon');

        componentIcons.forEach(icon => {
            icon.addEventListener('mouseenter', (e) => {
                const componentType = e.currentTarget.dataset.tooltip;
                const data = tooltipData[componentType];

                if (data) {
                    const specsHTML = data.specs.map(spec => `<div class="mb-1">${spec}</div>`).join('');
                    tooltip.innerHTML = `
                <div class="font-semibold text-blue-400 mb-2">${data.title}</div>
                <div class="text-gray-300">${specsHTML}</div>
            `;

                    const rect = e.currentTarget.getBoundingClientRect();
                    tooltip.style.left = rect.left + rect.width / 2 - tooltip.offsetWidth / 2 + 'px';
                    tooltip.style.top = rect.top - tooltip.offsetHeight - 10 + 'px';
                    tooltip.classList.add('show');
                }
            });

            icon.addEventListener('mouseleave', () => {
                tooltip.classList.remove('show');
            });
        });

        // Real-time performance simulation
        function simulatePerformance() {
            const fpsBase = 165;
            const tempBase = 68;
            const wattsBase = 125;

            // Simulate fluctuations
            const fpsVariation = Math.random() * 20 - 10;
            const tempVariation = Math.random() * 10 - 5;
            const wattsVariation = Math.random() * 30 - 15;

            const targetFPS = Math.max(30, fpsBase + fpsVariation);
            const targetTemp = Math.max(35, tempBase + tempVariation);
            const targetWatts = Math.max(50, wattsBase + wattsVariation);

            // Animate to new values
            gsap.to({
                fps: parseInt(document.getElementById('fps-counter').textContent)
            }, {
                fps: targetFPS,
                duration: 2,
                ease: "power2.out",
                onUpdate: function() {
                    document.getElementById('fps-counter').textContent = Math.floor(this.targets()[0].fps);
                }
            });

            gsap.to({
                temp: parseInt(document.getElementById('temp-counter').textContent)
            }, {
                temp: targetTemp,
                duration: 2,
                ease: "power2.out",
                onUpdate: function() {
                    document.getElementById('temp-counter').textContent = Math.floor(this.targets()[0].temp);
                }
            });

            gsap.to({
                watts: parseInt(document.getElementById('watts-counter').textContent)
            }, {
                watts: targetWatts,
                duration: 2,
                ease: "power2.out",
                onUpdate: function() {
                    document.getElementById('watts-counter').textContent = Math.floor(this.targets()[0].watts);
                }
            });
        }

        // Initialize animations on page load
        document.addEventListener('DOMContentLoaded', () => {
            // Animate headline entrance
            gsap.from('#headline', {
                opacity: 0,
                y: 50,
                duration: 1.5,
                ease: "power3.out"
            });

            // Animate tagline entrance
            gsap.from('#tagline', {
                opacity: 0,
                y: 30,
                duration: 1,
                delay: 0.5,
                ease: "power2.out"
            });

            // Animate stat cards
            gsap.from('.stat-card', {
                opacity: 0,
                y: 30,
                duration: 0.8,
                stagger: 0.2,
                delay: 1,
                ease: "power2.out"
            });

            // Animate component icons
            gsap.from('.component-icon', {
                opacity: 0,
                scale: 0.8,
                duration: 0.6,
                stagger: 0.1,
                delay: 1.5,
                ease: "back.out(1.7)"
            });

            // Start initial counter animations
            setTimeout(() => {
                animateCounter('fps-counter', 165);
                animateCounter('temp-counter', 68);
                animateCounter('watts-counter', 125);
            }, 2000);

            // Start periodic updates
            setInterval(simulatePerformance, 5000);
            setInterval(rotateTagline, 4000);
        });
    </script>
@endpush
