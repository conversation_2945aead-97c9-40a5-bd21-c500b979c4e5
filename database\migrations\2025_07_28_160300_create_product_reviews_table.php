<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('product_reviews', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('product_id')->constrained()->onDelete('cascade');
            $table->tinyInteger('rating')->unsigned(); // Better for small numbers
            $table->string('title', 255)->nullable(); // Specify length for better performance
            $table->text('comment')->nullable();
            $table->boolean('is_approved')->default(false);
            $table->boolean('is_verified_purchase')->default(false);
            $table->json('images')->nullable(); // Review images
            $table->timestamps();

            // Indexes
            $table->unique(['user_id', 'product_id'], 'unique_user_product_review');
            $table->index(['product_id', 'is_approved'], 'idx_product_approved');
            $table->index(['rating', 'is_approved'], 'idx_rating_approved');
            $table->index('created_at'); // Useful for sorting by date
        });

        // Add check constraint using raw SQL for better compatibility
        // DB::statement('ALTER TABLE product_reviews ADD CONSTRAINT chk_rating_range CHECK (rating >= 1 AND rating <= 5)');
    }

    public function down(): void
    {
        Schema::dropIfExists('product_reviews');
    }
};