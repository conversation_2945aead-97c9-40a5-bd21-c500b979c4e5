<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Transaction;
use App\Services\TransactionService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class TransactionController extends Controller
{
    public function __construct(
        private TransactionService $transactionService
    ) {}

    /**
     * Display a listing of transactions
     * Requirements: 5.1, 5.2
     */
    public function index(Request $request)
    {
        $query = Transaction::with(['user'])
            ->orderBy('created_at', 'desc');

        // Apply filters
        if ($request->filled('gateway')) {
            $query->where('gateway_name', $request->gateway);
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('transaction_id', 'like', "%{$search}%")
                  ->orWhere('gateway_transaction_id', 'like', "%{$search}%")
                  ->orWhere('amount', 'like', "%{$search}%")
                  ->orWhereHas('user', function ($userQuery) use ($search) {
                      $userQuery->where('email', 'like', "%{$search}%")
                               ->orWhere('name', 'like', "%{$search}%");
                  });
            });
        }

        $transactions = $query->paginate(20);

        // Get statistics
        $stats = [
            'total_transactions' => Transaction::count(),
            'completed_transactions' => Transaction::where('status', 'completed')->count(),
            'failed_transactions' => Transaction::where('status', 'failed')->count(),
            'pending_transactions' => Transaction::where('status', 'pending')->count(),
            'total_amount' => Transaction::where('status', 'completed')->sum('amount'),
        ];

        return view('admin.transactions.index', compact('transactions', 'stats'));
    }

    /**
     * Display the specified transaction
     * Requirements: 5.3
     */
    public function show(Transaction $transaction)
    {
        $transaction->load(['user']);
        
        return view('admin.transactions.show', compact('transaction'));
    }

    /**
     * Process refund for a transaction
     * Requirements: 5.4
     */
    public function refund(Request $request, Transaction $transaction)
    {
        $request->validate([
            'refund_amount' => 'required|numeric|min:0.01|max:' . $transaction->amount,
            'refund_reason' => 'required|string|max:255'
        ]);

        try {
            $result = $this->transactionService->processRefund(
                $transaction,
                $request->refund_amount,
                $request->refund_reason
            );

            // Log admin action
            Log::info('Transaction refund processed by admin', [
                'admin_id' => auth()->id(),
                'admin_email' => auth()->user()->email,
                'transaction_id' => $transaction->transaction_id,
                'refund_amount' => $request->refund_amount,
                'refund_reason' => $request->refund_reason,
                'timestamp' => now()->toISOString()
            ]);

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Refund processed successfully',
                    'data' => $result
                ]);
            }

            return redirect()->back()
                           ->with('success', 'Refund processed successfully');

        } catch (\Exception $e) {
            Log::error('Transaction refund failed', [
                'admin_id' => auth()->id(),
                'transaction_id' => $transaction->transaction_id,
                'error' => $e->getMessage()
            ]);

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'error' => 'Refund processing failed: ' . $e->getMessage()
                ], 400);
            }

            return redirect()->back()
                           ->with('error', 'Refund processing failed: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified transaction
     * Requirements: 5.4
     */
    public function destroy(Transaction $transaction)
    {
        // Only allow deletion of failed or cancelled transactions
        if (!in_array($transaction->status, ['failed', 'cancelled'])) {
            if (request()->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'error' => 'Only failed or cancelled transactions can be deleted'
                ], 400);
            }

            return redirect()->back()
                           ->with('error', 'Only failed or cancelled transactions can be deleted');
        }

        try {
            // Log admin action before deletion
            Log::warning('Transaction deleted by admin', [
                'admin_id' => auth()->id(),
                'admin_email' => auth()->user()->email,
                'transaction_id' => $transaction->transaction_id,
                'transaction_status' => $transaction->status,
                'transaction_amount' => $transaction->amount,
                'timestamp' => now()->toISOString()
            ]);

            $transaction->delete();

            if (request()->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Transaction deleted successfully'
                ]);
            }

            return redirect()->route('admin.transactions.index')
                           ->with('success', 'Transaction deleted successfully');

        } catch (\Exception $e) {
            Log::error('Transaction deletion failed', [
                'admin_id' => auth()->id(),
                'transaction_id' => $transaction->transaction_id,
                'error' => $e->getMessage()
            ]);

            if (request()->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'error' => 'Transaction deletion failed: ' . $e->getMessage()
                ], 500);
            }

            return redirect()->back()
                           ->with('error', 'Transaction deletion failed: ' . $e->getMessage());
        }
    }

    /**
     * Export transactions to CSV
     * Requirements: 5.4
     */
    public function export(Request $request)
    {
        try {
            $query = Transaction::with(['user']);

            // Apply same filters as index
            if ($request->filled('gateway')) {
                $query->where('gateway_name', $request->gateway);
            }

            if ($request->filled('status')) {
                $query->where('status', $request->status);
            }

            if ($request->filled('date_from')) {
                $query->whereDate('created_at', '>=', $request->date_from);
            }

            if ($request->filled('date_to')) {
                $query->whereDate('created_at', '<=', $request->date_to);
            }

            $transactions = $query->orderBy('created_at', 'desc')->get();

            $filename = 'transactions_' . now()->format('Y-m-d_H-i-s') . '.csv';
            
            $headers = [
                'Content-Type' => 'text/csv',
                'Content-Disposition' => "attachment; filename=\"{$filename}\"",
            ];

            $callback = function() use ($transactions) {
                $file = fopen('php://output', 'w');
                
                // CSV headers
                fputcsv($file, [
                    'Transaction ID',
                    'Gateway Transaction ID',
                    'Gateway',
                    'User Email',
                    'Amount',
                    'Currency',
                    'Status',
                    'Webhook Verified',
                    'Failure Reason',
                    'Created At',
                    'Updated At',
                    'Completed At'
                ]);

                // CSV data
                foreach ($transactions as $transaction) {
                    fputcsv($file, [
                        $transaction->transaction_id,
                        $transaction->gateway_transaction_id,
                        $transaction->gateway_name,
                        $transaction->user->email ?? 'N/A',
                        $transaction->amount,
                        $transaction->currency,
                        $transaction->status,
                        $transaction->webhook_verified ? 'Yes' : 'No',
                        $transaction->failure_reason,
                        $transaction->created_at->format('Y-m-d H:i:s'),
                        $transaction->updated_at->format('Y-m-d H:i:s'),
                        $transaction->completed_at ? $transaction->completed_at->format('Y-m-d H:i:s') : 'N/A'
                    ]);
                }

                fclose($file);
            };

            // Log export action
            Log::info('Transactions exported by admin', [
                'admin_id' => auth()->id(),
                'admin_email' => auth()->user()->email,
                'export_count' => $transactions->count(),
                'filters' => $request->only(['gateway', 'status', 'date_from', 'date_to']),
                'timestamp' => now()->toISOString()
            ]);

            return response()->stream($callback, 200, $headers);

        } catch (\Exception $e) {
            Log::error('Transaction export failed', [
                'admin_id' => auth()->id(),
                'error' => $e->getMessage()
            ]);

            return redirect()->back()
                           ->with('error', 'Export failed: ' . $e->getMessage());
        }
    }
}