<div class="bg-white rounded-lg shadow p-6">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-xl font-semibold text-gray-900">Popular Builds</h2>
        <button 
            wire:click="refresh"
            class="text-sm text-blue-600 hover:text-blue-800 focus:outline-none"
        >
            Refresh
        </button>
    </div>
    
    @if($builds->isEmpty())
        <div class="text-center py-8">
            <svg class="mx-auto h-12 w-12 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900">No builds found</h3>
            <p class="mt-1 text-sm text-gray-500">Be the first to create and share a build!</p>
        </div>
    @else
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            @foreach($builds as $build)
                <div class="border rounded-lg overflow-hidden hover:shadow-lg transition-shadow">
                    <!-- Build Header -->
                    <div class="p-4 border-b border-gray-200">
                        <div class="flex justify-between items-start">
                            <div class="flex-1 min-w-0">
                                <h3 class="text-lg font-medium text-gray-900 truncate">
                                    {{ $build->name }}
                                </h3>
                                @if($build->description)
                                    <p class="mt-1 text-sm text-gray-500 line-clamp-2">
                                        {{ $build->description }}
                                    </p>
                                @endif
                            </div>
                            
                            <!-- Build Status -->
                            @if($build->is_complete)
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    Complete
                                </span>
                            @else
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                    Incomplete
                                </span>
                            @endif
                        </div>
                        
                        <!-- Build Author -->
                        <div class="mt-2 flex items-center text-sm text-gray-500">
                            <svg class="mr-1.5 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                            </svg>
                            by {{ $build->user->name ?? 'Anonymous' }}
                            <span class="mx-1">•</span>
                            {{ $build->created_at->diffForHumans() }}
                        </div>
                    </div>
                    
                    <!-- Build Components -->
                    <div class="p-4">
                        <div class="space-y-2">
                            @foreach($build->components->take(3) as $buildComponent)
                                <div class="flex justify-between items-center text-sm">
                                    <span class="text-gray-600">{{ $buildComponent->component->category->name ?? 'Component' }}:</span>
                                    <span class="font-medium text-gray-900 truncate ml-2">
                                        {{ $buildComponent->component->name }}
                                    </span>
                                </div>
                            @endforeach
                            
                            @if($build->components->count() > 3)
                                <div class="text-sm text-gray-500">
                                    +{{ $build->components->count() - 3 }} more components
                                </div>
                            @endif
                        </div>
                        
                        <!-- Build Price -->
                        <div class="mt-4 pt-4 border-t border-gray-200">
                            <div class="flex justify-between items-center">
                                <span class="text-sm font-medium text-gray-900">Total Price:</span>
                                <span class="text-lg font-bold text-gray-900">
                                    ${{ number_format($build->total_price, 2) }}
                                </span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Build Actions -->
                    <div class="px-4 py-3 bg-gray-50 border-t border-gray-200">
                        <div class="flex justify-between space-x-2">
                            <button 
                                wire:click="viewBuild({{ $build->id }})"
                                class="flex-1 px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                            >
                                View Details
                            </button>
                            
                            @auth
                                <button 
                                    wire:click="cloneBuild({{ $build->id }})"
                                    class="flex-1 px-3 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                                >
                                    Clone Build
                                </button>
                            @else
                                <a 
                                    href="{{ route('login') }}"
                                    class="flex-1 px-3 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 text-center"
                                >
                                    Login to Clone
                                </a>
                            @endauth
                        </div>
                    </div>
                </div>
            @endforeach
        </div>
    @endif
    
    <!-- Flash Messages -->
    @if (session()->has('message'))
        <div class="mt-4 p-4 bg-green-50 border border-green-200 rounded-md">
            <div class="flex">
                <svg class="h-5 w-5 text-green-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                </svg>
                <p class="ml-3 text-sm text-green-700">{{ session('message') }}</p>
            </div>
        </div>
    @endif

    @if (session()->has('error'))
        <div class="mt-4 p-4 bg-red-50 border border-red-200 rounded-md">
            <div class="flex">
                <svg class="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                </svg>
                <p class="ml-3 text-sm text-red-700">{{ session('error') }}</p>
            </div>
        </div>
    @endif
</div>