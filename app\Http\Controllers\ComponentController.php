<?php

namespace App\Http\Controllers;

use App\Models\Component;
use App\Models\ComponentCategory;
use Illuminate\Http\Request;

class ComponentController extends Controller
{
    /**
     * Display a listing of the components.
     *
     * @return \Illuminate\View\View
     */
    public function index(Request $request)
    {   
        // The actual component listing is handled by the ProductList Livewire component
        return view('shop.index');
    }
    
    /**
     * Display the specified component by ID.
     *
     * @param  int  $id
     * @return \Illuminate\View\View
     */
    public function showById($id)
    {   
        // dd("Component");
        $component = Component::findOrFail($id);
        return view('shop.component', ['component' => $component]);
    }

    /**
     * Display the specified component by slug.
     *
     * @param  string  $slug
     * @return \Illuminate\View\View
     */
    public function show($slug)
    {   
        // The actual component detail is handled by the ProductDetail Livewire component
        return view('shop.product', ['slug' => $slug]);
    }

    /**
     * Display components by category.
     *
     * @param  string  $slug
     * @return \Illuminate\View\View
     */
    public function category($slug)
    {   
        $category = ComponentCategory::where('slug', $slug)->firstOrFail();
        return view('shop.category', ['category' => $category]);
    }
}