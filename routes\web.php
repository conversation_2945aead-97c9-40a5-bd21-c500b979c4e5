<?php

use App\Http\Controllers\InstallationController;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\SiteController;
use App\Http\Controllers\BlogPostController;

use App\Http\Controllers\PaymentController;
use App\Http\Controllers\WebhookController;


Route::group(['prefix' => 'install', 'middleware' => 'install'], function () {
    Route::get('/', [InstallationController::class, 'showWelcome'])->name('installer.welcome');
    Route::get('/requirements', [InstallationController::class, 'checkRequirements'])->name('installer.requirements');
    Route::get('/script-activation', [InstallationController::class, 'showLicense'])->name('installer.license');
    Route::post('/script-activation', [InstallationController::class, 'activateLicense'])->name('installer.license.save');
    Route::get('/email-config', [InstallationController::class, 'showEmailConfig'])->name('installer.email-config');
    Route::get('/email-config/test', [InstallationController::class, 'testEmailConfig'])->name('installer.email-config.test');
    Route::post('/email-config', [InstallationController::class, 'saveEmailConfig'])->name('installer.email-config.save');
    Route::get('/database', [InstallationController::class, 'showDatabaseConfig'])->name('installer.database');
    Route::post('/database', [InstallationController::class, 'saveDatabaseConfig'])->name('installer.database.save');
    Route::post('/test-connection', [InstallationController::class, 'testConnection'])->name('installer.database.test');
    Route::get('/migrations', [InstallationController::class, 'runMigrations'])->name('installer.migrations');
    Route::get('/create-admin', [InstallationController::class, 'showAdminCreation'])->name('installer.create-admin');
    Route::post('/create-admin', [InstallationController::class, 'createAdmin'])->name('installer.admin.save');
    Route::get('/complete', [InstallationController::class, 'complete'])->name('installer.complete');
});

// Route::get('/', function () {
//     return view('welcome');
// });

Route::get('/', [SiteController::class,'index'])->name('home');

Route::get('/hometest', function () {
    // return view('home.gamer_hero_section');
    // return view('home.hightech_pc_hero'); //--
    return view('home.pc_build_hero');
    // return view('home.pc_builder_hero');
    // return view('home.pc_config_hero');
});

Route::middleware([
    'auth:sanctum',
    config('jetstream.auth_session'),
    'verified',
])->group(function () {
    Route::get('/dashboard', function () {
        return view('dashboard');
    })->name('dashboard');
});

// User Dashboard Feature Routes - Using Livewire Components
Route::middleware(['auth'])->group(function () {
    // Main dashboard with all user functionality via Livewire components
    Route::get('/user/dashboard', function () {
        return view('user.dashboard');
    })->name('user.dashboard');
});

// Shop Routes
Route::prefix('shop')->name('shop.')->group(function () {
    Route::get('/', [\App\Http\Controllers\ComponentController::class, 'index'])->name('index');
    // Route::get('/component/{id}', [\App\Http\Controllers\ComponentController::class, 'showById'])->name('component');
    Route::get('/product/{slug}', [\App\Http\Controllers\ComponentController::class, 'show'])->name('product');
    Route::get('/category/{slug}', [\App\Http\Controllers\ComponentController::class, 'category'])->name('category');
    
    // Cart routes within shop namespace for compatibility
    Route::get('/cart', [\App\Http\Controllers\CartController::class, 'index'])->name('cart');
    Route::prefix('cart')->name('cart.')->group(function () {
        Route::post('/add', [\App\Http\Controllers\CartController::class, 'addItem'])->name('add');
        Route::post('/build/{buildId}', [\App\Http\Controllers\CartController::class, 'addBuild'])->name('add-build');
    });
});

// Product routes
Route::prefix('products')->name('products.')->group(function () {
    Route::get('/', [\App\Http\Controllers\ProductController::class, 'index'])->name('index');
    Route::get('/{slug}', [\App\Http\Controllers\ProductController::class, 'show'])->name('show');
    Route::get('/category/{category}', [\App\Http\Controllers\ProductController::class, 'category'])->name('category');
    Route::get('/featured', [\App\Http\Controllers\ProductController::class, 'featured'])->name('featured');
    Route::get('/sale', [\App\Http\Controllers\ProductController::class, 'sale'])->name('sale');
});

// Cart Routes (separate from shop for cleaner URLs)
Route::prefix('cart')->name('cart.')->group(function () {
    Route::get('/', [\App\Http\Controllers\CartController::class, 'index'])->name('index');
    Route::post('/add', [\App\Http\Controllers\CartController::class, 'addItem'])->name('add');
    Route::patch('/update', [\App\Http\Controllers\CartController::class, 'updateItems'])->name('update');
    Route::delete('/remove/{componentId}', [\App\Http\Controllers\CartController::class, 'removeItem'])->name('remove');
    Route::delete('/clear', [\App\Http\Controllers\CartController::class, 'clearCart'])->name('clear');
    Route::post('/apply-coupon', [\App\Http\Controllers\CartController::class, 'applyCoupon'])->name('apply-coupon');
    Route::post('/build/{buildId}', [\App\Http\Controllers\CartController::class, 'addBuild'])->name('add-build');
});

// Checkout Routes
Route::prefix('checkout')->name('checkout.')->group(function () {
    Route::get('/', [\App\Http\Controllers\CheckoutController::class, 'index'])->name('index');
    Route::post('/shipping', [\App\Http\Controllers\CheckoutController::class, 'saveShipping'])->name('shipping');
    Route::post('/shipping-method', [\App\Http\Controllers\CheckoutController::class, 'saveShippingMethod'])->name('shipping-method');
    Route::post('/billing', [\App\Http\Controllers\CheckoutController::class, 'saveBilling'])->name('billing');
    Route::post('/payment', [\App\Http\Controllers\CheckoutController::class, 'processPayment'])->name('payment');
    Route::post('/complete', [\App\Http\Controllers\CheckoutController::class, 'complete'])->name('complete');
    Route::post('/confirm-price-changes', [\App\Http\Controllers\CheckoutController::class, 'confirmPriceChanges'])->name('confirm-price-changes');
    Route::get('/totals', [\App\Http\Controllers\CheckoutController::class, 'getTotals'])->name('totals');
    Route::get('/success/{orderNumber}', [\App\Http\Controllers\CheckoutController::class, 'success'])->name('success');
});

// PC Builder Routes
Route::prefix('builder')->name('builder.')->group(function () {
    Route::get('/', [\App\Http\Controllers\BuildController::class, 'index'])->name('index');
    Route::get('/build/{id}', [\App\Http\Controllers\BuildController::class, 'show'])->name('show');
    Route::get('/share/{id}/{token}', [\App\Http\Controllers\BuildController::class, 'showShared'])->name('share');
    Route::post('/build/save', [\App\Http\Controllers\BuildController::class, 'save'])->name('save')->middleware(['auth']);
    Route::put('/build/update/{id}', [\App\Http\Controllers\BuildController::class, 'update'])->name('update')->middleware(['auth']);
    Route::delete('/build/delete/{id}', [\App\Http\Controllers\BuildController::class, 'delete'])->name('delete')->middleware(['auth']);
    Route::post('/build/clone/{id}', [\App\Http\Controllers\BuildController::class, 'clone'])->name('clone');
});

// Order Routes
Route::prefix('orders')->name('orders.')->middleware(['auth'])->group(function () {
    Route::get('/', [\App\Http\Controllers\OrderController::class, 'index'])->name('index');
    Route::get('/{id}', [\App\Http\Controllers\OrderController::class, 'show'])->name('show');
    Route::put('/{id}/cancel', [\App\Http\Controllers\OrderController::class, 'cancel'])->name('cancel');
    Route::get('/{id}/track', [\App\Http\Controllers\OrderController::class, 'track'])->name('track');
});

// Order Confirmation (public route for guests)
Route::get('/order/confirmation/{orderNumber}', [\App\Http\Controllers\OrderController::class, 'confirmation'])->name('order.confirmation');

// Account Routes
Route::prefix('account')->name('account.')->middleware(['auth'])->group(function () {
    Route::get('/orders', [\App\Http\Controllers\OrderController::class, 'userOrders'])->name('orders');
    Route::get('/order/{id}', [\App\Http\Controllers\OrderController::class, 'userOrder'])->name('order');
});


// Blog and Comments [public]
Route::controller(BlogPostController::class)->group(function () {
    Route::get('/blog', 'index')->name('blog.index');
    Route::get('/blog/{slug}', 'show')->name('blog.show');
    Route::get('/blog/category/{slug}', 'viewCategory')->name('blog.category');
    Route::get('/blog/tag/{slug}', 'viewTag')->name('blog.tag');
    Route::post('/posts/{post}/tags', 'manageTags')->name('blog.tags.manage');
    Route::get('/api/blog/autocomplete', 'autocomplete')->name('blog.autocomplete');
});

// Blog Comments [user auth]
Route::group(['middleware' => ['auth']], function () {
    Route::post('/posts/{post}/comments', [BlogPostController::class, 'storeComment'])
        ->name('comments.store');
    Route::patch('/comments/{comment}', [BlogPostController::class, 'updateComment'])
        ->name('comments.update');
    Route::delete('/comments/{comment}', [BlogPostController::class, 'deleteComment'])
        ->name('comments.delete');
});

// Payment routes - Requirements: 1.1, 1.3
Route::middleware(['auth'])->group(function () {
    Route::get('/payment/create', [PaymentController::class, 'create'])->name('payment.create');
    Route::post('/payment', [PaymentController::class, 'store'])
        ->middleware(['throttle:10,1']) // Rate limit payment initiation
        ->name('payment.store');
    Route::get('/payment/success/{transaction}', [PaymentController::class, 'success'])->name('payment.success');
    Route::get('/payment/failed/{transaction}', [PaymentController::class, 'failed'])->name('payment.failed');
    Route::get('/payment/status/{transaction}', [PaymentController::class, 'status'])->name('payment.status');
    Route::get('/payment/process/{transaction}', [PaymentController::class, 'process'])->name('payment.process');
    Route::post('/payment/cancel/{transaction}', [PaymentController::class, 'cancel'])->name('payment.cancel');
});

// Payment callback routes (no auth required) - Requirements: 1.3
Route::match(['get', 'post'], '/payment/callback', [PaymentController::class, 'callback'])
    ->middleware(['throttle:60,1']) // Rate limit callbacks
    ->name('payment.callback');
Route::post('/payment/verify', [PaymentController::class, 'verify'])
    ->middleware(['throttle:30,1'])
    ->name('payment.verify');

// PayUmoney specific callback routes - Requirements: 1.3
Route::match(['get', 'post'], '/payment/payumoney/success', [PaymentController::class, 'payumoneySuccess'])
    ->middleware(['throttle:60,1'])
    ->name('payment.payumoney.success');
Route::match(['get', 'post'], '/payment/payumoney/failure', [PaymentController::class, 'payumoneyFailure'])
    ->middleware(['throttle:60,1'])
    ->name('payment.payumoney.failure');
Route::match(['get', 'post'], '/payment/payumoney/cancel', [PaymentController::class, 'payumoneyCancel'])
    ->middleware(['throttle:60,1'])
    ->name('payment.payumoney.cancel');

// Webhook routes with comprehensive security - Requirements: 6.1, 6.2, 6.3, 6.4
Route::middleware([
    'webhook.security',      // Security filter for malicious attempts
    'webhook.rate.limit:60,1', // Rate limiting: 60 requests per minute
    'webhook.signature'      // Signature validation
])->group(function () {
    Route::post('/webhook/razorpay', [WebhookController::class, 'razorpay'])->name('webhook.razorpay');
    Route::post('/webhook/payumoney', [WebhookController::class, 'payumoney'])->name('webhook.payumoney');
    Route::post('/webhook/cashfree', [WebhookController::class, 'cashfree'])->name('webhook.cashfree');
    
    // Generic webhook route for payment gateways
    Route::post('/webhooks/payment/{gateway}', [WebhookController::class, 'handle'])->name('webhooks.payment');
});


require __DIR__ . '/admin.php';