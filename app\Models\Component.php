<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Component extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'slug',
        'description',
        'category_id',
        'brand',
        'model',
        'price',
        'stock',
        'image',
        'specs',
        'is_featured',
        'is_active',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'price' => 'decimal:2',
        'stock' => 'integer',
        'specs' => 'array',
        'is_featured' => 'boolean',
        'is_active' => 'boolean',
    ];

    /**
     * Get the category that owns the component.
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(ComponentCategory::class, 'category_id');
    }

    /**
     * Get the build components for the component.
     */
    public function buildComponents(): Has<PERSON>any
    {
        return $this->hasMany(BuildComponent::class);
    }

    /**
     * Get the compatibility rules for the component.
     */
    public function compatibilityRules(): HasMany
    {
        return $this->hasMany(ComponentCompatibility::class);
    }

    /**
     * Get the cart items for the component.
     */
    public function cartItems(): HasMany
    {
        return $this->hasMany(CartItem::class);
    }

    /**
     * Get the order items for the component.
     */
    public function orderItems(): HasMany
    {
        return $this->hasMany(OrderItem::class);
    }

    /**
     * Get the reviews for the component.
     */
    public function reviews(): HasMany
    {
        return $this->hasMany(Review::class);
    }

    /**
     * Get the price history for the component.
     */
    public function priceHistory(): HasMany
    {
        return $this->hasMany(PriceHistory::class);
    }

    /**
     * Scope a query to only include active components.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope a query to only include featured components.
     */
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    /**
     * Scope a query to only include components in stock.
     */
    public function scopeInStock($query)
    {
        return $query->where('stock', '>', 0);
    }

    /**
     * Check if this component is compatible with another component.
     */
    public function isCompatibleWith(Component $otherComponent): bool
    {
        $compatibilityService = app(\App\Services\CompatibilityService::class);
        
        // Use reflection to access the protected method for direct component pair checking
        $reflection = new \ReflectionClass($compatibilityService);
        $method = $reflection->getMethod('checkComponentPair');
        $method->setAccessible(true);
        
        $result = $method->invoke($compatibilityService, $this, $otherComponent);
        
        return $result['compatible'];
    }

    /**
     * Get compatible components for a specific category.
     */
    public function getCompatibleComponents(string $categorySlug): \Illuminate\Support\Collection
    {
        $compatibilityService = app(\App\Services\CompatibilityService::class);
        
        return $compatibilityService->getCompatibleComponents($this, $categorySlug);
    }

    /**
     * Get all compatibility issues with another component.
     */
    public function getCompatibilityIssues(Component $otherComponent): array
    {
        $compatibilityService = app(\App\Services\CompatibilityService::class);
        $result = $compatibilityService->checkCompatibility([$this, $otherComponent]);
        
        return $result->getIssues();
    }

    /**
     * Get power consumption value as integer.
     */
    public function getPowerConsumption(): int
    {
        $power = $this->specs['power_consumption'] ?? null;
        
        if (!$power) {
            return 0;
        }
        
        return (int) filter_var($power, FILTER_SANITIZE_NUMBER_INT);
    }

    /**
     * Check if component has specific specification.
     */
    public function hasSpec(string $key): bool
    {
        return isset($this->specs[$key]) && !empty($this->specs[$key]);
    }

    /**
     * Get specification value.
     */
    public function getSpec(string $key, $default = null)
    {
        return $this->specs[$key] ?? $default;
    }

    /**
     * Get approved reviews for the component.
     */
    public function approvedReviews(): HasMany
    {
        return $this->reviews()->where('is_approved', true);
    }

    /**
     * Get average rating for the component.
     */
    public function getAverageRatingAttribute(): float
    {
        return Review::getAverageRating($this->id);
    }

    /**
     * Get total review count for the component.
     */
    public function getReviewCountAttribute(): int
    {
        return Review::getReviewCount($this->id);
    }

    /**
     * Get rating distribution for the component.
     */
    public function getRatingDistributionAttribute(): array
    {
        return Review::getRatingDistribution($this->id);
    }

    /**
     * Get comprehensive review statistics.
     */
    public function getReviewStatsAttribute(): array
    {
        return Review::getComponentReviewStats($this->id);
    }

    /**
     * Check if user can review this component.
     */
    public function canBeReviewedBy(User $user): bool
    {
        // Check if user already reviewed this component
        $existingReview = $this->reviews()
            ->where('user_id', $user->id)
            ->exists();

        if ($existingReview) {
            return false;
        }

        // Check if user has purchased this component
        return \Illuminate\Support\Facades\DB::table('order_items')
            ->join('orders', 'order_items.order_id', '=', 'orders.id')
            ->where('orders.user_id', $user->id)
            ->where('order_items.component_id', $this->id)
            ->where('orders.status', 'completed')
            ->exists();
    }
}