<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\ComponentController;
use App\Http\Controllers\Api\BuildController;

Route::get('/user', function (Request $request) {
    return $request->user();
})->middleware('auth:sanctum');

// Public API routes
Route::prefix('v1')->middleware('throttle:60,1')->group(function () {
    // Component routes
    Route::get('/components', [ComponentController::class, 'index']);
    Route::get('/components/{component}', [ComponentController::class, 'show']);
    Route::get('/components/{component}/compatible', [ComponentController::class, 'compatible']);
    Route::get('/categories', [ComponentController::class, 'categories']);
    Route::get('/brands', [ComponentController::class, 'brands']);
    
    // Public build routes
Route::get('/builds', [BuildController::class, 'index']);
Route::get('/builds/{build}', [BuildController::class, 'show']);
Route::get('/builds/shared/{token}', [BuildController::class, 'shared']);
Route::post('/builds/compatibility', [BuildController::class, 'checkCompatibility']);
    
    // Public order tracking
    Route::get('/orders/track/{orderNumber}', [App\Http\Controllers\Api\OrderController::class, 'track']);
});

// Authenticated API routes
Route::prefix('v1')->middleware(['auth:sanctum', 'throttle:120,1'])->group(function () {
    // Build management routes
Route::post('/builds', [BuildController::class, 'store']);
Route::put('/builds/{build}', [BuildController::class, 'update']);
Route::delete('/builds/{build}', [BuildController::class, 'destroy']);
    
    // Cart management routes
    Route::get('/cart', [App\Http\Controllers\Api\CartController::class, 'index']);
    Route::post('/cart', [App\Http\Controllers\Api\CartController::class, 'store']);
    Route::put('/cart/items/{item}', [App\Http\Controllers\Api\CartController::class, 'update']);
    Route::delete('/cart/items/{item}', [App\Http\Controllers\Api\CartController::class, 'destroy']);
    Route::delete('/cart', [App\Http\Controllers\Api\CartController::class, 'clear']);
    Route::get('/cart/summary', [App\Http\Controllers\Api\CartController::class, 'summary']);
    
    // Order management routes
    Route::get('/orders', [App\Http\Controllers\Api\OrderController::class, 'index']);
    Route::post('/orders', [App\Http\Controllers\Api\OrderController::class, 'store']);
    Route::get('/orders/{order}', [App\Http\Controllers\Api\OrderController::class, 'show']);
    Route::put('/orders/{order}/cancel', [App\Http\Controllers\Api\OrderController::class, 'cancel']);
});
