<!-- Payment Gateway Statistics -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
    <!-- Payment Overview Card -->
    <div class="bg-white rounded-lg shadow-md">
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-semibold text-gray-900">Payment Overview</h3>
                <div class="flex items-center space-x-2">
                    <select id="paymentPeriod" onchange="updatePaymentStats()" class="text-sm border-gray-300 rounded-md">
                        <option value="today">Today</option>
                        <option value="week">This Week</option>
                        <option value="month" selected>This Month</option>
                        <option value="year">This Year</option>
                    </select>
                </div>
            </div>
        </div>
        
        <div class="p-6">
            <div class="grid grid-cols-2 gap-4">
                <div class="text-center">
                    <div class="text-3xl font-bold text-green-600" id="totalRevenue">
                        {{ $paymentStats['currency'] ?? 'INR' }} {{ number_format($paymentStats['total_revenue'] ?? 0, 2) }}
                    </div>
                    <div class="text-sm text-gray-500">Total Revenue</div>
                    <div class="text-xs text-green-600 mt-1" id="revenueChange">
                        +{{ number_format($paymentStats['revenue_change'] ?? 0, 1) }}% from last period
                    </div>
                </div>
                
                <div class="text-center">
                    <div class="text-3xl font-bold text-blue-600" id="totalTransactions">
                        {{ number_format($paymentStats['total_transactions'] ?? 0) }}
                    </div>
                    <div class="text-sm text-gray-500">Total Transactions</div>
                    <div class="text-xs text-blue-600 mt-1" id="transactionChange">
                        +{{ number_format($paymentStats['transaction_change'] ?? 0, 1) }}% from last period
                    </div>
                </div>
            </div>
            
            <div class="mt-6 pt-6 border-t border-gray-200">
                <div class="flex justify-between items-center mb-4">
                    <span class="text-sm font-medium text-gray-700">Success Rate</span>
                    <span class="text-sm font-bold text-green-600" id="successRate">
                        {{ number_format($paymentStats['success_rate'] ?? 0, 1) }}%
                    </span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                    <div class="bg-green-600 h-2 rounded-full transition-all duration-300" 
                         style="width: {{ $paymentStats['success_rate'] ?? 0 }}%" id="successRateBar"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Gateway Performance Card -->
    <div class="bg-white rounded-lg shadow-md">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">Gateway Performance</h3>
        </div>
        
        <div class="p-6">
            <div class="space-y-4" id="gatewayPerformance">
                @foreach($gatewayStats ?? [] as $gateway => $stats)
                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div class="flex items-center">
                            <div class="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center mr-3">
                                @if($gateway === 'razorpay')
                                    <img src="{{ asset('images/gateways/razorpay-logo.svg') }}" alt="Razorpay" class="w-6 h-6">
                                @elseif($gateway === 'payumoney')
                                    <img src="{{ asset('images/gateways/payumoney-logo.svg') }}" alt="PayUmoney" class="w-6 h-6">
                                @elseif($gateway === 'cashfree')
                                    <img src="{{ asset('images/gateways/cashfree-logo.svg') }}" alt="Cashfree" class="w-6 h-6">
                                @else
                                    <svg class="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                                    </svg>
                                @endif
                            </div>
                            <div>
                                <div class="font-medium text-gray-900">{{ ucfirst($gateway) }}</div>
                                <div class="text-sm text-gray-500">{{ $stats['transactions'] }} transactions</div>
                            </div>
                        </div>
                        
                        <div class="text-right">
                            <div class="font-medium text-gray-900">{{ number_format($stats['success_rate'], 1) }}%</div>
                            <div class="text-sm text-gray-500">{{ $stats['currency'] }} {{ number_format($stats['revenue'], 2) }}</div>
                        </div>
                    </div>
                @endforeach
            </div>
            
            @if(empty($gatewayStats))
                <div class="text-center py-8">
                    <svg class="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                    <p class="text-gray-500">No payment data available</p>
                </div>
            @endif
        </div>
    </div>
</div>

<!-- Recent Transactions and Charts -->
<div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
    <!-- Recent Transactions -->
    <div class="lg:col-span-2 bg-white rounded-lg shadow-md">
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-semibold text-gray-900">Recent Transactions</h3>
                <a href="{{ route('admin.transactions.index') }}" class="text-indigo-600 hover:text-indigo-800 text-sm font-medium">
                    View All
                </a>
            </div>
        </div>
        
        <div class="p-6">
            <div class="space-y-4" id="recentTransactions">
                @forelse($recentTransactions ?? [] as $transaction)
                    <div class="flex items-center justify-between p-3 hover:bg-gray-50 rounded-lg transition-colors">
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-gray-100 rounded flex items-center justify-center mr-3">
                                @if($transaction->gateway_name === 'razorpay')
                                    <img src="{{ asset('images/gateways/razorpay-logo.svg') }}" alt="Razorpay" class="w-5 h-5">
                                @elseif($transaction->gateway_name === 'payumoney')
                                    <img src="{{ asset('images/gateways/payumoney-logo.svg') }}" alt="PayUmoney" class="w-5 h-5">
                                @elseif($transaction->gateway_name === 'cashfree')
                                    <img src="{{ asset('images/gateways/cashfree-logo.svg') }}" alt="Cashfree" class="w-5 h-5">
                                @else
                                    <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                                    </svg>
                                @endif
                            </div>
                            <div>
                                <div class="font-medium text-gray-900">{{ $transaction->user->name ?? 'Unknown' }}</div>
                                <div class="text-sm text-gray-500">{{ $transaction->transaction_id }}</div>
                            </div>
                        </div>
                        
                        <div class="text-right">
                            <div class="font-medium text-gray-900">{{ $transaction->currency }} {{ number_format($transaction->amount, 2) }}</div>
                            <div class="text-sm">
                                <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium
                                    @switch($transaction->status)
                                        @case('completed')
                                            bg-green-100 text-green-800
                                            @break
                                        @case('failed')
                                            bg-red-100 text-red-800
                                            @break
                                        @case('pending')
                                            bg-yellow-100 text-yellow-800
                                            @break
                                        @case('processing')
                                            bg-blue-100 text-blue-800
                                            @break
                                        @default
                                            bg-gray-100 text-gray-800
                                    @endswitch
                                ">
                                    {{ ucfirst($transaction->status) }}
                                </span>
                            </div>
                        </div>
                    </div>
                @empty
                    <div class="text-center py-8">
                        <svg class="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        <p class="text-gray-500">No recent transactions</p>
                    </div>
                @endforelse
            </div>
        </div>
    </div>

    <!-- Payment Status Distribution -->
    <div class="bg-white rounded-lg shadow-md">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">Status Distribution</h3>
        </div>
        
        <div class="p-6">
            <div class="space-y-4" id="statusDistribution">
                @foreach($statusStats ?? [] as $status => $count)
                    @php
                        $total = array_sum($statusStats ?? []);
                        $percentage = $total > 0 ? ($count / $total) * 100 : 0;
                        $colorClass = match($status) {
                            'completed' => 'bg-green-500',
                            'failed' => 'bg-red-500',
                            'pending' => 'bg-yellow-500',
                            'processing' => 'bg-blue-500',
                            'cancelled' => 'bg-gray-500',
                            default => 'bg-gray-400'
                        };
                    @endphp
                    
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-3 h-3 {{ $colorClass }} rounded-full mr-3"></div>
                            <span class="text-sm font-medium text-gray-700">{{ ucfirst($status) }}</span>
                        </div>
                        <div class="text-right">
                            <div class="text-sm font-medium text-gray-900">{{ $count }}</div>
                            <div class="text-xs text-gray-500">{{ number_format($percentage, 1) }}%</div>
                        </div>
                    </div>
                @endforeach
            </div>
            
            @if(empty($statusStats))
                <div class="text-center py-8">
                    <svg class="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 3.055A9.001 9.001 0 1020.945 13H11V3.055z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.488 9H15V3.512A9.025 9.025 0 0120.488 9z"></path>
                    </svg>
                    <p class="text-gray-500">No status data available</p>
                </div>
            @endif
        </div>
    </div>
</div>

<!-- Payment Trends Chart -->
<div class="bg-white rounded-lg shadow-md mb-8">
    <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-900">Payment Trends</h3>
            <div class="flex items-center space-x-2">
                <select id="chartPeriod" onchange="updatePaymentChart()" class="text-sm border-gray-300 rounded-md">
                    <option value="7days">Last 7 Days</option>
                    <option value="30days" selected>Last 30 Days</option>
                    <option value="90days">Last 90 Days</option>
                    <option value="1year">Last Year</option>
                </select>
            </div>
        </div>
    </div>
    
    <div class="p-6">
        <div class="h-64 flex items-center justify-center" id="paymentChart">
            <div class="text-center">
                <svg class="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                </svg>
                <p class="text-gray-500">Chart will be rendered here</p>
                <p class="text-sm text-gray-400 mt-1">Integrate with Chart.js or similar library</p>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="bg-white rounded-lg shadow-md">
    <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-semibold text-gray-900">Quick Actions</h3>
    </div>
    
    <div class="p-6">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <a href="{{ route('admin.gateways.index') }}" 
               class="flex items-center p-4 bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors group">
                <div class="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center mr-3 group-hover:bg-blue-600 transition-colors">
                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    </svg>
                </div>
                <div>
                    <div class="font-medium text-gray-900">Gateway Settings</div>
                    <div class="text-sm text-gray-500">Configure payment gateways</div>
                </div>
            </a>

            <a href="{{ route('admin.transactions.index') }}" 
               class="flex items-center p-4 bg-green-50 hover:bg-green-100 rounded-lg transition-colors group">
                <div class="w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center mr-3 group-hover:bg-green-600 transition-colors">
                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                </div>
                <div>
                    <div class="font-medium text-gray-900">View Transactions</div>
                    <div class="text-sm text-gray-500">Monitor all payments</div>
                </div>
            </a>

            <button onclick="exportPaymentReport()" 
                    class="flex items-center p-4 bg-purple-50 hover:bg-purple-100 rounded-lg transition-colors group">
                <div class="w-10 h-10 bg-purple-500 rounded-lg flex items-center justify-center mr-3 group-hover:bg-purple-600 transition-colors">
                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                </div>
                <div>
                    <div class="font-medium text-gray-900">Export Report</div>
                    <div class="text-sm text-gray-500">Download payment data</div>
                </div>
            </button>

            <button onclick="testAllGateways()" 
                    class="flex items-center p-4 bg-yellow-50 hover:bg-yellow-100 rounded-lg transition-colors group">
                <div class="w-10 h-10 bg-yellow-500 rounded-lg flex items-center justify-center mr-3 group-hover:bg-yellow-600 transition-colors">
                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div>
                    <div class="font-medium text-gray-900">Test Gateways</div>
                    <div class="text-sm text-gray-500">Verify configurations</div>
                </div>
            </button>
        </div>
    </div>
</div>

<script>
// Auto-refresh data every 30 seconds
setInterval(function() {
    updatePaymentStats();
    updateRecentTransactions();
}, 30000);

async function updatePaymentStats() {
    const period = document.getElementById('paymentPeriod').value;
    
    try {
        const response = await fetch(`/admin/api/payment-stats?period=${period}`, {
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            }
        });
        
        if (response.ok) {
            const data = await response.json();
            
            // Update payment overview
            document.getElementById('totalRevenue').textContent = `${data.currency} ${data.total_revenue.toLocaleString()}`;
            document.getElementById('totalTransactions').textContent = data.total_transactions.toLocaleString();
            document.getElementById('successRate').textContent = `${data.success_rate.toFixed(1)}%`;
            document.getElementById('successRateBar').style.width = `${data.success_rate}%`;
            document.getElementById('revenueChange').textContent = `${data.revenue_change >= 0 ? '+' : ''}${data.revenue_change.toFixed(1)}% from last period`;
            document.getElementById('transactionChange').textContent = `${data.transaction_change >= 0 ? '+' : ''}${data.transaction_change.toFixed(1)}% from last period`;
        }
    } catch (error) {
        console.error('Failed to update payment stats:', error);
    }
}

async function updateRecentTransactions() {
    try {
        const response = await fetch('/admin/api/recent-transactions', {
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            }
        });
        
        if (response.ok) {
            const html = await response.text();
            document.getElementById('recentTransactions').innerHTML = html;
        }
    } catch (error) {
        console.error('Failed to update recent transactions:', error);
    }
}

async function updatePaymentChart() {
    const period = document.getElementById('chartPeriod').value;
    
    try {
        const response = await fetch(`/admin/api/payment-chart?period=${period}`, {
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            }
        });
        
        if (response.ok) {
            const data = await response.json();
            // Implement chart rendering with Chart.js or similar library
            console.log('Chart data:', data);
        }
    } catch (error) {
        console.error('Failed to update payment chart:', error);
    }
}

async function exportPaymentReport() {
    try {
        const response = await fetch('/admin/api/export-payment-report', {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            }
        });
        
        if (response.ok) {
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.style.display = 'none';
            a.href = url;
            a.download = `payment-report-${new Date().toISOString().split('T')[0]}.csv`;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
        } else {
            alert('Failed to export payment report');
        }
    } catch (error) {
        alert('Error: ' + error.message);
    }
}

async function testAllGateways() {
    const gateways = ['razorpay', 'payumoney', 'cashfree'];
    const results = [];
    
    for (const gateway of gateways) {
        try {
            const response = await fetch(`/admin/gateways/${gateway}/test`, {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                }
            });
            
            const data = await response.json();
            results.push(`${gateway}: ${data.success ? '✅ Passed' : '❌ Failed'}`);
        } catch (error) {
            results.push(`${gateway}: ❌ Error`);
        }
    }
    
    alert('Gateway Test Results:\n\n' + results.join('\n'));
}
</script>