<?php

namespace Tests\Performance;

use App\Models\Cart;
use App\Models\CartItem;
use App\Models\Component;
use App\Models\ComponentCategory;
use App\Models\User;
use App\Services\CartService;
use App\Services\CheckoutService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\DB;
use Tests\TestCase;

class CartCheckoutStressTest extends TestCase
{
    use RefreshDatabase;

    protected CartService $cartService;
    protected CheckoutService $checkoutService;
    protected array $users;
    protected array $components;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->cartService = app(CartService::class);
        $this->checkoutService = app(CheckoutService::class);
        
        // Create test data
        $this->createStressTestData();
    }

    /** @test */
    public function cart_handles_rapid_item_additions()
    {
        $user = $this->users[0];
        $startTime = microtime(true);
        $additionTimes = [];
        
        // Rapidly add 200 items to cart
        for ($i = 0; $i < 200; $i++) {
            $component = $this->components[array_rand($this->components)];
            $quantity = rand(1, 5);
            
            $itemStartTime = microtime(true);
            
            $this->cartService->addToCart($component, $quantity, $user);
            
            $itemEndTime = microtime(true);
            $itemTime = ($itemEndTime - $itemStartTime) * 1000;
            $additionTimes[] = $itemTime;
            
            // Each addition should complete quickly even under stress
            $this->assertLessThan(100, $itemTime, 
                "Cart addition {$i} took {$itemTime}ms, which exceeds 100ms limit");
        }
        
        $endTime = microtime(true);
        $totalTime = ($endTime - $startTime) * 1000;
        $averageTime = array_sum($additionTimes) / count($additionTimes);
        
        // Total time for 200 additions should be reasonable
        $this->assertLessThan(10000, $totalTime, 
            "200 cart additions took {$totalTime}ms, which exceeds 10000ms limit");
        
        // Average time should remain consistent
        $this->assertLessThan(50, $averageTime, 
            "Average cart addition time was {$averageTime}ms, which exceeds 50ms limit");
        
        // Verify cart integrity
        $cart = $user->cart;
        $this->assertNotNull($cart);
        $this->assertGreaterThan(0, $cart->items()->count());
    }

    /** @test */
    public function cart_handles_concurrent_modifications()
    {
        $user = $this->users[0];
        $cart = Cart::factory()->create(['user_id' => $user->id]);
        
        // Pre-populate cart with items
        for ($i = 0; $i < 50; $i++) {
            CartItem::factory()->create([
                'cart_id' => $cart->id,
                'component_id' => $this->components[array_rand($this->components)]->id,
                'quantity' => rand(1, 5),
                'price' => rand(50, 500)
            ]);
        }
        
        $startTime = microtime(true);
        $operationTimes = [];
        
        // Simulate concurrent cart modifications
        for ($i = 0; $i < 100; $i++) {
            $operationStartTime = microtime(true);
            
            switch ($i % 4) {
                case 0:
                    // Add new item
                    $component = $this->components[array_rand($this->components)];
                    $this->cartService->addToCart($component, 1, $user);
                    break;
                    
                case 1:
                    // Update existing item quantity
                    $cartItem = $cart->items()->inRandomOrder()->first();
                    if ($cartItem) {
                        $cartItem->update(['quantity' => rand(1, 10)]);
                    }
                    break;
                    
                case 2:
                    // Remove item
                    $cartItem = $cart->items()->inRandomOrder()->first();
                    if ($cartItem) {
                        $cartItem->delete();
                    }
                    break;
                    
                case 3:
                    // Calculate total
                    $this->cartService->getCartTotal($user);
                    break;
            }
            
            $operationEndTime = microtime(true);
            $operationTime = ($operationEndTime - $operationStartTime) * 1000;
            $operationTimes[] = $operationTime;
        }
        
        $endTime = microtime(true);
        $totalTime = ($endTime - $startTime) * 1000;
        $averageOperationTime = array_sum($operationTimes) / count($operationTimes);
        
        // All operations should complete under 5 seconds
        $this->assertLessThan(5000, $totalTime, 
            "100 concurrent cart operations took {$totalTime}ms, which exceeds 5000ms limit");
        
        // Average operation time should be reasonable
        $this->assertLessThan(50, $averageOperationTime, 
            "Average cart operation time was {$averageOperationTime}ms, which exceeds 50ms limit");
    }

    /** @test */
    public function checkout_handles_high_volume_orders()
    {
        $checkoutTimes = [];
        $startTime = microtime(true);
        
        // Process 100 orders rapidly
        for ($i = 0; $i < 100; $i++) {
            $user = $this->users[array_rand($this->users)];
            $cart = Cart::factory()->create(['user_id' => $user->id]);
            
            // Add random items to cart
            $itemCount = rand(1, 10);
            for ($j = 0; $j < $itemCount; $j++) {
                CartItem::factory()->create([
                    'cart_id' => $cart->id,
                    'component_id' => $this->components[array_rand($this->components)]->id,
                    'quantity' => rand(1, 3),
                    'price' => rand(50, 1000)
                ]);
            }
            
            $checkoutStartTime = microtime(true);
            
            try {
                $order = $this->checkoutService->processCheckout($cart, [
                    'name' => $user->name,
                    'email' => $user->email,
                    'phone' => '555-1234',
                    'address' => '123 Main St',
                    'city' => 'Anytown',
                    'state' => 'CA',
                    'zipcode' => '12345',
                    'country' => 'US'
                ], [
                    'payment_method' => 'credit_card',
                    'card_number' => '****************',
                    'card_expiry' => '12/25',
                    'card_cvv' => '123',
                    'card_name' => $user->name
                ]);
                
                $this->assertNotNull($order);
            } catch (\Exception $e) {
                $this->fail("Checkout {$i} failed: " . $e->getMessage());
            }
            
            $checkoutEndTime = microtime(true);
            $checkoutTime = ($checkoutEndTime - $checkoutStartTime) * 1000;
            $checkoutTimes[] = $checkoutTime;
            
            // Individual checkout should complete under 2 seconds
            $this->assertLessThan(2000, $checkoutTime, 
                "Checkout {$i} took {$checkoutTime}ms, which exceeds 2000ms limit");
        }
        
        $endTime = microtime(true);
        $totalTime = ($endTime - $startTime) * 1000;
        $averageCheckoutTime = array_sum($checkoutTimes) / count($checkoutTimes);
        
        // All 100 checkouts should complete under 60 seconds
        $this->assertLessThan(60000, $totalTime, 
            "100 checkouts took {$totalTime}ms, which exceeds 60000ms limit");
        
        // Average checkout time should be reasonable
        $this->assertLessThan(1000, $averageCheckoutTime, 
            "Average checkout time was {$averageCheckoutTime}ms, which exceeds 1000ms limit");
    }

    /** @test */
    public function cart_total_calculation_scales_with_large_carts()
    {
        $user = $this->users[0];
        $cart = Cart::factory()->create(['user_id' => $user->id]);
        
        $cartSizes = [10, 50, 100, 200, 500];
        
        foreach ($cartSizes as $size) {
            // Clear previous items
            $cart->items()->delete();
            
            // Add items to reach target size
            for ($i = 0; $i < $size; $i++) {
                CartItem::factory()->create([
                    'cart_id' => $cart->id,
                    'component_id' => $this->components[array_rand($this->components)]->id,
                    'quantity' => rand(1, 5),
                    'price' => rand(50, 1000) + (rand(0, 99) / 100)
                ]);
            }
            
            // Measure calculation time
            $calculationTimes = [];
            for ($j = 0; $j < 10; $j++) {
                $startTime = microtime(true);
                $total = $this->cartService->getCartTotal($user);
                $endTime = microtime(true);
                
                $calculationTime = ($endTime - $startTime) * 1000;
                $calculationTimes[] = $calculationTime;
                
                $this->assertGreaterThan(0, $total);
            }
            
            $averageCalculationTime = array_sum($calculationTimes) / count($calculationTimes);
            
            // Calculation time should scale reasonably
            $maxExpectedTime = min(200, $size * 0.5); // Max 200ms or 0.5ms per item
            $this->assertLessThan($maxExpectedTime, $averageCalculationTime, 
                "Cart total calculation for {$size} items took {$averageCalculationTime}ms, which exceeds {$maxExpectedTime}ms limit");
        }
    }

    /** @test */
    public function cart_session_handling_under_stress()
    {
        $sessionTimes = [];
        $startTime = microtime(true);
        
        // Simulate 200 guest cart operations
        for ($i = 0; $i < 200; $i++) {
            $sessionStartTime = microtime(true);
            
            // Simulate guest cart operations
            $sessionId = 'guest_session_' . $i;
            
            // Add items to guest cart
            for ($j = 0; $j < rand(1, 5); $j++) {
                $component = $this->components[array_rand($this->components)];
                
                $response = $this->withSession(['cart_session_id' => $sessionId])
                              ->post(route('cart.add'), [
                                  'component_id' => $component->id,
                                  'quantity' => 1
                              ]);
                
                $response->assertStatus(200);
            }
            
            // Get cart total
            $response = $this->withSession(['cart_session_id' => $sessionId])
                          ->get(route('cart.total'));
            
            $response->assertStatus(200);
            
            $sessionEndTime = microtime(true);
            $sessionTime = ($sessionEndTime - $sessionStartTime) * 1000;
            $sessionTimes[] = $sessionTime;
        }
        
        $endTime = microtime(true);
        $totalTime = ($endTime - $startTime) * 1000;
        $averageSessionTime = array_sum($sessionTimes) / count($sessionTimes);
        
        // All guest sessions should complete under 30 seconds
        $this->assertLessThan(30000, $totalTime, 
            "200 guest cart sessions took {$totalTime}ms, which exceeds 30000ms limit");
        
        // Average session time should be reasonable
        $this->assertLessThan(150, $averageSessionTime, 
            "Average guest session time was {$averageSessionTime}ms, which exceeds 150ms limit");
    }

    /** @test */
    public function inventory_updates_handle_concurrent_purchases()
    {
        // Select components with limited stock for stress testing
        $limitedStockComponents = collect($this->components)->take(10);
        
        foreach ($limitedStockComponents as $component) {
            $component->update(['stock' => 50]); // Set initial stock
        }
        
        $startTime = microtime(true);
        $purchaseResults = [];
        
        // Simulate 100 concurrent purchases
        for ($i = 0; $i < 100; $i++) {
            $user = $this->users[array_rand($this->users)];
            $component = $limitedStockComponents->random();
            $quantity = rand(1, 3);
            
            $purchaseStartTime = microtime(true);
            
            try {
                // Simulate purchase process
                DB::transaction(function() use ($component, $quantity, $user) {
                    // Check stock
                    $currentStock = $component->fresh()->stock;
                    if ($currentStock >= $quantity) {
                        // Decrement stock
                        $component->decrement('stock', $quantity);
                        
                        // Create cart and order simulation
                        $cart = Cart::factory()->create(['user_id' => $user->id]);
                        CartItem::factory()->create([
                            'cart_id' => $cart->id,
                            'component_id' => $component->id,
                            'quantity' => $quantity,
                            'price' => $component->price
                        ]);
                        
                        return true;
                    }
                    
                    return false;
                });
                
                $purchaseResults[] = 'success';
            } catch (\Exception $e) {
                $purchaseResults[] = 'failed';
            }
            
            $purchaseEndTime = microtime(true);
            $purchaseTime = ($purchaseEndTime - $purchaseStartTime) * 1000;
            
            // Each purchase should complete quickly
            $this->assertLessThan(500, $purchaseTime, 
                "Purchase {$i} took {$purchaseTime}ms, which exceeds 500ms limit");
        }
        
        $endTime = microtime(true);
        $totalTime = ($endTime - $startTime) * 1000;
        
        // All purchases should complete under 20 seconds
        $this->assertLessThan(20000, $totalTime, 
            "100 concurrent purchases took {$totalTime}ms, which exceeds 20000ms limit");
        
        // Most purchases should succeed (allowing for some stock conflicts)
        $successCount = count(array_filter($purchaseResults, fn($result) => $result === 'success'));
        $this->assertGreaterThan(70, $successCount, 
            "Only {$successCount} out of 100 purchases succeeded, expected at least 70");
        
        // Verify stock integrity
        foreach ($limitedStockComponents as $component) {
            $finalStock = $component->fresh()->stock;
            $this->assertGreaterThanOrEqual(0, $finalStock, 
                "Component {$component->id} has negative stock: {$finalStock}");
        }
    }

    /** @test */
    public function payment_processing_handles_high_load()
    {
        $paymentTimes = [];
        $paymentResults = [];
        $startTime = microtime(true);
        
        // Process 50 payments rapidly
        for ($i = 0; $i < 50; $i++) {
            $user = $this->users[array_rand($this->users)];
            
            $paymentStartTime = microtime(true);
            
            try {
                // Simulate payment processing
                $response = $this->actingAs($user)
                              ->post(route('checkout.payment'), [
                                  'payment_method' => 'credit_card',
                                  'card_number' => '****************',
                                  'card_expiry' => '12/25',
                                  'card_cvv' => '123',
                                  'card_name' => $user->name,
                                  'amount' => rand(100, 1000)
                              ]);
                
                $paymentResults[] = $response->status() === 200 ? 'success' : 'failed';
            } catch (\Exception $e) {
                $paymentResults[] = 'failed';
            }
            
            $paymentEndTime = microtime(true);
            $paymentTime = ($paymentEndTime - $paymentStartTime) * 1000;
            $paymentTimes[] = $paymentTime;
        }
        
        $endTime = microtime(true);
        $totalTime = ($endTime - $startTime) * 1000;
        $averagePaymentTime = array_sum($paymentTimes) / count($paymentTimes);
        
        // All payments should complete under 30 seconds
        $this->assertLessThan(30000, $totalTime, 
            "50 payments took {$totalTime}ms, which exceeds 30000ms limit");
        
        // Average payment time should be reasonable
        $this->assertLessThan(600, $averagePaymentTime, 
            "Average payment time was {$averagePaymentTime}ms, which exceeds 600ms limit");
        
        // Most payments should succeed
        $successCount = count(array_filter($paymentResults, fn($result) => $result === 'success'));
        $this->assertGreaterThan(40, $successCount, 
            "Only {$successCount} out of 50 payments succeeded, expected at least 40");
    }

    /** @test */
    public function memory_usage_remains_stable_during_cart_operations()
    {
        $initialMemory = memory_get_usage();
        $peakMemory = $initialMemory;
        $user = $this->users[0];
        
        // Perform 1000 cart operations
        for ($i = 0; $i < 1000; $i++) {
            switch ($i % 5) {
                case 0:
                    // Add item
                    $component = $this->components[array_rand($this->components)];
                    $this->cartService->addToCart($component, 1, $user);
                    break;
                    
                case 1:
                    // Calculate total
                    $this->cartService->getCartTotal($user);
                    break;
                    
                case 2:
                    // Get cart items
                    $user->cart?->items()->get();
                    break;
                    
                case 3:
                    // Update cart item
                    $cartItem = $user->cart?->items()->inRandomOrder()->first();
                    if ($cartItem) {
                        $cartItem->update(['quantity' => rand(1, 5)]);
                    }
                    break;
                    
                case 4:
                    // Remove cart item
                    $cartItem = $user->cart?->items()->inRandomOrder()->first();
                    if ($cartItem && $user->cart->items()->count() > 10) {
                        $cartItem->delete();
                    }
                    break;
            }
            
            $currentMemory = memory_get_usage();
            $peakMemory = max($peakMemory, $currentMemory);
            
            // Force garbage collection every 100 operations
            if ($i % 100 === 0) {
                gc_collect_cycles();
            }
        }
        
        $finalMemory = memory_get_usage();
        $memoryIncrease = ($finalMemory - $initialMemory) / 1024 / 1024; // MB
        $peakIncrease = ($peakMemory - $initialMemory) / 1024 / 1024; // MB
        
        // Memory increase should be minimal
        $this->assertLessThan(50, $memoryIncrease, 
            "Final memory increase was {$memoryIncrease}MB, which exceeds 50MB limit");
        
        // Peak memory should be reasonable
        $this->assertLessThan(200, $peakIncrease, 
            "Peak memory increase was {$peakIncrease}MB, which exceeds 200MB limit");
    }

    protected function createStressTestData(): void
    {
        // Create categories
        $categories = ComponentCategory::factory()->count(10)->create();
        
        // Create users for stress testing
        $this->users = User::factory()->count(200)->create();
        
        // Create components for stress testing
        $this->components = Component::factory()->count(2000)->create([
            'category_id' => function() use ($categories) {
                return $categories->random()->id;
            },
            'is_active' => true,
            'stock' => rand(10, 100),
            'price' => rand(50, 2000) + (rand(0, 99) / 100),
        ]);
    }
}