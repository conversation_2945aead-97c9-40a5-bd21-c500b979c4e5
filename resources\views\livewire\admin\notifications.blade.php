<div wire:poll.10s="loadNotifications">
    <h2 class="text-xl font-bold mb-4">Admin Notifications</h2>
    @if (empty($notifications))
        <div class="text-gray-500">No notifications at this time.</div>
    @else
        <ul class="divide-y divide-gray-200">
            @foreach ($notifications as $note)
                <li class="py-3 flex items-center">
                    <span class="mr-3">
                        @if ($note['type'] === 'order')
                            <svg class="w-5 h-5 text-blue-500" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" d="M3 7h18M3 12h18M3 17h18" /></svg>
                        @elseif ($note['type'] === 'failed_job')
                            <svg class="w-5 h-5 text-red-500" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" /></svg>
                        @elseif ($note['type'] === 'low_stock')
                            <svg class="w-5 h-5 text-yellow-500" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" d="M12 8v4m0 4h.01M21 12A9 9 0 113 12a9 9 0 0118 0z" /></svg>
                        @elseif ($note['type'] === 'new_user')
                            <svg class="w-5 h-5 text-green-500" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" d="M5.121 17.804A13.937 13.937 0 0112 15c2.5 0 4.847.655 6.879 1.804M15 11a3 3 0 11-6 0 3 3 0 016 0z" /></svg>
                        @endif
                    </span>
                    <span class="flex-1">{{ $note['message'] }}</span>
                    <span class="text-xs text-gray-400 ml-2">{{ \Carbon\Carbon::parse($note['date'])->diffForHumans() }}</span>
                    <button wire:click="dismiss('{{ $note['id'] }}')" class="ml-4 text-xs text-gray-500 hover:text-red-600">Dismiss</button>
                </li>
            @endforeach
        </ul>
    @endif
</div> 