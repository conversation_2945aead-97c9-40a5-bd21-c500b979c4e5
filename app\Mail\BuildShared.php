<?php

namespace App\Mail;

use App\Models\Build;
use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class BuildShared extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    /**
     * Create a new message instance.
     */
    public function __construct(
        public Build $build,
        public User $sharedBy,
        public string $recipientEmail,
        public ?string $message = null
    ) {
        //
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: $this->sharedBy->name . ' shared a PC build with you: ' . $this->build->name,
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'emails.build-shared',
            with: [
                'build' => $this->build,
                'sharedBy' => $this->sharedBy,
                'components' => $this->build->components()->with('component')->get(),
                'message' => $this->message,
                'buildUrl' => route('builder.share', ['id' => $this->build->id, 'token' => $this->build->share_token]),
            ],
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}