<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\BlogPostTag;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class BlogTagController extends Controller
{
    /**
     * Display a listing of the blog tags.
     */
    public function index()
    {
        $tags = BlogPostTag::withCount('posts')
            ->orderBy('name')
            ->paginate(20);

        return view('admin.blog.tags.index', compact('tags'));
    }

    /**
     * Show the form for creating a new blog tag.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        return view('admin.blog.tags.create');
    }

    /**
     * Store a newly created blog tag in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255|unique:blog_post_tags,name',
            'description' => 'nullable|string',
        ]);

        BlogPostTag::create([
            'name' => $request->name,
            'description' => $request->description,
        ]);

        return redirect()->route('admin.blog.tags.index')
            ->with('success', 'Blog tag created successfully.');
    }

    /**
     * Show the form for editing the specified blog tag.
     *
     * @param  \App\Models\BlogPostTag  $tag
     * @return \Illuminate\Http\Response
     */
    public function edit(BlogPostTag $tag)
    {
        return view('admin.blog.tags.edit', compact('tag'));
    }

    /**
     * Update the specified blog tag in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\BlogPostTag  $tag
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, BlogPostTag $tag)
    {
        $request->validate([
            'name' => 'required|string|max:255|unique:blog_post_tags,name,' . $tag->id,
            'description' => 'nullable|string',
        ]);

        $tag->update([
            'name' => $request->name,
            'description' => $request->description,
        ]);

        return redirect()->route('admin.blog.tags.index')
            ->with('success', 'Blog tag updated successfully.');
    }

    /**
     * Remove the specified blog tag from storage.
     *
     * @param  \App\Models\BlogPostTag  $tag
     * @return \Illuminate\Http\Response
     */
    public function destroy(BlogPostTag $tag)
    {
        // Check if tag has posts
        if ($tag->posts()->count() > 0) {
            return redirect()->route('admin.blog.tags.index')
                ->with('error', 'Cannot delete tag with associated posts.');
        }

        $tag->delete();

        return redirect()->route('admin.blog.tags.index')
            ->with('success', 'Blog tag deleted successfully.');
    }

    /**
     * API endpoint to get all tags (for select2 or similar)
     *
     * @return \Illuminate\Http\Response
     */
    public function apiIndex(Request $request)
    {
        $search = $request->get('search');
        $tags = BlogPostTag::when($search, function ($query) use ($search) {
                return $query->where('name', 'like', "%{$search}%");
            })
            ->orderBy('name')
            ->get(['id', 'name']);

        return response()->json($tags);
    }
}