<?php

namespace Tests\Feature\Integration;

use Tests\TestCase;
use App\Models\User;
use App\Models\Transaction;
use App\Models\GatewaySetting;
use App\Services\Payment\Gateways\RazorpayService;
use App\Services\Payment\Gateways\PayUmoneyService;
use App\Services\Payment\Gateways\CashfreeService;
use App\Services\PaymentGatewayFactory;
use App\Services\Payment\Exceptions\GatewayConfigurationException;
use App\Services\Payment\Exceptions\InvalidPaymentDataException;
use App\Services\Payment\Exceptions\PaymentGatewayException;
use App\Services\Payment\Exceptions\WebhookVerificationException;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Mockery;
use Razorpay\Api\Api;
use Razorpay\Api\Errors\BadRequestError;
use Razorpay\Api\Errors\ServerError;

class PaymentGatewayIntegrationTest extends TestCase
{
    use DatabaseTransactions;

    protected User $user;
    protected array $gatewaySettings;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test user
        $this->user = User::factory()->create([
            'email' => '<EMAIL>'
        ]);

        // Create gateway settings for all three gateways
        $this->gatewaySettings = [
            GatewaySetting::GATEWAY_RAZORPAY => GatewaySetting::create([
                'gateway_name' => GatewaySetting::GATEWAY_RAZORPAY,
                'is_enabled' => true,
                'is_test_mode' => true,
                'settings' => [
                    'key_id' => 'rzp_test_1234567890',
                    'key_secret' => 'test_secret_key',
                    'webhook_secret' => 'test_webhook_secret'
                ]
            ]),
            GatewaySetting::GATEWAY_PAYUMONEY => GatewaySetting::create([
                'gateway_name' => GatewaySetting::GATEWAY_PAYUMONEY,
                'is_enabled' => true,
                'is_test_mode' => true,
                'settings' => [
                    'merchant_key' => 'test_merchant_key',
                    'salt' => 'test_salt_key',
                    'auth_header' => 'test_auth_header'
                ]
            ]),
            GatewaySetting::GATEWAY_CASHFREE => GatewaySetting::create([
                'gateway_name' => GatewaySetting::GATEWAY_CASHFREE,
                'is_enabled' => true,
                'is_test_mode' => true,
                'settings' => [
                    'app_id' => 'test_app_id',
                    'secret_key' => 'test_secret_key',
                    'client_id' => 'test_client_id',
                    'client_secret' => 'test_client_secret'
                ]
            ])
        ];
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /**
     * Test complete payment flow for Razorpay
     */
    public function test_razorpay_complete_payment_flow()
    {
        // Create transaction
        $transaction = Transaction::create([
            'user_id' => $this->user->id,
            'gateway_name' => GatewaySetting::GATEWAY_RAZORPAY,
            'amount' => 100.00,
            'currency' => 'INR',
            'status' => Transaction::STATUS_PENDING,
            'transaction_id' => 'TXN_' . uniqid(),
        ]);

        // Mock Razorpay API for payment creation
        $mockApi = Mockery::mock(Api::class);
        $mockOrder = Mockery::mock();
        $mockUtility = Mockery::mock();
        $mockPayment = Mockery::mock();

        // Mock order creation
        $mockOrder->shouldReceive('create')
            ->once()
            ->andReturn([
                'id' => 'order_test123',
                'amount' => 10000,
                'currency' => 'INR',
                'status' => 'created'
            ]);

        // Mock payment verification
        $mockUtility->shouldReceive('verifyPaymentSignature')
            ->once()
            ->andReturn(true);

        $mockPayment->shouldReceive('fetch')
            ->once()
            ->andReturn([
                'id' => 'pay_test123',
                'status' => 'captured',
                'method' => 'card',
                'bank' => 'HDFC'
            ]);

        $mockApi->order = $mockOrder;
        $mockApi->utility = $mockUtility;
        $mockApi->payment = $mockPayment;

        // Create service and inject mock
        $service = new RazorpayService();
        $reflection = new \ReflectionClass($service);
        $razorpayProperty = $reflection->getProperty('razorpay');
        $razorpayProperty->setAccessible(true);
        $razorpayProperty->setValue($service, $mockApi);

        // Step 1: Create payment
        $paymentData = [
            'amount' => 100.00,
            'currency' => 'INR',
            'user_id' => $this->user->id,
            'transaction_id' => $transaction->transaction_id,
        ];

        $createResult = $service->createPayment($paymentData);
        
        $this->assertTrue($createResult['success']);
        $this->assertEquals('order_test123', $createResult['gateway_order_id']);

        // Step 2: Verify payment
        $verificationData = [
            'razorpay_order_id' => 'order_test123',
            'razorpay_payment_id' => 'pay_test123',
            'razorpay_signature' => 'test_signature'
        ];

        $verifyResult = $service->verifyPayment($transaction->transaction_id, $verificationData);
        
        $this->assertTrue($verifyResult);

        // Check transaction was updated
        $transaction->refresh();
        $this->assertEquals(Transaction::STATUS_COMPLETED, $transaction->status);
        $this->assertEquals('pay_test123', $transaction->gateway_transaction_id);

        // Step 3: Test webhook processing
        $webhookPayload = [
            'event' => 'payment.captured',
            'payload' => [
                'payment' => [
                    'entity' => [
                        'id' => 'pay_test123',
                        'order_id' => 'order_test123',
                        'status' => 'captured',
                        'method' => 'card'
                    ]
                ]
            ]
        ];

        // Mock webhook signature
        $_SERVER['HTTP_X_RAZORPAY_SIGNATURE'] = hash_hmac('sha256', json_encode($webhookPayload), 'test_webhook_secret');

        $webhookResult = $service->handleWebhook($webhookPayload);
        
        $this->assertTrue($webhookResult['success']);
        $this->assertEquals('Payment captured', $webhookResult['message']);

        // Verify webhook updated transaction
        $transaction->refresh();
        $this->assertTrue($transaction->webhook_verified);
    }

    /**
     * Test complete payment flow for PayUmoney
     */
    public function test_payumoney_complete_payment_flow()
    {
        // Create transaction
        $transaction = Transaction::create([
            'user_id' => $this->user->id,
            'gateway_name' => GatewaySetting::GATEWAY_PAYUMONEY,
            'amount' => 100.00,
            'currency' => 'INR',
            'status' => Transaction::STATUS_PENDING,
            'transaction_id' => 'TXN_' . uniqid(),
        ]);

        $service = new PayUmoneyService();

        // Step 1: Create payment
        $paymentData = [
            'amount' => 100.00,
            'transaction_id' => $transaction->transaction_id,
            'product_info' => 'Test Product',
            'firstname' => 'John',
            'email' => '<EMAIL>',
            'phone' => '9999999999'
        ];

        $createResult = $service->createPayment($paymentData);
        
        $this->assertTrue($createResult['success']);
        $this->assertEquals('POST', $createResult['method']);
        $this->assertArrayHasKey('payment_data', $createResult);
        $this->assertArrayHasKey('hash', $createResult['payment_data']);

        // Step 2: Verify payment (success scenario)
        $verificationData = [
            'status' => 'success',
            'txnid' => $transaction->transaction_id,
            'amount' => '100.00',
            'productinfo' => 'Payment',
            'firstname' => 'Customer',
            'email' => '<EMAIL>',
            'payuMoneyId' => 'payu_123456',
            'key' => 'test_merchant_key',
            'udf1' => (string)$this->user->id,
            'udf2' => (string)$transaction->id,
            'udf3' => '',
            'udf4' => '',
            'udf5' => '',
        ];

        // Generate valid hash for success - using the exact format from PayUmoneyService->verifyHash
        $hashString = 'test_salt_key|success|||||' . 
                     ($verificationData['udf5'] ?? '') . '|' . 
                     ($verificationData['udf4'] ?? '') . '|' . 
                     ($verificationData['udf3'] ?? '') . '|' . 
                     ($verificationData['udf2'] ?? '') . '|' . 
                     ($verificationData['udf1'] ?? '') . '|' . 
                     $verificationData['email'] . '|' . 
                     $verificationData['firstname'] . '|' . 
                     $verificationData['productinfo'] . '|' . 
                     $verificationData['amount'] . '|' . 
                     $verificationData['txnid'] . '|' . 
                     $verificationData['key'];
        $verificationData['hash'] = strtolower(hash('sha512', $hashString));

        $verifyResult = $service->verifyPayment($transaction->transaction_id, $verificationData);
        
        $this->assertTrue($verifyResult);

        // Check transaction was updated
        $transaction->refresh();
        $this->assertEquals(Transaction::STATUS_COMPLETED, $transaction->status);
        $this->assertEquals('payu_123456', $transaction->gateway_transaction_id);

        // Step 3: Test webhook/callback processing
        $callbackPayload = [
            'status' => 'success',
            'txnid' => $transaction->transaction_id,
            'amount' => '100.00',
            'productinfo' => 'Payment',
            'firstname' => 'Customer',
            'email' => '<EMAIL>',
            'payuMoneyId' => 'payu_123456',
            'key' => 'test_merchant_key',
            'udf1' => (string)$this->user->id,
            'udf2' => (string)$transaction->id,
            'udf3' => '',
            'udf4' => '',
            'udf5' => '',
        ];

        // Generate valid hash for callback
        $callbackHashString = 'test_salt_key|success|||||' . 
                             ($callbackPayload['udf5'] ?? '') . '|' . 
                             ($callbackPayload['udf4'] ?? '') . '|' . 
                             ($callbackPayload['udf3'] ?? '') . '|' . 
                             ($callbackPayload['udf2'] ?? '') . '|' . 
                             ($callbackPayload['udf1'] ?? '') . '|' . 
                             $callbackPayload['email'] . '|' . 
                             $callbackPayload['firstname'] . '|' . 
                             $callbackPayload['productinfo'] . '|' . 
                             $callbackPayload['amount'] . '|' . 
                             $callbackPayload['txnid'] . '|' . 
                             $callbackPayload['key'];
        $callbackPayload['hash'] = strtolower(hash('sha512', $callbackHashString));

        $callbackResult = $service->handleWebhook($callbackPayload);
        
        $this->assertTrue($callbackResult['success']);
        $this->assertEquals('Callback processed successfully', $callbackResult['message']);

        // Verify callback updated transaction
        $transaction->refresh();
        $this->assertTrue($transaction->webhook_verified);
    }

    /**
     * Test complete payment flow for Cashfree
     */
    public function test_cashfree_complete_payment_flow()
    {
        // Create transaction
        $transaction = Transaction::create([
            'user_id' => $this->user->id,
            'gateway_name' => GatewaySetting::GATEWAY_CASHFREE,
            'amount' => 100.00,
            'currency' => 'INR',
            'status' => Transaction::STATUS_PENDING,
            'transaction_id' => 'TXN_' . uniqid(),
        ]);

        $service = new CashfreeService();

        // Step 1: Create payment session
        Http::fake([
            'https://sandbox.cashfree.com/pg/orders' => Http::response([
                'cf_order_id' => 'order_test123',
                'payment_session_id' => 'session_test123',
                'order_status' => 'ACTIVE',
                'payment_link' => 'https://payments.cashfree.com/links/test123'
            ], 200)
        ]);

        $paymentData = [
            'amount' => 100.00,
            'currency' => 'INR',
            'transaction_id' => $transaction->transaction_id,
            'customer_name' => 'Test Customer',
            'customer_email' => '<EMAIL>',
            'customer_phone' => '9999999999'
        ];

        $createResult = $service->createPayment($paymentData);
        
        $this->assertTrue($createResult['success']);
        $this->assertEquals('order_test123', $createResult['cf_order_id']);
        $this->assertEquals('session_test123', $createResult['payment_session_id']);

        // Step 2: Verify payment
        Http::fake([
            "https://sandbox.cashfree.com/pg/orders/{$transaction->transaction_id}" => Http::response([
                'cf_order_id' => 'order_test123',
                'order_id' => $transaction->transaction_id,
                'order_amount' => 100.00,
                'order_currency' => 'INR',
                'order_status' => 'PAID',
                'payments' => [
                    [
                        'cf_payment_id' => 'payment_test123',
                        'payment_status' => 'SUCCESS',
                        'payment_amount' => 100.00,
                        'payment_method' => 'card'
                    ]
                ]
            ], 200)
        ]);

        $verifyResult = $service->verifyPayment($transaction->transaction_id, []);
        
        $this->assertTrue($verifyResult);

        // Check transaction was updated
        $transaction->refresh();
        $this->assertEquals(Transaction::STATUS_COMPLETED, $transaction->status);
        $this->assertEquals('order_test123', $transaction->gateway_transaction_id);

        // Step 3: Test webhook processing
        $webhookPayload = [
            'type' => 'PAYMENT_SUCCESS_WEBHOOK',
            'data' => [
                'order' => [
                    'order_id' => $transaction->transaction_id,
                    'cf_order_id' => 'order_test123',
                    'order_status' => 'PAID'
                ],
                'payment' => [
                    'cf_payment_id' => 'payment_test123',
                    'payment_status' => 'SUCCESS',
                    'payment_amount' => 100.00,
                    'payment_method' => 'card'
                ]
            ]
        ];

        // Mock webhook signature
        $timestamp = time();
        $signatureData = $timestamp . json_encode($webhookPayload);
        $expectedSignature = base64_encode(hash_hmac('sha256', $signatureData, 'test_secret_key', true));
        
        $_SERVER['HTTP_X_WEBHOOK_SIGNATURE'] = $expectedSignature;
        $_SERVER['HTTP_X_WEBHOOK_TIMESTAMP'] = $timestamp;

        $webhookResult = $service->handleWebhook($webhookPayload);
        
        $this->assertTrue($webhookResult['success']);
        $this->assertEquals('Payment success processed', $webhookResult['message']);

        // Verify webhook updated transaction
        $transaction->refresh();
        $this->assertTrue($transaction->webhook_verified);
    }

    /**
     * Test payment gateway factory integration
     */
    public function test_payment_gateway_factory_integration()
    {
        $factory = new PaymentGatewayFactory();

        // Test creating each gateway service
        $razorpayService = $factory->create(GatewaySetting::GATEWAY_RAZORPAY);
        $this->assertInstanceOf(RazorpayService::class, $razorpayService);
        $this->assertEquals(GatewaySetting::GATEWAY_RAZORPAY, $razorpayService->getGatewayName());

        $payumoneyService = $factory->create(GatewaySetting::GATEWAY_PAYUMONEY);
        $this->assertInstanceOf(PayUmoneyService::class, $payumoneyService);
        $this->assertEquals(GatewaySetting::GATEWAY_PAYUMONEY, $payumoneyService->getGatewayName());

        $cashfreeService = $factory->create(GatewaySetting::GATEWAY_CASHFREE);
        $this->assertInstanceOf(CashfreeService::class, $cashfreeService);
        $this->assertEquals(GatewaySetting::GATEWAY_CASHFREE, $cashfreeService->getGatewayName());

        // Test invalid gateway
        $this->expectException(\InvalidArgumentException::class);
        $factory->create('invalid_gateway');
    }

    /**
     * Test error handling across all gateways
     */
    public function test_error_handling_integration()
    {
        // Test configuration errors
        $this->gatewaySettings[GatewaySetting::GATEWAY_RAZORPAY]->delete();
        
        $this->expectException(GatewayConfigurationException::class);
        new RazorpayService();
    }

    /**
     * Test webhook signature verification for all gateways
     */
    public function test_webhook_signature_verification_integration()
    {
        // Test Razorpay webhook signature verification
        $razorpayService = new RazorpayService();
        $webhookPayload = ['event' => 'payment.captured', 'payload' => []];
        
        // Test with invalid signature
        $_SERVER['HTTP_X_RAZORPAY_SIGNATURE'] = 'invalid_signature';
        
        $this->expectException(WebhookVerificationException::class);
        $razorpayService->handleWebhook($webhookPayload);
    }

    /**
     * Test payment status retrieval integration
     */
    public function test_payment_status_retrieval_integration()
    {
        // Create transactions for each gateway
        $transactions = [
            GatewaySetting::GATEWAY_RAZORPAY => Transaction::create([
                'user_id' => $this->user->id,
                'gateway_name' => GatewaySetting::GATEWAY_RAZORPAY,
                'amount' => 100.00,
                'currency' => 'INR',
                'status' => Transaction::STATUS_PENDING,
                'transaction_id' => 'TXN_RZP_' . uniqid(),
                'gateway_transaction_id' => 'pay_test123'
            ]),
            GatewaySetting::GATEWAY_PAYUMONEY => Transaction::create([
                'user_id' => $this->user->id,
                'gateway_name' => GatewaySetting::GATEWAY_PAYUMONEY,
                'amount' => 100.00,
                'currency' => 'INR',
                'status' => Transaction::STATUS_COMPLETED,
                'transaction_id' => 'TXN_PAYU_' . uniqid(),
            ]),
            GatewaySetting::GATEWAY_CASHFREE => Transaction::create([
                'user_id' => $this->user->id,
                'gateway_name' => GatewaySetting::GATEWAY_CASHFREE,
                'amount' => 100.00,
                'currency' => 'INR',
                'status' => Transaction::STATUS_PENDING,
                'transaction_id' => 'TXN_CF_' . uniqid(),
            ])
        ];

        // Test Razorpay status retrieval
        $mockApi = Mockery::mock(Api::class);
        $mockPayment = Mockery::mock();
        $mockPayment->shouldReceive('fetch')->andReturn(['status' => 'captured']);
        $mockApi->payment = $mockPayment;

        $razorpayService = new RazorpayService();
        $reflection = new \ReflectionClass($razorpayService);
        $razorpayProperty = $reflection->getProperty('razorpay');
        $razorpayProperty->setAccessible(true);
        $razorpayProperty->setValue($razorpayService, $mockApi);

        $status = $razorpayService->getPaymentStatus($transactions[GatewaySetting::GATEWAY_RAZORPAY]->transaction_id);
        $this->assertEquals(Transaction::STATUS_COMPLETED, $status);

        // Test PayUmoney status retrieval (returns current transaction status)
        $payumoneyService = new PayUmoneyService();
        $status = $payumoneyService->getPaymentStatus($transactions[GatewaySetting::GATEWAY_PAYUMONEY]->transaction_id);
        $this->assertEquals(Transaction::STATUS_COMPLETED, $status);

        // Test Cashfree status retrieval
        Http::fake([
            "https://sandbox.cashfree.com/pg/orders/{$transactions[GatewaySetting::GATEWAY_CASHFREE]->transaction_id}" => Http::response([
                'order_status' => 'PAID'
            ], 200)
        ]);

        $cashfreeService = new CashfreeService();
        $status = $cashfreeService->getPaymentStatus($transactions[GatewaySetting::GATEWAY_CASHFREE]->transaction_id);
        $this->assertEquals(Transaction::STATUS_COMPLETED, $status);
    }

    /**
     * Test gateway enable/disable functionality integration
     */
    public function test_gateway_enable_disable_integration()
    {
        $razorpayService = new RazorpayService();
        $this->assertTrue($razorpayService->isEnabled());

        // Disable gateway
        $this->gatewaySettings[GatewaySetting::GATEWAY_RAZORPAY]->update(['is_enabled' => false]);
        
        $razorpayService = new RazorpayService();
        $this->assertFalse($razorpayService->isEnabled());
    }

    /**
     * Test test/live mode switching integration
     */
    public function test_test_live_mode_switching_integration()
    {
        // Test PayUmoney URL switching
        $payumoneyService = new PayUmoneyService();
        
        $transaction = Transaction::create([
            'user_id' => $this->user->id,
            'gateway_name' => GatewaySetting::GATEWAY_PAYUMONEY,
            'amount' => 100.00,
            'currency' => 'INR',
            'status' => Transaction::STATUS_PENDING,
            'transaction_id' => 'TXN_' . uniqid(),
        ]);

        // Test mode (default)
        $result = $payumoneyService->createPayment([
            'amount' => 100.00,
            'transaction_id' => $transaction->transaction_id,
        ]);
        
        $this->assertTrue($result['test_mode']);
        $this->assertStringContainsString('test.payu.in', $result['payment_url']);

        // Switch to live mode
        $this->gatewaySettings[GatewaySetting::GATEWAY_PAYUMONEY]->update(['is_test_mode' => false]);
        
        $payumoneyService = new PayUmoneyService();
        $result = $payumoneyService->createPayment([
            'amount' => 100.00,
            'transaction_id' => $transaction->transaction_id,
        ]);
        
        $this->assertFalse($result['test_mode']);
        $this->assertStringContainsString('secure.payu.in', $result['payment_url']);
    }

    /**
     * Test concurrent payment processing
     */
    public function test_concurrent_payment_processing_integration()
    {
        // Create multiple transactions
        $transactions = [];
        for ($i = 0; $i < 3; $i++) {
            $transactions[] = Transaction::create([
                'user_id' => $this->user->id,
                'gateway_name' => GatewaySetting::GATEWAY_RAZORPAY,
                'amount' => 100.00 + $i,
                'currency' => 'INR',
                'status' => Transaction::STATUS_PENDING,
                'transaction_id' => 'TXN_CONCURRENT_' . $i . '_' . uniqid(),
            ]);
        }

        // Mock Razorpay API for multiple calls
        $mockApi = Mockery::mock(Api::class);
        $mockOrder = Mockery::mock();
        
        $mockOrder->shouldReceive('create')
            ->times(3)
            ->andReturnUsing(function($data) {
                return [
                    'id' => 'order_' . uniqid(),
                    'amount' => $data['amount'],
                    'currency' => $data['currency'],
                    'status' => 'created'
                ];
            });

        $mockApi->order = $mockOrder;

        $service = new RazorpayService();
        $reflection = new \ReflectionClass($service);
        $razorpayProperty = $reflection->getProperty('razorpay');
        $razorpayProperty->setAccessible(true);
        $razorpayProperty->setValue($service, $mockApi);

        // Process all payments
        $results = [];
        foreach ($transactions as $transaction) {
            $paymentData = [
                'amount' => $transaction->amount,
                'currency' => 'INR',
                'user_id' => $this->user->id,
                'transaction_id' => $transaction->transaction_id,
            ];

            $results[] = $service->createPayment($paymentData);
        }

        // Verify all payments were created successfully
        foreach ($results as $result) {
            $this->assertTrue($result['success']);
            $this->assertArrayHasKey('gateway_order_id', $result);
        }
    }

    /**
     * Test payment failure scenarios integration
     */
    public function test_payment_failure_scenarios_integration()
    {
        // Test Razorpay API failure
        $transaction = Transaction::create([
            'user_id' => $this->user->id,
            'gateway_name' => GatewaySetting::GATEWAY_RAZORPAY,
            'amount' => 100.00,
            'currency' => 'INR',
            'status' => Transaction::STATUS_PENDING,
            'transaction_id' => 'TXN_FAIL_' . uniqid(),
        ]);

        $mockApi = Mockery::mock(Api::class);
        $mockOrder = Mockery::mock();
        
        $mockOrder->shouldReceive('create')
            ->once()
            ->andThrow(new BadRequestError('Invalid amount', 'BAD_REQUEST_ERROR', 400));

        $mockApi->order = $mockOrder;

        $service = new RazorpayService();
        $reflection = new \ReflectionClass($service);
        $razorpayProperty = $reflection->getProperty('razorpay');
        $razorpayProperty->setAccessible(true);
        $razorpayProperty->setValue($service, $mockApi);

        $this->expectException(InvalidPaymentDataException::class);
        
        $service->createPayment([
            'amount' => 100.00,
            'currency' => 'INR',
            'user_id' => $this->user->id,
            'transaction_id' => $transaction->transaction_id,
        ]);
    }

    /**
     * Test webhook processing for failed payments
     */
    public function test_webhook_failed_payment_processing_integration()
    {
        // Test Razorpay failed payment webhook
        $transaction = Transaction::create([
            'user_id' => $this->user->id,
            'gateway_name' => GatewaySetting::GATEWAY_RAZORPAY,
            'amount' => 100.00,
            'currency' => 'INR',
            'status' => Transaction::STATUS_PROCESSING,
            'transaction_id' => 'TXN_FAIL_WEBHOOK_' . uniqid(),
            'gateway_transaction_id' => 'pay_test123',
            'payment_details' => ['razorpay_order_id' => 'order_test123']
        ]);

        $service = new RazorpayService();

        $webhookPayload = [
            'event' => 'payment.failed',
            'payload' => [
                'payment' => [
                    'entity' => [
                        'id' => 'pay_test123',
                        'order_id' => 'order_test123',
                        'status' => 'failed',
                        'error_code' => 'BAD_REQUEST_ERROR',
                        'error_description' => 'Payment failed due to insufficient funds'
                    ]
                ]
            ]
        ];

        // Mock webhook signature
        $_SERVER['HTTP_X_RAZORPAY_SIGNATURE'] = hash_hmac('sha256', json_encode($webhookPayload), 'test_webhook_secret');

        $result = $service->handleWebhook($webhookPayload);
        
        $this->assertTrue($result['success']);
        $this->assertEquals('Payment failure processed', $result['message']);

        // Verify transaction was updated to failed
        $transaction->refresh();
        $this->assertEquals(Transaction::STATUS_FAILED, $transaction->status);
        $this->assertEquals('Payment failed due to insufficient funds', $transaction->failure_reason);
        $this->assertTrue($transaction->webhook_verified);
    }

    /**
     * Test data validation across all gateways
     */
    public function test_data_validation_integration()
    {
        $services = [
            'razorpay' => new RazorpayService(),
            'payumoney' => new PayUmoneyService(),
            'cashfree' => new CashfreeService()
        ];

        foreach ($services as $gatewayName => $service) {
            // Test missing amount
            try {
                if ($gatewayName === 'payumoney') {
                    $service->createPayment([
                        'transaction_id' => 'test_txn'
                    ]);
                } else {
                    $service->createPayment([
                        'currency' => 'INR',
                        'transaction_id' => 'test_txn',
                        'user_id' => $this->user->id
                    ]);
                }
                $this->fail('Expected InvalidPaymentDataException for missing amount');
            } catch (InvalidPaymentDataException $e) {
                $this->assertStringContainsString('amount', $e->getMessage());
            }

            // Test invalid amount - need to create transaction for PayUmoney first
            if ($gatewayName === 'payumoney') {
                $testTransaction = Transaction::create([
                    'user_id' => $this->user->id,
                    'gateway_name' => GatewaySetting::GATEWAY_PAYUMONEY,
                    'amount' => 100.00,
                    'currency' => 'INR',
                    'status' => Transaction::STATUS_PENDING,
                    'transaction_id' => 'test_txn_invalid_' . uniqid(),
                ]);
                
                try {
                    $service->createPayment([
                        'amount' => -100,
                        'transaction_id' => $testTransaction->transaction_id,
                    ]);
                    $this->fail('Expected InvalidPaymentDataException for invalid amount');
                } catch (InvalidPaymentDataException $e) {
                    $this->assertStringContainsString('positive number', $e->getMessage());
                }
            } else {
                try {
                    $service->createPayment([
                        'amount' => -100,
                        'currency' => 'INR',
                        'transaction_id' => 'test_txn',
                        'user_id' => $this->user->id
                    ]);
                    $this->fail('Expected InvalidPaymentDataException for invalid amount');
                } catch (InvalidPaymentDataException $e) {
                    $this->assertStringContainsString('positive number', $e->getMessage());
                }
            }
        }
    }
}