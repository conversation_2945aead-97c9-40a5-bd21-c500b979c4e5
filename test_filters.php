<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== TESTING IMPROVED FILTERS ===\n\n";

try {
    // Create an instance of the ProductList component
    $component = new \App\Livewire\Shop\ProductList();
    $component->mount();
    
    echo "1. Testing Categories:\n";
    echo "   Total categories loaded: " . $component->categories->count() . "\n";
    
    $componentCategories = $component->categories->where('type', '!=', 'product')->count();
    $productCategories = $component->categories->where('type', 'product')->count();
    echo "   Component categories: $componentCategories\n";
    echo "   Product categories: $productCategories\n";
    
    echo "\n2. Testing Available Brands:\n";
    echo "   Total brands: " . count($component->availableBrands) . "\n";
    echo "   First 5 brands: " . implode(', ', array_slice($component->availableBrands, 0, 5)) . "\n";
    
    echo "\n3. Testing Price Range:\n";
    echo "   Min price: $" . $component->priceRange['min'] . "\n";
    echo "   Max price: $" . $component->priceRange['max'] . "\n";
    
    echo "\n4. Testing Available Specs:\n";
    echo "   Spec categories: " . count($component->availableSpecs) . "\n";
    foreach (array_slice($component->availableSpecs, 0, 3) as $specKey => $specValues) {
        echo "   - $specKey: " . count($specValues) . " options\n";
    }
    
    echo "\n5. Testing Brand Filter:\n";
    if (!empty($component->availableBrands)) {
        $testBrand = $component->availableBrands[0];
        $component->selectedBrands = [$testBrand];
        $filteredResults = $component->components;
        echo "   Filtering by '$testBrand': " . $filteredResults->total() . " results\n";
        
        // Check if results actually contain the brand
        $brandMatch = 0;
        foreach ($filteredResults->items() as $item) {
            if ($item->brand === $testBrand) {
                $brandMatch++;
            }
        }
        echo "   Items matching brand on current page: $brandMatch\n";
    }
    
    echo "\n6. Testing Price Filter:\n";
    $component->selectedBrands = []; // Clear brand filter
    $component->priceMin = 1000;
    $component->priceMax = 5000;
    $priceFilteredResults = $component->components;
    echo "   Filtering by price $1000-$5000: " . $priceFilteredResults->total() . " results\n";
    
    // Check if results are within price range
    $priceMatch = 0;
    foreach ($priceFilteredResults->items() as $item) {
        if ($item->price >= 1000 && $item->price <= 5000) {
            $priceMatch++;
        }
    }
    echo "   Items within price range on current page: $priceMatch\n";
    
    echo "\n7. Testing Stock Filter:\n";
    $component->priceMin = null; // Clear price filter
    $component->priceMax = null;
    $component->inStockOnly = true;
    $stockFilteredResults = $component->components;
    echo "   Filtering by in-stock only: " . $stockFilteredResults->total() . " results\n";
    
    // Check if results are in stock
    $stockMatch = 0;
    foreach ($stockFilteredResults->items() as $item) {
        if ($item->stock > 0) {
            $stockMatch++;
        }
    }
    echo "   Items in stock on current page: $stockMatch\n";
    
} catch (Exception $e) {
    echo "ERROR: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . ":" . $e->getLine() . "\n";
}

echo "\n=== FILTER TEST COMPLETE ===\n";