<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class RedirectIfAdminAuthenticated
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // If user is authenticated and is an admin, redirect to admin dashboard
        if (Auth::check() && Auth::user()->role === 'admin' && Auth::user()->status === 'active') {
            return redirect()->route('admin.dashboard');
        }

        return $next($request);
    }
}