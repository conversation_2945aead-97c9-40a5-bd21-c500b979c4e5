<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Order;
use App\Services\CheckoutService;
use App\Services\CartService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;

class OrderController extends Controller
{
    public function __construct(
        private CheckoutService $checkoutService,
        private CartService $cartService
    ) {}

    /**
     * Get user's orders.
     */
    public function index(Request $request): JsonResponse
    {
        $request->validate([
            'status' => 'sometimes|string|in:pending,processing,shipped,delivered,cancelled',
            'sort' => 'sometimes|string|in:created_at,total,status',
            'direction' => 'sometimes|string|in:asc,desc',
            'per_page' => 'sometimes|integer|min:1|max:50',
        ]);

        $user = Auth::user();
        $query = Order::where('user_id', $user->id)
            ->with(['items.component.category']);

        // Apply filters
        if ($request->has('status')) {
            $query->where('status', $request->status);
        }

        // Apply sorting
        $sort = $request->get('sort', 'created_at');
        $direction = $request->get('direction', 'desc');
        $query->orderBy($sort, $direction);

        $perPage = $request->get('per_page', 15);
        $orders = $query->paginate($perPage);

        return response()->json([
            'data' => $orders->items(),
            'meta' => [
                'current_page' => $orders->currentPage(),
                'last_page' => $orders->lastPage(),
                'per_page' => $orders->perPage(),
                'total' => $orders->total(),
                'from' => $orders->firstItem(),
                'to' => $orders->lastItem(),
            ],
            'links' => [
                'first' => $orders->url(1),
                'last' => $orders->url($orders->lastPage()),
                'prev' => $orders->previousPageUrl(),
                'next' => $orders->nextPageUrl(),
            ]
        ]);
    }

    /**
     * Create a new order from cart.
     */
    public function store(Request $request): JsonResponse
    {
        $request->validate([
            'shipping_address' => 'required|array',
            'shipping_address.name' => 'required|string|max:255',
            'shipping_address.address_line_1' => 'required|string|max:255',
            'shipping_address.address_line_2' => 'nullable|string|max:255',
            'shipping_address.city' => 'required|string|max:100',
            'shipping_address.state' => 'required|string|max:100',
            'shipping_address.postal_code' => 'required|string|max:20',
            'shipping_address.country' => 'required|string|max:100',
            'billing_address' => 'sometimes|array',
            'billing_address.name' => 'required_with:billing_address|string|max:255',
            'billing_address.address_line_1' => 'required_with:billing_address|string|max:255',
            'billing_address.address_line_2' => 'nullable|string|max:255',
            'billing_address.city' => 'required_with:billing_address|string|max:100',
            'billing_address.state' => 'required_with:billing_address|string|max:100',
            'billing_address.postal_code' => 'required_with:billing_address|string|max:20',
            'billing_address.country' => 'required_with:billing_address|string|max:100',
            'payment_method' => 'required|string|in:credit_card,paypal,stripe',
            'payment_details' => 'required|array',
        ]);

        $user = Auth::user();
        $cart = $this->cartService->getCart($user);

        // Check if cart is empty
        if ($cart->items()->count() === 0) {
            return response()->json([
                'message' => 'Cart is empty'
            ], 422);
        }

        // Check stock availability for all items
        foreach ($cart->items as $item) {
            if ($item->component->stock < $item->quantity) {
                return response()->json([
                    'message' => "Insufficient stock for {$item->component->name}",
                    'component' => $item->component->name,
                    'requested' => $item->quantity,
                    'available' => $item->component->stock
                ], 422);
            }
        }

        try {
            // Format shipping data for CheckoutService
            $shippingData = [
                'shipping' => [
                    'name' => $request->shipping_address['name'],
                    'email' => $request->shipping_address['email'],
                    'phone' => $request->shipping_address['phone'],
                    'address' => $request->shipping_address['address_line_1'],
                    'city' => $request->shipping_address['city'],
                    'state' => $request->shipping_address['state'],
                    'zipcode' => $request->shipping_address['postal_code'],
                    'country' => $request->shipping_address['country'],
                ],
                'billing' => []
            ];
            
            // Use shipping as billing if billing not provided
            $billingAddress = $request->billing_address ?? $request->shipping_address;
            $shippingData['billing'] = [
                'name' => $billingAddress['name'],
                'email' => $billingAddress['email'],
                'phone' => $billingAddress['phone'],
                'address' => $billingAddress['address_line_1'],
                'city' => $billingAddress['city'],
                'state' => $billingAddress['state'],
                'zipcode' => $billingAddress['postal_code'],
                'country' => $billingAddress['country'],
            ];
            
            // Format payment data
            $paymentData = [
                'payment_method' => $request->payment_method,
            ];
            
            // Add payment details with proper formatting
            if ($request->payment_method === 'credit_card') {
                // Format card expiry from month/year to required format
                if (isset($request->payment_details['expiry_month']) && isset($request->payment_details['expiry_year'])) {
                    $paymentData['card_expiry'] = $request->payment_details['expiry_month'] . '/' . $request->payment_details['expiry_year'];
                }
                
                // Map other fields directly
                if (isset($request->payment_details['card_number'])) {
                    $paymentData['card_number'] = $request->payment_details['card_number'];
                }
                
                if (isset($request->payment_details['cvv'])) {
                    $paymentData['card_cvv'] = $request->payment_details['cvv'];
                }
                
                // Use shipping name as card name if not provided
                $paymentData['card_name'] = $request->payment_details['card_name'] ?? $request->shipping_address['name'];
            } else {
                // For other payment methods, add all details
                foreach ($request->payment_details as $key => $value) {
                    $paymentData[$key] = $value;
                }
            }
            
            $order = $this->checkoutService->processCheckout(
                $cart,
                $shippingData,
                $paymentData
            );

            return response()->json([
                'data' => $order->load(['items.component.category']),
                'message' => 'Order placed successfully'
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Order processing failed',
                'error' => $e->getMessage()
            ], 422);
        }
    }

    /**
     * Get specific order details.
     */
    public function show(Order $order): JsonResponse
    {
        // Check if user owns this order
        if ($order->user_id !== Auth::id()) {
            return response()->json(['message' => 'Order not found'], 404);
        }

        $order->load(['items.component.category']);

        return response()->json([
            'data' => $order
        ]);
    }

    /**
     * Cancel an order.
     */
    public function cancel(Order $order): JsonResponse
    {
        // Check if user owns this order
        if ($order->user_id !== Auth::id()) {
            return response()->json(['message' => 'Order not found'], 404);
        }

        // Check if order can be cancelled
        if (!in_array($order->status, ['pending', 'processing'])) {
            return response()->json([
                'message' => 'Order cannot be cancelled at this stage'
            ], 422);
        }

        $order->update(['status' => 'canceled']);

        // Restore stock for cancelled items
        foreach ($order->items as $item) {
            $item->component->increment('stock', $item->quantity);
        }

        return response()->json([
            'data' => $order,
            'message' => 'Order canceled successfully'
        ]);
    }

    /**
     * Track order status.
     */
    public function track(string $orderNumber): JsonResponse
    {
        $order = Order::where('order_number', $orderNumber)
            ->with(['items.component.category'])
            ->first();

        if (!$order) {
            return response()->json(['message' => 'Order not found'], 404);
        }

        // Check if user owns this order or if it's a guest order
        if ($order->user_id && $order->user_id !== Auth::id()) {
            return response()->json(['message' => 'Order not found'], 404);
        }

        return response()->json([
            'data' => [
                'order' => $order,
                'tracking_info' => [
                    'status' => $order->status,
                    'tracking_number' => $order->tracking_number,
                    'tracking_url' => $order->tracking_url,
                    'estimated_delivery' => $order->estimated_delivery_date,
                    'status_history' => $this->getOrderStatusHistory($order),
                ]
            ]
        ]);
    }

    /**
     * Get order status history.
     */
    private function getOrderStatusHistory(Order $order): array
    {
        $history = [];
        
        $history[] = [
            'status' => 'pending',
            'date' => $order->created_at,
            'description' => 'Order placed and payment confirmed'
        ];

        if (in_array($order->status, ['processing', 'completed'])) {
            $history[] = [
                'status' => 'processing',
                'date' => $order->updated_at,
                'description' => 'Order is being processed'
            ];
        }

        if ($order->status === 'completed') {
            $history[] = [
                'status' => 'completed',
                'date' => $order->updated_at,
                'description' => 'Order has been completed'
            ];
        }

        // Removed delivered status as it's no longer in the enum
        // if ($order->status === 'completed') {
        //     $history[] = [
        //         'status' => 'delivered',
        //         'date' => $order->updated_at,
        //         'description' => 'Order has been delivered'
        //     ];
        // }

        if ($order->status === 'canceled') {
            $history[] = [
                'status' => 'canceled',
                'date' => $order->updated_at,
                'description' => 'Order has been canceled'
            ];
        }

        return $history;
    }
}