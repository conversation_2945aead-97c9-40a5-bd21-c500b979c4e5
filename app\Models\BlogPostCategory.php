<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class BlogPostCategory extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'slug',
        'description',
        'display_order'
    ];

    protected $casts = [
        'display_order' => 'integer'
    ];

    /**
     * Boot function to auto-generate slug before saving
     */
    // protected static function boot()
    // {
    //     parent::boot();

    //     static::creating(function ($category) {
    //         $category->slug = generateSlug($category->name);
    //     });

    //     static::updating(function ($category) {
    //         $category->slug = generateSlug($category->name);
    //     });
    // }

    /**
     * Get the posts for the category
     */
    public function posts(): HasMany
    {
        return $this->hasMany(BlogPost::class, 'blog_post_category_id');
    }

    /**
     * Get color class for category badge
     */
    public function getBadgeColorAttribute()
    {
        $colors = [
            'logistics' => 'bg-teal-600',
            'guides' => 'bg-emerald-600',
            'updates' => 'bg-cyan-600',
            'news' => 'bg-blue-600',
            'tutorials' => 'bg-indigo-600',
            'tips' => 'bg-teal-500',
            'case-studies' => 'bg-purple-600',
            'interviews' => 'bg-pink-600',
            'technology' => 'bg-slate-600',
            'business' => 'bg-emerald-700',
            'education' => 'bg-blue-700',
            'health' => 'bg-red-600',
            'travel' => 'bg-orange-600',
            'food' => 'bg-amber-600',
            'lifestyle' => 'bg-rose-600',
        ];

        $slug = strtolower($this->slug);
        
        // Try exact match first
        if (isset($colors[$slug])) {
            return $colors[$slug];
        }

        // Try partial match
        foreach ($colors as $key => $color) {
            if (str_contains($slug, $key)) {
                return $color;
            }
        }

        // Default teal color
        return 'bg-teal-600';
    }
}