<?php

namespace App\Services\Payment\Gateways;

use App\Models\GatewaySetting;
use App\Models\Transaction;
use App\Services\Payment\Contracts\PaymentGatewayInterface;
use App\Services\Payment\Exceptions\GatewayConfigurationException;
use App\Services\Payment\Exceptions\InvalidPaymentDataException;
use App\Services\Payment\Exceptions\PaymentGatewayException;
use App\Services\Payment\Exceptions\WebhookVerificationException;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Exception;

class CashfreeService implements PaymentGatewayInterface
{
    private ?GatewaySetting $gatewaySetting;
    private array $config;

    // Cashfree API endpoints
    private const TEST_BASE_URL = 'https://sandbox.cashfree.com/pg';
    private const LIVE_BASE_URL = 'https://api.cashfree.com/pg';

    public function __construct()
    {
        $this->loadConfiguration();
    }

    /**
     * Load gateway configuration from database
     */
    private function loadConfiguration(): void
    {
        $this->gatewaySetting = GatewaySetting::byGateway(GatewaySetting::GATEWAY_CASHFREE)->first();
        
        if (!$this->gatewaySetting) {
            throw new GatewayConfigurationException(
                'Cashfree gateway configuration not found',
                'CASHFREE_CONFIG_NOT_FOUND'
            );
        }

        // Handle both encrypted and plain JSON settings (for testing)
        $settings = $this->gatewaySetting->settings;
        if (is_string($settings)) {
            $settings = json_decode($settings, true);
        }
        $this->config = $settings ?? [];
        
        if (empty($this->config['app_id']) || empty($this->config['secret_key'])) {
            throw new GatewayConfigurationException(
                'Cashfree API credentials not configured',
                'CASHFREE_CREDENTIALS_MISSING'
            );
        }
    }

    /**
     * Create a payment session with Cashfree
     */
    public function createPayment(array $data): array
    {
        $this->validatePaymentData($data);

        try {
            $transaction = Transaction::where('transaction_id', $data['transaction_id'])->first();
            
            if (!$transaction) {
                Log::error('Transaction not found for payment creation', [
                    'transaction_id' => $data['transaction_id']
                ]);
                throw new InvalidPaymentDataException(
                    'Transaction not found',
                    'TRANSACTION_NOT_FOUND'
                );
            }

            // Prepare payment session data for Cashfree
            $sessionData = [
                'order_id' => $data['transaction_id'],
                'order_amount' => number_format($data['amount'], 2, '.', ''),
                'order_currency' => $data['currency'] ?? 'INR',
                'customer_details' => [
                    'customer_id' => (string) $transaction->user_id,
                    'customer_name' => $data['customer_name'] ?? 'Customer',
                    'customer_email' => $data['customer_email'] ?? $transaction->user->email ?? '<EMAIL>',
                    'customer_phone' => $data['customer_phone'] ?? '9999999999',
                ],
                'order_meta' => [
                    'return_url' => $data['return_url'] ?? url('/payment/cashfree/return'),
                    'notify_url' => $data['notify_url'] ?? url('/webhooks/cashfree'),
                ],
                'order_note' => $data['order_note'] ?? 'Payment for order ' . $data['transaction_id'],
            ];

            // Make API request to create payment session
            $response = $this->makeApiRequest('POST', '/orders', $sessionData);

            if (!$response['success']) {
                throw new PaymentGatewayException(
                    'Failed to create Cashfree payment session: ' . ($response['message'] ?? 'Unknown error'),
                    'CASHFREE_SESSION_FAILED',
                    ['response' => $response]
                );
            }

            $sessionResponse = $response['data'];

            Log::info('Cashfree payment session created successfully', [
                'order_id' => $data['transaction_id'],
                'cf_order_id' => $sessionResponse['cf_order_id'] ?? null,
                'amount' => $data['amount']
            ]);

            return [
                'success' => true,
                'cf_order_id' => $sessionResponse['cf_order_id'],
                'payment_session_id' => $sessionResponse['payment_session_id'],
                'order_status' => $sessionResponse['order_status'],
                'payment_link' => $sessionResponse['payment_link'] ?? null,
                'order_token' => $sessionResponse['order_token'] ?? null,
                'session_data' => $sessionResponse,
                'test_mode' => $this->gatewaySetting->is_test_mode
            ];

        } catch (InvalidPaymentDataException $e) {
            // Re-throw validation exceptions as-is
            throw $e;
        } catch (PaymentGatewayException $e) {
            // Re-throw payment gateway exceptions as-is
            throw $e;
        } catch (Exception $e) {
            Log::error('Cashfree payment creation failed', [
                'error' => $e->getMessage(),
                'transaction_id' => $data['transaction_id'] ?? null
            ]);

            throw new PaymentGatewayException(
                'Payment creation failed: ' . $e->getMessage(),
                'CASHFREE_PAYMENT_FAILED',
                ['error' => $e->getMessage()]
            );
        }
    }

    /**
     * Verify payment with Cashfree
     */
    public function verifyPayment(string $transactionId, array $data): bool
    {
        try {
            $transaction = Transaction::where('transaction_id', $transactionId)->first();
            
            if (!$transaction) {
                Log::error('Transaction not found for verification', ['transaction_id' => $transactionId]);
                return false;
            }

            // Get payment details from Cashfree API
            $response = $this->makeApiRequest('GET', "/orders/{$transactionId}");

            if (!$response['success']) {
                Log::error('Failed to fetch payment details from Cashfree', [
                    'transaction_id' => $transactionId,
                    'error' => $response['message'] ?? 'Unknown error'
                ]);
                return false;
            }

            $orderData = $response['data'];

            // Verify order details
            if ($orderData['order_id'] !== $transactionId) {
                Log::error('Order ID mismatch in Cashfree verification', [
                    'expected' => $transactionId,
                    'received' => $orderData['order_id']
                ]);
                return false;
            }

            // Check amount
            $expectedAmount = number_format($transaction->amount, 2, '.', '');
            if (number_format($orderData['order_amount'], 2, '.', '') !== $expectedAmount) {
                Log::error('Amount mismatch in Cashfree verification', [
                    'expected' => $expectedAmount,
                    'received' => $orderData['order_amount'],
                    'transaction_id' => $transactionId
                ]);
                return false;
            }

            // Determine status based on Cashfree response
            $isSuccess = strtoupper($orderData['order_status']) === 'PAID';
            $status = match(strtoupper($orderData['order_status'])) {
                'PAID' => Transaction::STATUS_COMPLETED,
                'ACTIVE' => Transaction::STATUS_PROCESSING,
                'EXPIRED', 'CANCELLED', 'TERMINATED' => Transaction::STATUS_FAILED,
                default => Transaction::STATUS_PENDING
            };

            // Get payment details if available
            $paymentDetails = [];
            if (isset($orderData['payments']) && !empty($orderData['payments'])) {
                $payment = $orderData['payments'][0]; // Get the first payment
                $paymentDetails = [
                    'cf_payment_id' => $payment['cf_payment_id'] ?? null,
                    'payment_status' => $payment['payment_status'] ?? null,
                    'payment_amount' => $payment['payment_amount'] ?? null,
                    'payment_currency' => $payment['payment_currency'] ?? null,
                    'payment_method' => $payment['payment_method'] ?? null,
                    'bank_reference' => $payment['bank_reference'] ?? null,
                    'auth_id' => $payment['auth_id'] ?? null,
                ];
            }

            // Update transaction with payment details
            $transaction->update([
                'gateway_transaction_id' => $orderData['cf_order_id'] ?? null,
                'status' => $status,
                'failure_reason' => $isSuccess ? null : ($orderData['order_note'] ?? 'Payment failed'),
                'payment_details' => array_merge($transaction->payment_details ?? [], [
                    'cashfree_order_id' => $orderData['cf_order_id'],
                    'order_status' => $orderData['order_status'],
                    'order_amount' => $orderData['order_amount'],
                    'order_currency' => $orderData['order_currency'],
                    'payment_details' => $paymentDetails,
                    'verified_at' => now()->toISOString()
                ])
            ]);

            Log::info('Cashfree payment verified successfully', [
                'transaction_id' => $transactionId,
                'cf_order_id' => $orderData['cf_order_id'],
                'status' => $orderData['order_status']
            ]);

            return $isSuccess;

        } catch (Exception $e) {
            Log::error('Cashfree payment verification failed', [
                'error' => $e->getMessage(),
                'transaction_id' => $transactionId
            ]);
            return false;
        }
    }

    /**
     * Handle webhook from Cashfree
     */
    public function handleWebhook(array $payload): array
    {
        try {
            // Verify webhook signature
            $this->verifyWebhookSignature($payload);

            $type = $payload['type'] ?? '';
            $data = $payload['data'] ?? [];

            Log::info('Processing Cashfree webhook', [
                'type' => $type,
                'order_id' => $data['order']['order_id'] ?? null
            ]);

            // Handle different webhook types
            switch ($type) {
                case 'PAYMENT_SUCCESS_WEBHOOK':
                    return $this->handlePaymentSuccess($data);
                
                case 'PAYMENT_FAILED_WEBHOOK':
                    return $this->handlePaymentFailed($data);
                
                case 'PAYMENT_USER_DROPPED_WEBHOOK':
                    return $this->handlePaymentDropped($data);
                
                default:
                    Log::info('Unhandled Cashfree webhook type', ['type' => $type]);
                    return [
                        'success' => true,
                        'message' => 'Webhook type not handled',
                        'type' => $type
                    ];
            }

        } catch (WebhookVerificationException $e) {
            Log::error('Cashfree webhook verification failed', [
                'error' => $e->getMessage(),
                'payload' => $payload
            ]);
            throw $e;

        } catch (Exception $e) {
            Log::error('Cashfree webhook processing failed', [
                'error' => $e->getMessage(),
                'payload' => $payload
            ]);

            throw new PaymentGatewayException(
                'Webhook processing failed: ' . $e->getMessage(),
                'CASHFREE_WEBHOOK_FAILED',
                ['error' => $e->getMessage()]
            );
        }
    }

    /**
     * Get payment status from Cashfree
     */
    public function getPaymentStatus(string $transactionId): string
    {
        try {
            $response = $this->makeApiRequest('GET', "/orders/{$transactionId}");
            
            if (!$response['success']) {
                return Transaction::STATUS_PENDING;
            }

            $orderData = $response['data'];
            
            $status = strtoupper($orderData['order_status']);
            return match($status) {
                'PAID' => Transaction::STATUS_COMPLETED,
                'ACTIVE' => Transaction::STATUS_PROCESSING,
                'EXPIRED', 'CANCELLED', 'TERMINATED' => Transaction::STATUS_FAILED,
                default => Transaction::STATUS_PENDING
            };

        } catch (Exception $e) {
            Log::error('Failed to get payment status from Cashfree', [
                'error' => $e->getMessage(),
                'transaction_id' => $transactionId
            ]);
            
            return Transaction::STATUS_PENDING;
        }
    }

    /**
     * Get gateway name
     */
    public function getGatewayName(): string
    {
        return GatewaySetting::GATEWAY_CASHFREE;
    }

    /**
     * Check if gateway is enabled
     */
    public function isEnabled(): bool
    {
        return $this->gatewaySetting->is_enabled;
    }

    /**
     * Make API request to Cashfree
     */
    private function makeApiRequest(string $method, string $endpoint, array $data = []): array
    {
        $baseUrl = $this->gatewaySetting->is_test_mode ? self::TEST_BASE_URL : self::LIVE_BASE_URL;
        $url = $baseUrl . $endpoint;

        $headers = [
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
            'x-api-version' => '2023-08-01',
            'x-client-id' => $this->config['app_id'],
            'x-client-secret' => $this->config['secret_key'],
        ];

        try {
            $response = Http::withHeaders($headers)
                ->timeout(30)
                ->{strtolower($method)}($url, $data);

            $responseData = $response->json();

            if ($response->successful()) {
                return [
                    'success' => true,
                    'data' => $responseData,
                    'status_code' => $response->status()
                ];
            }

            Log::error('Cashfree API request failed', [
                'method' => $method,
                'endpoint' => $endpoint,
                'status_code' => $response->status(),
                'response' => $responseData
            ]);

            return [
                'success' => false,
                'message' => $responseData['message'] ?? 'API request failed',
                'error_code' => $responseData['code'] ?? null,
                'status_code' => $response->status(),
                'data' => $responseData
            ];

        } catch (Exception $e) {
            Log::error('Cashfree API request exception', [
                'method' => $method,
                'endpoint' => $endpoint,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => 'API request failed: ' . $e->getMessage(),
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Validate payment data
     */
    private function validatePaymentData(array $data): void
    {
        $requiredFields = ['amount', 'currency', 'transaction_id'];
        
        foreach ($requiredFields as $field) {
            if (!isset($data[$field]) || empty($data[$field])) {
                throw new InvalidPaymentDataException(
                    "Missing required field: {$field}",
                    'MISSING_REQUIRED_FIELD',
                    ['field' => $field]
                );
            }
        }

        if (!is_numeric($data['amount']) || $data['amount'] <= 0) {
            throw new InvalidPaymentDataException(
                'Amount must be a positive number',
                'INVALID_AMOUNT',
                ['amount' => $data['amount']]
            );
        }

        if (strlen($data['currency']) !== 3) {
            throw new InvalidPaymentDataException(
                'Currency must be a 3-character code',
                'INVALID_CURRENCY',
                ['currency' => $data['currency']]
            );
        }
    }

    /**
     * Verify webhook signature
     */
    private function verifyWebhookSignature(array $payload): void
    {
        $receivedSignature = $_SERVER['HTTP_X_WEBHOOK_SIGNATURE'] ?? '';
        
        if (empty($receivedSignature)) {
            throw new WebhookVerificationException(
                'Webhook signature missing',
                'WEBHOOK_SIGNATURE_MISSING'
            );
        }

        $webhookBody = json_encode($payload);
        $timestamp = $_SERVER['HTTP_X_WEBHOOK_TIMESTAMP'] ?? '';
        
        if (empty($timestamp)) {
            throw new WebhookVerificationException(
                'Webhook timestamp missing',
                'WEBHOOK_TIMESTAMP_MISSING'
            );
        }

        // Cashfree webhook signature verification
        $signatureData = $timestamp . $webhookBody;
        $expectedSignature = base64_encode(hash_hmac('sha256', $signatureData, $this->config['secret_key'], true));

        if (!hash_equals($expectedSignature, $receivedSignature)) {
            throw new WebhookVerificationException(
                'Webhook signature verification failed',
                'WEBHOOK_SIGNATURE_INVALID'
            );
        }
    }

    /**
     * Handle payment success webhook
     */
    private function handlePaymentSuccess(array $data): array
    {
        $orderData = $data['order'] ?? [];
        $paymentData = $data['payment'] ?? [];
        
        $orderId = $orderData['order_id'] ?? '';
        
        if (empty($orderId)) {
            Log::warning('Order ID missing in payment success webhook');
            return ['success' => false, 'message' => 'Order ID missing'];
        }

        $transaction = Transaction::where('transaction_id', $orderId)->first();

        if (!$transaction) {
            Log::warning('Transaction not found for payment success webhook', [
                'order_id' => $orderId
            ]);
            return ['success' => false, 'message' => 'Transaction not found'];
        }

        $transaction->update([
            'status' => Transaction::STATUS_COMPLETED,
            'gateway_transaction_id' => $orderData['cf_order_id'] ?? null,
            'webhook_verified' => true,
            'payment_details' => array_merge($transaction->payment_details ?? [], [
                'webhook_success_at' => now()->toISOString(),
                'cf_payment_id' => $paymentData['cf_payment_id'] ?? null,
                'payment_status' => $paymentData['payment_status'] ?? null,
                'payment_amount' => $paymentData['payment_amount'] ?? null,
                'payment_method' => $paymentData['payment_method'] ?? null,
                'bank_reference' => $paymentData['bank_reference'] ?? null,
                'auth_id' => $paymentData['auth_id'] ?? null,
            ])
        ]);

        Log::info('Payment success processed via webhook', [
            'transaction_id' => $orderId,
            'cf_order_id' => $orderData['cf_order_id'] ?? null,
            'cf_payment_id' => $paymentData['cf_payment_id'] ?? null
        ]);

        return ['success' => true, 'message' => 'Payment success processed'];
    }

    /**
     * Handle payment failed webhook
     */
    private function handlePaymentFailed(array $data): array
    {
        $orderData = $data['order'] ?? [];
        $paymentData = $data['payment'] ?? [];
        
        $orderId = $orderData['order_id'] ?? '';
        
        if (empty($orderId)) {
            Log::warning('Order ID missing in payment failed webhook');
            return ['success' => false, 'message' => 'Order ID missing'];
        }

        $transaction = Transaction::where('transaction_id', $orderId)->first();

        if (!$transaction) {
            Log::warning('Transaction not found for payment failed webhook', [
                'order_id' => $orderId
            ]);
            return ['success' => false, 'message' => 'Transaction not found'];
        }

        $transaction->update([
            'status' => Transaction::STATUS_FAILED,
            'gateway_transaction_id' => $orderData['cf_order_id'] ?? null,
            'webhook_verified' => true,
            'failure_reason' => $paymentData['payment_message'] ?? 'Payment failed',
            'payment_details' => array_merge($transaction->payment_details ?? [], [
                'webhook_failed_at' => now()->toISOString(),
                'cf_payment_id' => $paymentData['cf_payment_id'] ?? null,
                'payment_status' => $paymentData['payment_status'] ?? null,
                'payment_message' => $paymentData['payment_message'] ?? null,
                'error_details' => $paymentData['error_details'] ?? null,
            ])
        ]);

        Log::info('Payment failure processed via webhook', [
            'transaction_id' => $orderId,
            'cf_order_id' => $orderData['cf_order_id'] ?? null,
            'error' => $paymentData['payment_message'] ?? 'Unknown error'
        ]);

        return ['success' => true, 'message' => 'Payment failure processed'];
    }

    /**
     * Handle payment dropped webhook (user cancelled)
     */
    private function handlePaymentDropped(array $data): array
    {
        $orderData = $data['order'] ?? [];
        
        $orderId = $orderData['order_id'] ?? '';
        
        if (empty($orderId)) {
            Log::warning('Order ID missing in payment dropped webhook');
            return ['success' => false, 'message' => 'Order ID missing'];
        }

        $transaction = Transaction::where('transaction_id', $orderId)->first();

        if (!$transaction) {
            Log::warning('Transaction not found for payment dropped webhook', [
                'order_id' => $orderId
            ]);
            return ['success' => false, 'message' => 'Transaction not found'];
        }

        $transaction->update([
            'status' => Transaction::STATUS_CANCELLED,
            'gateway_transaction_id' => $orderData['cf_order_id'] ?? null,
            'webhook_verified' => true,
            'failure_reason' => 'Payment cancelled by user',
            'payment_details' => array_merge($transaction->payment_details ?? [], [
                'webhook_dropped_at' => now()->toISOString(),
                'order_status' => $orderData['order_status'] ?? null,
            ])
        ]);

        Log::info('Payment dropped processed via webhook', [
            'transaction_id' => $orderId,
            'cf_order_id' => $orderData['cf_order_id'] ?? null
        ]);

        return ['success' => true, 'message' => 'Payment cancellation processed'];
    }
}