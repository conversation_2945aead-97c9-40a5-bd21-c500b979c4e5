<?php

namespace App\Services;

use App\Models\Component;
use App\Models\ComponentCategory;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class SearchService
{
    protected array $currentSearchParams = [];
    
    /**
     * Perform advanced search on components with specification filtering.
     */
    public function searchComponents(array $params = []): Builder
    {
        $query = Component::query()
            ->with(['category', 'reviews'])
            ->where('is_active', true);

        // Apply text search
        if (!empty($params['search'])) {
            $query = $this->applyTextSearch($query, $params['search']);
        }

        // Apply category filter
        if (!empty($params['category'])) {
            $query = $this->applyCategoryFilter($query, $params['category']);
        }

        // Apply brand filter
        if (!empty($params['brands'])) {
            $query = $this->applyBrandFilter($query, $params['brands']);
        }

        // Apply price range filter
        if (!empty($params['price_min']) || !empty($params['price_max'])) {
            $query = $this->applyPriceFilter($query, $params['price_min'] ?? null, $params['price_max'] ?? null);
        }

        // Apply specification filters
        if (!empty($params['specs'])) {
            $query = $this->applySpecificationFilters($query, $params['specs']);
        }

        // Apply stock filter
        if (!empty($params['in_stock_only'])) {
            $query = $this->applyStockFilter($query);
        }

        // Apply rating filter
        if (!empty($params['min_rating'])) {
            $query = $this->applyRatingFilter($query, $params['min_rating']);
        }

        // Apply sorting with relevance scoring
        $query = $this->applySorting($query, $params['sort_by'] ?? 'relevance', $params['sort_direction'] ?? 'desc', $params['search'] ?? null);

        return $query;
    }

    /**
     * Apply full-text search with relevance scoring.
     */
    protected function applyTextSearch(Builder $query, string $searchTerm): Builder
    {
        $searchTerm = trim($searchTerm);
        
        if (empty($searchTerm)) {
            return $query;
        }

        // Split search term into individual words for better matching
        $words = explode(' ', $searchTerm);
        $words = array_filter($words, fn($word) => strlen(trim($word)) > 2);
        $words = array_map('trim', $words);

        return $query->where(function ($q) use ($searchTerm, $words) {
            // Exact phrase match (highest priority)
            $q->where('name', 'like', "%{$searchTerm}%")
              ->orWhere('description', 'like', "%{$searchTerm}%")
              ->orWhere('brand', 'like', "%{$searchTerm}%")
              ->orWhere('model', 'like', "%{$searchTerm}%");

            // JSON specification search for exact phrase (database-agnostic)
            $this->addJsonSpecificationSearch($q, $searchTerm);
            
            // Individual word matches only if we have multiple words and they are significant
            if (count($words) > 1) {
                foreach ($words as $word) {
                    if (strlen($word) > 3) { // Only search for words longer than 3 characters
                        $q->orWhere('name', 'like', "%{$word}%")
                          ->orWhere('description', 'like', "%{$word}%")
                          ->orWhere('brand', 'like', "%{$word}%")
                          ->orWhere('model', 'like', "%{$word}%");
                        
                        // JSON specification search for individual words
                        $this->addJsonSpecificationSearch($q, $word);
                    }
                }
            }
        });
    }

    /**
     * Add JSON specification search in a database-agnostic way.
     */
    protected function addJsonSpecificationSearch($query, string $searchTerm): void
    {
        // Get the database connection to determine the driver
        $connection = $query->getConnection();
        $driver = $connection->getDriverName();

        if ($driver === 'mysql') {
            // Use JSON_SEARCH for MySQL
            $query->orWhereRaw("JSON_SEARCH(specs, 'one', ?) IS NOT NULL", ["%{$searchTerm}%"]);
        } else {
            // For SQLite and other databases, use a different approach
            // Search through common specification keys
            $commonSpecKeys = ['memory', 'memory_interface', 'boost_clock', 'power_consumption', 
                              'socket', 'cores', 'threads', 'frequency', 'cache', 'tdp'];
            
            foreach ($commonSpecKeys as $key) {
                $query->orWhereRaw("JSON_EXTRACT(specs, '$.{$key}') LIKE ?", ["%{$searchTerm}%"]);
            }
        }
    }

    /**
     * Apply category filter.
     */
    protected function applyCategoryFilter(Builder $query, $category): Builder
    {
        if (is_string($category)) {
            // Filter by category slug
            $categoryModel = ComponentCategory::where('slug', $category)->first();
            if ($categoryModel) {
                $query->where('category_id', $categoryModel->id);
            }
        } elseif (is_array($category)) {
            // Filter by multiple categories
            $categoryIds = ComponentCategory::whereIn('slug', $category)->pluck('id');
            $query->whereIn('category_id', $categoryIds);
        } elseif (is_numeric($category)) {
            // Filter by category ID
            $query->where('category_id', $category);
        }

        return $query;
    }

    /**
     * Apply brand filter.
     */
    protected function applyBrandFilter(Builder $query, $brands): Builder
    {
        if (is_string($brands)) {
            $brands = [$brands];
        }

        return $query->whereIn('brand', $brands);
    }

    /**
     * Apply price range filter.
     */
    protected function applyPriceFilter(Builder $query, ?float $minPrice, ?float $maxPrice): Builder
    {
        if ($minPrice !== null && $minPrice > 0) {
            $query->where('price', '>=', $minPrice);
        }

        if ($maxPrice !== null && $maxPrice > 0) {
            $query->where('price', '<=', $maxPrice);
        }

        return $query;
    }

    /**
     * Apply specification filters.
     */
    protected function applySpecificationFilters(Builder $query, array $specs): Builder
    {
        foreach ($specs as $key => $value) {
            if (is_array($value)) {
                // Multiple values for the same spec (OR condition)
                $query->where(function ($q) use ($key, $value) {
                    foreach ($value as $val) {
                        $q->orWhereRaw("JSON_EXTRACT(specs, '$.{$key}') = ?", [$val]);
                    }
                });
            } else {
                // Single value
                $query->whereRaw("JSON_EXTRACT(specs, '$.{$key}') = ?", [$value]);
            }
        }

        return $query;
    }

    /**
     * Apply stock filter.
     */
    protected function applyStockFilter(Builder $query): Builder
    {
        return $query->where('stock', '>', 0);
    }

    /**
     * Apply rating filter.
     */
    protected function applyRatingFilter(Builder $query, float $minRating): Builder
    {
        // Temporarily disabled due to SQLite compatibility issues
        // TODO: Implement a proper rating filter that works with SQLite
        return $query;
    }

    /**
     * Apply sorting with relevance scoring.
     */
    protected function applySorting(Builder $query, string $sortBy, string $direction, ?string $searchTerm): Builder
    {
        $direction = strtolower($direction) === 'desc' ? 'desc' : 'asc';

        switch ($sortBy) {
            case 'relevance':
                if ($searchTerm) {
                    return $this->applyRelevanceSort($query, $searchTerm, $direction);
                }
                // Fall back to name sorting if no search term
                return $query->orderBy('name', $direction);

            case 'price':
                return $query->orderBy('price', $direction);

            case 'rating':
                return $query->leftJoin('reviews', function($join) {
                        $join->on('components.id', '=', 'reviews.component_id')
                             ->where('reviews.is_approved', '=', true);
                    })
                    ->selectRaw('components.*, AVG(reviews.rating) as avg_rating')
                    ->groupBy('components.id', 'components.name', 'components.slug', 'components.description', 
                             'components.category_id', 'components.brand', 'components.model', 'components.price', 
                             'components.stock', 'components.image', 'components.specs', 'components.is_featured', 
                             'components.is_active', 'components.created_at', 'components.updated_at')
                    ->orderByRaw('avg_rating IS NULL, avg_rating ' . $direction);

            case 'popularity':
                return $query->leftJoin('order_items', 'components.id', '=', 'order_items.component_id')
                    ->selectRaw('components.*, COUNT(order_items.id) as order_count')
                    ->groupBy('components.id', 'components.name', 'components.slug', 'components.description', 
                             'components.category_id', 'components.brand', 'components.model', 'components.price', 
                             'components.stock', 'components.image', 'components.specs', 'components.is_featured', 
                             'components.is_active', 'components.created_at', 'components.updated_at')
                    ->orderBy('order_count', $direction);

            case 'stock':
                return $query->orderBy('stock', $direction);

            case 'created_at':
                return $query->orderBy('created_at', $direction);

            case 'name':
            default:
                return $query->orderBy('name', $direction);
        }
    }

    /**
     * Apply relevance-based sorting.
     */
    protected function applyRelevanceSort(Builder $query, string $searchTerm, string $direction): Builder
    {
        $words = explode(' ', $searchTerm);
        $words = array_filter($words, fn($word) => strlen(trim($word)) > 2);

        // Build relevance score calculation
        $relevanceScore = "CASE ";
        
        // Exact name match (highest score)
        $relevanceScore .= "WHEN name = '{$searchTerm}' THEN 100 ";
        $relevanceScore .= "WHEN name LIKE '{$searchTerm}%' THEN 90 ";
        $relevanceScore .= "WHEN name LIKE '%{$searchTerm}%' THEN 80 ";
        
        // Brand exact match
        $relevanceScore .= "WHEN brand = '{$searchTerm}' THEN 70 ";
        $relevanceScore .= "WHEN brand LIKE '{$searchTerm}%' THEN 60 ";
        
        // Model match
        $relevanceScore .= "WHEN model LIKE '%{$searchTerm}%' THEN 50 ";
        
        // Description match
        $relevanceScore .= "WHEN description LIKE '%{$searchTerm}%' THEN 40 ";
        
        // Individual word matches
        foreach ($words as $word) {
            $relevanceScore .= "WHEN name LIKE '%{$word}%' THEN 30 ";
            $relevanceScore .= "WHEN brand LIKE '%{$word}%' THEN 25 ";
            $relevanceScore .= "WHEN description LIKE '%{$word}%' THEN 20 ";
        }
        
        $relevanceScore .= "ELSE 10 END";

        return $query->selectRaw("components.*, ({$relevanceScore}) as relevance_score")
            ->orderBy('relevance_score', $direction)
            ->orderBy('name', 'asc'); // Secondary sort by name
    }

    /**
     * Get search suggestions based on partial input.
     */
    public function getSearchSuggestions(string $query, int $limit = 10): Collection
    {
        $query = trim($query);
        
        if (strlen($query) < 2) {
            return collect();
        }

        $suggestions = collect();

        // Component names
        $componentNames = Component::where('is_active', true)
            ->where('name', 'like', "%{$query}%")
            ->limit($limit)
            ->pluck('name')
            ->map(fn($name) => ['type' => 'component', 'value' => $name]);

        $suggestions = $suggestions->merge($componentNames);

        // Brands
        $brands = Component::where('is_active', true)
            ->where('brand', 'like', "%{$query}%")
            ->distinct()
            ->limit($limit)
            ->pluck('brand')
            ->map(fn($brand) => ['type' => 'brand', 'value' => $brand]);

        $suggestions = $suggestions->merge($brands);

        // Categories
        $categories = ComponentCategory::where('name', 'like', "%{$query}%")
            ->limit($limit)
            ->pluck('name')
            ->map(fn($category) => ['type' => 'category', 'value' => $category]);

        $suggestions = $suggestions->merge($categories);

        return $suggestions->take($limit);
    }

    /**
     * Get available filter options for the current search.
     */
    public function getFilterOptions(array $searchParams = []): array
    {
        $this->currentSearchParams = $searchParams;
        $baseQuery = $this->searchComponents($searchParams);

        return [
            'brands' => $this->getAvailableBrands($baseQuery),
            'categories' => $this->getAvailableCategories($baseQuery),
            'price_range' => $this->getPriceRange($baseQuery),
            'specifications' => $this->getAvailableSpecifications($baseQuery),
        ];
    }

    /**
     * Get available brands for current search.
     */
    protected function getAvailableBrands(Builder $query): Collection
    {
        return $query->clone()
            ->reorder() // Remove any existing order clauses
            ->distinct()
            ->whereNotNull('brand')
            ->where('brand', '!=', '')
            ->pluck('brand')
            ->sort()
            ->values();
    }

    /**
     * Get available categories for current search.
     */
    protected function getAvailableCategories(Builder $query): Collection
    {
        // Get the base query without text search to avoid column ambiguity
        $baseQuery = Component::query()->where('is_active', true);
        
        // Apply only non-text filters to avoid column conflicts
        if (!empty($this->currentSearchParams['category'])) {
            $baseQuery = $this->applyCategoryFilter($baseQuery, $this->currentSearchParams['category']);
        }
        if (!empty($this->currentSearchParams['brands'])) {
            $baseQuery = $this->applyBrandFilter($baseQuery, $this->currentSearchParams['brands']);
        }
        if (!empty($this->currentSearchParams['price_min']) || !empty($this->currentSearchParams['price_max'])) {
            $baseQuery = $this->applyPriceFilter($baseQuery, $this->currentSearchParams['price_min'] ?? null, $this->currentSearchParams['price_max'] ?? null);
        }
        if (!empty($this->currentSearchParams['in_stock_only'])) {
            $baseQuery = $this->applyStockFilter($baseQuery);
        }
        
        return $baseQuery
            ->join('component_categories', 'components.category_id', '=', 'component_categories.id')
            ->distinct()
            ->select('component_categories.id', 'component_categories.name', 'component_categories.slug')
            ->orderBy('component_categories.name')
            ->get();
    }

    /**
     * Get price range for current search.
     */
    protected function getPriceRange(Builder $query): array
    {
        // Create a fresh query to avoid conflicts with existing SELECT clauses
        $baseQuery = Component::query()->where('is_active', true);
        
        // Apply the same filters as the main query (except sorting and text search that adds SELECT clauses)
        if (!empty($this->currentSearchParams['category'])) {
            $baseQuery = $this->applyCategoryFilter($baseQuery, $this->currentSearchParams['category']);
        }
        if (!empty($this->currentSearchParams['brands'])) {
            $baseQuery = $this->applyBrandFilter($baseQuery, $this->currentSearchParams['brands']);
        }
        if (!empty($this->currentSearchParams['specs'])) {
            $baseQuery = $this->applySpecificationFilters($baseQuery, $this->currentSearchParams['specs']);
        }
        if (!empty($this->currentSearchParams['in_stock_only'])) {
            $baseQuery = $this->applyStockFilter($baseQuery);
        }
        if (!empty($this->currentSearchParams['min_rating'])) {
            $baseQuery = $this->applyRatingFilter($baseQuery, $this->currentSearchParams['min_rating']);
        }
        
        // Apply text search if present
        if (!empty($this->currentSearchParams['search'])) {
            $baseQuery = $this->applyTextSearch($baseQuery, $this->currentSearchParams['search']);
        }
        
        $result = $baseQuery->selectRaw('MIN(price) as min_price, MAX(price) as max_price')->first();

        return [
            'min' => $result->min_price ?? 0,
            'max' => $result->max_price ?? 0,
        ];
    }

    /**
     * Get available specifications for current search.
     */
    protected function getAvailableSpecifications(Builder $query): array
    {
        $components = $query->clone()
            ->reorder() // Remove any existing order clauses
            ->get();
        $specifications = [];

        foreach ($components as $component) {
            if ($component->specs) {
                foreach ($component->specs as $key => $value) {
                    if (!isset($specifications[$key])) {
                        $specifications[$key] = [];
                    }
                    
                    if (!in_array($value, $specifications[$key])) {
                        $specifications[$key][] = $value;
                    }
                }
            }
        }

        // Sort specification values
        foreach ($specifications as $key => $values) {
            sort($specifications[$key]);
        }

        return $specifications;
    }
}