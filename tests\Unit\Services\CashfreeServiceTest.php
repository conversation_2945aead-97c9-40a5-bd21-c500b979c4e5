<?php

namespace Tests\Unit\Services;

use Tests\TestCase;
use App\Services\Payment\Gateways\CashfreeService;
use App\Models\GatewaySetting;
use App\Models\Transaction;
use App\Models\User;
use App\Services\Payment\Exceptions\GatewayConfigurationException;
use App\Services\Payment\Exceptions\InvalidPaymentDataException;
use App\Services\Payment\Exceptions\PaymentGatewayException;
use App\Services\Payment\Exceptions\WebhookVerificationException;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Mockery;

class CashfreeServiceTest extends TestCase
{
    use DatabaseTransactions;

    protected CashfreeService $cashfreeService;
    protected GatewaySetting $gatewaySetting;
    protected User $user;
    protected Transaction $transaction;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test user
        $this->user = User::factory()->create();

        // Create gateway setting for testing
        $this->gatewaySetting = new GatewaySetting();
        $this->gatewaySetting->gateway_name = GatewaySetting::GATEWAY_CASHFREE;
        $this->gatewaySetting->is_enabled = true;
        $this->gatewaySetting->is_test_mode = true;
        $this->gatewaySetting->settings = [
            'app_id' => 'test_app_id',
            'secret_key' => 'test_secret_key',
            'client_id' => 'test_client_id',
            'client_secret' => 'test_client_secret'
        ];
        $this->gatewaySetting->save();

        // Create test transaction
        $this->transaction = Transaction::create([
            'user_id' => $this->user->id,
            'gateway_name' => GatewaySetting::GATEWAY_CASHFREE,
            'amount' => 100.00,
            'currency' => 'INR',
            'status' => Transaction::STATUS_PENDING,
            'transaction_id' => 'TXN_' . uniqid(),
        ]);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_constructor_loads_configuration_successfully()
    {
        $service = new CashfreeService();
        
        $this->assertEquals(GatewaySetting::GATEWAY_CASHFREE, $service->getGatewayName());
        $this->assertTrue($service->isEnabled());
    }

    public function test_constructor_throws_exception_when_configuration_not_found()
    {
        // Delete the gateway setting
        $this->gatewaySetting->delete();

        $this->expectException(GatewayConfigurationException::class);
        $this->expectExceptionMessage('Cashfree gateway configuration not found');

        new CashfreeService();
    }

    public function test_constructor_throws_exception_when_credentials_missing()
    {
        // Update settings to remove credentials
        $this->gatewaySetting->forceFill([
            'settings' => json_encode([
                'app_id' => '',
                'secret_key' => '',
                'client_id' => 'test_client_id',
                'client_secret' => 'test_client_secret'
            ])
        ]);
        $this->gatewaySetting->save();

        $this->expectException(GatewayConfigurationException::class);
        $this->expectExceptionMessage('Cashfree API credentials not configured');

        new CashfreeService();
    }

    public function test_create_payment_success()
    {
        // Mock HTTP response
        Http::fake([
            'https://sandbox.cashfree.com/pg/orders' => Http::response([
                'cf_order_id' => 'order_test123',
                'payment_session_id' => 'session_test123',
                'order_status' => 'ACTIVE',
                'payment_link' => 'https://payments.cashfree.com/links/test123',
                'order_token' => 'token_test123'
            ], 200)
        ]);

        $service = new CashfreeService();

        $paymentData = [
            'amount' => 100.00,
            'currency' => 'INR',
            'transaction_id' => $this->transaction->transaction_id,
            'customer_name' => 'Test Customer',
            'customer_email' => '<EMAIL>',
            'customer_phone' => '9999999999',
            'return_url' => 'https://example.com/return',
            'notify_url' => 'https://example.com/notify'
        ];

        $result = $service->createPayment($paymentData);

        $this->assertTrue($result['success']);
        $this->assertEquals('order_test123', $result['cf_order_id']);
        $this->assertEquals('session_test123', $result['payment_session_id']);
        $this->assertEquals('ACTIVE', $result['order_status']);
        $this->assertTrue($result['test_mode']);

        // Verify HTTP request was made with correct data
        Http::assertSent(function ($request) {
            $data = $request->data();
            return $request->url() === 'https://sandbox.cashfree.com/pg/orders' &&
                   $request->method() === 'POST' &&
                   $data['order_id'] === $this->transaction->transaction_id &&
                   $data['order_amount'] === '100.00' &&
                   $data['order_currency'] === 'INR' &&
                   $data['customer_details']['customer_id'] === (string) $this->user->id;
        });
    }

    public function test_create_payment_validates_required_fields()
    {
        $service = new CashfreeService();

        $this->expectException(InvalidPaymentDataException::class);
        $this->expectExceptionMessage('Missing required field: amount');

        $service->createPayment([
            'currency' => 'INR',
            'transaction_id' => $this->transaction->transaction_id,
        ]);
    }

    public function test_create_payment_validates_amount()
    {
        $service = new CashfreeService();

        $this->expectException(InvalidPaymentDataException::class);
        $this->expectExceptionMessage('Amount must be a positive number');

        $service->createPayment([
            'amount' => -100,
            'currency' => 'INR',
            'transaction_id' => $this->transaction->transaction_id,
        ]);
    }

    public function test_create_payment_validates_currency()
    {
        $service = new CashfreeService();

        $this->expectException(InvalidPaymentDataException::class);
        $this->expectExceptionMessage('Currency must be a 3-character code');

        $service->createPayment([
            'amount' => 100.00,
            'currency' => 'INVALID',
            'transaction_id' => $this->transaction->transaction_id,
        ]);
    }

    public function test_create_payment_handles_transaction_not_found()
    {
        $service = new CashfreeService();

        $this->expectException(InvalidPaymentDataException::class);
        $this->expectExceptionMessage('Transaction not found');

        $service->createPayment([
            'amount' => 100.00,
            'currency' => 'INR',
            'transaction_id' => 'invalid_transaction_id',
        ]);
    }

    public function test_create_payment_handles_api_error()
    {
        // Mock HTTP error response
        Http::fake([
            'https://sandbox.cashfree.com/pg/orders' => Http::response([
                'message' => 'Invalid request',
                'code' => 'invalid_request_error'
            ], 400)
        ]);

        $service = new CashfreeService();

        $this->expectException(PaymentGatewayException::class);
        $this->expectExceptionMessage('Failed to create Cashfree payment session: Invalid request');

        $service->createPayment([
            'amount' => 100.00,
            'currency' => 'INR',
            'transaction_id' => $this->transaction->transaction_id,
            'return_url' => 'https://example.com/return',
            'notify_url' => 'https://example.com/notify'
        ]);
    }

    public function test_create_payment_handles_http_exception()
    {
        // Mock HTTP exception
        Http::fake([
            'https://sandbox.cashfree.com/pg/orders' => function () {
                throw new \Exception('Network error');
            }
        ]);

        $service = new CashfreeService();

        $this->expectException(PaymentGatewayException::class);
        $this->expectExceptionMessage('Failed to create Cashfree payment session: API request failed: Network error');

        $service->createPayment([
            'amount' => 100.00,
            'currency' => 'INR',
            'transaction_id' => $this->transaction->transaction_id,
            'return_url' => 'https://example.com/return',
            'notify_url' => 'https://example.com/notify'
        ]);
    }

    public function test_verify_payment_success()
    {
        // Mock HTTP response for order details
        Http::fake([
            "https://sandbox.cashfree.com/pg/orders/{$this->transaction->transaction_id}" => Http::response([
                'cf_order_id' => 'order_test123',
                'order_id' => $this->transaction->transaction_id,
                'order_amount' => 100.00,
                'order_currency' => 'INR',
                'order_status' => 'PAID',
                'payments' => [
                    [
                        'cf_payment_id' => 'payment_test123',
                        'payment_status' => 'SUCCESS',
                        'payment_amount' => 100.00,
                        'payment_currency' => 'INR',
                        'payment_method' => 'card',
                        'bank_reference' => 'bank_ref_123',
                        'auth_id' => 'auth_123'
                    ]
                ]
            ], 200)
        ]);

        $service = new CashfreeService();

        $result = $service->verifyPayment($this->transaction->transaction_id, []);

        $this->assertTrue($result);

        // Check transaction was updated
        $this->transaction->refresh();
        $this->assertEquals(Transaction::STATUS_COMPLETED, $this->transaction->status);
        $this->assertEquals('order_test123', $this->transaction->gateway_transaction_id);
        $this->assertArrayHasKey('cashfree_order_id', $this->transaction->payment_details);
        $this->assertEquals('PAID', $this->transaction->payment_details['order_status']);
    }

    public function test_verify_payment_fails_with_invalid_transaction()
    {
        $service = new CashfreeService();

        $result = $service->verifyPayment('invalid_transaction_id', []);

        $this->assertFalse($result);
    }

    public function test_verify_payment_handles_api_error()
    {
        // Mock HTTP error response
        Http::fake([
            "https://sandbox.cashfree.com/pg/orders/{$this->transaction->transaction_id}" => Http::response([
                'message' => 'Order not found'
            ], 404)
        ]);

        $service = new CashfreeService();

        $result = $service->verifyPayment($this->transaction->transaction_id, []);

        $this->assertFalse($result);
    }

    public function test_verify_payment_handles_order_id_mismatch()
    {
        // Mock HTTP response with different order ID
        Http::fake([
            "https://sandbox.cashfree.com/pg/orders/{$this->transaction->transaction_id}" => Http::response([
                'cf_order_id' => 'order_test123',
                'order_id' => 'different_order_id',
                'order_amount' => 100.00,
                'order_currency' => 'INR',
                'order_status' => 'PAID'
            ], 200)
        ]);

        $service = new CashfreeService();

        $result = $service->verifyPayment($this->transaction->transaction_id, []);

        $this->assertFalse($result);
    }

    public function test_verify_payment_handles_amount_mismatch()
    {
        // Mock HTTP response with different amount
        Http::fake([
            "https://sandbox.cashfree.com/pg/orders/{$this->transaction->transaction_id}" => Http::response([
                'cf_order_id' => 'order_test123',
                'order_id' => $this->transaction->transaction_id,
                'order_amount' => 200.00, // Different amount
                'order_currency' => 'INR',
                'order_status' => 'PAID'
            ], 200)
        ]);

        $service = new CashfreeService();

        $result = $service->verifyPayment($this->transaction->transaction_id, []);

        $this->assertFalse($result);
    }

    public function test_handle_webhook_payment_success()
    {
        $service = new CashfreeService();

        // Create webhook payload
        $webhookPayload = [
            'type' => 'PAYMENT_SUCCESS_WEBHOOK',
            'data' => [
                'order' => [
                    'order_id' => $this->transaction->transaction_id,
                    'cf_order_id' => 'order_test123',
                    'order_status' => 'PAID'
                ],
                'payment' => [
                    'cf_payment_id' => 'payment_test123',
                    'payment_status' => 'SUCCESS',
                    'payment_amount' => 100.00,
                    'payment_method' => 'card',
                    'bank_reference' => 'bank_ref_123',
                    'auth_id' => 'auth_123'
                ]
            ]
        ];

        // Mock webhook signature verification
        $timestamp = time();
        $signatureData = $timestamp . json_encode($webhookPayload);
        $expectedSignature = base64_encode(hash_hmac('sha256', $signatureData, 'test_secret_key', true));
        
        $_SERVER['HTTP_X_WEBHOOK_SIGNATURE'] = $expectedSignature;
        $_SERVER['HTTP_X_WEBHOOK_TIMESTAMP'] = $timestamp;

        $result = $service->handleWebhook($webhookPayload);

        $this->assertTrue($result['success']);
        $this->assertEquals('Payment success processed', $result['message']);

        // Check transaction was updated
        $this->transaction->refresh();
        $this->assertEquals(Transaction::STATUS_COMPLETED, $this->transaction->status);
        $this->assertEquals('order_test123', $this->transaction->gateway_transaction_id);
        $this->assertTrue($this->transaction->webhook_verified);
        $this->assertArrayHasKey('cf_payment_id', $this->transaction->payment_details);
    }

    public function test_handle_webhook_payment_failed()
    {
        $service = new CashfreeService();

        // Create webhook payload
        $webhookPayload = [
            'type' => 'PAYMENT_FAILED_WEBHOOK',
            'data' => [
                'order' => [
                    'order_id' => $this->transaction->transaction_id,
                    'cf_order_id' => 'order_test123',
                    'order_status' => 'FAILED'
                ],
                'payment' => [
                    'cf_payment_id' => 'payment_test123',
                    'payment_status' => 'FAILED',
                    'payment_message' => 'Payment failed due to insufficient funds',
                    'error_details' => 'Insufficient balance'
                ]
            ]
        ];

        // Mock webhook signature verification
        $timestamp = time();
        $signatureData = $timestamp . json_encode($webhookPayload);
        $expectedSignature = base64_encode(hash_hmac('sha256', $signatureData, 'test_secret_key', true));
        
        $_SERVER['HTTP_X_WEBHOOK_SIGNATURE'] = $expectedSignature;
        $_SERVER['HTTP_X_WEBHOOK_TIMESTAMP'] = $timestamp;

        $result = $service->handleWebhook($webhookPayload);

        $this->assertTrue($result['success']);
        $this->assertEquals('Payment failure processed', $result['message']);

        // Check transaction was updated
        $this->transaction->refresh();
        $this->assertEquals(Transaction::STATUS_FAILED, $this->transaction->status);
        $this->assertEquals('Payment failed due to insufficient funds', $this->transaction->failure_reason);
        $this->assertTrue($this->transaction->webhook_verified);
    }

    public function test_handle_webhook_payment_dropped()
    {
        $service = new CashfreeService();

        // Create webhook payload
        $webhookPayload = [
            'type' => 'PAYMENT_USER_DROPPED_WEBHOOK',
            'data' => [
                'order' => [
                    'order_id' => $this->transaction->transaction_id,
                    'cf_order_id' => 'order_test123',
                    'order_status' => 'TERMINATED'
                ]
            ]
        ];

        // Mock webhook signature verification
        $timestamp = time();
        $signatureData = $timestamp . json_encode($webhookPayload);
        $expectedSignature = base64_encode(hash_hmac('sha256', $signatureData, 'test_secret_key', true));
        
        $_SERVER['HTTP_X_WEBHOOK_SIGNATURE'] = $expectedSignature;
        $_SERVER['HTTP_X_WEBHOOK_TIMESTAMP'] = $timestamp;

        $result = $service->handleWebhook($webhookPayload);

        $this->assertTrue($result['success']);
        $this->assertEquals('Payment cancellation processed', $result['message']);

        // Check transaction was updated
        $this->transaction->refresh();
        $this->assertEquals(Transaction::STATUS_CANCELLED, $this->transaction->status);
        $this->assertEquals('Payment cancelled by user', $this->transaction->failure_reason);
        $this->assertTrue($this->transaction->webhook_verified);
    }

    public function test_handle_webhook_unhandled_type()
    {
        $service = new CashfreeService();

        // Create webhook payload
        $webhookPayload = [
            'type' => 'UNKNOWN_WEBHOOK_TYPE',
            'data' => []
        ];

        // Mock webhook signature verification
        $timestamp = time();
        $signatureData = $timestamp . json_encode($webhookPayload);
        $expectedSignature = base64_encode(hash_hmac('sha256', $signatureData, 'test_secret_key', true));
        
        $_SERVER['HTTP_X_WEBHOOK_SIGNATURE'] = $expectedSignature;
        $_SERVER['HTTP_X_WEBHOOK_TIMESTAMP'] = $timestamp;

        $result = $service->handleWebhook($webhookPayload);

        $this->assertTrue($result['success']);
        $this->assertEquals('Webhook type not handled', $result['message']);
        $this->assertEquals('UNKNOWN_WEBHOOK_TYPE', $result['type']);
    }

    public function test_handle_webhook_fails_missing_signature()
    {
        $this->expectException(WebhookVerificationException::class);
        $this->expectExceptionMessage('Webhook signature missing');

        $service = new CashfreeService();

        // Remove any existing signature
        if (isset($_SERVER['HTTP_X_WEBHOOK_SIGNATURE'])) {
            unset($_SERVER['HTTP_X_WEBHOOK_SIGNATURE']);
        }

        $webhookPayload = [
            'type' => 'PAYMENT_SUCCESS_WEBHOOK',
            'data' => []
        ];

        $service->handleWebhook($webhookPayload);
    }

    public function test_handle_webhook_fails_missing_timestamp()
    {
        $this->expectException(WebhookVerificationException::class);
        $this->expectExceptionMessage('Webhook timestamp missing');

        $service = new CashfreeService();

        $_SERVER['HTTP_X_WEBHOOK_SIGNATURE'] = 'test_signature';
        
        // Remove any existing timestamp
        if (isset($_SERVER['HTTP_X_WEBHOOK_TIMESTAMP'])) {
            unset($_SERVER['HTTP_X_WEBHOOK_TIMESTAMP']);
        }

        $webhookPayload = [
            'type' => 'PAYMENT_SUCCESS_WEBHOOK',
            'data' => []
        ];

        $service->handleWebhook($webhookPayload);
    }

    public function test_handle_webhook_fails_invalid_signature()
    {
        $this->expectException(WebhookVerificationException::class);
        $this->expectExceptionMessage('Webhook signature verification failed');

        $service = new CashfreeService();

        $_SERVER['HTTP_X_WEBHOOK_SIGNATURE'] = 'invalid_signature';
        $_SERVER['HTTP_X_WEBHOOK_TIMESTAMP'] = time();

        $webhookPayload = [
            'type' => 'PAYMENT_SUCCESS_WEBHOOK',
            'data' => []
        ];

        $service->handleWebhook($webhookPayload);
    }

    public function test_handle_webhook_transaction_not_found()
    {
        $service = new CashfreeService();

        // Create webhook payload with non-existent transaction
        $webhookPayload = [
            'type' => 'PAYMENT_SUCCESS_WEBHOOK',
            'data' => [
                'order' => [
                    'order_id' => 'non_existent_transaction',
                    'cf_order_id' => 'order_test123'
                ],
                'payment' => []
            ]
        ];

        // Mock webhook signature verification
        $timestamp = time();
        $signatureData = $timestamp . json_encode($webhookPayload);
        $expectedSignature = base64_encode(hash_hmac('sha256', $signatureData, 'test_secret_key', true));
        
        $_SERVER['HTTP_X_WEBHOOK_SIGNATURE'] = $expectedSignature;
        $_SERVER['HTTP_X_WEBHOOK_TIMESTAMP'] = $timestamp;

        $result = $service->handleWebhook($webhookPayload);

        $this->assertFalse($result['success']);
        $this->assertEquals('Transaction not found', $result['message']);
    }

    public function test_get_payment_status_success()
    {
        // Mock HTTP response
        Http::fake([
            "https://sandbox.cashfree.com/pg/orders/{$this->transaction->transaction_id}" => Http::response([
                'order_status' => 'PAID'
            ], 200)
        ]);

        $service = new CashfreeService();

        $status = $service->getPaymentStatus($this->transaction->transaction_id);

        $this->assertEquals(Transaction::STATUS_COMPLETED, $status);
    }

    public function test_get_payment_status_handles_api_error()
    {
        // Mock HTTP error response
        Http::fake([
            "https://sandbox.cashfree.com/pg/orders/{$this->transaction->transaction_id}" => Http::response([], 404)
        ]);

        $service = new CashfreeService();

        $status = $service->getPaymentStatus($this->transaction->transaction_id);

        $this->assertEquals(Transaction::STATUS_PENDING, $status);
    }

    public function test_get_payment_status_handles_exception()
    {
        // Mock HTTP exception
        Http::fake([
            "https://sandbox.cashfree.com/pg/orders/{$this->transaction->transaction_id}" => function () {
                throw new \Exception('Network error');
            }
        ]);

        $service = new CashfreeService();

        $status = $service->getPaymentStatus($this->transaction->transaction_id);

        $this->assertEquals(Transaction::STATUS_PENDING, $status);
    }

    public function test_gateway_name_and_enabled_status()
    {
        $service = new CashfreeService();

        $this->assertEquals(GatewaySetting::GATEWAY_CASHFREE, $service->getGatewayName());
        $this->assertTrue($service->isEnabled());

        // Disable gateway
        $this->gatewaySetting->update(['is_enabled' => false]);
        
        $service = new CashfreeService();
        $this->assertFalse($service->isEnabled());
    }

    public function test_payment_status_mapping()
    {
        $statusMappings = [
            'PAID' => Transaction::STATUS_COMPLETED,
            'ACTIVE' => Transaction::STATUS_PROCESSING,
            'EXPIRED' => Transaction::STATUS_FAILED,
            'CANCELLED' => Transaction::STATUS_FAILED,
            'TERMINATED' => Transaction::STATUS_FAILED,
            'UNKNOWN_STATUS' => Transaction::STATUS_PENDING,
        ];

        foreach ($statusMappings as $cashfreeStatus => $expectedStatus) {
            // Create a new transaction for each test to avoid conflicts
            $transaction = Transaction::create([
                'user_id' => $this->user->id,
                'gateway_name' => GatewaySetting::GATEWAY_CASHFREE,
                'amount' => 100.00,
                'currency' => 'INR',
                'status' => Transaction::STATUS_PENDING,
                'transaction_id' => 'TXN_' . uniqid(),
            ]);

            // Mock HTTP response for this specific transaction
            Http::fake([
                "https://sandbox.cashfree.com/pg/orders/{$transaction->transaction_id}" => Http::response([
                    'order_status' => $cashfreeStatus
                ], 200)
            ]);

            // Create a new service instance for each test
            $service = new CashfreeService();
            $status = $service->getPaymentStatus($transaction->transaction_id);

            $this->assertEquals($expectedStatus, $status, "Failed for Cashfree status: {$cashfreeStatus}");
        }
    }

    public function test_uses_correct_api_endpoints_for_test_and_live_mode()
    {
        // Test mode (default)
        Http::fake([
            'https://sandbox.cashfree.com/pg/orders' => Http::response([
                'cf_order_id' => 'order_test123',
                'payment_session_id' => 'session_test123',
                'order_status' => 'ACTIVE'
            ], 200)
        ]);
        
        $service = new CashfreeService();
        $service->createPayment([
            'amount' => 100.00,
            'currency' => 'INR',
            'transaction_id' => $this->transaction->transaction_id,
            'return_url' => 'https://example.com/return',
            'notify_url' => 'https://example.com/notify'
        ]);

        Http::assertSent(function ($request) {
            return str_contains($request->url(), 'sandbox.cashfree.com');
        });

        // Live mode
        $this->gatewaySetting->update(['is_test_mode' => false]);
        Http::fake([
            'https://api.cashfree.com/pg/orders' => Http::response([
                'cf_order_id' => 'order_test123',
                'payment_session_id' => 'session_test123',
                'order_status' => 'ACTIVE'
            ], 200)
        ]);
        
        $service = new CashfreeService();
        $service->createPayment([
            'amount' => 100.00,
            'currency' => 'INR',
            'transaction_id' => $this->transaction->transaction_id,
            'return_url' => 'https://example.com/return',
            'notify_url' => 'https://example.com/notify'
        ]);

        Http::assertSent(function ($request) {
            return str_contains($request->url(), 'api.cashfree.com');
        });
    }

    public function test_api_request_includes_correct_headers()
    {
        Http::fake([
            'https://sandbox.cashfree.com/pg/orders' => Http::response([
                'cf_order_id' => 'order_test123',
                'payment_session_id' => 'session_test123',
                'order_status' => 'ACTIVE'
            ], 200)
        ]);
        
        $service = new CashfreeService();
        $service->createPayment([
            'amount' => 100.00,
            'currency' => 'INR',
            'transaction_id' => $this->transaction->transaction_id,
            'return_url' => 'https://example.com/return',
            'notify_url' => 'https://example.com/notify'
        ]);

        Http::assertSent(function ($request) {
            $headers = $request->headers();
            return $headers['Accept'][0] === 'application/json' &&
                   $headers['Content-Type'][0] === 'application/json' &&
                   $headers['x-api-version'][0] === '2023-08-01' &&
                   $headers['x-client-id'][0] === 'test_app_id' &&
                   $headers['x-client-secret'][0] === 'test_secret_key';
        });
    }
}