<?php

namespace Tests\Feature;

use App\Models\Component;
use App\Models\ComponentCategory;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Livewire\Livewire;
use Tests\TestCase;

class ProductBrowsingTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test categories
        $this->cpuCategory = ComponentCategory::factory()->create([
            'name' => 'CPU',
            'slug' => 'cpu',
            'display_order' => 1
        ]);
        
        $this->gpuCategory = ComponentCategory::factory()->create([
            'name' => 'GPU',
            'slug' => 'gpu',
            'display_order' => 2
        ]);
        
        // Create test components
        $this->intelCpu = Component::factory()->create([
            'name' => 'Intel Core i7-12700K',
            'category_id' => $this->cpuCategory->id,
            'brand' => 'Intel',
            'model' => 'i7-12700K',
            'price' => 399.99,
            'stock' => 10,
            'is_active' => true,
            'specs' => [
                'cores' => '12',
                'threads' => '20',
                'base_clock' => '3.6 GHz'
            ]
        ]);
        
        $this->amdCpu = Component::factory()->create([
            'name' => 'AMD Ryzen 7 5800X',
            'category_id' => $this->cpuCategory->id,
            'brand' => 'AMD',
            'model' => '5800X',
            'price' => 299.99,
            'stock' => 5,
            'is_active' => true,
            'specs' => [
                'cores' => '8',
                'threads' => '16',
                'base_clock' => '3.8 GHz'
            ]
        ]);
        
        $this->nvidiaGpu = Component::factory()->create([
            'name' => 'NVIDIA RTX 4070',
            'category_id' => $this->gpuCategory->id,
            'brand' => 'NVIDIA',
            'model' => 'RTX 4070',
            'price' => 599.99,
            'stock' => 0, // Out of stock
            'is_active' => true,
            'specs' => [
                'memory' => '12GB GDDR6X',
                'memory_bus' => '192-bit'
            ]
        ]);
    }

    /** @test */
    public function it_displays_product_list_component()
    {
        $response = $this->get(route('shop.index'));
        
        $response->assertStatus(200);
        $response->assertSeeLivewire('shop.product-list');
    }

    /** @test */
    public function it_displays_all_active_components_by_default()
    {
        Livewire::test('shop.product-list')
            ->assertSee('Intel Core i7-12700K')
            ->assertSee('AMD Ryzen 7 5800X')
            ->assertSee('NVIDIA RTX 4070')
            ->assertSee('$399.99')
            ->assertSee('$299.99')
            ->assertSee('$599.99');
    }

    /** @test */
    public function it_filters_components_by_category()
    {
        Livewire::test('shop.product-list')
            ->call('selectCategory', 'cpu')
            ->assertSee('Intel Core i7-12700K')
            ->assertSee('AMD Ryzen 7 5800X')
            ->assertDontSee('NVIDIA RTX 4070');
    }

    /** @test */
    public function it_clears_category_filter()
    {
        Livewire::test('shop.product-list')
            ->call('selectCategory', 'cpu')
            ->call('clearCategory')
            ->assertSee('Intel Core i7-12700K')
            ->assertSee('AMD Ryzen 7 5800X')
            ->assertSee('NVIDIA RTX 4070');
    }

    /** @test */
    public function it_searches_components_by_name()
    {
        Livewire::test('shop.product-list')
            ->set('search', 'Intel')
            ->assertSee('Intel Core i7-12700K')
            ->assertDontSee('AMD Ryzen 7 5800X')
            ->assertDontSee('NVIDIA RTX 4070');
    }

    /** @test */
    public function it_searches_components_by_brand()
    {
        Livewire::test('shop.product-list')
            ->set('search', 'AMD')
            ->assertSee('AMD Ryzen 7 5800X')
            ->assertDontSee('Intel Core i7-12700K')
            ->assertDontSee('NVIDIA RTX 4070');
    }

    /** @test */
    public function it_filters_components_by_brand()
    {
        Livewire::test('shop.product-list')
            ->set('selectedBrands', ['Intel'])
            ->assertSee('Intel Core i7-12700K')
            ->assertDontSee('AMD Ryzen 7 5800X')
            ->assertDontSee('NVIDIA RTX 4070');
    }

    /** @test */
    public function it_filters_components_by_multiple_brands()
    {
        Livewire::test('shop.product-list')
            ->set('selectedBrands', ['Intel', 'AMD'])
            ->assertSee('Intel Core i7-12700K')
            ->assertSee('AMD Ryzen 7 5800X')
            ->assertDontSee('NVIDIA RTX 4070');
    }

    /** @test */
    public function it_filters_components_by_price_range()
    {
        Livewire::test('shop.product-list')
            ->set('priceMin', 300)
            ->set('priceMax', 400)
            ->assertSee('Intel Core i7-12700K')
            ->assertDontSee('AMD Ryzen 7 5800X') // $299.99 is below min
            ->assertDontSee('NVIDIA RTX 4070'); // $599.99 is above max
    }

    /** @test */
    public function it_filters_components_by_minimum_price_only()
    {
        Livewire::test('shop.product-list')
            ->set('priceMin', 400)
            ->assertDontSee('Intel Core i7-12700K') // $399.99 < 400
            ->assertSee('NVIDIA RTX 4070') // $599.99 >= 400
            ->assertDontSee('AMD Ryzen 7 5800X'); // $299.99 < 400
    }

    /** @test */
    public function it_filters_components_by_maximum_price_only()
    {
        Livewire::test('shop.product-list')
            ->set('priceMax', 400)
            ->assertSee('Intel Core i7-12700K')
            ->assertSee('AMD Ryzen 7 5800X')
            ->assertDontSee('NVIDIA RTX 4070');
    }

    /** @test */
    public function it_filters_in_stock_components_only()
    {
        Livewire::test('shop.product-list')
            ->set('inStockOnly', true)
            ->assertSee('Intel Core i7-12700K')
            ->assertSee('AMD Ryzen 7 5800X')
            ->assertDontSee('NVIDIA RTX 4070'); // Out of stock
    }

    /** @test */
    public function it_sorts_components_by_name_ascending()
    {
        Livewire::test('shop.product-list')
            ->set('sortBy', 'name')
            ->set('sortDirection', 'asc')
            ->assertSeeInOrder(['AMD Ryzen 7 5800X', 'Intel Core i7-12700K', 'NVIDIA RTX 4070']);
    }

    /** @test */
    public function it_sorts_components_by_name_descending()
    {
        Livewire::test('shop.product-list')
            ->set('sortBy', 'name')
            ->set('sortDirection', 'desc')
            ->assertSeeInOrder(['NVIDIA RTX 4070', 'Intel Core i7-12700K', 'AMD Ryzen 7 5800X']);
    }

    /** @test */
    public function it_sorts_components_by_price_ascending()
    {
        Livewire::test('shop.product-list')
            ->set('sortBy', 'price')
            ->set('sortDirection', 'asc')
            ->assertSeeInOrder(['$299.99', '$399.99', '$599.99']);
    }

    /** @test */
    public function it_sorts_components_by_price_descending()
    {
        Livewire::test('shop.product-list')
            ->set('sortBy', 'price')
            ->set('sortDirection', 'desc')
            ->assertSeeInOrder(['$599.99', '$399.99', '$299.99']);
    }

    /** @test */
    public function it_displays_stock_status_correctly()
    {
        Livewire::test('shop.product-list')
            ->assertSee('In Stock (10 available)') // Intel CPU
            ->assertSee('In Stock (5 available)')  // AMD CPU
            ->assertSee('Out of Stock');           // NVIDIA GPU
    }

    /** @test */
    public function it_displays_component_specifications()
    {
        Livewire::test('shop.product-list')
            ->assertSee('12') // Intel CPU cores
            ->assertSee('8')  // AMD CPU cores
            ->assertSee('12GB GDDR6X'); // NVIDIA GPU memory
    }

    /** @test */
    public function it_adds_component_to_cart()
    {
        $user = User::factory()->create();
        $this->actingAs($user);

        Livewire::test('shop.product-list')
            ->call('addToCart', $this->intelCpu->id)
            ->assertDispatched('cartUpdated');

        // Check that the item was actually added to the cart
        $this->assertDatabaseHas('carts', ['user_id' => $user->id]);
        $this->assertDatabaseHas('cart_items', ['component_id' => $this->intelCpu->id]);
    }

    /** @test */
    public function it_prevents_adding_out_of_stock_component_to_cart()
    {
        $user = User::factory()->create();
        $this->actingAs($user);

        Livewire::test('shop.product-list')
            ->call('addToCart', $this->nvidiaGpu->id);

        // Check that the out of stock item was NOT added to the cart
        $this->assertDatabaseMissing('cart_items', ['component_id' => $this->nvidiaGpu->id]);
    }

    /** @test */
    public function it_displays_no_products_message_when_no_results()
    {
        Livewire::test('shop.product-list')
            ->set('search', 'nonexistent product')
            ->assertSee('No products found')
            ->assertSee('Try adjusting your search or filter criteria.');
    }

    /** @test */
    public function it_updates_available_brands_when_category_changes()
    {
        Livewire::test('shop.product-list')
            ->call('selectCategory', 'cpu')
            ->assertSet('availableBrands', ['AMD', 'Intel'])
            ->call('selectCategory', 'gpu')
            ->assertSet('availableBrands', ['NVIDIA']);
    }

    /** @test */
    public function it_resets_page_when_filters_change()
    {
        // Create enough components to trigger pagination
        Component::factory()->count(20)->create([
            'category_id' => $this->cpuCategory->id,
            'is_active' => true
        ]);

        $component = Livewire::test('shop.product-list');
        
        // Go to page 2 by calling nextPage
        $component->call('nextPage');
        
        // Then set a search filter which should reset to page 1
        $component->set('search', 'Intel');
        
        // Check that we're back on page 1 by checking the URL query string
        $component->assertSet('search', 'Intel');
    }

    /** @test */
    public function it_persists_filters_in_query_string()
    {
        Livewire::test('shop.product-list')
            ->set('search', 'Intel')
            ->set('selectedCategory', 'cpu')
            ->set('priceMin', 100)
            ->set('priceMax', 500)
            ->set('inStockOnly', true)
            ->set('sortBy', 'price')
            ->set('sortDirection', 'desc')
            ->assertSet('search', 'Intel')
            ->assertSet('selectedCategory', 'cpu')
            ->assertSet('priceMin', 100)
            ->assertSet('priceMax', 500)
            ->assertSet('inStockOnly', true)
            ->assertSet('sortBy', 'price')
            ->assertSet('sortDirection', 'desc');
    }

    /** @test */
    public function it_displays_product_cards_correctly()
    {
        Livewire::test('shop.product-list')
            ->assertSee('Intel Core i7-12700K')
            ->assertSee('AMD Ryzen 7 5800X')
            ->assertSee('NVIDIA RTX 4070')
            ->assertSee('Add to Cart');
    }

    /** @test */
    public function product_card_displays_component_information()
    {
        Livewire::test('shop.product-card', ['component' => $this->intelCpu])
            ->assertSee('Intel Core i7-12700K')
            ->assertSee('Intel')
            ->assertSee('i7-12700K')
            ->assertSee('$399.99')
            ->assertSee('In Stock (10 available)')
            ->assertSee('CPU');
    }

    /** @test */
    public function product_card_shows_quick_view_modal()
    {
        Livewire::test('shop.product-card', ['component' => $this->intelCpu])
            ->call('toggleQuickView')
            ->assertSet('showQuickView', true)
            ->assertSee('Specifications:')
            ->call('toggleQuickView')
            ->assertSet('showQuickView', false);
    }

    /** @test */
    public function product_card_manages_quantity_correctly()
    {
        Livewire::test('shop.product-card', ['component' => $this->intelCpu])
            ->assertSet('quantity', 1)
            ->call('incrementQuantity')
            ->assertSet('quantity', 2)
            ->call('decrementQuantity')
            ->assertSet('quantity', 1)
            ->call('decrementQuantity') // Should not go below 1
            ->assertSet('quantity', 1);
    }

    /** @test */
    public function product_card_respects_stock_limits()
    {
        $lowStockComponent = Component::factory()->create([
            'stock' => 2,
            'is_active' => true
        ]);

        Livewire::test('shop.product-card', ['component' => $lowStockComponent])
            ->call('incrementQuantity')
            ->call('incrementQuantity')
            ->assertSet('quantity', 2)
            ->call('incrementQuantity') // Should not exceed stock
            ->assertSet('quantity', 2);
    }

    /** @test */
    public function product_card_adds_to_cart_with_quantity()
    {
        $user = User::factory()->create();
        $this->actingAs($user);

        $livewireTest = Livewire::test('shop.product-card', ['component' => $this->intelCpu])
            ->set('quantity', 3)
            ->call('addToCart')
            ->assertDispatched('cartUpdated')
            ->assertSet('quantity', 1); // Should reset after adding
        
        // Check that the item was actually added to the cart
        $this->assertDatabaseHas('carts', ['user_id' => $user->id]);
        $this->assertDatabaseHas('cart_items', [
            'component_id' => $this->intelCpu->id,
            'quantity' => 3
        ]);
    }
}