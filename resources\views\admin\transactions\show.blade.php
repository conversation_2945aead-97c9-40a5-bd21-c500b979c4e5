@if(request()->ajax())
    @include('admin.transactions._details')
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                            @switch($transaction->status)
                                @case('completed')
                                    bg-green-100 text-green-800
                                    @break
                                @case('failed')
                                    bg-red-100 text-red-800
                                    @break
                                @case('pending')
                                    bg-yellow-100 text-yellow-800
                                    @break
                                @case('processing')
                                    bg-blue-100 text-blue-800
                                    @break
                                @case('cancelled')
                                    bg-gray-100 text-gray-800
                                    @break
                                @default
                                    bg-gray-100 text-gray-800
                            @endswitch
                        ">
                            {{ ucfirst($transaction->status) }}
                        </span>
                    </dd>
                </div>
                <div class="flex justify-between">
                    <dt class="text-sm text-gray-600">Webhook Verified:</dt>
                    <dd class="text-sm font-medium">
                        @if($transaction->webhook_verified)
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                Yes
                            </span>
                        @else
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                                </svg>
                                No
                            </span>
                        @endif
                    </dd>
                </div>
            </dl>
        </div>

        <div class="bg-gray-50 rounded-lg p-4">
            <h4 class="text-sm font-medium text-gray-900 mb-3">User Information</h4>
            <dl class="space-y-2">
                <div class="flex justify-between">
                    <dt class="text-sm text-gray-600">Name:</dt>
                    <dd class="text-sm font-medium text-gray-900">{{ $transaction->user->name ?? 'Unknown' }}</dd>
                </div>
                <div class="flex justify-between">
                    <dt class="text-sm text-gray-600">Email:</dt>
                    <dd class="text-sm font-medium text-gray-900">{{ $transaction->user->email ?? 'No email' }}</dd>
                </div>
                <div class="flex justify-between">
                    <dt class="text-sm text-gray-600">User ID:</dt>
                    <dd class="text-sm font-medium text-gray-900">{{ $transaction->user_id }}</dd>
                </div>
            </dl>
        </div>
    </div>

    <!-- Timestamps -->
    <div class="bg-gray-50 rounded-lg p-4 mb-6">
        <h4 class="text-sm font-medium text-gray-900 mb-3">Timeline</h4>
        <dl class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="flex justify-between">
                <dt class="text-sm text-gray-600">Created:</dt>
                <dd class="text-sm font-medium text-gray-900">
                    {{ $transaction->created_at->format('M d, Y h:i A') }}
                    <span class="text-gray-500">({{ $transaction->created_at->diffForHumans() }})</span>
                </dd>
            </div>
            <div class="flex justify-between">
                <dt class="text-sm text-gray-600">Last Updated:</dt>
                <dd class="text-sm font-medium text-gray-900">
                    {{ $transaction->updated_at->format('M d, Y h:i A') }}
                    <span class="text-gray-500">({{ $transaction->updated_at->diffForHumans() }})</span>
                </dd>
            </div>
        </dl>
    </div>

    @if($transaction->failure_reason)
        <!-- Failure Information -->
        <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
            <h4 class="text-sm font-medium text-red-800 mb-2">Failure Reason</h4>
            <p class="text-sm text-red-700">{{ $transaction->failure_reason }}</p>
        </div>
    @endif

    @if($transaction->payment_details && !empty($transaction->payment_details))
        <!-- Payment Details -->
        <div class="bg-gray-50 rounded-lg p-4 mb-6">
            <h4 class="text-sm font-medium text-gray-900 mb-3">Payment Details</h4>
            <div class="bg-white rounded border p-3">
                <pre class="text-xs text-gray-700 whitespace-pre-wrap">{{ json_encode($transaction->payment_details, JSON_PRETTY_PRINT) }}</pre>
            </div>
        </div>
    @endif

    <!-- Actions -->
    <div class="flex justify-end space-x-3 pt-4 border-t border-gray-200">
        <button onclick="downloadReceipt('{{ $transaction->id }}')" 
                class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 transition duration-200 flex items-center">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
            Download Receipt
        </button>
        
        @if($transaction->status === 'pending' || $transaction->status === 'processing')
            <button onclick="refreshTransactionStatus('{{ $transaction->id }}')" 
                    class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition duration-200 flex items-center">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
                Refresh Status
            </button>
        @endif
        
        <button onclick="closeTransactionModal()" 
                class="bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700 transition duration-200">
            Close
        </button>
    </div>

    <script>
    async function refreshTransactionStatus(transactionId) {
        showLoading('Refreshing transaction status...');
        
        try {
            const response = await fetch(`/admin/transactions/${transactionId}/refresh-status`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                }
            });
            
            const data = await response.json();
            
            if (response.ok && data.success) {
                // Reload the modal content
                viewTransaction(transactionId);
                // Also refresh the main table if needed
                if (data.status_changed) {
                    setTimeout(() => {
                        window.location.reload();
                    }, 1000);
                }
            } else {
                alert('Failed to refresh status: ' + (data.message || 'Unknown error'));
            }
        } catch (error) {
            alert('Error: ' + error.message);
        } finally {
            hideLoading();
        }
    }
    </script>
@else
    @extends('layouts.admin')

    @section('title', 'Transaction Details')

    @section('content')
    <div class="min-h-screen bg-gray-50 py-12">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Header -->
            <div class="mb-8">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <a href="{{ route('admin.transactions.index') }}" class="mr-4 text-gray-500 hover:text-gray-700">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                            </svg>
                        </a>
                        <div>
                            <h1 class="text-3xl font-bold text-gray-900">Transaction Details</h1>
                            <p class="text-gray-600">{{ $transaction->transaction_id }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-md p-6">
                @include('admin.transactions._details')
            </div>
        </div>
    </div>
    @endsection
@endif