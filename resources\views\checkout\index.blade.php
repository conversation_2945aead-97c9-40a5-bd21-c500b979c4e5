@extends('layouts.app')

@section('title', 'Checkout')

@section('content')
<div class="py-12">
    <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg p-6">
            <h1 class="text-2xl font-semibold mb-6">Checkout</h1>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <!-- Order Summary -->
                <div class="md:col-span-1">
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h2 class="text-lg font-medium mb-4">Order Summary</h2>
                        
                        <div class="space-y-3 mb-4">
                            @foreach($cart->items as $item)
                                <div class="flex justify-between">
                                    <div>
                                        <span class="font-medium">{{ $item->component->name }}</span>
                                        <span class="text-gray-500 text-sm block">Qty: {{ $item->quantity }}</span>
                                    </div>
                                    <span>${{ number_format($item->price * $item->quantity, 2) }}</span>
                                </div>
                            @endforeach
                        </div>
                        
                        <div class="border-t pt-3">
                            <div class="flex justify-between font-bold">
                                <span>Total:</span>
                                <span>${{ number_format($total, 2) }}</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Checkout Form -->
                <div class="md:col-span-2">
                    <form action="{{ route('checkout.process') }}" method="POST">
                        @csrf
                        
                        <div class="mb-6">
                            <h2 class="text-lg font-medium mb-4">Shipping Information</h2>
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div class="col-span-2">
                                    <label for="shipping_address" class="block text-sm font-medium text-gray-700 mb-1">Address</label>
                                    <input type="text" name="shipping_address" id="shipping_address" class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50" required>
                                </div>
                                
                                <div>
                                    <label for="shipping_city" class="block text-sm font-medium text-gray-700 mb-1">City</label>
                                    <input type="text" name="shipping_city" id="shipping_city" class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50" required>
                                </div>
                                
                                <div>
                                    <label for="shipping_state" class="block text-sm font-medium text-gray-700 mb-1">State/Province</label>
                                    <input type="text" name="shipping_state" id="shipping_state" class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50" required>
                                </div>
                                
                                <div>
                                    <label for="shipping_zip" class="block text-sm font-medium text-gray-700 mb-1">ZIP/Postal Code</label>
                                    <input type="text" name="shipping_zip" id="shipping_zip" class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50" required>
                                </div>
                                
                                <div>
                                    <label for="shipping_country" class="block text-sm font-medium text-gray-700 mb-1">Country</label>
                                    <input type="text" name="shipping_country" id="shipping_country" class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50" required>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-6">
                            <h2 class="text-lg font-medium mb-4">Payment Method</h2>
                            
                            <div class="space-y-3">
                                <div class="flex items-center">
                                    <input type="radio" name="payment_method" id="payment_method_credit_card" value="credit_card" class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300" checked>
                                    <label for="payment_method_credit_card" class="ml-3 block text-sm font-medium text-gray-700">
                                        Credit Card
                                    </label>
                                </div>
                                
                                <div class="flex items-center">
                                    <input type="radio" name="payment_method" id="payment_method_paypal" value="paypal" class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300">
                                    <label for="payment_method_paypal" class="ml-3 block text-sm font-medium text-gray-700">
                                        PayPal
                                    </label>
                                </div>
                            </div>
                            
                            <!-- Credit Card Details (simplified for demo) -->
                            <div class="mt-4 p-4 border border-gray-200 rounded-md">
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div class="col-span-2">
                                        <label for="card_number" class="block text-sm font-medium text-gray-700 mb-1">Card Number</label>
                                        <input type="text" id="card_number" class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50" placeholder="**** **** **** ****">
                                    </div>
                                    
                                    <div>
                                        <label for="expiry_date" class="block text-sm font-medium text-gray-700 mb-1">Expiry Date</label>
                                        <input type="text" id="expiry_date" class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50" placeholder="MM/YY">
                                    </div>
                                    
                                    <div>
                                        <label for="cvv" class="block text-sm font-medium text-gray-700 mb-1">CVV</label>
                                        <input type="text" id="cvv" class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50" placeholder="123">
                                    </div>
                                </div>
                                <p class="text-xs text-gray-500 mt-2">Note: This is a demo. No actual payment will be processed.</p>
                            </div>
                        </div>
                        
                        <div class="flex justify-between items-center mt-8">
                            <a href="{{ route('cart.index') }}" class="inline-flex items-center px-4 py-2 bg-gray-200 border border-transparent rounded-md font-semibold text-xs text-gray-700 uppercase tracking-widest hover:bg-gray-300 active:bg-gray-400 focus:outline-none focus:border-gray-500 focus:ring focus:ring-gray-300 disabled:opacity-25 transition">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                                </svg>
                                Back to Cart
                            </a>
                            
                            <button type="submit" class="inline-flex items-center px-6 py-3 bg-indigo-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-indigo-700 active:bg-indigo-900 focus:outline-none focus:border-indigo-900 focus:ring focus:ring-indigo-300 disabled:opacity-25 transition">
                                Complete Order
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
                                </svg>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection