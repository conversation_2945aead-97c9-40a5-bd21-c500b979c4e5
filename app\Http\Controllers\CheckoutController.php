<?php

namespace App\Http\Controllers;

use App\Models\Order;
use App\Services\CartService;
use App\Services\CheckoutService;
use Illuminate\Http\Request;

class CheckoutController extends Controller
{
    protected CartService $cartService;
    protected CheckoutService $checkoutService;

    public function __construct(CartService $cartService, CheckoutService $checkoutService)
    {
        $this->cartService = $cartService;
        $this->checkoutService = $checkoutService;
    }

    /**
     * Show the checkout page.
     */
    public function index()
    {
        // Redirect to cart if no items
        if (!$this->cartService->hasItems()) {
            return redirect()->route('cart.index')->with('error', 'Your cart is empty.');
        }

        return view('shop.checkout');
    }

    /**
     * Save shipping information.
     */
    public function saveShipping(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'required|string|max:20',
            'address' => 'required|string|max:255',
            'city' => 'required|string|max:100',
            'state' => 'required|string|max:100',
            'zipcode' => 'required|string|max:20',
            'country' => 'required|string|max:100',
        ]);

        try {
            $this->checkoutService->saveShippingInfo($validated);
            
            if ($request->wantsJson()) {
                return response()->json(['success' => true]);
            }
            
            return redirect()->back()->with('success', 'Shipping information saved.');
        } catch (\Exception $e) {
            if ($request->wantsJson()) {
                return response()->json(['success' => false, 'error' => $e->getMessage()], 422);
            }
            
            return redirect()->back()->with('error', $e->getMessage());
        }
    }

    /**
     * Save shipping method.
     */
    public function saveShippingMethod(Request $request)
    {
        $validated = $request->validate([
            'shipping_method' => 'required|string|in:standard,express,overnight',
        ]);

        try {
            $this->checkoutService->saveShippingMethod($validated['shipping_method']);
            
            if ($request->wantsJson()) {
                return response()->json(['success' => true]);
            }
            
            return redirect()->back()->with('success', 'Shipping method selected.');
        } catch (\Exception $e) {
            if ($request->wantsJson()) {
                return response()->json(['success' => false, 'error' => $e->getMessage()], 422);
            }
            
            return redirect()->back()->with('error', $e->getMessage());
        }
    }

    /**
     * Save billing information.
     */
    public function saveBilling(Request $request)
    {
        $rules = [
            'same_as_shipping' => 'boolean',
        ];

        if (!$request->boolean('same_as_shipping')) {
            $rules = array_merge($rules, [
                'billing_name' => 'required|string|max:255',
                'billing_address' => 'required|string|max:255',
                'billing_city' => 'required|string|max:100',
                'billing_state' => 'required|string|max:100',
                'billing_zipcode' => 'required|string|max:20',
                'billing_country' => 'required|string|max:100',
            ]);
        }

        $validated = $request->validate($rules);

        try {
            $this->checkoutService->saveBillingInfo($validated);
            
            if ($request->wantsJson()) {
                return response()->json(['success' => true]);
            }
            
            return redirect()->back()->with('success', 'Billing information saved.');
        } catch (\Exception $e) {
            if ($request->wantsJson()) {
                return response()->json(['success' => false, 'error' => $e->getMessage()], 422);
            }
            
            return redirect()->back()->with('error', $e->getMessage());
        }
    }

    /**
     * Process payment.
     */
    public function processPayment(Request $request)
    {
        $validated = $request->validate([
            'payment_method' => 'required|string|in:credit_card,paypal,stripe',
            'card_number' => 'required_if:payment_method,credit_card|string',
            'card_expiry' => 'required_if:payment_method,credit_card|string',
            'card_cvv' => 'required_if:payment_method,credit_card|string',
            'card_name' => 'required_if:payment_method,credit_card|string',
        ]);

        try {
            $result = $this->checkoutService->processPayment($validated);
            
            if ($request->wantsJson()) {
                if ($result['success']) {
                    return response()->json(['success' => true]);
                } else {
                    return response()->json([
                        'success' => false,
                        'payment_error' => $result['error']
                    ], 422);
                }
            }
            
            if ($result['success']) {
                return redirect()->back()->with('success', 'Payment processed successfully.');
            } else {
                return redirect()->back()->with('error', $result['error']);
            }
        } catch (\Exception $e) {
            if ($request->wantsJson()) {
                return response()->json(['success' => false, 'error' => $e->getMessage()], 422);
            }
            
            return redirect()->back()->with('error', $e->getMessage());
        }
    }

    /**
     * Complete the order.
     */
    public function complete(Request $request)
    {
        try {
            $order = $this->checkoutService->completeOrder();
            
            if ($request->wantsJson()) {
                return response()->json([
                    'order_id' => $order->id,
                    'order_number' => $order->order_number,
                    'total' => $order->total_amount,
                    'redirect_url' => route('order.confirmation', $order->order_number)
                ], 201);
            }
            
            return redirect()->route('checkout.success', $order->order_number);
        } catch (\Exception $e) {
            if ($request->wantsJson()) {
                return response()->json(['success' => false, 'error' => $e->getMessage()], 422);
            }
            
            return redirect()->back()->with('error', $e->getMessage());
        }
    }

    /**
     * Confirm price changes.
     */
    public function confirmPriceChanges(Request $request)
    {
        try {
            $this->checkoutService->confirmPriceChanges();
            
            if ($request->wantsJson()) {
                return response()->json(['success' => true]);
            }
            
            return redirect()->back()->with('success', 'Price changes confirmed.');
        } catch (\Exception $e) {
            if ($request->wantsJson()) {
                return response()->json(['success' => false, 'error' => $e->getMessage()], 422);
            }
            
            return redirect()->back()->with('error', $e->getMessage());
        }
    }

    /**
     * Get checkout totals.
     */
    public function getTotals(Request $request)
    {
        try {
            $totals = $this->checkoutService->calculateTotals();
            
            return response()->json($totals);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 422);
        }
    }

    /**
     * Show order success page.
     */
    public function success(string $orderNumber)
    {
        $order = Order::where('order_number', $orderNumber)->firstOrFail();

        // Ensure user can view this order
        if (auth()->check() && $order->user_id !== auth()->id()) {
            abort(403);
        }

        return view('shop.checkout-success', compact('order'));
    }
}