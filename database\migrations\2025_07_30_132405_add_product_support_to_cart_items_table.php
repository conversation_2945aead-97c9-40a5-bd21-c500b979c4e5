<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('cart_items', function (Blueprint $table) {
            // Add product_id column (nullable since existing records have component_id)
            $table->foreignId('product_id')->nullable()->constrained()->onDelete('cascade');

            // Add item_type column to distinguish between component and product
            $table->enum('item_type', ['component', 'product'])->default('component');

            // Make component_id nullable since we now support products too
            $table->unsignedBigInteger('component_id')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('cart_items', function (Blueprint $table) {
            // Make component_id not nullable again
            $table->unsignedBigInteger('component_id')->nullable(false)->change();

            // Drop the new columns
            $table->dropColumn(['product_id', 'item_type']);
        });
    }
};
