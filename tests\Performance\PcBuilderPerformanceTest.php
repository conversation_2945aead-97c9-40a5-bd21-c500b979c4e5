<?php

namespace Tests\Performance;

use App\Models\Component;
use App\Models\ComponentCategory;
use App\Models\User;
use App\Services\BuilderService;
use App\Services\CompatibilityService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\DB;
use Tests\TestCase;

class PcBuilderPerformanceTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;
    protected array $categories;
    protected array $components;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->user = User::factory()->create();
        
        // Create categories
        $this->categories = [
            'motherboard' => ComponentCategory::factory()->create(['name' => 'Motherboard', 'slug' => 'motherboard']),
            'cpu' => ComponentCategory::factory()->create(['name' => 'CPU', 'slug' => 'cpu']),
            'ram' => ComponentCategory::factory()->create(['name' => 'RAM', 'slug' => 'ram']),
            'gpu' => ComponentCategory::factory()->create(['name' => 'GPU', 'slug' => 'gpu']),
            'storage' => ComponentCategory::factory()->create(['name' => 'Storage', 'slug' => 'storage']),
            'psu' => ComponentCategory::factory()->create(['name' => 'PSU', 'slug' => 'psu']),
            'case' => ComponentCategory::factory()->create(['name' => 'Case', 'slug' => 'case']),
        ];
        
        // Create large dataset of components for performance testing
        $this->createLargeComponentDataset();
    }

    /** @test */
    public function pc_builder_loads_components_efficiently_with_large_dataset()
    {
        $startTime = microtime(true);
        $startMemory = memory_get_usage();
        
        // Test loading components for each category
        foreach ($this->categories as $slug => $category) {
            $categoryStartTime = microtime(true);
            
            $response = $this->actingAs($this->user)
                          ->get(route('builder.components', ['category' => $slug]));
            
            $categoryEndTime = microtime(true);
            $categoryLoadTime = ($categoryEndTime - $categoryStartTime) * 1000; // Convert to milliseconds
            
            $response->assertStatus(200);
            
            // Assert response time is under 500ms for component loading
            $this->assertLessThan(500, $categoryLoadTime, 
                "Loading {$slug} components took {$categoryLoadTime}ms, which exceeds 500ms limit");
            
            // Assert reasonable memory usage (under 50MB increase per category)
            $currentMemory = memory_get_usage();
            $memoryIncrease = ($currentMemory - $startMemory) / 1024 / 1024; // Convert to MB
            $this->assertLessThan(50, $memoryIncrease, 
                "Memory usage increased by {$memoryIncrease}MB, which exceeds 50MB limit");
        }
        
        $endTime = microtime(true);
        $totalTime = ($endTime - $startTime) * 1000;
        
        // Total time for all categories should be under 2 seconds
        $this->assertLessThan(2000, $totalTime, 
            "Total component loading time was {$totalTime}ms, which exceeds 2000ms limit");
    }

    /** @test */
    public function compatibility_checking_performs_efficiently_with_large_dataset()
    {
        $compatibilityService = app(CompatibilityService::class);
        
        // Select a motherboard and test compatibility checking with all CPUs
        $motherboard = $this->components['motherboard'][0];
        $cpus = $this->components['cpu'];
        
        $startTime = microtime(true);
        $startMemory = memory_get_usage();
        
        foreach ($cpus as $cpu) {
            $compatibilityResult = $compatibilityService->checkCompatibility([
                'motherboard' => $motherboard,
                'cpu' => $cpu
            ]);
            
            $this->assertNotNull($compatibilityResult);
        }
        
        $endTime = microtime(true);
        $endMemory = memory_get_usage();
        
        $totalTime = ($endTime - $startTime) * 1000;
        $memoryUsage = ($endMemory - $startMemory) / 1024 / 1024;
        
        // Compatibility checking for 1000 CPUs should complete under 1 second
        $this->assertLessThan(1000, $totalTime, 
            "Compatibility checking took {$totalTime}ms, which exceeds 1000ms limit");
        
        // Memory usage should be reasonable (under 100MB)
        $this->assertLessThan(100, $memoryUsage, 
            "Memory usage was {$memoryUsage}MB, which exceeds 100MB limit");
    }

    /** @test */
    public function build_creation_and_validation_performs_efficiently()
    {
        $builderService = app(BuilderService::class);
        
        $startTime = microtime(true);
        
        // Create 100 builds to test performance
        for ($i = 0; $i < 100; $i++) {
            $components = [
                'motherboard' => $this->components['motherboard'][array_rand($this->components['motherboard'])],
                'cpu' => $this->components['cpu'][array_rand($this->components['cpu'])],
                'ram' => $this->components['ram'][array_rand($this->components['ram'])],
                'gpu' => $this->components['gpu'][array_rand($this->components['gpu'])],
                'storage' => $this->components['storage'][array_rand($this->components['storage'])],
                'psu' => $this->components['psu'][array_rand($this->components['psu'])],
                'case' => $this->components['case'][array_rand($this->components['case'])],
            ];
            
            $build = $builderService->createBuild($this->user, $components);
            $this->assertNotNull($build);
        }
        
        $endTime = microtime(true);
        $totalTime = ($endTime - $startTime) * 1000;
        
        // Creating 100 builds should complete under 5 seconds
        $this->assertLessThan(5000, $totalTime, 
            "Creating 100 builds took {$totalTime}ms, which exceeds 5000ms limit");
        
        // Average time per build should be under 50ms
        $averageTime = $totalTime / 100;
        $this->assertLessThan(50, $averageTime, 
            "Average build creation time was {$averageTime}ms, which exceeds 50ms limit");
    }

    /** @test */
    public function component_search_performs_efficiently_with_large_dataset()
    {
        $searchTerms = ['Intel', 'AMD', 'NVIDIA', 'Corsair', 'ASUS', 'MSI', 'Samsung'];
        
        foreach ($searchTerms as $term) {
            $startTime = microtime(true);
            
            $response = $this->actingAs($this->user)
                          ->get(route('shop.search', ['q' => $term]));
            
            $endTime = microtime(true);
            $searchTime = ($endTime - $startTime) * 1000;
            
            $response->assertStatus(200);
            
            // Search should complete under 200ms
            $this->assertLessThan(200, $searchTime, 
                "Search for '{$term}' took {$searchTime}ms, which exceeds 200ms limit");
        }
    }

    /** @test */
    public function database_queries_are_optimized_for_component_loading()
    {
        DB::enableQueryLog();
        
        // Load components for motherboard category
        $response = $this->actingAs($this->user)
                      ->get(route('builder.components', ['category' => 'motherboard']));
        
        $queries = DB::getQueryLog();
        $queryCount = count($queries);
        
        $response->assertStatus(200);
        
        // Should not exceed 5 queries for loading components
        $this->assertLessThan(5, $queryCount, 
            "Component loading executed {$queryCount} queries, which exceeds 5 query limit");
        
        // Check for N+1 query problems
        $selectQueries = array_filter($queries, function($query) {
            return strpos(strtolower($query['query']), 'select') === 0;
        });
        
        $this->assertLessThan(3, count($selectQueries), 
            "Too many SELECT queries detected, possible N+1 problem");
    }

    /** @test */
    public function concurrent_build_operations_handle_load_efficiently()
    {
        $startTime = microtime(true);
        
        // Simulate concurrent build operations
        $processes = [];
        for ($i = 0; $i < 10; $i++) {
            $processes[] = $this->simulateConcurrentBuildCreation();
        }
        
        // Wait for all processes to complete
        foreach ($processes as $process) {
            $process->wait();
        }
        
        $endTime = microtime(true);
        $totalTime = ($endTime - $startTime) * 1000;
        
        // 10 concurrent build operations should complete under 3 seconds
        $this->assertLessThan(3000, $totalTime, 
            "Concurrent build operations took {$totalTime}ms, which exceeds 3000ms limit");
    }

    /** @test */
    public function memory_usage_remains_stable_during_extended_operations()
    {
        $initialMemory = memory_get_usage();
        $peakMemory = $initialMemory;
        
        // Perform 500 component compatibility checks
        $compatibilityService = app(CompatibilityService::class);
        
        for ($i = 0; $i < 500; $i++) {
            $motherboard = $this->components['motherboard'][array_rand($this->components['motherboard'])];
            $cpu = $this->components['cpu'][array_rand($this->components['cpu'])];
            
            $compatibilityService->checkCompatibility([
                'motherboard' => $motherboard,
                'cpu' => $cpu
            ]);
            
            $currentMemory = memory_get_usage();
            $peakMemory = max($peakMemory, $currentMemory);
            
            // Force garbage collection every 100 iterations
            if ($i % 100 === 0) {
                gc_collect_cycles();
            }
        }
        
        $finalMemory = memory_get_usage();
        $memoryIncrease = ($finalMemory - $initialMemory) / 1024 / 1024; // MB
        $peakIncrease = ($peakMemory - $initialMemory) / 1024 / 1024; // MB
        
        // Memory increase should be minimal (under 50MB)
        $this->assertLessThan(50, $memoryIncrease, 
            "Final memory increase was {$memoryIncrease}MB, which exceeds 50MB limit");
        
        // Peak memory usage should be reasonable (under 200MB)
        $this->assertLessThan(200, $peakIncrease, 
            "Peak memory increase was {$peakIncrease}MB, which exceeds 200MB limit");
    }

    /** @test */
    public function component_filtering_and_sorting_performs_efficiently()
    {
        $filters = [
            ['brand' => 'Intel'],
            ['price_min' => 100, 'price_max' => 500],
            ['category' => 'cpu', 'brand' => 'AMD'],
            ['in_stock' => true],
            ['featured' => true]
        ];
        
        $sortOptions = ['name', 'price', 'brand', 'stock'];
        
        foreach ($filters as $filter) {
            foreach ($sortOptions as $sort) {
                $startTime = microtime(true);
                
                $queryParams = array_merge($filter, ['sort' => $sort]);
                $response = $this->actingAs($this->user)
                              ->get(route('shop.index', $queryParams));
                
                $endTime = microtime(true);
                $filterTime = ($endTime - $startTime) * 1000;
                
                $response->assertStatus(200);
                
                // Filtering and sorting should complete under 300ms
                $this->assertLessThan(300, $filterTime, 
                    "Filtering with " . json_encode($filter) . " and sorting by {$sort} took {$filterTime}ms");
            }
        }
    }

    /** @test */
    public function cache_effectiveness_improves_performance()
    {
        // First request (cold cache)
        $startTime = microtime(true);
        $response1 = $this->actingAs($this->user)
                        ->get(route('builder.components', ['category' => 'cpu']));
        $endTime = microtime(true);
        $coldCacheTime = ($endTime - $startTime) * 1000;
        
        $response1->assertStatus(200);
        
        // Second request (warm cache)
        $startTime = microtime(true);
        $response2 = $this->actingAs($this->user)
                        ->get(route('builder.components', ['category' => 'cpu']));
        $endTime = microtime(true);
        $warmCacheTime = ($endTime - $startTime) * 1000;
        
        $response2->assertStatus(200);
        
        // Warm cache should be significantly faster (at least 50% improvement)
        $improvement = (($coldCacheTime - $warmCacheTime) / $coldCacheTime) * 100;
        $this->assertGreaterThan(50, $improvement, 
            "Cache only improved performance by {$improvement}%, expected at least 50%");
        
        // Warm cache response should be under 100ms
        $this->assertLessThan(100, $warmCacheTime, 
            "Warm cache response took {$warmCacheTime}ms, which exceeds 100ms limit");
    }

    protected function createLargeComponentDataset(): void
    {
        $this->components = [];
        
        // Create 1000 components per category for performance testing
        foreach ($this->categories as $slug => $category) {
            $this->components[$slug] = Component::factory()->count(1000)->create([
                'category_id' => $category->id,
                'is_active' => true,
                'stock' => rand(0, 100),
                'price' => rand(50, 2000) + (rand(0, 99) / 100), // Random price between $50-$2000
            ]);
        }
    }

    protected function simulateConcurrentBuildCreation()
    {
        // This would typically use a process pool or async operations
        // For testing purposes, we'll simulate with a simple closure
        return new class {
            public function wait() {
                // Simulate build creation work
                usleep(rand(100000, 500000)); // 100-500ms
                return true;
            }
        };
    }
}