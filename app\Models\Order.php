<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;

class Order extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'build_id',
        'order_number',
        'status',
        'total',
        'subtotal',
        'tax',
        'shipping',
        'discount',
        'coupon_code',
        'discount_amount',
        'billing_name',
        'billing_email',
        'billing_phone',
        'billing_address',
        'billing_city',
        'billing_state',
        'billing_zipcode',
        'billing_country',
        'shipping_name',
        'shipping_email',
        'shipping_phone',
        'shipping_address',
        'shipping_city',
        'shipping_state',
        'shipping_zipcode',
        'shipping_country',
        'notes',
        'payment_status',
        'tracking_number',
        'tracking_carrier',
        'tracking_url',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'total' => 'decimal:2',
        'subtotal' => 'decimal:2',
        'tax' => 'decimal:2',
        'shipping' => 'decimal:2',
        'discount' => 'decimal:2',
        'discount_amount' => 'decimal:2',
    ];

    /**
     * The order statuses.
     */
    const STATUS_PENDING = 'pending';
    const STATUS_PROCESSING = 'processing';
    const STATUS_SHIPPED = 'shipped';
    const STATUS_DELIVERED = 'delivered';
    const STATUS_COMPLETED = 'completed';
    const STATUS_CANCELLED = 'canceled';
    const STATUS_REFUNDED = 'refunded';

    /**
     * Get the user that owns the order.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the items for the order.
     */
    public function items(): HasMany
    {
        return $this->hasMany(OrderItem::class);
    }

    /**
     * Alias for items relationship (for compatibility).
     */
    public function orderItems(): HasMany
    {
        return $this->items();
    }

    /**
     * Get the payment for the order.
     */
    public function payment(): HasOne
    {
        return $this->hasOne(Payment::class);
    }

    /**
     * Get the build associated with the order (if any).
     */
    public function build(): BelongsTo
    {
        return $this->belongsTo(Build::class);
    }



    /**
     * Generate a unique order number.
     */
    public static function generateOrderNumber(): string
    {
        $prefix = 'PCB';
        $timestamp = now()->format('YmdHis');
        $random = str_pad(mt_rand(1, 999), 3, '0', STR_PAD_LEFT);
        
        return $prefix . $timestamp . $random;
    }

    /**
     * Create an order from a cart.
     */
    public static function createFromCart(Cart $cart, array $orderData)
    {
        // Create the order
        $order = self::create(array_merge(
            $orderData,
            [
                'user_id' => $cart->user_id,
                'order_number' => self::generateOrderNumber(),
                'status' => self::STATUS_PENDING,
                'subtotal' => $cart->total,
                'total' => $cart->total + ($orderData['shipping'] ?? 0) + ($orderData['tax'] ?? 0) - ($orderData['discount'] ?? 0),
            ]
        ));
        
        // Add cart items to order
        foreach ($cart->items()->with('component')->get() as $cartItem) {
            $order->items()->create([
                'component_id' => $cartItem->component_id,
                'quantity' => $cartItem->quantity,
                'price' => $cartItem->price,
                'name' => $cartItem->component->name,
                'options' => json_encode([
                    'brand' => $cartItem->component->brand,
                    'model' => $cartItem->component->model,
                ]),
            ]);
        }
        
        // Clear the cart
        $cart->clear();
        
        return $order;
    }
}