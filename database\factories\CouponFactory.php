<?php

namespace Database\Factories;

use App\Models\Coupon;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

class CouponFactory extends Factory
{
    protected $model = Coupon::class;

    public function definition(): array
    {
        $type = $this->faker->randomElement(['fixed', 'percentage']);
        
        return [
            'code' => strtoupper(Str::random(8)),
            'name' => $this->faker->words(3, true),
            'description' => $this->faker->sentence(),
            'type' => $type,
            'value' => $type === 'fixed' ? $this->faker->numberBetween(50, 500) : $this->faker->numberBetween(5, 50),
            'minimum_amount' => $this->faker->optional(0.7)->numberBetween(100, 1000),
            'maximum_discount' => $type === 'percentage' ? $this->faker->optional(0.5)->numberBetween(100, 1000) : null,
            'usage_limit' => $this->faker->optional(0.6)->numberBetween(10, 1000),
            'usage_limit_per_user' => $this->faker->optional(0.4)->numberBetween(1, 5),
            'used_count' => 0,
            'starts_at' => $this->faker->optional(0.3)->dateTimeBetween('-1 month', '+1 month'),
            'expires_at' => $this->faker->optional(0.8)->dateTimeBetween('+1 day', '+6 months'),
            'is_active' => $this->faker->boolean(85),
            'applicable_products' => null,
            'applicable_categories' => null,
            'excluded_products' => null,
            'excluded_categories' => null,
        ];
    }

    public function fixed(float $value = null): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'fixed',
            'value' => $value ?? $this->faker->numberBetween(50, 500),
            'maximum_discount' => null,
        ]);
    }

    public function percentage(float $value = null): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'percentage',
            'value' => $value ?? $this->faker->numberBetween(5, 50),
            'maximum_discount' => $this->faker->optional(0.5)->numberBetween(100, 1000),
        ]);
    }

    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => true,
        ]);
    }

    public function expired(): static
    {
        return $this->state(fn (array $attributes) => [
            'expires_at' => $this->faker->dateTimeBetween('-1 month', '-1 day'),
        ]);
    }

    public function unlimited(): static
    {
        return $this->state(fn (array $attributes) => [
            'usage_limit' => null,
            'usage_limit_per_user' => null,
        ]);
    }
}