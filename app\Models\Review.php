<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;

class Review extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'component_id',
        'rating',
        'title',
        'comment',
        'is_approved',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'rating' => 'integer',
        'is_approved' => 'boolean',
    ];

    /**
     * Get the user that owns the review.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the component that the review is for.
     */
    public function component(): BelongsTo
    {
        return $this->belongsTo(Component::class);
    }

    /**
     * Scope to get only approved reviews.
     */
    public function scopeApproved(Builder $query): Builder
    {
        return $query->where('is_approved', true);
    }

    /**
     * Scope to get reviews pending approval.
     */
    public function scopePending(Builder $query): Builder
    {
        return $query->where('is_approved', false);
    }

    /**
     * Scope to get reviews for a specific component.
     */
    public function scopeForComponent(Builder $query, int $componentId): Builder
    {
        return $query->where('component_id', $componentId);
    }

    /**
     * Scope to get reviews by rating.
     */
    public function scopeByRating(Builder $query, int $rating): Builder
    {
        return $query->where('rating', $rating);
    }

    /**
     * Get validation rules for review creation.
     */
    public static function validationRules(int $userId, int $componentId): array
    {
        return [
            'rating' => ['required', 'integer', 'min:1', 'max:5'],
            'title' => ['nullable', 'string', 'max:255'],
            'comment' => ['nullable', 'string', 'max:2000'],
            'user_id' => [
                'required',
                'exists:users,id',
                Rule::unique('reviews')->where(function ($query) use ($userId, $componentId) {
                    return $query->where('user_id', $userId)
                                ->where('component_id', $componentId);
                }),
            ],
            'component_id' => ['required', 'exists:components,id'],
        ];
    }

    /**
     * Check if review content needs moderation.
     */
    public function needsModeration(): bool
    {
        $flaggedWords = [
            'spam', 'fake', 'scam', 'terrible', 'worst', 'hate',
            'stupid', 'garbage', 'trash', 'useless', 'crap'
        ];

        $content = strtolower($this->title . ' ' . $this->comment);
        
        foreach ($flaggedWords as $word) {
            if (str_contains($content, $word)) {
                return true;
            }
        }

        // Flag reviews that are too short or too long
        if (strlen($this->comment) < 10 || strlen($this->comment) > 2000) {
            return true;
        }

        // Flag reviews with extreme ratings without sufficient comment
        if (($this->rating <= 2 || $this->rating >= 5) && strlen($this->comment) < 50) {
            return true;
        }

        return false;
    }

    /**
     * Auto-approve review if it passes moderation checks.
     */
    public function autoApprove(): void
    {
        if (!$this->needsModeration()) {
            $this->update(['is_approved' => true]);
        }
    }

    /**
     * Get average rating for a component.
     */
    public static function getAverageRating(int $componentId): float
    {
        return static::approved()
            ->forComponent($componentId)
            ->avg('rating') ?? 0.0;
    }

    /**
     * Get rating distribution for a component.
     */
    public static function getRatingDistribution(int $componentId): array
    {
        $distribution = static::approved()
            ->forComponent($componentId)
            ->select('rating', DB::raw('count(*) as count'))
            ->groupBy('rating')
            ->pluck('count', 'rating')
            ->toArray();

        // Fill in missing ratings with 0
        for ($i = 1; $i <= 5; $i++) {
            if (!isset($distribution[$i])) {
                $distribution[$i] = 0;
            }
        }

        ksort($distribution);
        return $distribution;
    }

    /**
     * Get total review count for a component.
     */
    public static function getReviewCount(int $componentId): int
    {
        return static::approved()
            ->forComponent($componentId)
            ->count();
    }

    /**
     * Check if user has purchased the component (verified purchase).
     */
    public function isVerifiedPurchase(): bool
    {
        return DB::table('order_items')
            ->join('orders', 'order_items.order_id', '=', 'orders.id')
            ->where('orders.user_id', $this->user_id)
            ->where('order_items.component_id', $this->component_id)
            ->where('orders.status', 'completed')
            ->exists();
    }

    /**
     * Get helpful review statistics.
     */
    public static function getComponentReviewStats(int $componentId): array
    {
        $reviews = static::approved()->forComponent($componentId);
        
        return [
            'average_rating' => round(static::getAverageRating($componentId), 1),
            'total_reviews' => static::getReviewCount($componentId),
            'rating_distribution' => static::getRatingDistribution($componentId),
            'verified_purchases' => $reviews->get()->filter(fn($review) => $review->isVerifiedPurchase())->count(),
        ];
    }
}