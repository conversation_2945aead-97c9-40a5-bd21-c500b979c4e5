<?php

namespace App\Services;

use App\Models\GatewaySetting;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;

class GatewaySettingsService
{
    /**
     * Get all gateway settings
     */
    public function getAllSettings(): array
    {
        $settings = GatewaySetting::all()->keyBy('gateway_name');
        
        // Ensure all supported gateways have entries
        $supportedGateways = ['razorpay', 'payumoney', 'cashfree'];
        
        foreach ($supportedGateways as $gateway) {
            if (!$settings->has($gateway)) {
                $settings[$gateway] = $this->createDefaultSetting($gateway);
            }
        }
        
        return $settings->toArray();
    }
    
    /**
     * Get settings for a specific gateway
     */
    public function getGatewaySettings(string $gatewayName): ?GatewaySetting
    {
        $setting = GatewaySetting::where('gateway_name', $gatewayName)->first();
        
        if (!$setting) {
            $setting = $this->createDefaultSetting($gatewayName);
        }
        
        return $setting;
    }
    
    /**
     * Update gateway settings
     */
    public function updateGatewaySettings(string $gatewayName, array $data): GatewaySetting
    {
        $this->validateGatewaySettings($gatewayName, $data);
        
        $setting = GatewaySetting::updateOrCreate(
            ['gateway_name' => $gatewayName],
            [
                'is_enabled' => $data['is_enabled'] ?? false,
                'is_test_mode' => $data['is_test_mode'] ?? true,
                'settings' => $this->prepareSettingsData($gatewayName, $data['settings'] ?? [])
            ]
        );
        
        return $setting;
    }
    
    /**
     * Enable or disable a gateway
     */
    public function toggleGateway(string $gatewayName, bool $enabled): GatewaySetting
    {
        $setting = $this->getGatewaySettings($gatewayName);
        $setting->is_enabled = $enabled;
        $setting->save();
        
        return $setting;
    }
    
    /**
     * Switch gateway between test and live mode
     */
    public function switchMode(string $gatewayName, bool $testMode): GatewaySetting
    {
        $setting = $this->getGatewaySettings($gatewayName);
        $setting->is_test_mode = $testMode;
        $setting->save();
        
        return $setting;
    }
    
    /**
     * Get enabled gateways only
     */
    public function getEnabledGateways(): array
    {
        return GatewaySetting::where('is_enabled', true)
            ->pluck('gateway_name')
            ->toArray();
    }
    
    /**
     * Validate gateway settings based on gateway type
     */
    private function validateGatewaySettings(string $gatewayName, array $data): void
    {
        $rules = [
            'is_enabled' => 'boolean',
            'is_test_mode' => 'boolean',
            'settings' => 'required|array'
        ];
        
        // Add gateway-specific validation rules
        $settingsRules = $this->getGatewayValidationRules($gatewayName);
        
        foreach ($settingsRules as $key => $rule) {
            $rules["settings.{$key}"] = $rule;
        }
        
        $validator = Validator::make($data, $rules);
        
        if ($validator->fails()) {
            throw new ValidationException($validator);
        }
    }
    
    /**
     * Get validation rules for specific gateway
     */
    private function getGatewayValidationRules(string $gatewayName): array
    {
        return match ($gatewayName) {
            'razorpay' => [
                'key_id' => 'required|string',
                'key_secret' => 'required|string',
                'webhook_secret' => 'nullable|string'
            ],
            'payumoney' => [
                'merchant_key' => 'required|string',
                'salt' => 'required|string',
                'auth_header' => 'nullable|string'
            ],
            'cashfree' => [
                'app_id' => 'required|string',
                'secret_key' => 'required|string',
                'client_id' => 'required|string',
                'client_secret' => 'required|string'
            ],
            default => []
        };
    }
    
    /**
     * Prepare settings data for storage
     */
    private function prepareSettingsData(string $gatewayName, array $settings): array
    {
        // Remove empty values and trim strings
        $cleanSettings = [];
        
        foreach ($settings as $key => $value) {
            if (!empty($value) && is_string($value)) {
                $cleanSettings[$key] = trim($value);
            } elseif (!empty($value)) {
                $cleanSettings[$key] = $value;
            }
        }
        
        return $cleanSettings;
    }
    
    /**
     * Create default setting for a gateway
     */
    private function createDefaultSetting(string $gatewayName): GatewaySetting
    {
        return GatewaySetting::create([
            'gateway_name' => $gatewayName,
            'is_enabled' => false,
            'is_test_mode' => true,
            'settings' => $this->getDefaultSettings($gatewayName)
        ]);
    }
    
    /**
     * Get default settings structure for a gateway
     */
    private function getDefaultSettings(string $gatewayName): array
    {
        return match ($gatewayName) {
            'razorpay' => [
                'key_id' => '',
                'key_secret' => '',
                'webhook_secret' => ''
            ],
            'payumoney' => [
                'merchant_key' => '',
                'salt' => '',
                'auth_header' => ''
            ],
            'cashfree' => [
                'app_id' => '',
                'secret_key' => '',
                'client_id' => '',
                'client_secret' => ''
            ],
            default => []
        };
    }
    
    /**
     * Test gateway configuration
     */
    public function testGatewayConfiguration(string $gatewayName): array
    {
        $setting = $this->getGatewaySettings($gatewayName);
        
        if (!$setting->is_enabled) {
            return [
                'success' => false,
                'message' => 'Gateway is disabled'
            ];
        }
        
        // Basic validation - check if required fields are present
        $requiredFields = array_keys($this->getGatewayValidationRules($gatewayName));
        $settings = $setting->settings;
        
        foreach ($requiredFields as $field) {
            if (empty($settings[$field])) {
                return [
                    'success' => false,
                    'message' => "Missing required field: {$field}"
                ];
            }
        }
        
        return [
            'success' => true,
            'message' => 'Configuration appears valid'
        ];
    }
}