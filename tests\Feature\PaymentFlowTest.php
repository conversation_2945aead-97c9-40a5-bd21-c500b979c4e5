<?php

namespace Tests\Feature;

use App\Models\GatewaySetting;
use App\Models\Transaction;
use App\Models\User;
use App\Services\PaymentGatewayFactory;
use App\Services\PaymentService;
use App\Services\Payment\Gateways\RazorpayService;
use App\Services\Payment\Gateways\PayUmoneyService;
use App\Services\Payment\Gateways\CashfreeService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Tests\TestCase;

class PaymentFlowTest extends TestCase
{
    use RefreshDatabase;

    private User $user;
    private array $paymentData;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->user = User::factory()->create();
        
        $this->paymentData = [
            'gateway' => 'razorpay',
            'amount' => 100.00,
            'currency' => 'INR',
            'description' => 'Test payment'
        ];

        // Create gateway settings for all gateways
        $this->createGatewaySettings();
    }

    private function createGatewaySettings(): void
    {
        GatewaySetting::create([
            'gateway_name' => 'razorpay',
            'is_enabled' => true,
            'is_test_mode' => true,
            'settings' => [
                'key_id' => 'rzp_test_key',
                'key_secret' => 'test_secret',
                'webhook_secret' => 'webhook_secret'
            ]
        ]);

        GatewaySetting::create([
            'gateway_name' => 'payumoney',
            'is_enabled' => true,
            'is_test_mode' => true,
            'settings' => [
                'merchant_key' => 'test_merchant_key',
                'salt' => 'test_salt',
                'auth_header' => 'test_auth'
            ]
        ]);

        GatewaySetting::create([
            'gateway_name' => 'cashfree',
            'is_enabled' => true,
            'is_test_mode' => true,
            'settings' => [
                'app_id' => 'test_app_id',
                'secret_key' => 'test_secret',
                'client_id' => 'test_client_id',
                'client_secret' => 'test_client_secret'
            ]
        ]);
    }

    /** @test */
    public function user_can_view_payment_form_with_available_gateways()
    {
        // Requirements: 1.1, 1.4
        $response = $this->actingAs($this->user)
                        ->get(route('payment.create'));

        $response->assertStatus(200);
        $response->assertViewIs('payments.create');
        $response->assertViewHas('gateways');
        $response->assertViewHas('gatewayOptions');
        
        // Check that all enabled gateways are available
        $gateways = $response->viewData('gateways');
        $this->assertContains('razorpay', $gateways);
        $this->assertContains('payumoney', $gateways);
        $this->assertContains('cashfree', $gateways);
    }

    /** @test */
    public function user_cannot_see_disabled_gateways()
    {
        // Requirements: 1.1
        // Disable PayUmoney
        GatewaySetting::where('gateway_name', 'payumoney')
                     ->update(['is_enabled' => false]);

        $response = $this->actingAs($this->user)
                        ->get(route('payment.create'));

        $gateways = $response->viewData('gateways');
        $this->assertContains('razorpay', $gateways);
        $this->assertNotContains('payumoney', $gateways);
        $this->assertContains('cashfree', $gateways);
    }

    /** @test */
    public function user_can_initiate_razorpay_payment()
    {
        // Requirements: 1.2, 1.3, 7.1
        // Mock RazorpayService
        $this->mock(\App\Services\Payment\Gateways\RazorpayService::class, function ($mock) {
            $mock->shouldReceive('createPayment')
                 ->once()
                 ->andReturn([
                     'success' => true,
                     'gateway_order_id' => 'order_test123',
                     'amount' => 10000,
                     'currency' => 'INR',
                     'key_id' => 'rzp_test_key',
                     'order_data' => [
                         'id' => 'order_test123',
                         'amount' => 10000,
                         'currency' => 'INR',
                         'status' => 'created'
                     ],
                     'test_mode' => true
                 ]);
        });

        $response = $this->actingAs($this->user)
                        ->postJson(route('payment.store'), $this->paymentData);

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'success',
            'transaction',
            'gateway_data',
            'redirect_url'
        ]);

        // Check transaction was created
        $this->assertDatabaseHas('transactions', [
            'user_id' => $this->user->id,
            'gateway_name' => 'razorpay',
            'amount' => 100.00,
            'status' => Transaction::STATUS_PROCESSING
        ]);
    }

    /** @test */
    public function user_can_initiate_payumoney_payment()
    {
        // Requirements: 1.2, 1.3, 7.2
        // Mock PayUmoneyService
        $this->mock(\App\Services\Payment\Gateways\PayUmoneyService::class, function ($mock) {
            $mock->shouldReceive('createPayment')
                 ->once()
                 ->andReturn([
                     'key' => 'test_merchant_key',
                     'txnid' => 'TXN_TEST123',
                     'amount' => 100.00,
                     'productinfo' => 'Test payment',
                     'firstname' => 'Test User',
                     'email' => '<EMAIL>',
                     'phone' => '',
                     'surl' => 'http://127.0.0.1:8000/payment/callback/success',
                     'furl' => 'http://127.0.0.1:8000/payment/callback/failure',
                     'service_provider' => 'payu_paisa',
                     'hash' => 'test_hash',
                     'action_url' => 'https://test.payu.in/_payment'
                 ]);
        });

        $paymentData = array_merge($this->paymentData, ['gateway' => 'payumoney']);

        $response = $this->actingAs($this->user)
                        ->post(route('payment.store'), $paymentData);

        $response->assertStatus(200);
        $response->assertViewIs('payments.payumoney-redirect');
        $response->assertViewHas('payment_data');

        // Check transaction was created
        $this->assertDatabaseHas('transactions', [
            'user_id' => $this->user->id,
            'gateway_name' => 'payumoney',
            'amount' => 100.00,
            'status' => Transaction::STATUS_PROCESSING
        ]);
    }

    /** @test */
    public function user_can_initiate_cashfree_payment()
    {
        // Requirements: 1.2, 1.3, 7.3
        // Mock CashfreeService
        $this->mock(\App\Services\Payment\Gateways\CashfreeService::class, function ($mock) {
            $mock->shouldReceive('createPayment')
                 ->once()
                 ->andReturn([
                     'success' => true,
                     'payment_session_id' => 'session_test123',
                     'order_id' => 'order_test123',
                     'payment_url' => 'https://sandbox.cashfree.com/pg/view/session_test123'
                 ]);
        });

        $paymentData = array_merge($this->paymentData, ['gateway' => 'cashfree']);

        $response = $this->actingAs($this->user)
                        ->postJson(route('payment.store'), $paymentData);

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'success',
            'transaction',
            'gateway_data',
            'redirect_url'
        ]);

        // Check transaction was created
        $this->assertDatabaseHas('transactions', [
            'user_id' => $this->user->id,
            'gateway_name' => 'cashfree',
            'amount' => 100.00,
            'status' => Transaction::STATUS_PROCESSING
        ]);
    }

    /** @test */
    public function payment_initiation_fails_with_invalid_gateway()
    {
        // Requirements: 7.4
        $paymentData = array_merge($this->paymentData, ['gateway' => 'invalid_gateway']);

        $response = $this->actingAs($this->user)
                        ->postJson(route('payment.store'), $paymentData);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['gateway']);
    }

    /** @test */
    public function payment_initiation_fails_with_invalid_amount()
    {
        // Requirements: 1.2, 8.2
        $paymentData = array_merge($this->paymentData, ['amount' => -10]);

        $response = $this->actingAs($this->user)
                        ->postJson(route('payment.store'), $paymentData);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['amount']);
    }

    /** @test */
    public function payment_initiation_fails_with_disabled_gateway()
    {
        // Requirements: 1.1
        // Disable Razorpay
        GatewaySetting::where('gateway_name', 'razorpay')
                     ->update(['is_enabled' => false]);

        $response = $this->actingAs($this->user)
                        ->postJson(route('payment.store'), $this->paymentData);

        $response->assertStatus(422);
        $response->assertJson([
            'success' => false,
            'errors' => [
                'gateway' => []
            ]
        ]);
    }

    /** @test */
    public function successful_razorpay_payment_verification()
    {
        // Requirements: 2.1, 2.2, 6.1, 6.2
        // Create a processing transaction
        $transaction = Transaction::factory()->create([
            'user_id' => $this->user->id,
            'gateway_name' => 'razorpay',
            'status' => Transaction::STATUS_PROCESSING,
            'transaction_id' => 'TXN_TEST123',
            'payment_details' => [
                'gateway_response' => ['id' => 'order_test123']
            ]
        ]);

        // Mock successful verification
        $this->mock(RazorpayService::class, function ($mock) {
            $mock->shouldReceive('verifyPayment')
                 ->once()
                 ->andReturn(true);
        });

        $verificationData = [
            'transaction_id' => 'TXN_TEST123',
            'gateway_transaction_id' => 'pay_test123',
            'razorpay_payment_id' => 'pay_test123',
            'razorpay_order_id' => 'order_test123',
            'razorpay_signature' => 'valid_signature'
        ];

        $response = $this->actingAs($this->user)
                        ->postJson(route('payment.verify'), $verificationData);

        $response->assertStatus(200);
        $response->assertJson(['verified' => true]);

        // Check transaction was updated
        $transaction->refresh();
        $this->assertEquals(Transaction::STATUS_COMPLETED, $transaction->status);
        $this->assertEquals('pay_test123', $transaction->gateway_transaction_id);
    }

    /** @test */
    public function failed_payment_verification()
    {
        // Requirements: 2.3, 6.3
        $transaction = Transaction::factory()->create([
            'user_id' => $this->user->id,
            'gateway_name' => 'razorpay',
            'status' => Transaction::STATUS_PROCESSING,
            'transaction_id' => 'TXN_TEST123'
        ]);

        // Mock failed verification
        $this->mock(RazorpayService::class, function ($mock) {
            $mock->shouldReceive('verifyPayment')
                 ->once()
                 ->andReturn(false);
        });

        $verificationData = [
            'transaction_id' => 'TXN_TEST123',
            'razorpay_payment_id' => 'pay_test123',
            'razorpay_order_id' => 'order_test123',
            'razorpay_signature' => 'invalid_signature'
        ];

        $response = $this->actingAs($this->user)
                        ->postJson(route('payment.verify'), $verificationData);

        $response->assertStatus(200);
        $response->assertJson([
            'verified' => [
                'success' => false,
                'status' => 'failed'
            ]
        ]);

        // Check transaction was marked as failed
        $transaction->refresh();
        $this->assertEquals(Transaction::STATUS_FAILED, $transaction->status);
    }

    /** @test */
    public function user_can_view_payment_success_page()
    {
        // Requirements: 2.2
        $transaction = Transaction::factory()->create([
            'user_id' => $this->user->id,
            'status' => Transaction::STATUS_COMPLETED
        ]);

        $response = $this->actingAs($this->user)
                        ->get(route('payment.success', $transaction));

        $response->assertStatus(200);
        $response->assertViewIs('payments.success');
        $response->assertViewHas('transaction', $transaction);
    }

    /** @test */
    public function user_can_view_payment_failure_page()
    {
        // Requirements: 2.3
        $transaction = Transaction::factory()->create([
            'user_id' => $this->user->id,
            'status' => Transaction::STATUS_FAILED,
            'failure_reason' => 'Payment declined by bank'
        ]);

        $response = $this->actingAs($this->user)
                        ->get(route('payment.failed', $transaction));

        $response->assertStatus(200);
        $response->assertViewIs('payments.failed');
        $response->assertViewHas('transaction', $transaction);
    }

    /** @test */
    public function user_can_view_payment_status_page()
    {
        // Requirements: 2.4
        $transaction = Transaction::factory()->create([
            'user_id' => $this->user->id,
            'status' => Transaction::STATUS_PROCESSING
        ]);

        $response = $this->actingAs($this->user)
                        ->get(route('payment.status', $transaction));

        $response->assertStatus(200);
        $response->assertViewIs('payments.status');
        $response->assertViewHas('transaction', $transaction);
    }

    /** @test */
    public function user_can_get_payment_status_via_api()
    {
        // Requirements: 2.4
        $transaction = Transaction::factory()->create([
            'user_id' => $this->user->id,
            'status' => Transaction::STATUS_COMPLETED,
            'gateway_name' => 'razorpay',
            'amount' => 100.00
        ]);

        $response = $this->actingAs($this->user)
                        ->getJson(route('payment.status', $transaction));

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'transaction_id',
            'status',
            'amount',
            'gateway',
            'created_at',
            'updated_at'
        ]);
        
        $response->assertJson([
            'status' => Transaction::STATUS_COMPLETED,
            'gateway' => 'razorpay',
            'amount' => 100.00
        ]);
    }

    /** @test */
    public function user_cannot_access_other_users_transactions()
    {
        // Security test
        $otherUser = User::factory()->create();
        $transaction = Transaction::factory()->create([
            'user_id' => $otherUser->id,
            'status' => Transaction::STATUS_COMPLETED
        ]);

        $response = $this->actingAs($this->user)
                        ->get(route('payment.success', $transaction));

        $response->assertStatus(403);
    }

    /** @test */
    public function razorpay_webhook_processes_payment_success()
    {
        // Disable webhook middleware for this test
        $this->withoutMiddleware([
            \App\Http\Middleware\WebhookSecurityFilter::class,
            \App\Http\Middleware\WebhookRateLimit::class,
            \App\Http\Middleware\WebhookSignatureValidation::class,
        ]);

        // Requirements: 6.1, 6.2, 6.4
        $transaction = Transaction::factory()->create([
            'gateway_name' => 'razorpay',
            'status' => Transaction::STATUS_PROCESSING,
            'transaction_id' => 'TXN_TEST123',
            'payment_details' => [
                'gateway_response' => ['id' => 'order_test123']
            ]
        ]);

        $webhookPayload = [
            'event' => 'payment.captured',
            'payload' => [
                'payment' => [
                    'entity' => [
                        'id' => 'pay_test123',
                        'order_id' => 'order_test123',
                        'status' => 'captured',
                        'amount' => 10000
                    ]
                ]
            ]
        ];

        // Mock RazorpayService for webhook processing
        $this->mock(\App\Services\Payment\Gateways\RazorpayService::class, function ($mock) use ($webhookPayload) {
            $mock->shouldReceive('handleWebhook')
                 ->once()
                 ->with($webhookPayload)
                 ->andReturn([
                     'success' => true,
                     'transaction_id' => 'TXN_TEST123',
                     'status' => 'completed'
                 ]);
        });

        $response = $this->postJson(route('webhooks.payment', 'razorpay'), $webhookPayload, [
            'X-Razorpay-Signature' => 'valid_signature'
        ]);

        $response->assertStatus(200);

        // Check transaction was updated
        $transaction->refresh();
        $this->assertEquals(Transaction::STATUS_COMPLETED, $transaction->status);
        $this->assertTrue($transaction->webhook_verified);
    }

    /** @test */
    public function payumoney_callback_processes_successful_payment()
    {
        // Requirements: 6.1, 6.2, 7.2
        $transaction = Transaction::factory()->create([
            'gateway_name' => 'payumoney',
            'status' => Transaction::STATUS_PROCESSING,
            'transaction_id' => 'TXN_TEST123'
        ]);

        $callbackData = [
            'txnid' => 'TXN_TEST123',
            'status' => 'success',
            'amount' => '100.00',
            'hash' => 'valid_hash',
            'mihpayid' => 'payumoney_txn_123'
        ];

        // Mock PayUmoney service
        $this->mock(PayUmoneyService::class, function ($mock) use ($callbackData) {
            $mock->shouldReceive('handleWebhook')
                 ->once()
                 ->with($callbackData)
                 ->andReturn([
                     'success' => true,
                     'transaction_id' => 'TXN_TEST123',
                     'status' => 'completed'
                 ]);
        });

        $response = $this->post(route('payment.payumoney.success'), $callbackData);

        $response->assertRedirect(route('payment.success', $transaction->id));

        // Check transaction was updated
        $transaction->refresh();
        $this->assertEquals(Transaction::STATUS_COMPLETED, $transaction->status);
    }

    /** @test */
    public function cashfree_webhook_processes_payment_success()
    {
        // Disable webhook middleware for this test
        $this->withoutMiddleware([
            \App\Http\Middleware\WebhookSecurityFilter::class,
            \App\Http\Middleware\WebhookRateLimit::class,
            \App\Http\Middleware\WebhookSignatureValidation::class,
        ]);

        // Requirements: 6.1, 6.2, 7.3
        $transaction = Transaction::factory()->create([
            'gateway_name' => 'cashfree',
            'status' => Transaction::STATUS_PROCESSING,
            'transaction_id' => 'TXN_TEST123'
        ]);

        $webhookPayload = [
            'type' => 'PAYMENT_SUCCESS_WEBHOOK',
            'data' => [
                'order' => [
                    'order_id' => 'TXN_TEST123',
                    'order_status' => 'PAID'
                ],
                'payment' => [
                    'cf_payment_id' => 'cf_payment_123',
                    'payment_status' => 'SUCCESS',
                    'payment_amount' => 100.00
                ]
            ]
        ];

        // Mock Cashfree service
        $this->mock(CashfreeService::class, function ($mock) use ($webhookPayload) {
            $mock->shouldReceive('handleWebhook')
                 ->once()
                 ->with($webhookPayload)
                 ->andReturn([
                     'success' => true,
                     'transaction_id' => 'TXN_TEST123',
                     'status' => 'completed'
                 ]);
        });

        $response = $this->postJson(route('webhooks.payment', 'cashfree'), $webhookPayload, [
            'x-webhook-signature' => 'valid_signature',
            'x-webhook-timestamp' => time()
        ]);

        $response->assertStatus(200);

        // Check transaction was updated
        $transaction->refresh();
        $this->assertEquals(Transaction::STATUS_COMPLETED, $transaction->status);
        $this->assertTrue($transaction->webhook_verified);
    }

    /** @test */
    public function webhook_rejects_invalid_signature()
    {
        // Requirements: 6.3, 6.4
        $transaction = Transaction::factory()->create([
            'gateway_name' => 'razorpay',
            'status' => Transaction::STATUS_PROCESSING
        ]);

        $webhookPayload = [
            'event' => 'payment.captured',
            'payload' => ['payment' => ['entity' => ['id' => 'pay_test123']]]
        ];

        // The middleware will reject this before reaching the service
        // So we don't need to mock the service for this test
        $response = $this->postJson(route('webhooks.payment', 'razorpay'), $webhookPayload, [
            'X-Razorpay-Signature' => 'invalid_signature'
        ]);

        $response->assertStatus(401);

        // Transaction should remain unchanged
        $transaction->refresh();
        $this->assertEquals(Transaction::STATUS_PROCESSING, $transaction->status);
        $this->assertFalse($transaction->webhook_verified);
    }

    /** @test */
    public function payment_flow_handles_gateway_api_failures()
    {
        // Requirements: 7.4
        // Mock RazorpayService to throw exception
        $this->mock(\App\Services\Payment\Gateways\RazorpayService::class, function ($mock) {
            $mock->shouldReceive('createPayment')
                 ->once()
                 ->andThrow(new \App\Services\Payment\Exceptions\PaymentGatewayException(
                     'API Error',
                     'RAZORPAY_API_ERROR'
                 ));
        });

        $response = $this->actingAs($this->user)
                        ->postJson(route('payment.store'), $this->paymentData);

        $response->assertStatus(400);
        $response->assertJson([
            'success' => false
        ]);

        // Check that transaction was created but marked as failed
        $this->assertDatabaseHas('transactions', [
            'user_id' => $this->user->id,
            'gateway_name' => 'razorpay',
            'status' => Transaction::STATUS_FAILED
        ]);
    }

    /** @test */
    public function payment_form_validation_works()
    {
        // Requirements: 1.2, 8.2
        $invalidData = [
            'gateway' => '',
            'amount' => '',
            'currency' => 'INVALID'
        ];

        $response = $this->actingAs($this->user)
                        ->postJson(route('payment.store'), $invalidData);

        $response->assertStatus(422);
        $response->assertJsonStructure([
            'success',
            'message',
            'errors'
        ]);
        $response->assertJson([
            'success' => false,
            'errors' => [
                'gateway' => [],
                'amount' => []
            ]
        ]);
    }

    /** @test */
    public function guest_user_cannot_access_payment_pages()
    {
        $response = $this->get(route('payment.create'));
        $response->assertRedirect(route('login'));

        $response = $this->postJson(route('payment.store'), $this->paymentData);
        $response->assertStatus(401);
    }

    /** @test */
    public function payment_logging_works_correctly()
    {
        // Requirements: 6.4, 7.4
        // Mock RazorpayService
        $this->mock(\App\Services\Payment\Gateways\RazorpayService::class, function ($mock) {
            $mock->shouldReceive('createPayment')
                 ->once()
                 ->andReturn([
                     'success' => true,
                     'gateway_order_id' => 'order_test123',
                     'amount' => 10000,
                     'currency' => 'INR',
                     'key_id' => 'rzp_test_key',
                     'order_data' => [
                         'id' => 'order_test123',
                         'amount' => 10000,
                         'currency' => 'INR',
                         'status' => 'created'
                     ],
                     'test_mode' => true
                 ]);
        });

        $response = $this->actingAs($this->user)
             ->postJson(route('payment.store'), $this->paymentData);

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'success',
            'transaction',
            'gateway_data'
        ]);
    }
}