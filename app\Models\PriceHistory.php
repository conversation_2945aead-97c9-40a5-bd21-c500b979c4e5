<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class PriceHistory extends Model
{
    use HasFactory;

    protected $fillable = [
        'component_id',
        'price',
        'previous_price',
        'source',
        'metadata',
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'previous_price' => 'decimal:2',
        'metadata' => 'array',
    ];

    public function component(): BelongsTo
    {
        return $this->belongsTo(Component::class);
    }

    public function getPriceChangeAttribute(): ?float
    {
        if ($this->previous_price === null) {
            return null;
        }
        
        return $this->price - $this->previous_price;
    }

    public function getPriceChangePercentageAttribute(): ?float
    {
        if ($this->previous_price === null || $this->previous_price == 0) {
            return null;
        }
        
        return (($this->price - $this->previous_price) / $this->previous_price) * 100;
    }
}