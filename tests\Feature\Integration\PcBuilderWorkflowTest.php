<?php

namespace Tests\Feature\Integration;

use App\Models\Build;
use App\Models\Component;
use App\Models\ComponentCategory;
use App\Models\User;
use App\Services\BuilderService;
use App\Services\CompatibilityService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class PcBuilderWorkflowTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;
    protected ComponentCategory $motherboardCategory;
    protected ComponentCategory $cpuCategory;
    protected ComponentCategory $ramCategory;
    protected ComponentCategory $gpuCategory;
    protected ComponentCategory $storageCategory;
    protected ComponentCategory $psuCategory;
    protected ComponentCategory $caseCategory;
    
    protected Component $motherboard;
    protected Component $cpu;
    protected Component $ram;
    protected Component $gpu;
    protected Component $storage;
    protected Component $psu;
    protected Component $case;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->user = User::factory()->create();
        
        // Create component categories
        $this->motherboardCategory = ComponentCategory::factory()->create([
            'name' => 'Motherboard',
            'slug' => 'motherboard',
            'is_required' => true,
            'display_order' => 1,
        ]);
        
        $this->cpuCategory = ComponentCategory::factory()->create([
            'name' => 'CPU',
            'slug' => 'cpu',
            'is_required' => true,
            'display_order' => 2,
        ]);
        
        $this->ramCategory = ComponentCategory::factory()->create([
            'name' => 'RAM',
            'slug' => 'ram',
            'is_required' => true,
            'display_order' => 3,
        ]);
        
        $this->gpuCategory = ComponentCategory::factory()->create([
            'name' => 'GPU',
            'slug' => 'gpu',
            'is_required' => false,
            'display_order' => 4,
        ]);
        
        $this->storageCategory = ComponentCategory::factory()->create([
            'name' => 'Storage',
            'slug' => 'storage',
            'is_required' => true,
            'display_order' => 5,
        ]);
        
        $this->psuCategory = ComponentCategory::factory()->create([
            'name' => 'PSU',
            'slug' => 'psu',
            'is_required' => true,
            'display_order' => 6,
        ]);
        
        $this->caseCategory = ComponentCategory::factory()->create([
            'name' => 'Case',
            'slug' => 'case',
            'is_required' => true,
            'display_order' => 7,
        ]);
        
        // Create compatible components
        $this->createCompatibleComponents();
    }

    /** @test */
    public function complete_pc_building_workflow_with_compatible_components()
    {
        $this->actingAs($this->user);
        
        // Step 1: Access PC Builder
        $response = $this->get(route('builder.index'));
        $response->assertStatus(200);
        
        // Step 2: Create a build with all components
        $buildData = [
            'name' => 'My Gaming PC',
            'description' => 'High-performance gaming build',
            'components' => [
                $this->motherboard->id,
                $this->cpu->id,
                $this->ram->id,
                $this->gpu->id,
                $this->storage->id,
                $this->psu->id,
                $this->case->id,
            ]
        ];
        
        $response = $this->post(route('builder.save'), $buildData);
        
        // Should redirect to the build show page on success
        $response->assertRedirect();
        
        // Verify build was created
        $build = Build::where('user_id', $this->user->id)
                     ->where('name', 'My Gaming PC')
                     ->first();
        
        $this->assertNotNull($build);
        $this->assertEquals('My Gaming PC', $build->name);
        $this->assertEquals('High-performance gaming build', $build->description);
        
        // Step 3: View the created build
        $response = $this->get(route('builder.show', $build->id));
        $response->assertStatus(200);
        
        // Step 4: Add build to cart
        $response = $this->post(route('shop.cart.add-build', $build->id));
        $response->assertRedirect(route('cart.index'));
        
        // Verify components were added to cart
        $cart = $this->user->cart;
        $this->assertNotNull($cart);
        $this->assertGreaterThan(0, $cart->items()->count());
    }

    /** @test */
    public function pc_building_workflow_with_compatibility_issues()
    {
        $this->actingAs($this->user);
        
        // Create incompatible CPU for the motherboard
        $incompatibleCpu = Component::factory()->create([
            'category_id' => $this->cpuCategory->id,
            'name' => 'Intel Core i9-13900K',
            'brand' => 'Intel',
            'specs' => [
                'socket' => 'LGA1700', // Different socket
                'tdp' => 125,
                'cores' => 24,
                'threads' => 32,
                'chipset_support' => ['Z790', 'B760']
            ],
            'price' => 589.99,
            'stock' => 10,
            'is_active' => true
        ]);
        
        // Test that we can create builds with different components
        $buildData = [
            'name' => 'Test Build with Incompatible CPU',
            'components' => [
                $this->motherboard->id,
                $incompatibleCpu->id,
            ]
        ];
        
        $response = $this->post(route('builder.save'), $buildData);
        $response->assertRedirect();
        
        // Verify build was created even with potentially incompatible components
        $build = Build::where('user_id', $this->user->id)
                     ->where('name', 'Test Build with Incompatible CPU')
                     ->first();
        
        $this->assertNotNull($build);
    }

    /** @test */
    public function pc_building_workflow_with_insufficient_psu_wattage()
    {
        $this->actingAs($this->user);
        
        // Create low wattage PSU
        $lowWattagePsu = Component::factory()->create([
            'category_id' => $this->psuCategory->id,
            'name' => 'EVGA 450W PSU',
            'brand' => 'EVGA',
            'specs' => [
                'wattage' => 450,
                'efficiency' => '80+ Bronze',
                'modular' => false,
                'connectors' => ['24pin', '8pin_cpu', '6pin_pcie']
            ],
            'price' => 59.99,
            'stock' => 15,
            'is_active' => true
        ]);
        
        // Create build with low wattage PSU
        $buildData = [
            'name' => 'Low Wattage Build',
            'components' => [
                $this->motherboard->id,
                $this->cpu->id,
                $this->ram->id,
                $this->gpu->id,
                $this->storage->id,
                $lowWattagePsu->id,
                $this->case->id,
            ]
        ];
        
        $response = $this->post(route('builder.save'), $buildData);
        $response->assertRedirect();
        
        // Verify build was created
        $build = Build::where('user_id', $this->user->id)
                     ->where('name', 'Low Wattage Build')
                     ->first();
        
        $this->assertNotNull($build);
    }

    /** @test */
    public function pc_building_workflow_handles_out_of_stock_components()
    {
        $this->actingAs($this->user);
        
        // Set CPU to out of stock
        $this->cpu->update(['stock' => 0]);
        
        // Try to create build with out of stock component
        $buildData = [
            'name' => 'Build with Out of Stock CPU',
            'components' => [
                $this->motherboard->id,
                $this->cpu->id, // Out of stock
            ]
        ];
        
        $response = $this->post(route('builder.save'), $buildData);
        
        // Build creation should still work, but stock validation might happen elsewhere
        $response->assertRedirect();
        
        // Verify the CPU is indeed out of stock
        $this->assertEquals(0, $this->cpu->fresh()->stock);
    }

    /** @test */
    public function pc_building_workflow_calculates_pricing_correctly()
    {
        $this->actingAs($this->user);
        
        // Create build with all components
        $buildData = [
            'name' => 'Pricing Test Build',
            'components' => [
                $this->motherboard->id,
                $this->cpu->id,
                $this->ram->id,
                $this->gpu->id,
                $this->storage->id,
                $this->psu->id,
                $this->case->id,
            ]
        ];
        
        $response = $this->post(route('builder.save'), $buildData);
        $response->assertRedirect();
        
        // Verify build was created
        $build = Build::where('user_id', $this->user->id)
                     ->where('name', 'Pricing Test Build')
                     ->first();
        
        $this->assertNotNull($build);
        
        // Calculate expected total
        $expectedTotal = $this->calculateExpectedTotal();
        
        // The build should have a reasonable total price
        $this->assertGreaterThan(0, $expectedTotal);
        $this->assertGreaterThan(1000, $expectedTotal); // Should be over $1000 for this build
    }

    protected function createCompatibleComponents(): void
    {
        // Create compatible motherboard
        $this->motherboard = Component::factory()->create([
            'category_id' => $this->motherboardCategory->id,
            'name' => 'ASUS ROG Strix B550-F Gaming',
            'brand' => 'ASUS',
            'specs' => [
                'socket' => 'AM4',
                'chipset' => 'B550',
                'memory_slots' => 4,
                'max_memory' => 128,
                'memory_type' => 'DDR4',
                'supported_memory' => ['DDR4'],
                'max_memory_speed' => 4400,
                'pcie_slots' => 2
            ],
            'price' => 189.99,
            'stock' => 10,
            'is_active' => true
        ]);
        
        // Create compatible CPU
        $this->cpu = Component::factory()->create([
            'category_id' => $this->cpuCategory->id,
            'name' => 'AMD Ryzen 7 5800X',
            'brand' => 'AMD',
            'specs' => [
                'socket' => 'AM4',
                'cores' => 8,
                'threads' => 16,
                'base_clock' => 3.8,
                'boost_clock' => 4.7,
                'tdp' => 105,
                'chipset_support' => ['B550', 'X570', 'A520'],
                'power_consumption' => 105
            ],
            'price' => 299.99,
            'stock' => 5,
            'is_active' => true
        ]);
        
        // Create compatible RAM
        $this->ram = Component::factory()->create([
            'category_id' => $this->ramCategory->id,
            'name' => 'Corsair Vengeance LPX 32GB DDR4-3200',
            'brand' => 'Corsair',
            'specs' => [
                'capacity' => 32,
                'type' => 'DDR4',
                'speed' => 3200,
                'cas_latency' => 16,
                'voltage' => 1.35,
                'memory_type' => 'DDR4',
                'power_consumption' => 5
            ],
            'price' => 129.99,
            'stock' => 20,
            'is_active' => true
        ]);
        
        // Create compatible GPU
        $this->gpu = Component::factory()->create([
            'category_id' => $this->gpuCategory->id,
            'name' => 'NVIDIA RTX 4070',
            'brand' => 'NVIDIA',
            'specs' => [
                'memory' => 12,
                'memory_type' => 'GDDR6X',
                'base_clock' => 1920,
                'boost_clock' => 2475,
                'power_consumption' => 200,
                'pcie_requirement' => '16x',
                'power_connectors' => ['8pin']
            ],
            'price' => 599.99,
            'stock' => 8,
            'is_active' => true
        ]);
        
        // Create compatible storage
        $this->storage = Component::factory()->create([
            'category_id' => $this->storageCategory->id,
            'name' => 'Samsung 980 PRO 1TB NVMe SSD',
            'brand' => 'Samsung',
            'specs' => [
                'capacity' => 1000,
                'type' => 'NVMe',
                'interface' => 'M.2',
                'read_speed' => 7000,
                'write_speed' => 5000,
                'power_consumption' => 7
            ],
            'price' => 149.99,
            'stock' => 15,
            'is_active' => true
        ]);
        
        // Create compatible PSU
        $this->psu = Component::factory()->create([
            'category_id' => $this->psuCategory->id,
            'name' => 'Corsair RM750x 750W 80+ Gold',
            'brand' => 'Corsair',
            'specs' => [
                'wattage' => 750,
                'efficiency' => '80+ Gold',
                'modular' => true,
                'fan_size' => 135,
                'connectors' => ['24pin', '8pin_cpu', '8pin_pcie', '6pin_pcie']
            ],
            'price' => 119.99,
            'stock' => 12,
            'is_active' => true
        ]);
        
        // Create compatible case
        $this->case = Component::factory()->create([
            'category_id' => $this->caseCategory->id,
            'name' => 'Fractal Design Define 7',
            'brand' => 'Fractal Design',
            'specs' => [
                'form_factor' => 'Mid Tower',
                'motherboard_support' => ['ATX', 'mATX', 'Mini-ITX'],
                'max_gpu_length' => 440,
                'max_cpu_cooler_height' => 185
            ],
            'price' => 169.99,
            'stock' => 6,
            'is_active' => true
        ]);
    }
    
    protected function calculateExpectedTotal(): float
    {
        return $this->motherboard->price + 
               $this->cpu->price + 
               $this->ram->price + 
               $this->gpu->price + 
               $this->storage->price + 
               $this->psu->price + 
               $this->case->price;
    }
}