<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Payment;
use App\Models\Order;

class PaymentSeeder extends Seeder
{
    public function run(): void
    {
        $orders = Order::whereIn('status', [
            Order::STATUS_PROCESSING,
            Order::STATUS_SHIPPED,
            Order::STATUS_DELIVERED,
            Order::STATUS_COMPLETED
        ])->get();

        foreach ($orders as $order) {
            $paymentMethod = $this->getRandomPaymentMethod();
            $status = $this->getPaymentStatus($order->status);

            Payment::create([
                'order_id' => $order->id,
                'payment_method' => $paymentMethod,
                'transaction_id' => $this->generateTransactionId($paymentMethod),
                'amount' => $order->total,
                'status' => $status,
                'payment_data' => $this->getPaymentData($paymentMethod),
            ]);
        }

        // Create some failed payments
        $pendingOrders = Order::where('status', Order::STATUS_PENDING)->take(3)->get();
        foreach ($pendingOrders as $order) {
            Payment::create([
                'order_id' => $order->id,
                'payment_method' => $this->getRandomPaymentMethod(),
                'transaction_id' => $this->generateTransactionId('failed'),
                'amount' => $order->total,
                'status' => Payment::STATUS_FAILED,
                'payment_data' => [
                    'error_code' => 'PAYMENT_FAILED',
                    'error_message' => 'Insufficient funds',
                    'gateway_response' => 'Transaction declined by bank',
                ],
            ]);
        }
    }

    private function getRandomPaymentMethod(): string
    {
        $methods = [
            'razorpay',
            'payumoney',
            'cashfree',
            'credit_card',
            'debit_card',
            'upi',
            'net_banking',
        ];

        return $methods[array_rand($methods)];
    }

    private function getPaymentStatus(string $orderStatus): string
    {
        switch ($orderStatus) {
            case Order::STATUS_PROCESSING:
            case Order::STATUS_SHIPPED:
            case Order::STATUS_DELIVERED:
            case Order::STATUS_COMPLETED:
                return Payment::STATUS_COMPLETED;
            case Order::STATUS_CANCELLED:
                return Payment::STATUS_REFUNDED;
            default:
                return Payment::STATUS_PENDING;
        }
    }

    private function generateTransactionId(string $method): string
    {
        $prefix = match($method) {
            'razorpay' => 'pay_',
            'payumoney' => 'PU_',
            'cashfree' => 'CF_',
            'failed' => 'FAIL_',
            default => 'TXN_',
        };

        return $prefix . uniqid() . rand(1000, 9999);
    }

    private function getPaymentData(string $method): array
    {
        return match($method) {
            'razorpay' => [
                'razorpay_payment_id' => 'pay_' . uniqid(),
                'razorpay_order_id' => 'order_' . uniqid(),
                'razorpay_signature' => hash('sha256', 'test_signature'),
                'method' => 'card',
                'card_network' => 'Visa',
                'card_last4' => rand(1000, 9999),
            ],
            'payumoney' => [
                'mihpayid' => rand(*********, *********),
                'mode' => 'CC',
                'status' => 'success',
                'hash' => hash('sha512', 'test_hash'),
                'bank_ref_num' => rand(************, ************),
            ],
            'cashfree' => [
                'cf_payment_id' => rand(*********, *********),
                'payment_status' => 'SUCCESS',
                'payment_amount' => rand(1000, 50000),
                'payment_currency' => 'INR',
                'payment_method' => 'card',
            ],
            'upi' => [
                'upi_id' => 'user@paytm',
                'transaction_ref' => 'UPI' . rand(************, ************),
                'bank' => 'PAYTM',
            ],
            default => [
                'gateway' => $method,
                'reference' => uniqid(),
                'processed_at' => now()->toISOString(),
            ],
        };
    }
}