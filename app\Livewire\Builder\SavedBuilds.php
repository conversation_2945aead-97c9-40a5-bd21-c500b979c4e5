<?php

namespace App\Livewire\Builder;

use App\Models\Build;
use App\Services\BuilderService;
use Livewire\Component;
use Livewire\WithPagination;

class SavedBuilds extends Component
{
    use WithPagination;
    
    public $search = '';
    public $sortBy = 'updated_at';
    public $sortDirection = 'desc';
    public $showPublicOnly = false;
    
    protected $queryString = [
        'search' => ['except' => ''],
        'sortBy' => ['except' => 'updated_at'],
        'sortDirection' => ['except' => 'desc'],
        'showPublicOnly' => ['except' => false],
    ];
    
    protected $listeners = [
        'buildSaved' => 'refreshBuilds',
        'buildDeleted' => 'refreshBuilds',
        'buildCloned' => 'refreshBuilds',
    ];
    
    public function updatingSearch()
    {
        $this->resetPage();
    }
    
    public function updatingSortBy()
    {
        $this->resetPage();
    }
    
    public function updatingSortDirection()
    {
        $this->resetPage();
    }
    
    public function updatingShowPublicOnly()
    {
        $this->resetPage();
    }
    
    public function refreshBuilds()
    {
        $this->resetPage();
    }
    
    public function sortBy($field)
    {
        if ($this->sortBy === $field) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortBy = $field;
            $this->sortDirection = 'asc';
        }
    }
    
    public function editBuild($buildId)
    {
        return redirect()->route('builder.index', ['build' => $buildId]);
    }
    
    public function cloneBuild($buildId)
    {
        $originalBuild = Build::find($buildId);
        
        if (!$originalBuild) {
            session()->flash('error', 'Build not found.');
            return;
        }
        
        $builderService = app(BuilderService::class);
        
        try {
            $clonedBuild = $builderService->cloneBuild($originalBuild, auth()->user(), [
                'name' => 'Copy of ' . $originalBuild->name,
                'is_public' => false,
            ]);
            
            session()->flash('message', 'Build cloned successfully!');
            $this->dispatch('buildCloned', $clonedBuild->id);
            
        } catch (\Exception $e) {
            session()->flash('error', 'Failed to clone build: ' . $e->getMessage());
        }
    }
    
    public function deleteBuild($buildId)
    {
        $build = Build::find($buildId);
        
        if (!$build || $build->user_id !== auth()->id()) {
            session()->flash('error', 'Build not found or access denied.');
            return;
        }
        
        $builderService = app(BuilderService::class);
        
        try {
            $builderService->deleteBuild($build, auth()->user());
            session()->flash('message', 'Build deleted successfully.');
            $this->dispatch('buildDeleted', $buildId);
            
        } catch (\Exception $e) {
            session()->flash('error', 'Failed to delete build: ' . $e->getMessage());
        }
    }
    
    public function toggleVisibility($buildId)
    {
        $build = Build::find($buildId);
        
        if (!$build || $build->user_id !== auth()->id()) {
            session()->flash('error', 'Build not found or access denied.');
            return;
        }
        
        $builderService = app(BuilderService::class);
        $build = $builderService->toggleBuildVisibility($build);
        
        $visibility = $build->is_public ? 'public' : 'private';
        session()->flash('message', "Build is now {$visibility}.");
    }
    
    public function shareUrl($buildId)
    {
        $build = Build::find($buildId);
        
        if (!$build || $build->user_id !== auth()->id()) {
            return null;
        }
        
        $builderService = app(BuilderService::class);
        
        try {
            if ($build->is_public) {
                return $builderService->generateShareableUrl($build);
            } else {
                return $builderService->generatePrivateShareUrl($build);
            }
        } catch (\Exception $e) {
            return null;
        }
    }
    
    public function copyShareUrl($buildId)
    {
        $shareUrl = $this->shareUrl($buildId);
        
        if ($shareUrl) {
            $this->dispatch('copyToClipboard', $shareUrl);
            session()->flash('message', 'Share URL copied to clipboard!');
        } else {
            session()->flash('error', 'Unable to generate share URL.');
        }
    }
    
    public function render()
    {
        if (!auth()->check()) {
            return view('livewire.builder.saved-builds', ['builds' => collect()]);
        }
        
        $query = Build::where('user_id', auth()->id());
        
        // Apply search filter
        if (!empty($this->search)) {
            $query->where(function ($q) {
                $q->where('name', 'like', '%' . $this->search . '%')
                  ->orWhere('description', 'like', '%' . $this->search . '%');
            });
        }
        
        // Apply visibility filter
        if ($this->showPublicOnly) {
            $query->where('is_public', true);
        }
        
        // Apply sorting
        $query->orderBy($this->sortBy, $this->sortDirection);
        
        $builds = $query->with(['components.component.category'])
            ->paginate(12);
        
        return view('livewire.builder.saved-builds', compact('builds'));
    }
}