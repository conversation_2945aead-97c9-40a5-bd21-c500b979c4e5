<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\StockMovement;
use App\Models\Component;
use App\Models\Product;
use App\Models\Order;
use Carbon\Carbon;

class StockMovementSeeder extends Seeder
{
    public function run(): void
    {
        $components = Component::all();
        $products = Product::all();
        $orders = Order::whereIn('status', [
            Order::STATUS_COMPLETED,
            Order::STATUS_PROCESSING,
            Order::STATUS_SHIPPED,
            Order::STATUS_DELIVERED
        ])->get();

        // Create stock movements for components
        foreach ($components as $component) {
            $this->createComponentStockMovements($component);
        }

        // Create stock movements for products
        foreach ($products as $product) {
            $this->createProductStockMovements($product);
        }

        // Create stock movements based on orders
        foreach ($orders->take(20) as $order) {
            $this->createOrderStockMovements($order);
        }
    }

    private function createComponentStockMovements(Component $component): void
    {
        $currentStock = $component->stock;
        $movementHistory = [];
        
        // Create initial stock entry using 'restock' type
        $initialStock = rand(50, 200);
        $movementHistory[] = [
            'component_id' => $component->id,
            'type' => 'restock',
            'quantity_change' => $initialStock,
            'previous_stock' => 0,
            'new_stock' => $initialStock,
            'reason' => 'Initial inventory setup',
            'reference' => 'INIT_' . $component->id,
            'created_at' => Carbon::now()->subMonths(6),
        ];

        $runningStock = $initialStock;

        // Create various stock movements over time
        for ($i = 0; $i < rand(10, 30); $i++) {
            $movementType = $this->getRandomMovementType();
            $quantityChange = $this->getQuantityChange($movementType, $runningStock);
            $previousStock = $runningStock;
            $newStock = max(0, $runningStock + $quantityChange);
            
            $movementHistory[] = [
                'component_id' => $component->id,
                'type' => $movementType,
                'quantity_change' => $quantityChange,
                'previous_stock' => $previousStock,
                'new_stock' => $newStock,
                'reason' => $this->getMovementReason($movementType),
                'reference' => $this->generateReference($movementType),
                'metadata' => $this->getMovementMetadata($movementType),
                'created_at' => Carbon::now()->subDays(rand(1, 180)),
            ];

            $runningStock = $newStock;
        }

        // Update component stock to final value
        $component->update(['stock' => $runningStock]);

        // Insert all movements
        foreach ($movementHistory as $movement) {
            StockMovement::create($movement);
        }
    }

    private function createProductStockMovements(Product $product): void
    {
        if (!$product->manage_stock) {
            return;
        }

        $currentStock = $product->stock_quantity;
        $movementHistory = [];
        
        // Create initial stock entry using 'restock' type
        $initialStock = rand(20, 100);
        $movementHistory[] = [
            'product_id' => $product->id,
            'type' => 'restock',
            'quantity_change' => $initialStock,
            'previous_stock' => 0,
            'new_stock' => $initialStock,
            'reason' => 'Initial product inventory',
            'reference' => 'PROD_INIT_' . $product->id,
            'created_at' => Carbon::now()->subMonths(3),
        ];

        $runningStock = $initialStock;

        // Create stock movements
        for ($i = 0; $i < rand(5, 15); $i++) {
            $movementType = $this->getRandomProductMovementType();
            $quantityChange = $this->getProductQuantityChange($movementType, $runningStock);
            $previousStock = $runningStock;
            $newStock = max(0, $runningStock + $quantityChange);
            
            $movementHistory[] = [
                'product_id' => $product->id,
                'type' => $movementType,
                'quantity_change' => $quantityChange,
                'previous_stock' => $previousStock,
                'new_stock' => $newStock,
                'reason' => $this->getProductMovementReason($movementType),
                'reference' => $this->generateProductReference($movementType),
                'metadata' => $this->getProductMovementMetadata($movementType),
                'created_at' => Carbon::now()->subDays(rand(1, 90)),
            ];

            $runningStock = $newStock;
        }

        // Update product stock
        $product->update([
            'stock_quantity' => $runningStock,
            'in_stock' => $runningStock > 0,
        ]);

        // Insert movements
        foreach ($movementHistory as $movement) {
            StockMovement::create($movement);
        }
    }

    private function createOrderStockMovements(Order $order): void
    {
        foreach ($order->items as $orderItem) {
            if ($orderItem->component) {
                StockMovement::create([
                    'component_id' => $orderItem->component_id,
                    'type' => 'sale',
                    'quantity_change' => -$orderItem->quantity,
                    'previous_stock' => $orderItem->component->stock + $orderItem->quantity,
                    'new_stock' => $orderItem->component->stock,
                    'reason' => 'Order sale',
                    'reference' => 'ORDER_' . $order->order_number,
                    'metadata' => [
                        'order_id' => $order->id,
                        'order_number' => $order->order_number,
                        'customer_name' => $order->billing_name,
                        'sale_price' => $orderItem->price,
                    ],
                    'created_at' => $order->created_at,
                ]);
            }
        }
    }

    private function getRandomMovementType(): string
    {
        // Only use valid enum values: 'sale', 'restock', 'adjustment', 'return', 'damage', 'supplier_sync'
        $types = [
            'restock' => 40,
            'sale' => 30,
            'adjustment' => 10,
            'return' => 8,
            'damage' => 7,
            'supplier_sync' => 5,
        ];

        return $this->weightedRandom($types);
    }

    private function getRandomProductMovementType(): string
    {
        $types = [
            'restock' => 35,
            'sale' => 40,
            'adjustment' => 10,
            'return' => 10,
            'damage' => 5,
        ];

        return $this->weightedRandom($types);
    }

    private function getQuantityChange(string $type, int $currentStock): int
    {
        return match($type) {
            'restock', 'return', 'supplier_sync' => rand(10, 50),
            'sale', 'damage' => -rand(1, min(10, $currentStock)),
            'adjustment' => rand(-5, 5),
            default => 0,
        };
    }

    private function getProductQuantityChange(string $type, int $currentStock): int
    {
        return match($type) {
            'restock', 'return' => rand(5, 30),
            'sale', 'damage' => -rand(1, min(5, $currentStock)),
            'adjustment' => rand(-3, 3),
            default => 0,
        };
    }

    private function getMovementReason(string $type): string
    {
        return match($type) {
            'restock' => 'Stock restocked from supplier',
            'sale' => 'Component sold to customer',
            'adjustment' => 'Stock count adjustment',
            'return' => 'Customer return processed',
            'damage' => 'Damaged goods written off',
            'supplier_sync' => 'Supplier inventory synchronization',
            default => 'Stock movement',
        };
    }

    private function getProductMovementReason(string $type): string
    {
        return match($type) {
            'restock' => 'Product restocked',
            'sale' => 'Product sold',
            'adjustment' => 'Inventory adjustment',
            'return' => 'Product return',
            'damage' => 'Damaged product',
            default => 'Stock movement',
        };
    }

    private function generateReference(string $type): string
    {
        return match($type) {
            'restock' => 'RESTOCK_' . rand(100000, 999999),
            'sale' => 'SALE_' . rand(100000, 999999),
            'adjustment' => 'ADJ_' . rand(100000, 999999),
            'return' => 'RET_' . rand(100000, 999999),
            'damage' => 'DMG_' . rand(100000, 999999),
            'supplier_sync' => 'SYNC_' . rand(100000, 999999),
            default => 'REF_' . rand(100000, 999999),
        };
    }

    private function generateProductReference(string $type): string
    {
        return match($type) {
            'restock' => 'RESTOCK_' . rand(100000, 999999),
            'sale' => 'PSALE_' . rand(100000, 999999),
            'adjustment' => 'PADJ_' . rand(100000, 999999),
            'return' => 'PRET_' . rand(100000, 999999),
            'damage' => 'PDMG_' . rand(100000, 999999),
            default => 'PREF_' . rand(100000, 999999),
        };
    }

    private function getMovementMetadata(string $type): array
    {
        $baseMetadata = [
            'processed_by' => 'system',
            'location' => $this->getRandomLocation(),
        ];

        return match($type) {
            'purchase' => array_merge($baseMetadata, [
                'supplier' => $this->getRandomSupplier(),
                'purchase_order' => 'PO' . rand(100000, 999999),
                'unit_cost' => rand(500, 5000),
            ]),
            'sale' => array_merge($baseMetadata, [
                'customer_type' => rand(1, 100) <= 80 ? 'retail' : 'wholesale',
                'sale_price' => rand(600, 6000),
                'profit_margin' => rand(10, 40) . '%',
            ]),
            'return' => array_merge($baseMetadata, [
                'return_reason' => $this->getReturnReason(),
                'condition' => $this->getReturnCondition(),
                'refund_amount' => rand(500, 5000),
            ]),
            'damage' => array_merge($baseMetadata, [
                'damage_type' => $this->getDamageType(),
                'insurance_claim' => rand(1, 100) <= 30,
                'write_off_value' => rand(500, 5000),
            ]),
            default => $baseMetadata,
        };
    }

    private function getProductMovementMetadata(string $type): array
    {
        return [
            'processed_by' => 'system',
            'location' => $this->getRandomLocation(),
            'batch_number' => 'BATCH_' . rand(1000, 9999),
        ];
    }

    private function getRandomLocation(): string
    {
        $locations = [
            'Main Warehouse',
            'Store Front',
            'Online Fulfillment',
            'Repair Center',
            'Display Area',
        ];

        return $locations[array_rand($locations)];
    }

    private function getRandomSupplier(): string
    {
        $suppliers = [
            'TechDistributor Ltd',
            'ComponentHub India',
            'ElectroSupply Co',
            'DigitalParts Inc',
            'HardwareSource',
        ];

        return $suppliers[array_rand($suppliers)];
    }

    private function getReturnReason(): string
    {
        $reasons = [
            'Defective product',
            'Wrong item ordered',
            'Customer changed mind',
            'Compatibility issue',
            'Damaged in shipping',
        ];

        return $reasons[array_rand($reasons)];
    }

    private function getReturnCondition(): string
    {
        $conditions = ['New', 'Like New', 'Good', 'Fair', 'Poor'];
        return $conditions[array_rand($conditions)];
    }

    private function getDamageType(): string
    {
        $types = [
            'Physical damage',
            'Water damage',
            'Electrical failure',
            'Manufacturing defect',
            'Shipping damage',
        ];

        return $types[array_rand($types)];
    }

    private function weightedRandom(array $weights): string
    {
        $totalWeight = array_sum($weights);
        $random = rand(1, $totalWeight);
        $cumulative = 0;

        foreach ($weights as $item => $weight) {
            $cumulative += $weight;
            if ($random <= $cumulative) {
                return $item;
            }
        }

        return array_key_first($weights);
    }
}