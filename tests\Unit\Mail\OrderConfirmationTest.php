<?php

namespace Tests\Unit\Mail;

use App\Mail\OrderConfirmation;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class OrderConfirmationTest extends TestCase
{
    use RefreshDatabase;

    public function test_order_confirmation_email_has_correct_subject()
    {
        $user = User::factory()->create();
        $order = Order::factory()->create([
            'user_id' => $user->id,
            'order_number' => 'PCB20240101123456789',
        ]);

        $mail = new OrderConfirmation($order);
        $envelope = $mail->envelope();

        $this->assertEquals('Order Confirmation - PCB20240101123456789', $envelope->subject);
    }

    public function test_order_confirmation_email_contains_order_details()
    {
        $user = User::factory()->create(['name' => 'John Doe']);
        $order = Order::factory()->create([
            'user_id' => $user->id,
            'order_number' => 'PCB20240101123456789',
            'total' => 1299.99,
            'subtotal' => 1199.99,
            'tax' => 100.00,
            'shipping' => 0.00,
        ]);

        OrderItem::factory()->create([
            'order_id' => $order->id,
            'name' => 'AMD Ryzen 7 5800X',
            'quantity' => 1,
            'price' => 299.99,
        ]);

        OrderItem::factory()->create([
            'order_id' => $order->id,
            'name' => 'NVIDIA RTX 4070',
            'quantity' => 1,
            'price' => 899.99,
        ]);

        $mail = new OrderConfirmation($order);
        $content = $mail->content();

        $this->assertEquals('emails.order-confirmation', $content->view);
        $this->assertArrayHasKey('order', $content->with);
        $this->assertArrayHasKey('user', $content->with);
        $this->assertArrayHasKey('items', $content->with);
        $this->assertEquals($order->id, $content->with['order']->id);
        $this->assertEquals($user->id, $content->with['user']->id);
    }

    public function test_order_confirmation_email_includes_shipping_address()
    {
        $user = User::factory()->create();
        $order = Order::factory()->create([
            'user_id' => $user->id,
            'shipping_name' => 'John Doe',
            'shipping_address' => '123 Main St',
            'shipping_city' => 'Anytown',
            'shipping_state' => 'CA',
            'shipping_zipcode' => '12345',
            'shipping_country' => 'USA',
        ]);

        $mail = new OrderConfirmation($order);
        $content = $mail->content();

        $this->assertEquals('John Doe', $content->with['order']->shipping_name);
        $this->assertEquals('123 Main St', $content->with['order']->shipping_address);
    }

    public function test_order_confirmation_email_is_queueable()
    {
        $user = User::factory()->create();
        $order = Order::factory()->create(['user_id' => $user->id]);

        $mail = new OrderConfirmation($order);

        $this->assertInstanceOf(\Illuminate\Contracts\Queue\ShouldQueue::class, $mail);
    }

    public function test_order_confirmation_email_serializes_models()
    {
        $user = User::factory()->create();
        $order = Order::factory()->create(['user_id' => $user->id]);

        $mail = new OrderConfirmation($order);
        
        // Test that the mail can be serialized and unserialized
        $serialized = serialize($mail);
        $unserialized = unserialize($serialized);
        
        $this->assertEquals($order->id, $unserialized->order->id);
    }
}