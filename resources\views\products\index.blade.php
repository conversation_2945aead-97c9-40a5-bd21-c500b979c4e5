@extends('layouts.user')

@section('title', 'Products')

@section('content')
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-8">
        <h1 class="text-3xl font-bold text-gray-900">Products</h1>
        
        <!-- Search and Filters -->
        <div class="flex space-x-4">
            <form method="GET" class="flex space-x-2">
                <input type="text" name="search" value="{{ request('search') }}" 
                       placeholder="Search products..." 
                       class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
                    Search
                </button>
            </form>
        </div>
    </div>

    <!-- Filter Bar -->
    <div class="mb-6 p-4 bg-gray-50 rounded-lg">
        <form method="GET" class="flex flex-wrap gap-4 items-center">
            <input type="hidden" name="search" value="{{ request('search') }}">
            
            <select name="category" class="px-3 py-2 border border-gray-300 rounded-lg">
                <option value="">All Categories</option>
                <option value="Electronics" {{ request('category') == 'Electronics' ? 'selected' : '' }}>Electronics</option>
                <option value="PC Components" {{ request('category') == 'PC Components' ? 'selected' : '' }}>PC Components</option>
                <option value="Gaming" {{ request('category') == 'Gaming' ? 'selected' : '' }}>Gaming</option>
            </select>

            <select name="sort" class="px-3 py-2 border border-gray-300 rounded-lg">
                <option value="sort_order" {{ request('sort') == 'sort_order' ? 'selected' : '' }}>Default</option>
                <option value="name" {{ request('sort') == 'name' ? 'selected' : '' }}>Name</option>
                <option value="price" {{ request('sort') == 'price' ? 'selected' : '' }}>Price</option>
                <option value="created_at" {{ request('sort') == 'created_at' ? 'selected' : '' }}>Newest</option>
            </select>

            <label class="flex items-center">
                <input type="checkbox" name="featured" value="1" {{ request('featured') ? 'checked' : '' }} class="mr-2">
                Featured Only
            </label>

            <label class="flex items-center">
                <input type="checkbox" name="on_sale" value="1" {{ request('on_sale') ? 'checked' : '' }} class="mr-2">
                On Sale
            </label>

            <button type="submit" class="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700">
                Apply Filters
            </button>
        </form>
    </div>

    <!-- Products Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        @forelse($products as $product)
            <div class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
                <div class="relative">
                    <img src="{{ $product->primary_image }}" alt="{{ $product->name }}" 
                         class="w-full h-48 object-cover">
                    
                    @if($product->featured)
                        <span class="absolute top-2 left-2 bg-yellow-500 text-white px-2 py-1 text-xs rounded">
                            Featured
                        </span>
                    @endif
                    
                    @if($product->is_on_sale)
                        <span class="absolute top-2 right-2 bg-red-500 text-white px-2 py-1 text-xs rounded">
                            {{ $product->discount_percentage }}% OFF
                        </span>
                    @endif
                </div>

                <div class="p-4">
                    <h3 class="font-semibold text-lg mb-2 line-clamp-2">
                        <a href="{{ route('shop.product', $product->slug) }}" class="hover:text-blue-600">
                            {{ $product->name }}
                        </a>
                    </h3>
                    
                    <p class="text-gray-600 text-sm mb-3 line-clamp-2">{{ $product->short_description }}</p>
                    
                    <div class="flex items-center justify-between mb-3">
                        <div class="flex items-center space-x-2">
                            @if($product->is_on_sale)
                                <span class="text-lg font-bold text-red-600">{{ $product->formatted_price }}</span>
                                <span class="text-sm text-gray-500 line-through">{{ $product->original_price }}</span>
                            @else
                                <span class="text-lg font-bold text-gray-900">{{ $product->formatted_price }}</span>
                            @endif
                        </div>
                        
                        <span class="text-xs px-2 py-1 rounded {{ $product->in_stock ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                            {{ $product->getStockStatus() }}
                        </span>
                    </div>

                    <div class="flex space-x-2">
                        <a href="{{ route('shop.product', $product->slug) }}" 
                           class="flex-1 bg-blue-600 text-white text-center py-2 px-4 rounded hover:bg-blue-700 transition-colors">
                            View Details
                        </a>
                        
                        @if($product->isAvailable())
                            <button class="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 transition-colors">
                                Add to Cart
                            </button>
                        @endif
                    </div>
                </div>
            </div>
        @empty
            <div class="col-span-full text-center py-12">
                <p class="text-gray-500 text-lg">No products found.</p>
            </div>
        @endforelse
    </div>

    <!-- Pagination -->
    <div class="mt-8">
        {{ $products->appends(request()->query())->links() }}
    </div>
</div>
@endsection