<?php

namespace App\Services;

use App\Services\Payment\Contracts\PaymentGatewayInterface;
use App\Services\Payment\Exceptions\GatewayConfigurationException;
use App\Models\GatewaySetting;
use InvalidArgumentException;

class PaymentGatewayFactory
{
    private array $gateways = [];
    private array $supportedGateways = ['razorpay', 'payumoney', 'cashfree'];
    private array $registeredGateways = [];

    /**
     * Create a payment gateway instance
     *
     * @param string $gateway Gateway name
     * @return PaymentGatewayInterface
     * @throws GatewayConfigurationException
     */
    public function create(string $gateway): PaymentGatewayInterface
    {
        if (!$this->isSupported($gateway)) {
            throw new InvalidArgumentException("Unsupported gateway: $gateway");
        }

        if (!isset($this->gateways[$gateway])) {
            $this->gateways[$gateway] = $this->createGatewayInstance($gateway);
        }

        return $this->gateways[$gateway];
    }

    /**
     * Get all available (enabled) gateways
     *
     * @return array
     */
    public function getAvailableGateways(): array
    {
        $enabledGateways = GatewaySetting::enabled()->get();
        $available = [];

        foreach ($enabledGateways as $setting) {
            if ($this->isSupported($setting->gateway_name)) {
                $available[] = [
                    'name' => $setting->gateway_name,
                    'display_name' => config("payment.gateways.{$setting->gateway_name}.name", ucfirst($setting->gateway_name)),
                    'is_enabled' => $setting->is_enabled,
                    'is_test_mode' => $setting->is_test_mode,
                ];
            }
        }

        return $available;
    }

    /**
     * Check if a gateway is supported
     *
     * @param string $gateway
     * @return bool
     */
    public function isSupported(string $gateway): bool
    {
        return in_array($gateway, $this->supportedGateways) || isset($this->registeredGateways[$gateway]);
    }

    /**
     * Get supported gateway names
     *
     * @return array
     */
    public function getSupportedGateways(): array
    {
        return array_merge($this->supportedGateways, array_keys($this->registeredGateways));
    }

    /**
     * Register a custom gateway
     *
     * @param string $name Gateway name
     * @param string $serviceClass Service class name
     * @return void
     */
    public function registerGateway(string $name, string $serviceClass): void
    {
        $this->registeredGateways[$name] = $serviceClass;
    }

    /**
     * Create gateway instance based on gateway name
     *
     * @param string $gateway
     * @return PaymentGatewayInterface
     * @throws GatewayConfigurationException
     */
    private function createGatewayInstance(string $gateway): PaymentGatewayInterface
    {
        // Check if it's a registered custom gateway first
        if (isset($this->registeredGateways[$gateway])) {
            return app()->make($this->registeredGateways[$gateway]);
        }

        // Handle built-in gateways
        return match($gateway) {
            'razorpay' => app()->make('App\Services\Payment\Gateways\RazorpayService'),
            'payumoney' => app()->make('App\Services\Payment\Gateways\PayUmoneyService'),
            'cashfree' => app()->make('App\Services\Payment\Gateways\CashfreeService'),
            default => throw new GatewayConfigurationException("Gateway service for '$gateway' not implemented")
        };
    }
}