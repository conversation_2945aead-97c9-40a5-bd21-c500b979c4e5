<?php

namespace Tests\Unit\Services;

use App\Models\Component;
use App\Models\ComponentCategory;
use App\Models\PriceHistory;
use App\Services\PriceTrackingService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Tests\TestCase;

class PriceTrackingServiceTest extends TestCase
{
    use RefreshDatabase;

    protected PriceTrackingService $priceTrackingService;
    protected Component $component;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->priceTrackingService = new PriceTrackingService();
        
        $category = ComponentCategory::factory()->create();
        $this->component = Component::factory()->create([
            'category_id' => $category->id,
            'price' => 100.00,
            'is_active' => true
        ]);
    }

    public function test_updates_component_price_when_new_price_found()
    {
        // Mock the protected method to return a new price
        $service = $this->getMockBuilder(PriceTrackingService::class)
            ->onlyMethods(['fetchPriceFromProviders'])
            ->getMock();
        
        $service->expects($this->once())
            ->method('fetchPriceFromProviders')
            ->with($this->component)
            ->willReturn(120.00);

        $result = $service->updatePricesForComponent($this->component);

        $this->assertTrue($result);
        $this->assertEquals(120.00, $this->component->fresh()->price);
        
        // Check price history was recorded
        $this->assertDatabaseHas('price_histories', [
            'component_id' => $this->component->id,
            'price' => 120.00,
            'previous_price' => 100.00,
            'source' => 'automated'
        ]);
    }

    public function test_does_not_update_price_when_no_new_price_found()
    {
        $service = $this->getMockBuilder(PriceTrackingService::class)
            ->onlyMethods(['fetchPriceFromProviders'])
            ->getMock();
        
        $service->expects($this->once())
            ->method('fetchPriceFromProviders')
            ->with($this->component)
            ->willReturn(null);

        $result = $service->updatePricesForComponent($this->component);

        $this->assertFalse($result);
        $this->assertEquals(100.00, $this->component->fresh()->price);
        $this->assertDatabaseMissing('price_histories', [
            'component_id' => $this->component->id
        ]);
    }

    public function test_does_not_update_price_when_price_unchanged()
    {
        $service = $this->getMockBuilder(PriceTrackingService::class)
            ->onlyMethods(['fetchPriceFromProviders'])
            ->getMock();
        
        $service->expects($this->once())
            ->method('fetchPriceFromProviders')
            ->with($this->component)
            ->willReturn(100.00);

        $result = $service->updatePricesForComponent($this->component);

        $this->assertFalse($result);
        $this->assertEquals(100.00, $this->component->fresh()->price);
        $this->assertDatabaseMissing('price_histories', [
            'component_id' => $this->component->id
        ]);
    }

    public function test_handles_exception_during_price_update()
    {
        Log::shouldReceive('error')->once();

        $service = $this->getMockBuilder(PriceTrackingService::class)
            ->onlyMethods(['fetchPriceFromProviders'])
            ->getMock();
        
        $service->expects($this->once())
            ->method('fetchPriceFromProviders')
            ->with($this->component)
            ->willThrowException(new \Exception('API Error'));

        $result = $service->updatePricesForComponent($this->component);

        $this->assertFalse($result);
        $this->assertEquals(100.00, $this->component->fresh()->price);
    }

    public function test_updates_all_prices_for_active_components()
    {
        $category = ComponentCategory::factory()->create();
        
        // Create active and inactive components
        $activeComponent1 = Component::factory()->create([
            'category_id' => $category->id,
            'price' => 100.00,
            'is_active' => true
        ]);
        
        $activeComponent2 = Component::factory()->create([
            'category_id' => $category->id,
            'price' => 200.00,
            'is_active' => true
        ]);
        
        $inactiveComponent = Component::factory()->create([
            'category_id' => $category->id,
            'price' => 300.00,
            'is_active' => false
        ]);

        $service = $this->getMockBuilder(PriceTrackingService::class)
            ->onlyMethods(['updatePricesForComponent'])
            ->getMock();
        
        $service->expects($this->exactly(3)) // 2 active + 1 from setUp
            ->method('updatePricesForComponent')
            ->willReturn(true);

        $results = $service->updateAllPrices();

        $this->assertEquals(3, $results['updated']);
        $this->assertEquals(0, $results['failed']);
        $this->assertEquals(0, $results['unchanged']);
    }

    public function test_gets_price_history_for_component()
    {
        // Create price history records
        PriceHistory::factory()->create([
            'component_id' => $this->component->id,
            'price' => 90.00,
            'created_at' => now()->subDays(5)
        ]);
        
        PriceHistory::factory()->create([
            'component_id' => $this->component->id,
            'price' => 110.00,
            'created_at' => now()->subDays(10)
        ]);
        
        PriceHistory::factory()->create([
            'component_id' => $this->component->id,
            'price' => 120.00,
            'created_at' => now()->subDays(40) // Outside 30-day window
        ]);

        $history = $this->priceTrackingService->getPriceHistory($this->component, 30);

        $this->assertCount(2, $history);
        $this->assertEquals(90.00, $history->first()->price);
    }

    public function test_gets_lowest_price_from_history()
    {
        PriceHistory::factory()->create([
            'component_id' => $this->component->id,
            'price' => 90.00,
            'created_at' => now()->subDays(5)
        ]);
        
        PriceHistory::factory()->create([
            'component_id' => $this->component->id,
            'price' => 110.00,
            'created_at' => now()->subDays(10)
        ]);

        $lowestPrice = $this->priceTrackingService->getLowestPrice($this->component, 30);

        $this->assertEquals(90.00, $lowestPrice);
    }

    public function test_gets_highest_price_from_history()
    {
        PriceHistory::factory()->create([
            'component_id' => $this->component->id,
            'price' => 90.00,
            'created_at' => now()->subDays(5)
        ]);
        
        PriceHistory::factory()->create([
            'component_id' => $this->component->id,
            'price' => 110.00,
            'created_at' => now()->subDays(10)
        ]);

        $highestPrice = $this->priceTrackingService->getHighestPrice($this->component, 30);

        $this->assertEquals(110.00, $highestPrice);
    }

    public function test_gets_average_price_from_history()
    {
        PriceHistory::factory()->create([
            'component_id' => $this->component->id,
            'price' => 90.00,
            'created_at' => now()->subDays(5)
        ]);
        
        PriceHistory::factory()->create([
            'component_id' => $this->component->id,
            'price' => 110.00,
            'created_at' => now()->subDays(10)
        ]);

        $averagePrice = $this->priceTrackingService->getAveragePrice($this->component, 30);

        $this->assertEquals(100.00, $averagePrice);
    }

    public function test_creates_price_alert()
    {
        Log::shouldReceive('info')->once()->with(
            "Price alert created for component {$this->component->id} at target price 80 for <EMAIL>"
        );

        $result = $this->priceTrackingService->createPriceAlert($this->component, 80.00, '<EMAIL>');

        $this->assertTrue($result);
    }
}