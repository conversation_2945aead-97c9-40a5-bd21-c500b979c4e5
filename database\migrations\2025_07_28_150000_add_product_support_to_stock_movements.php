<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('stock_movements', function (Blueprint $table) {
            // Add product_id column
            $table->foreignId('product_id')->nullable()->constrained()->onDelete('cascade');
            
            // Make component_id nullable since we now support both components and products
            $table->foreignId('component_id')->nullable()->change();
            
            // Add index for product_id
            $table->index(['product_id', 'created_at']);
        });
    }

    public function down(): void
    {
        Schema::table('stock_movements', function (Blueprint $table) {
            // Check if the foreign key exists before trying to drop it
            if (Schema::hasColumn('stock_movements', 'product_id')) {
                // Try to drop foreign key if it exists
                try {
                    $table->dropForeign(['product_id']);
                } catch (\Exception $e) {
                    // Foreign key might not exist, continue
                }
                $table->dropColumn('product_id');
            }

            // Note: We cannot revert component_id to NOT NULL if there are records with NULL values
            // This would require data cleanup first, so we'll leave it nullable
            // $table->foreignId('component_id')->nullable(false)->change();
        });
    }
};