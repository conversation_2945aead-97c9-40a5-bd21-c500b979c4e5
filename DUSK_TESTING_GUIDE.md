# Laravel Dusk Testing Guide

## Overview

Laravel Dusk has been successfully set up for your project with comprehensive browser testing capabilities. This guide covers everything you need to know about running and writing Dusk tests.

## Quick Start

### 1. Initial Setup
```bash
# Run the setup script to configure everything
php setup_dusk.php
```

### 2. Run Tests
```bash
# Run all tests
php run_dusk_tests.php

# Run specific test suite
php run_dusk_tests.php --suite payment
php run_dusk_tests.php --suite admin
php run_dusk_tests.php --suite products

# Run with visible browser (for debugging)
php run_dusk_tests.php --no-headless

# Run individual test
php artisan dusk tests/Browser/PaymentFlowTest.php
```

## Test Structure

### Test Files Created
- `tests/Browser/PaymentFlowTest.php` - Payment gateway testing
- `tests/Browser/AdminPanelTest.php` - Admin panel functionality
- `tests/Browser/ProductBrowsingTest.php` - Product browsing and search
- `tests/Browser/ExampleTest.php` - Basic application tests

### Page Objects
- `tests/Browser/Pages/LoginPage.php` - Login functionality
- `tests/Browser/Pages/PaymentPage.php` - Payment form interactions
- `tests/Browser/Pages/AdminDashboardPage.php` - Admin dashboard navigation
- `tests/Browser/Pages/ProductsPage.php` - Product browsing and filtering

### Components
- `tests/Browser/Components/PaymentGatewaySelector.php` - Payment gateway selection
- `tests/Browser/Components/AdminSidebar.php` - Admin navigation sidebar

## Configuration Files

### Environment Configuration
- `.env.dusk.local` - Dusk-specific environment settings
- Uses SQLite in-memory database for faster testing
- Configured for all three payment gateways (Razorpay, PayUmoney, Cashfree)

### Test Configuration
- `tests/DuskTestCase.php` - Base test case with Chrome options
- Configured for headless testing by default
- Includes database migrations for clean test state

## Test Suites

### Payment Tests (`--suite payment`)
- Payment form validation
- Gateway selection testing
- Payment flow end-to-end
- Error handling scenarios
- Loading state verification

### Admin Tests (`--suite admin`)
- Admin authentication
- Dashboard navigation
- Statistics display
- Access control verification
- Recent transactions view

### Product Tests (`--suite products`)
- Product listing display
- Search functionality
- Category filtering
- Add to cart functionality
- Product detail views

## Writing New Tests

### Basic Test Structure
```php
<?php

namespace Tests\Browser;

use Laravel\Dusk\Browser;
use Tests\DuskTestCase;

class MyNewTest extends DuskTestCase
{
    public function test_my_feature(): void
    {
        $this->browse(function (Browser $browser) {
            $browser->visit('/my-page')
                    ->assertSee('Expected Text')
                    ->click('#my-button')
                    ->waitForText('Success Message');
        });
    }
}
```

### Using Page Objects
```php
use Tests\Browser\Pages\PaymentPage;

public function test_payment_flow(): void
{
    $this->browse(function (Browser $browser) {
        $browser->visit(new PaymentPage)
                ->fillPaymentForm([
                    'amount' => '100.00',
                    'gateway' => 'razorpay'
                ])
                ->submitPayment()
                ->waitForLocation('/payments/status');
    });
}
```

### Using Components
```php
use Tests\Browser\Components\PaymentGatewaySelector;

public function test_gateway_selection(): void
{
    $this->browse(function (Browser $browser) {
        $browser->visit('/payments/create')
                ->within(new PaymentGatewaySelector, function ($browser) {
                    $browser->selectGateway('razorpay')
                            ->assertGatewaySelected('razorpay');
                });
    });
}
```

## Common Dusk Methods

### Navigation
```php
$browser->visit('/path')           // Navigate to URL
$browser->back()                   // Go back
$browser->forward()                // Go forward
$browser->refresh()                // Refresh page
```

### Assertions
```php
$browser->assertSee('text')        // Text is visible
$browser->assertDontSee('text')    // Text is not visible
$browser->assertPresent('#id')     // Element exists
$browser->assertMissing('#id')     // Element doesn't exist
$browser->assertPathIs('/path')    // Current path matches
$browser->assertTitle('title')     // Page title matches
```

### Interactions
```php
$browser->click('#button')         // Click element
$browser->type('#input', 'text')   // Type in input
$browser->select('#select', 'value') // Select option
$browser->check('#checkbox')       // Check checkbox
$browser->uncheck('#checkbox')     // Uncheck checkbox
```

### Waiting
```php
$browser->waitFor('#element')      // Wait for element to appear
$browser->waitForText('text')      // Wait for text to appear
$browser->waitForLocation('/path') // Wait for URL change
$browser->waitUntilMissing('#el')  // Wait for element to disappear
```

## Debugging

### Screenshots
Screenshots are automatically taken on test failures and saved to `tests/Browser/screenshots/`

### Manual Screenshots
```php
$browser->screenshot('my-test-screenshot');
```

### Console Logs
Console logs are saved to `tests/Browser/console/` for debugging JavaScript issues

### Page Source
Page source is saved to `tests/Browser/source/` when tests fail

### Running with Visible Browser
```bash
php run_dusk_tests.php --no-headless
```

## Best Practices

### 1. Use Page Objects
- Create page objects for complex pages
- Keep selectors in one place
- Make tests more readable and maintainable

### 2. Use Components
- Create reusable components for common UI elements
- Share functionality across multiple tests

### 3. Database Management
- Use `DatabaseMigrations` trait for clean test state
- Create factories for test data
- Use transactions when possible

### 4. Waiting Strategies
- Always wait for dynamic content
- Use specific waits (`waitForText`) over generic delays
- Set reasonable timeouts

### 5. Test Organization
- Group related tests in the same file
- Use descriptive test method names
- Keep tests focused and atomic

## Troubleshooting

### Common Issues

#### ChromeDriver Not Found
```bash
php artisan dusk:chrome-driver --detect
```

#### Tests Timing Out
- Increase wait timeouts
- Check for JavaScript errors in console logs
- Verify element selectors are correct

#### Database Issues
- Ensure `.env.dusk.local` is configured correctly
- Run migrations: `php artisan migrate --env=dusk.local`
- Check database permissions

#### Browser Not Starting
- Verify Chrome/Chromium is installed
- Check ChromeDriver compatibility
- Try running without headless mode

### Getting Help

1. Check the Laravel Dusk documentation: https://laravel.com/docs/dusk
2. Review test output and error messages
3. Use `--verbose` flag for detailed output
4. Check screenshots and console logs for debugging

## Advanced Features

### Custom Assertions
Create custom assertions in your test case:

```php
protected function assertPaymentSuccessful(Browser $browser): void
{
    $browser->waitForLocation('/payments/success')
            ->assertSee('Payment Successful')
            ->assertPresent('.success-icon');
}
```

### Environment-Specific Tests
```php
public function test_production_feature(): void
{
    if (env('APP_ENV') !== 'production') {
        $this->markTestSkipped('Production only test');
    }
    
    // Test production-specific functionality
}
```

### Parallel Testing
For faster test execution, consider running tests in parallel using tools like Paratest.

This comprehensive setup provides you with a robust browser testing framework for your Laravel application with payment gateway integration.