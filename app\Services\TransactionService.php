<?php

namespace App\Services;

use App\Models\Transaction;
use App\Models\User;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;
use Exception;

class TransactionService
{
    /**
     * Create a new transaction
     *
     * @param array $data
     * @return Transaction
     */
    public function createTransaction(array $data): Transaction
    {
        $transactionData = [
            'user_id' => $data['user_id'],
            'gateway_name' => $data['gateway_name'],
            'amount' => $data['amount'],
            'currency' => $data['currency'] ?? 'INR',
            'status' => $data['status'] ?? Transaction::STATUS_PENDING,
            'transaction_id' => $data['transaction_id'],
            'gateway_transaction_id' => $data['gateway_transaction_id'] ?? null,
            'payment_details' => $data['payment_details'] ?? null,
            'webhook_verified' => $data['webhook_verified'] ?? false,
            'failure_reason' => $data['failure_reason'] ?? null,
        ];
        
        // Set completed_at timestamp if transaction is created with completed status
        if (($data['status'] ?? Transaction::STATUS_PENDING) === Transaction::STATUS_COMPLETED) {
            $transactionData['completed_at'] = $data['completed_at'] ?? now();
        }
        
        return Transaction::create($transactionData);
    }

    /**
     * Update transaction
     *
     * @param int $id
     * @param array $data
     * @return bool
     */
    public function updateTransaction(int $id, array $data): bool
    {
        $transaction = Transaction::find($id);
        
        if (!$transaction) {
            return false;
        }

        // Log status transitions
        if (isset($data['status']) && $data['status'] !== $transaction->status) {
            Log::info('Transaction status changed', [
                'transaction_id' => $transaction->transaction_id,
                'old_status' => $transaction->status,
                'new_status' => $data['status']
            ]);
            
            // Set completed_at timestamp when status is changed to completed
            if ($data['status'] === Transaction::STATUS_COMPLETED && !isset($data['completed_at'])) {
                $data['completed_at'] = now();
            }
        }

        return $transaction->update($data);
    }

    /**
     * Get transaction by ID
     *
     * @param int $id
     * @return Transaction|null
     */
    public function getTransaction(int $id): ?Transaction
    {
        return Transaction::find($id);
    }

    /**
     * Get transaction by transaction ID
     *
     * @param string $transactionId
     * @return Transaction|null
     */
    public function getTransactionByTransactionId(string $transactionId): ?Transaction
    {
        return Transaction::where('transaction_id', $transactionId)->first();
    }

    /**
     * Get transaction by gateway transaction ID
     *
     * @param string $gatewayTransactionId
     * @param string|null $gateway
     * @return Transaction|null
     */
    public function getTransactionByGatewayTransactionId(string $gatewayTransactionId, ?string $gateway = null): ?Transaction
    {
        $query = Transaction::where('gateway_transaction_id', $gatewayTransactionId);
        
        if ($gateway) {
            $query->where('gateway_name', $gateway);
        }
        
        return $query->first();
    }

    /**
     * Get transactions with filters and pagination
     *
     * @param array $filters
     * @param int $perPage
     * @return LengthAwarePaginator
     */
    public function getTransactions(array $filters = [], int $perPage = 15): LengthAwarePaginator
    {
        $query = Transaction::with('user');

        // Apply filters
        if (!empty($filters['user_id'])) {
            $query->byUser($filters['user_id']);
        }

        if (!empty($filters['gateway'])) {
            $query->byGateway($filters['gateway']);
        }

        if (!empty($filters['status'])) {
            $query->byStatus($filters['status']);
        }

        if (!empty($filters['start_date']) && !empty($filters['end_date'])) {
            $query->byDateRange($filters['start_date'], $filters['end_date']);
        }

        if (!empty($filters['search'])) {
            $query->where(function ($q) use ($filters) {
                $q->where('transaction_id', 'like', '%' . $filters['search'] . '%')
                  ->orWhere('gateway_transaction_id', 'like', '%' . $filters['search'] . '%')
                  ->orWhere('amount', 'like', '%' . $filters['search'] . '%')
                  ->orWhereHas('user', function ($userQuery) use ($filters) {
                      $userQuery->where('email', 'like', '%' . $filters['search'] . '%')
                               ->orWhere('name', 'like', '%' . $filters['search'] . '%');
                  });
            });
        }

        // Apply sorting
        $sortBy = $filters['sort_by'] ?? 'created_at';
        $sortOrder = $filters['sort_order'] ?? 'desc';
        $query->orderBy($sortBy, $sortOrder);

        return $query->paginate($perPage);
    }

    /**
     * Get user transactions
     *
     * @param int $userId
     * @param array $filters
     * @param int $perPage
     * @return LengthAwarePaginator
     */
    public function getUserTransactions(int $userId, array $filters = [], int $perPage = 15): LengthAwarePaginator
    {
        $filters['user_id'] = $userId;
        return $this->getTransactions($filters, $perPage);
    }

    /**
     * Get transaction statistics
     *
     * @param array $filters
     * @return array
     */
    public function getTransactionStatistics(array $filters = []): array
    {
        $query = Transaction::query();

        // Apply date filter if provided
        if (!empty($filters['start_date']) && !empty($filters['end_date'])) {
            $query->byDateRange($filters['start_date'], $filters['end_date']);
        }

        // Apply gateway filter if provided
        if (!empty($filters['gateway'])) {
            $query->byGateway($filters['gateway']);
        }

        $stats = [
            'total_transactions' => $query->count(),
            'total_amount' => $query->sum('amount'),
            'successful_transactions' => $query->where('status', Transaction::STATUS_COMPLETED)->count(),
            'failed_transactions' => $query->where('status', Transaction::STATUS_FAILED)->count(),
            'pending_transactions' => $query->where('status', Transaction::STATUS_PENDING)->count(),
            'processing_transactions' => $query->where('status', Transaction::STATUS_PROCESSING)->count(),
            'cancelled_transactions' => $query->where('status', Transaction::STATUS_CANCELLED)->count(),
        ];

        // Calculate success rate
        $stats['success_rate'] = $stats['total_transactions'] > 0 
            ? round(($stats['successful_transactions'] / $stats['total_transactions']) * 100, 2)
            : 0;

        // Calculate average transaction amount
        $stats['average_amount'] = $stats['total_transactions'] > 0
            ? round($stats['total_amount'] / $stats['total_transactions'], 2)
            : 0;

        // Get successful amount
        $stats['successful_amount'] = $query->where('status', Transaction::STATUS_COMPLETED)->sum('amount');

        return $stats;
    }

    /**
     * Get gateway-wise statistics
     *
     * @param array $filters
     * @return array
     */
    public function getGatewayStatistics(array $filters = []): array
    {
        $query = Transaction::query();

        // Apply date filter if provided
        if (!empty($filters['start_date']) && !empty($filters['end_date'])) {
            $query->byDateRange($filters['start_date'], $filters['end_date']);
        }

        $gatewayStats = $query->select([
                'gateway_name',
                DB::raw('COUNT(*) as total_transactions'),
                DB::raw('SUM(amount) as total_amount'),
                DB::raw('COUNT(CASE WHEN status = "completed" THEN 1 END) as successful_transactions'),
                DB::raw('COUNT(CASE WHEN status = "failed" THEN 1 END) as failed_transactions'),
                DB::raw('COUNT(CASE WHEN status = "pending" THEN 1 END) as pending_transactions'),
                DB::raw('SUM(CASE WHEN status = "completed" THEN amount ELSE 0 END) as successful_amount')
            ])
            ->groupBy('gateway_name')
            ->get()
            ->map(function ($stat) {
                $stat->success_rate = $stat->total_transactions > 0 
                    ? round(($stat->successful_transactions / $stat->total_transactions) * 100, 2)
                    : 0;
                $stat->average_amount = $stat->total_transactions > 0
                    ? round($stat->total_amount / $stat->total_transactions, 2)
                    : 0;
                return $stat;
            });

        return $gatewayStats->toArray();
    }

    /**
     * Get daily transaction statistics
     *
     * @param int $days
     * @param string|null $gateway
     * @return array
     */
    public function getDailyStatistics(int $days = 30, ?string $gateway = null): array
    {
        $startDate = Carbon::now()->subDays($days)->startOfDay();
        $endDate = Carbon::now()->endOfDay();

        $query = Transaction::whereBetween('created_at', [$startDate, $endDate]);

        if ($gateway) {
            $query->byGateway($gateway);
        }

        $dailyStats = $query->select([
                DB::raw('DATE(created_at) as date'),
                DB::raw('COUNT(*) as total_transactions'),
                DB::raw('SUM(amount) as total_amount'),
                DB::raw('COUNT(CASE WHEN status = "completed" THEN 1 END) as successful_transactions'),
                DB::raw('SUM(CASE WHEN status = "completed" THEN amount ELSE 0 END) as successful_amount')
            ])
            ->groupBy(DB::raw('DATE(created_at)'))
            ->orderBy('date')
            ->get()
            ->map(function ($stat) {
                $stat->success_rate = $stat->total_transactions > 0 
                    ? round(($stat->successful_transactions / $stat->total_transactions) * 100, 2)
                    : 0;
                return $stat;
            });

        return $dailyStats->toArray();
    }

    /**
     * Update transaction status
     *
     * @param string $transactionId
     * @param string $status
     * @param array $additionalData
     * @return bool
     */
    public function updateTransactionStatus(string $transactionId, string $status, array $additionalData = []): bool
    {
        $transaction = $this->getTransactionByTransactionId($transactionId);
        
        if (!$transaction) {
            return false;
        }

        $updateData = array_merge(['status' => $status], $additionalData);
        
        // Set completed_at timestamp when status is changed to completed
        if ($status === Transaction::STATUS_COMPLETED && $transaction->status !== Transaction::STATUS_COMPLETED) {
            $updateData['completed_at'] = now();
        }
        
        return $this->updateTransaction($transaction->id, $updateData);
    }

    /**
     * Mark transaction as webhook verified
     *
     * @param string $transactionId
     * @param array $webhookData
     * @return bool
     */
    public function markWebhookVerified(string $transactionId, array $webhookData = []): bool
    {
        $transaction = $this->getTransactionByTransactionId($transactionId);
        
        if (!$transaction) {
            return false;
        }

        $paymentDetails = $transaction->payment_details ?? [];
        $paymentDetails['webhook_data'] = $webhookData;
        $paymentDetails['webhook_verified_at'] = now();

        return $this->updateTransaction($transaction->id, [
            'webhook_verified' => true,
            'payment_details' => $paymentDetails
        ]);
    }

    /**
     * Get transactions that need status update
     *
     * @param int $minutes
     * @return Collection
     */
    public function getStaleTransactions(int $minutes = 30): Collection
    {
        return Transaction::whereIn('status', [Transaction::STATUS_PENDING, Transaction::STATUS_PROCESSING])
            ->where('created_at', '<', Carbon::now()->subMinutes($minutes))
            ->get();
    }

    /**
     * Get recent transactions
     *
     * @param int $limit
     * @param int|null $userId
     * @return Collection
     */
    public function getRecentTransactions(int $limit = 10, ?int $userId = null): Collection
    {
        $query = Transaction::with('user')->orderBy('created_at', 'desc');
        
        if ($userId) {
            $query->byUser($userId);
        }
        
        return $query->limit($limit)->get();
    }

    /**
     * Delete old transactions
     *
     * @param int $days
     * @return int Number of deleted transactions
     */
    public function deleteOldTransactions(int $days = 365): int
    {
        $cutoffDate = Carbon::now()->subDays($days);
        
        return Transaction::where('created_at', '<', $cutoffDate)
            ->whereIn('status', [Transaction::STATUS_COMPLETED, Transaction::STATUS_FAILED, Transaction::STATUS_CANCELLED])
            ->delete();
    }

    /**
     * Export transactions to array
     *
     * @param array $filters
     * @return array
     */
    public function exportTransactions(array $filters = []): array
    {
        $query = Transaction::with('user');

        // Apply same filters as getTransactions
        if (!empty($filters['user_id'])) {
            $query->byUser($filters['user_id']);
        }

        if (!empty($filters['gateway'])) {
            $query->byGateway($filters['gateway']);
        }

        if (!empty($filters['status'])) {
            $query->byStatus($filters['status']);
        }

        if (!empty($filters['start_date']) && !empty($filters['end_date'])) {
            $query->byDateRange($filters['start_date'], $filters['end_date']);
        }

        return $query->orderBy('created_at', 'desc')
            ->get()
            ->map(function ($transaction) {
                return [
                    'transaction_id' => $transaction->transaction_id,
                    'gateway_transaction_id' => $transaction->gateway_transaction_id,
                    'user_email' => $transaction->user->email ?? 'N/A',
                    'user_name' => $transaction->user->name ?? 'N/A',
                    'gateway_name' => $transaction->gateway_name,
                    'amount' => $transaction->amount,
                    'currency' => $transaction->currency,
                    'status' => $transaction->status,
                    'webhook_verified' => $transaction->webhook_verified ? 'Yes' : 'No',
                    'failure_reason' => $transaction->failure_reason,
                    'created_at' => $transaction->created_at->format('Y-m-d H:i:s'),
                    'updated_at' => $transaction->updated_at->format('Y-m-d H:i:s'),
                    'completed_at' => $transaction->completed_at ? $transaction->completed_at->format('Y-m-d H:i:s') : 'N/A',
                ];
            })
            ->toArray();
    }
}