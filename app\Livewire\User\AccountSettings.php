<?php

namespace App\Livewire\User;

use Livewire\Component;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;

class AccountSettings extends Component
{
    // Profile fields
    public $name;
    public $email;
    public $phone;
    public $address;
    
    // Password fields
    public $current_password;
    public $new_password;
    public $new_password_confirmation;
    
    // Notification settings
    public $order_confirmation = true;
    public $order_status_updates = true;
    public $build_shared = true;
    public $marketing_emails = false;
    public $price_alerts = true;
    public $stock_alerts = true;
    
    // User preferences
    public $currency = 'USD';
    public $theme = 'light';
    public $items_per_page = 20;
    public $default_build_visibility = 'private';
    
    // UI state
    public $successMessage;
    public $activeTab = 'profile';

    public function mount()
    {
        $user = Auth::user();
        
        // Load profile data
        $this->name = $user->name;
        $this->email = $user->email;
        $this->phone = $user->phone;
        $this->address = $user->address;
        
        // Load notification settings
        $defaultNotifications = $user->getDefaultNotificationSettings();
        $userNotifications = $user->notification_settings ?? [];
        
        $this->order_confirmation = $userNotifications['order_confirmation'] ?? $defaultNotifications['order_confirmation'];
        $this->order_status_updates = $userNotifications['order_status_updates'] ?? $defaultNotifications['order_status_updates'];
        $this->build_shared = $userNotifications['build_shared'] ?? $defaultNotifications['build_shared'];
        $this->marketing_emails = $userNotifications['marketing_emails'] ?? $defaultNotifications['marketing_emails'];
        $this->price_alerts = $userNotifications['price_alerts'] ?? $defaultNotifications['price_alerts'];
        $this->stock_alerts = $userNotifications['stock_alerts'] ?? $defaultNotifications['stock_alerts'];
        
        // Load user preferences
        $defaultPreferences = $user->getDefaultPreferences();
        $userPreferences = $user->preferences ?? [];
        
        $this->currency = $userPreferences['currency'] ?? $defaultPreferences['currency'];
        $this->theme = $userPreferences['theme'] ?? $defaultPreferences['theme'];
        $this->items_per_page = $userPreferences['items_per_page'] ?? $defaultPreferences['items_per_page'];
        $this->default_build_visibility = $userPreferences['default_build_visibility'] ?? $defaultPreferences['default_build_visibility'];
    }

    public function setActiveTab($tab)
    {
        $this->activeTab = $tab;
        $this->successMessage = '';
    }

    public function updateProfile()
    {
        $user = Auth::user();
        
        $this->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255|unique:users,email,' . $user->id,
            'phone' => 'nullable|string|max:20',
            'address' => 'nullable|string|max:500',
        ]);
        
        $user->update([
            'name' => $this->name,
            'email' => $this->email,
            'phone' => $this->phone,
            'address' => $this->address,
        ]);
        
        $this->successMessage = 'Profile updated successfully!';
    }

    public function updateNotificationSettings()
    {
        $user = Auth::user();
        
        $notificationSettings = [
            'order_confirmation' => $this->order_confirmation,
            'order_status_updates' => $this->order_status_updates,
            'build_shared' => $this->build_shared,
            'marketing_emails' => $this->marketing_emails,
            'price_alerts' => $this->price_alerts,
            'stock_alerts' => $this->stock_alerts,
        ];
        
        $user->update(['notification_settings' => $notificationSettings]);
        
        $this->successMessage = 'Notification settings updated successfully!';
    }

    public function updatePreferences()
    {
        $user = Auth::user();
        
        $this->validate([
            'currency' => 'required|in:USD,EUR,GBP,CAD',
            'theme' => 'required|in:light,dark',
            'items_per_page' => 'required|integer|min:10|max:100',
            'default_build_visibility' => 'required|in:private,public',
        ]);
        
        $preferences = [
            'currency' => $this->currency,
            'theme' => $this->theme,
            'items_per_page' => $this->items_per_page,
            'default_build_visibility' => $this->default_build_visibility,
        ];
        
        $user->update(['preferences' => $preferences]);
        
        $this->successMessage = 'Preferences updated successfully!';
    }

    public function changePassword()
    {
        $user = Auth::user();
        
        $this->validate([
            'current_password' => 'required',
            'new_password' => 'required|min:8|confirmed',
        ]);
        
        if (!Hash::check($this->current_password, $user->password)) {
            $this->addError('current_password', 'Current password is incorrect.');
            return;
        }
        
        $user->update(['password' => Hash::make($this->new_password)]);
        
        $this->successMessage = 'Password changed successfully!';
        $this->current_password = '';
        $this->new_password = '';
        $this->new_password_confirmation = '';
    }

    public function render()
    {
        return view('livewire.user.account-settings');
    }
} 