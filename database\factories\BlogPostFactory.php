<?php

namespace Database\Factories;

use App\Models\BlogPost;
use App\Models\User;
use App\Models\BlogPostCategory;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

class BlogPostFactory extends Factory
{
    protected $model = BlogPost::class;

    public function definition()
    {
        return [
            'title' => $this->faker->realText(50),
            'slug' => null,
            'content' => $this->faker->paragraphs(3, true),
            'excerpt' => $this->faker->paragraph(),
            'featured_image' => $this->faker->imageUrl(800, 600),
            'is_published' => true,
            'published_at' => now(),
            'created_at' => now(),
            'updated_at' => now(),
            'user_id' => User::factory(),
            // 'blog_post_category_id' is intentionally omitted. Tests can provide it, or it will be null (schema permitting).
            'meta_title' => $this->faker->sentence(4),
            'meta_description' => $this->faker->sentence(8),
            'meta_keywords' => implode(' ', $this->faker->words(5)),
        ];
    }

    public function unpublished()
    {
        return $this->state(function (array $attributes) {
            return [
                'is_published' => false,
                'published_at' => null,
            ];
        });
    }

    public function draft()
    {
        return $this->state(function (array $attributes) {
            return [
                'is_published' => false,
                'published_at' => null,
                'content' => null,
            ];
        });
    }
}