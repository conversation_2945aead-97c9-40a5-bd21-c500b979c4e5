<?php

/**
 * Dusk Test Runner with Server Management
 * 
 * This script automatically starts the Laravel development server,
 * runs Dusk tests, and then stops the server.
 */

class DuskServerManager
{
    private $serverProcess = null;
    private $serverPort = 8000;
    private $serverHost = '127.0.0.1';

    public function run(string $suite = 'all'): void
    {
        $this->displayHeader();
        
        try {
            $this->startServer();
            $this->waitForServer();
            $this->runTests($suite);
        } finally {
            $this->stopServer();
        }
    }

    private function displayHeader(): void
    {
        echo "\n";
        echo "╔══════════════════════════════════════════════════════════════╗\n";
        echo "║              Dusk Test Runner with Server                   ║\n";
        echo "║                                                              ║\n";
        echo "║    Automatically manages Laravel server for testing         ║\n";
        echo "╚══════════════════════════════════════════════════════════════╝\n";
        echo "\n";
    }

    private function startServer(): void
    {
        echo "🚀 Starting Laravel development server...\n";
        
        // Set environment for testing
        putenv('APP_ENV=dusk.local');
        
        $command = "php artisan serve --host={$this->serverHost} --port={$this->serverPort} --env=dusk.local";
        
        // Start server in background
        if (PHP_OS_FAMILY === 'Windows') {
            $this->serverProcess = popen("start /B {$command} 2>nul", 'r');
        } else {
            $this->serverProcess = popen("{$command} > /dev/null 2>&1 &", 'r');
        }
        
        echo "✅ Server starting on http://{$this->serverHost}:{$this->serverPort}\n";
    }

    private function waitForServer(): void
    {
        echo "⏳ Waiting for server to be ready...\n";
        
        $maxAttempts = 30;
        $attempt = 0;
        
        while ($attempt < $maxAttempts) {
            $connection = @fsockopen($this->serverHost, $this->serverPort, $errno, $errstr, 1);
            
            if ($connection) {
                fclose($connection);
                echo "✅ Server is ready!\n\n";
                return;
            }
            
            $attempt++;
            sleep(1);
            echo ".";
        }
        
        throw new Exception("Server failed to start after {$maxAttempts} seconds");
    }

    private function runTests(string $suite): void
    {
        echo "🧪 Running Dusk tests...\n\n";
        
        $command = "php run_dusk_tests.php --suite {$suite}";
        
        // Run tests and capture output
        $output = [];
        $returnCode = 0;
        exec($command, $output, $returnCode);
        
        // Display output
        foreach ($output as $line) {
            echo $line . "\n";
        }
        
        if ($returnCode === 0) {
            echo "\n✅ All tests completed successfully!\n";
        } else {
            echo "\n❌ Some tests failed. Check output above for details.\n";
        }
    }

    private function stopServer(): void
    {
        echo "\n🛑 Stopping Laravel development server...\n";
        
        if (PHP_OS_FAMILY === 'Windows') {
            // Kill PHP processes on Windows
            exec('taskkill /F /IM php.exe 2>nul');
        } else {
            // Kill server process on Unix-like systems
            exec("pkill -f 'artisan serve'");
        }
        
        if ($this->serverProcess) {
            pclose($this->serverProcess);
        }
        
        echo "✅ Server stopped\n";
    }
}

// CLI Interface
if (php_sapi_name() === 'cli') {
    $options = getopt('s:h', ['suite:', 'help']);
    
    if (isset($options['help']) || isset($options['h'])) {
        echo "Dusk Test Runner with Server Management\n\n";
        echo "Usage: php run_dusk_with_server.php [options]\n\n";
        echo "Options:\n";
        echo "  -s, --suite <suite>    Run specific test suite (payment, admin, products, all)\n";
        echo "  -h, --help             Show this help message\n\n";
        echo "Examples:\n";
        echo "  php run_dusk_with_server.php --suite products\n";
        echo "  php run_dusk_with_server.php --suite payment\n";
        echo "  php run_dusk_with_server.php\n\n";
        exit(0);
    }
    
    $suite = $options['s'] ?? $options['suite'] ?? 'all';
    
    try {
        $manager = new DuskServerManager();
        $manager->run($suite);
    } catch (Exception $e) {
        echo "❌ Error: " . $e->getMessage() . "\n";
        exit(1);
    }
}