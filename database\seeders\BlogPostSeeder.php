<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\BlogPost;
use App\Models\BlogPostCategory;
use App\Models\BlogPostTag;

class BlogPostSeeder extends Seeder
{
    public function run(): void
    {
        $categories = BlogPostCategory::all();
        $tags = BlogPostTag::all();

        BlogPost::factory(20)->create()->each(function ($post) use ($categories, $tags) {
            // Assign a random category
            $post->blog_post_category_id = $categories->random()->id;
            $post->save();

            // Attach 1-3 random tags
            $post->tags()->sync($tags->random(rand(1, 3))->pluck('id')->toArray());
        });
    }
} 