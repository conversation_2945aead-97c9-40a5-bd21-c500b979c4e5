<?php

namespace Database\Factories;

use App\Models\Product;
use App\Models\ProductReview;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class ProductReviewFactory extends Factory
{
    protected $model = ProductReview::class;

    public function definition(): array
    {
        $rating = $this->faker->numberBetween(1, 5);
        
        return [
            'user_id' => User::factory(),
            'product_id' => Product::factory(),
            'rating' => $rating,
            'title' => $this->faker->optional(0.8)->sentence(4),
            'comment' => $this->faker->optional(0.9)->paragraph(3),
            'is_approved' => $this->faker->boolean(80),
            'is_verified_purchase' => $this->faker->boolean(60),
            'images' => $this->faker->optional(0.2)->randomElements([
                'reviews/image1.jpg',
                'reviews/image2.jpg',
                'reviews/image3.jpg',
            ], $this->faker->numberBetween(1, 3)),
        ];
    }

    public function approved(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_approved' => true,
        ]);
    }

    public function pending(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_approved' => false,
        ]);
    }

    public function verifiedPurchase(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_verified_purchase' => true,
        ]);
    }

    public function withRating(int $rating): static
    {
        return $this->state(fn (array $attributes) => [
            'rating' => $rating,
        ]);
    }

    public function withImages(): static
    {
        return $this->state(fn (array $attributes) => [
            'images' => [
                'reviews/image1.jpg',
                'reviews/image2.jpg',
            ],
        ]);
    }

    public function excellent(): static
    {
        return $this->state(fn (array $attributes) => [
            'rating' => 5,
            'title' => 'Excellent product!',
            'comment' => 'This product exceeded my expectations. Great quality and fast delivery.',
        ]);
    }

    public function poor(): static
    {
        return $this->state(fn (array $attributes) => [
            'rating' => 1,
            'title' => 'Not satisfied',
            'comment' => 'The product did not meet my expectations. Quality could be better.',
        ]);
    }
}