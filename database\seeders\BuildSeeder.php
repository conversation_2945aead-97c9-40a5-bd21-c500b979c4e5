<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Build;
use App\Models\User;
use Illuminate\Support\Str;

class BuildSeeder extends Seeder
{
    public function run()
    {
        // Check if a user exists, if not create one
        $user = User::first();
        
        if (!$user) {
            $user = User::create([
                'name' => 'Admin User',
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
                'email_verified_at' => now(),
            ]);
        }

        Build::create([
            'user_id' => $user->id,
            'name' => 'Sample Build',
            'description' => 'A sample PC build',
            'is_public' => true,
            'is_complete' => false,
            'total_price' => 0,
            'compatibility_issues' => [],
            'share_token' => Str::random(32),
        ]);
    }
}