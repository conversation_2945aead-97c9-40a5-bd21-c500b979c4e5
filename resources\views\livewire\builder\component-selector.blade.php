<div>
    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
            <h2 class="text-xl font-semibold">Select {{ $category->name }}</h2>
            
            <!-- Search Bar -->
            <div class="w-full md:w-auto">
                <div class="relative">
                    <input 
                        type="text" 
                        wire:model.debounce.300ms="search" 
                        placeholder="Search components..." 
                        class="w-full md:w-64 pl-10 pr-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                    <div class="absolute left-3 top-2.5 text-gray-400">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd" />
                        </svg>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
            <!-- Filters Sidebar -->
            <div class="lg:col-span-1 space-y-6">
                <!-- Brand Filter -->
                @if(count($brands) > 0)
                    <div>
                        <h3 class="font-medium text-gray-900 mb-2">Brands</h3>
                        <div class="space-y-2 max-h-48 overflow-y-auto">
                            @foreach($brands as $brand)
                                <label class="flex items-center">
                                    <input 
                                        type="checkbox" 
                                        wire:model="selectedBrands" 
                                        value="{{ $brand }}" 
                                        class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                    >
                                    <span class="ml-2 text-gray-700">{{ $brand }}</span>
                                </label>
                            @endforeach
                        </div>
                    </div>
                @endif
                
                <!-- Price Range Filter -->
                <div>
                    <h3 class="font-medium text-gray-900 mb-2">Price Range</h3>
                    <div class="grid grid-cols-2 gap-2">
                        <div>
                            <label for="priceMin" class="block text-sm text-gray-600">Min ($)</label>
                            <input 
                                type="number" 
                                id="priceMin" 
                                wire:model.debounce.500ms="priceMin" 
                                min="0" 
                                class="w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                            >
                        </div>
                        <div>
                            <label for="priceMax" class="block text-sm text-gray-600">Max ($)</label>
                            <input 
                                type="number" 
                                id="priceMax" 
                                wire:model.debounce.500ms="priceMax" 
                                min="0" 
                                class="w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                            >
                        </div>
                    </div>
                </div>
                
                <!-- Stock Filter -->
                <div>
                    <label class="flex items-center">
                        <input 
                            type="checkbox" 
                            wire:model="inStockOnly" 
                            class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        >
                        <span class="ml-2 text-gray-700">In Stock Only</span>
                    </label>
                </div>
                
                <!-- Sort Options -->
                <div>
                    <h3 class="font-medium text-gray-900 mb-2">Sort By</h3>
                    <select 
                        wire:model="sortBy" 
                        class="w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    >
                        <option value="price">Price</option>
                        <option value="name">Name</option>
                        <option value="brand">Brand</option>
                        <option value="stock">Stock</option>
                    </select>
                    
                    <div class="mt-2 flex space-x-4">
                        <label class="flex items-center">
                            <input 
                                type="radio" 
                                wire:model="sortDirection" 
                                value="asc" 
                                class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                            >
                            <span class="ml-2 text-gray-700">Ascending</span>
                        </label>
                        <label class="flex items-center">
                            <input 
                                type="radio" 
                                wire:model="sortDirection" 
                                value="desc" 
                                class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                            >
                            <span class="ml-2 text-gray-700">Descending</span>
                        </label>
                    </div>
                </div>
            </div>
            
            <!-- Components Grid -->
            <div class="lg:col-span-3">
                @if($components->isEmpty())
                    <div class="text-center py-12 bg-gray-50 rounded-lg">
                        <svg xmlns="http://www.w3.org/2000/svg" class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <h3 class="mt-2 text-sm font-medium text-gray-900">No components found</h3>
                        <p class="mt-1 text-sm text-gray-500">Try adjusting your search or filter criteria.</p>
                    </div>
                @else
                    <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
                        @foreach($components as $component)
                            <div class="border rounded-lg overflow-hidden hover:shadow-lg transition-shadow {{ $component->is_compatible ? '' : 'border-red-300 bg-red-50' }}">
                                @if($component->image)
                                    <img src="{{ $component->image }}" alt="{{ $component->name }}" class="w-full h-48 object-cover">
                                @else
                                    <div class="w-full h-48 bg-gray-200 flex items-center justify-center">
                                        <span class="text-gray-400">No image</span>
                                    </div>
                                @endif
                                
                                <div class="p-4">
                                    <h3 class="font-semibold text-lg mb-2">{{ $component->name }}</h3>
                                    <p class="text-gray-600 mb-1">{{ $component->brand }} {{ $component->model }}</p>
                                    <p class="text-gray-800 font-bold mb-2">${{ number_format($component->price, 2) }}</p>
                                    
                                    @if($component->stock > 0)
                                        <span class="text-green-600 text-sm">In Stock ({{ $component->stock }})</span>
                                    @else
                                        <span class="text-red-600 text-sm">Out of Stock</span>
                                    @endif
                                    
                                    @if(!$component->is_compatible)
                                        <div class="mt-2 p-2 bg-red-100 rounded-md">
                                            <p class="text-red-700 text-sm font-medium">Compatibility Issues:</p>
                                            <ul class="text-red-600 text-xs list-disc list-inside">
                                                @foreach((array) $component->compatibility_issues as $issue)
                                                    <li>{{ $issue }}</li>
                                                @endforeach
                                            </ul>
                                        </div>
                                    @endif
                                    
                                    <div class="mt-4 flex space-x-2">
                                        <button 
                                            wire:click="selectComponent({{ $component->id }})" 
                                            class="flex-1 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 {{ $component->stock <= 0 ? 'opacity-50 cursor-not-allowed' : '' }}"
                                            {{ $component->stock <= 0 ? 'disabled' : '' }}
                                        >
                                            {{ $selectedComponentId == $component->id ? 'Selected' : 'Select' }}
                                        </button>
                                        
                                        <button 
                                            class="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                                            onclick="showComponentDetails({{ $component->id }})"
                                        >
                                            Details
                                        </button>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @endif
            </div>
        </div>
    </div>
    
    <!-- Component Details Modal (placeholder) -->
    <script>
        function showComponentDetails(componentId) {
            // This would be implemented with Alpine.js or similar
            alert('Component details for ID: ' + componentId);
        }
    </script>
</div>