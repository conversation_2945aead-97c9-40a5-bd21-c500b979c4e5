<?php

namespace App\Livewire\Admin;

use App\Models\Component;
use App\Models\ComponentCategory;
use Livewire\Component as LivewireComponent;
use Livewire\WithPagination;
use Illuminate\Support\Facades\Storage;

class ComponentsList extends LivewireComponent
{
    use WithPagination;

    public $search = '';
    public $categoryFilter = '';
    public $statusFilter = '';
    public $sortBy = 'name';
    public $sortDirection = 'asc';
    public $perPage = 15;
    public $selectedComponents = [];
    public $selectAll = false;

    public $categories = [];
    public $showingForm = false;
    public $editingComponent = null;

    protected $queryString = [
        'search' => ['except' => ''],
        'categoryFilter' => ['except' => ''],
        'statusFilter' => ['except' => ''],
        'sortBy' => ['except' => 'name'],
        'sortDirection' => ['except' => 'asc'],
    ];

    protected $listeners = [
        'component-saved' => 'handleComponentSaved',
        'form-cancelled' => 'hideForm',
    ];

    public function mount()
    {
        $this->categories = ComponentCategory::orderBy('name')->get();
    }

    public function updatedSearch()
    {
        $this->resetPage();
    }

    public function updatedCategoryFilter()
    {
        $this->resetPage();
    }

    public function updatedStatusFilter()
    {
        $this->resetPage();
    }

    public function updatedSelectAll()
    {
        if ($this->selectAll) {
            $this->selectedComponents = $this->getComponents()->pluck('id')->toArray();
        } else {
            $this->selectedComponents = [];
        }
    }

    public function sortBy($field)
    {
        if ($this->sortBy === $field) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortBy = $field;
            $this->sortDirection = 'asc';
        }
        $this->resetPage();
    }

    public function getComponents()
    {
        $query = Component::with('category');

        if ($this->search) {
            $query->where(function ($q) {
                $q->where('name', 'like', '%' . $this->search . '%')
                  ->orWhere('brand', 'like', '%' . $this->search . '%')
                  ->orWhere('model', 'like', '%' . $this->search . '%');
            });
        }

        if ($this->categoryFilter) {
            $query->where('category_id', $this->categoryFilter);
        }

        if ($this->statusFilter !== '') {
            $query->where('is_active', $this->statusFilter === '1');
        }

        return $query->orderBy($this->sortBy, $this->sortDirection)
                    ->paginate($this->perPage);
    }

    public function showForm($componentId = null)
    {
        $this->editingComponent = $componentId;
        $this->showingForm = true;
    }

    public function hideForm()
    {
        $this->showingForm = false;
        $this->editingComponent = null;
    }

    public function handleComponentSaved()
    {
        $this->hideForm();
        $this->resetPage();
    }

    public function deleteComponent($componentId)
    {
        $component = Component::findOrFail($componentId);
        
        // Delete image if exists
        if ($component->image) {
            Storage::disk('public')->delete($component->image);
        }
        
        $component->delete();
        
        session()->flash('message', 'Component deleted successfully.');
        $this->resetPage();
    }

    public function toggleStatus($componentId)
    {
        $component = Component::findOrFail($componentId);
        $component->update(['is_active' => !$component->is_active]);
        
        session()->flash('message', 'Component status updated successfully.');
    }

    public function toggleFeatured($componentId)
    {
        $component = Component::findOrFail($componentId);
        $component->update(['is_featured' => !$component->is_featured]);
        
        session()->flash('message', 'Component featured status updated successfully.');
    }

    public function bulkDelete()
    {
        if (empty($this->selectedComponents)) {
            session()->flash('error', 'No components selected.');
            return;
        }

        $components = Component::whereIn('id', $this->selectedComponents)->get();
        
        foreach ($components as $component) {
            if ($component->image) {
                Storage::disk('public')->delete($component->image);
            }
            $component->delete();
        }

        $this->selectedComponents = [];
        $this->selectAll = false;
        
        session()->flash('message', count($components) . ' components deleted successfully.');
        $this->resetPage();
    }

    public function bulkToggleStatus($status)
    {
        if (empty($this->selectedComponents)) {
            session()->flash('error', 'No components selected.');
            return;
        }

        Component::whereIn('id', $this->selectedComponents)
                ->update(['is_active' => $status]);

        $this->selectedComponents = [];
        $this->selectAll = false;
        
        $statusText = $status ? 'activated' : 'deactivated';
        session()->flash('message', 'Selected components ' . $statusText . ' successfully.');
    }

    public function bulkToggleFeatured($featured)
    {
        if (empty($this->selectedComponents)) {
            session()->flash('error', 'No components selected.');
            return;
        }

        Component::whereIn('id', $this->selectedComponents)
                ->update(['is_featured' => $featured]);

        $this->selectedComponents = [];
        $this->selectAll = false;
        
        $featuredText = $featured ? 'marked as featured' : 'unmarked as featured';
        session()->flash('message', 'Selected components ' . $featuredText . ' successfully.');
    }

    public function render()
    {
        return view('livewire.admin.components-list', [
            'components' => $this->getComponents(),
        ]);
    }
}