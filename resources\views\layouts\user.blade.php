<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" class="light">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="User dashboard for managing orders and account">
    <title>@yield('title', 'User Dashboard')</title>
    <meta name="csrf-token" content="{{ csrf_token() }}">
    {{-- <link rel="icon" href="{{ uploads_url(get_setting('site_favicon')) }}"> --}}

    <!-- Dark mode script - must be before CSS loads -->
    <script>
        (function() {
            try {
                var theme = localStorage.getItem('theme');
                if (theme === 'dark' || (!theme && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
                    document.documentElement.classList.add('dark');
                    document.documentElement.classList.remove('light');
                    document.documentElement.setAttribute('data-theme', 'dark');
                } else {
                    document.documentElement.classList.remove('dark');
                    document.documentElement.classList.add('light');
                    document.documentElement.setAttribute('data-theme', 'light');
                }
            } catch (e) {}
        })();
    </script>

    @vite(['resources/css/app.css', 'resources/js/app.js'])
    @livewireStyles
    @stack('styles')


    <style>
        /* Ensure proper height for sticky positioning */
        html, body {
            height: 100%;
            overflow: hidden;
        }
        
        @media (max-width: 1023px) {
            html, body {
                height: auto;
                overflow: visible;
            }
        }
        
        /* Custom glassmorphism and modern effects */
        .glass-card {
            background: rgba(255, 255, 255, 0.08);
            backdrop-filter: blur(16px);
            -webkit-backdrop-filter: blur(16px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .dark .glass-card {
            background: rgba(15, 23, 42, 0.6);
            border: 1px solid rgba(255, 255, 255, 0.05);
        }
        
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .dark .gradient-bg {
            background: linear-gradient(135deg, #4c1d95 0%, #1e1b4b 100%);
        }
        
        .floating-element {
            animation: float 6s ease-in-out infinite;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-10px) rotate(2deg); }
        }
        
        .sidebar-gradient {
            background: linear-gradient(180deg, 
                rgba(255, 255, 255, 0.95) 0%, 
                rgba(248, 250, 252, 0.9) 100%);
        }
        
        .dark .sidebar-gradient {
            background: linear-gradient(180deg, 
                rgba(15, 23, 42, 0.95) 0%, 
                rgba(30, 41, 59, 0.9) 100%);
        }
        
        .nav-item-hover {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        .nav-item-hover:hover {
            transform: translateX(4px);
            background: linear-gradient(90deg, 
                rgba(99, 102, 241, 0.1) 0%, 
                transparent 100%);
        }
        
        /* Mobile navigation improvements */
        .mobile-nav-item {
            padding: 0.625rem 0.75rem;
            font-size: 0.875rem;
            display: flex;
            align-items: center;
            border-radius: 0.75rem;
            font-weight: 500;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        .mobile-nav-item svg {
            width: 1.25rem;
            height: 1.25rem;
            margin-right: 0.5rem;
            flex-shrink: 0;
        }
        
        .mobile-nav-item span {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        
        @media (min-width: 640px) {
            .mobile-nav-item {
                padding: 0.75rem 1rem;
            }
            
            .mobile-nav-item svg {
                margin-right: 0.75rem;
            }
        }
        
        @media (max-width: 1023px) {
            .nav-item-hover:hover {
                transform: none; /* Disable transform on mobile for better touch experience */
            }
            
            .nav-item-hover:active {
                background: rgba(99, 102, 241, 0.1);
                transform: scale(0.98);
            }
        }
        
        .modern-shadow {
            box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 
                        0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
        
        .dark .modern-shadow {
            box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.4), 
                        0 10px 10px -5px rgba(0, 0, 0, 0.2);
        }
        
        .pulse-ring {
            animation: pulse-ring 2s cubic-bezier(0.455, 0.03, 0.515, 0.955) infinite;
        }
        
        @keyframes pulse-ring {
            0% { transform: scale(0.8); opacity: 1; }
            100% { transform: scale(2.4); opacity: 0; }
        }
        
        .glow-effect {
            box-shadow: 0 0 20px rgba(99, 102, 241, 0.3);
        }
        
        .dark .glow-effect {
            box-shadow: 0 0 20px rgba(139, 92, 246, 0.4);
        }
        
        /* Layout structure for sticky elements */
        .app-layout {
            display: flex;
            min-height: 100vh;
            max-height: 100vh;
            overflow: hidden;
        }
        
        /* Sticky sidebar - Fixed positioning for desktop */
        .app-sidebar {
            width: 320px;
            flex-shrink: 0;
            position: sticky;
            top: 0;
            height: 100vh;
            max-height: 100vh;
            overflow-y: auto;
            overflow-x: hidden;
            z-index: 50;
        }
        
        /* Main content area */
        .app-main {
            flex: 1;
            display: flex;
            flex-direction: column;
            height: 100vh;
            max-height: 100vh;
            overflow: hidden;
            width: 0; /* Prevent flex item from growing beyond container */
        }
        
        /* Sticky top navigation */
        .app-topbar {
            position: sticky;
            top: 0;
            z-index: 40;
            flex-shrink: 0;
            width: 100%;
        }
        
        /* Scrollable content area */
        .app-content {
            flex: 1;
            overflow-y: auto;
            overflow-x: hidden;
            padding: 1rem;
            height: 0; /* Allow flex item to shrink */
        }
        
        /* Mobile responsive improvements */
        @media (max-width: 1023px) {
            .app-layout {
                display: block;
                min-height: 100vh;
                max-height: none;
                overflow: visible;
            }
            
            .app-sidebar {
                position: fixed !important;
                top: 0;
                left: 0;
                height: 100vh;
                max-height: 100vh;
                z-index: 50;
                transform: translateX(-100%);
                transition: transform 0.3s ease-in-out;
                width: 280px; /* Slightly smaller on mobile */
                box-shadow: 0 0 20px rgba(0, 0, 0, 0.3);
            }
            
            .app-sidebar.open {
                transform: translateX(0);
            }
            
            .app-main {
                min-height: 100vh;
                max-height: none;
                height: auto;
                overflow: visible;
                width: 100%;
            }
            
            .app-content {
                overflow: visible;
                padding: 0.75rem;
                height: auto;
            }
            
            .app-topbar {
                position: sticky;
                top: 0;
                z-index: 30;
            }
        }
        
        /* Desktop sticky positioning reinforcement */
        @media (min-width: 1024px) {
            .app-layout {
                height: 100vh;
                max-height: 100vh;
                overflow: hidden;
            }
            
            .app-sidebar {
                position: sticky !important;
                top: 0 !important;
                height: 100vh !important;
                max-height: 100vh !important;
                overflow-y: auto !important;
                flex-shrink: 0 !important;
            }
            
            .app-main {
                height: 100vh !important;
                max-height: 100vh !important;
                overflow: hidden !important;
            }
            
            .app-topbar {
                position: sticky !important;
                top: 0 !important;
                z-index: 40 !important;
            }
            
            .app-content {
                height: 0 !important;
                flex: 1 !important;
                overflow-y: auto !important;
            }
        }
        
        /* Tablet responsive */
        @media (min-width: 768px) and (max-width: 1023px) {
            .app-content {
                padding: 1.5rem;
            }
        }
        
        /* Small mobile devices */
        @media (max-width: 640px) {
            .app-sidebar {
                width: 100vw;
                max-width: 320px;
            }
            
            .app-content {
                padding: 0.5rem;
            }
            
            .app-topbar {
                padding-left: 0.75rem;
                padding-right: 0.75rem;
            }
        }
        
        /* Custom scrollbar for sidebar */
        .app-sidebar::-webkit-scrollbar {
            width: 6px;
        }
        
        .app-sidebar::-webkit-scrollbar-track {
            background: transparent;
        }
        
        .app-sidebar::-webkit-scrollbar-thumb {
            background: rgba(99, 102, 241, 0.3);
            border-radius: 3px;
        }
        
        .app-sidebar::-webkit-scrollbar-thumb:hover {
            background: rgba(99, 102, 241, 0.5);
        }
        
        /* Content scrollbar */
        .app-content::-webkit-scrollbar {
            width: 8px;
        }
        
        .app-content::-webkit-scrollbar-track {
            background: rgba(0, 0, 0, 0.05);
        }
        
        .app-content::-webkit-scrollbar-thumb {
            background: rgba(99, 102, 241, 0.3);
            border-radius: 4px;
        }
        
        .app-content::-webkit-scrollbar-thumb:hover {
            background: rgba(99, 102, 241, 0.5);
        }
    </style>
</head>

<body class="bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 dark:from-gray-900 dark:via-slate-900 dark:to-purple-900 h-screen font-sans text-gray-900 dark:text-gray-100 overflow-hidden">
    <!-- Background decorative elements -->
    <div class="fixed inset-0 overflow-hidden pointer-events-none">
        <div class="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-purple-400 to-pink-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 floating-element"></div>
        <div class="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-br from-blue-400 to-cyan-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 floating-element" style="animation-delay: 2s;"></div>
        <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-80 h-80 bg-gradient-to-br from-green-400 to-blue-400 rounded-full mix-blend-multiply filter blur-xl opacity-10 floating-element" style="animation-delay: 4s;"></div>
    </div>

    <div x-data="{ 
            sidebarOpen: false,
            init() {
                this.handleResize();
                window.addEventListener('resize', () => this.handleResize());
            },
            handleResize() {
                if (window.innerWidth >= 1024) {
                    this.sidebarOpen = false; // On desktop, sidebar is always visible via CSS
                } else {
                    this.sidebarOpen = false; // On mobile, sidebar is hidden by default
                }
            }
         }" 
         class="app-layout">
        
        <!-- Mobile sidebar backdrop overlay -->
        <div x-show="sidebarOpen"
            class="fixed inset-0 bg-black/60 dark:bg-black/80 z-40 lg:hidden backdrop-blur-sm transition-all duration-300"
            x-cloak 
            x-transition:enter="transition ease-out duration-300" 
            x-transition:enter-start="opacity-0"
            x-transition:enter-end="opacity-100" 
            x-transition:leave="transition ease-in duration-200"
            x-transition:leave-start="opacity-100" 
            x-transition:leave-end="opacity-0" 
            @click="sidebarOpen = false"
            aria-hidden="true"></div>

        <!-- Sticky Sidebar -->
        <aside class="app-sidebar sidebar-gradient backdrop-blur-xl modern-shadow border-r border-white/20 dark:border-gray-700/50"
            :class="{ 'open': sidebarOpen }">
            
            <!-- Logo Section -->
            <div class="flex items-center justify-center h-20 px-6 border-b border-white/10 dark:border-gray-700/50">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-xl flex items-center justify-center glow-effect">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                        </svg>
                    </div>
                    <span class="text-xl font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">Dashboard</span>
                </div>
            </div>

            <!-- Navigation -->
            <nav class="flex-1 px-3 sm:px-4 py-4 sm:py-6 space-y-2 overflow-y-auto">
                <!-- Main Section -->
                <div class="mb-4">
                    <h3 class="px-2 sm:px-4 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider mb-2">Main</h3>
                    <div class="space-y-1">
                        <!-- Dashboard -->
                        <a href="{{ route('dashboard') }}" class="nav-item-hover flex items-center px-3 sm:px-4 py-2.5 sm:py-3 text-sm font-medium rounded-xl {{ request()->routeIs('dashboard') ? 'text-indigo-600 dark:text-indigo-400 bg-indigo-50 dark:bg-indigo-900/30' : 'text-gray-700 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400' }}" @click="sidebarOpen = false">
                            <svg class="w-5 h-5 mr-2 sm:mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"/>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5a2 2 0 012-2h4a2 2 0 012 2v4H8V5z"/>
                            </svg>
                            <span class="truncate">Dashboard</span>
                            @if(request()->routeIs('dashboard'))
                                <div class="ml-auto w-2 h-2 bg-indigo-500 rounded-full flex-shrink-0"></div>
                            @endif
                        </a>
                        
                        <!-- PC Builder -->
                        <a href="{{ route('builder.index') }}" class="nav-item-hover flex items-center px-3 sm:px-4 py-2.5 sm:py-3 text-sm font-medium rounded-xl {{ request()->routeIs('builder.*') ? 'text-indigo-600 dark:text-indigo-400 bg-indigo-50 dark:bg-indigo-900/30' : 'text-gray-700 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400' }}" @click="sidebarOpen = false">
                            <svg class="w-5 h-5 mr-2 sm:mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z"/>
                            </svg>
                            <span class="truncate">PC Builder</span>
                        </a>
                        
                        <!-- Shop -->
                        <a href="{{ route('shop.index') }}" class="nav-item-hover flex items-center px-4 py-3 text-sm font-medium rounded-xl {{ request()->routeIs('shop.*') ? 'text-indigo-600 dark:text-indigo-400 bg-indigo-50 dark:bg-indigo-900/30' : 'text-gray-700 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400' }}">
                            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"/>
                            </svg>
                            Shop
                        </a>
                        
                        <!-- Cart -->
                        <a href="{{ route('cart.index') }}" class="nav-item-hover flex items-center px-4 py-3 text-sm font-medium rounded-xl {{ request()->routeIs('cart.*') ? 'text-indigo-600 dark:text-indigo-400 bg-indigo-50 dark:bg-indigo-900/30' : 'text-gray-700 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400' }}">
                            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17M17 13v4a2 2 0 01-2 2H9a2 2 0 01-2-2v-4m8 0V9a2 2 0 00-2-2H9a2 2 0 00-2 2v4.01"/>
                            </svg>
                            Shopping Cart
                            @if(auth()->user()->cart && auth()->user()->cart->items->count() > 0)
                                <span class="ml-auto bg-indigo-500 text-white text-xs px-2 py-1 rounded-full">{{ auth()->user()->cart->items->count() }}</span>
                            @endif
                        </a>
                    </div>
                </div>

                <!-- Account Section -->
                <div class="mb-4">
                    <h3 class="px-2 sm:px-4 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider mb-2">Account</h3>
                    <div class="space-y-1">
                        <!-- Account Settings -->
                        <a href="{{ route('dashboard') }}#account" class="nav-item-hover flex items-center px-4 py-3 text-sm font-medium rounded-xl text-gray-700 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400">
                            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                            </svg>
                            Account Settings
                        </a>
                        
                        <!-- Order History -->
                        <a href="{{ route('dashboard') }}#orders" class="nav-item-hover flex items-center px-4 py-3 text-sm font-medium rounded-xl text-gray-700 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400">
                            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"/>
                            </svg>
                            Order History
                            @if(auth()->user()->orders()->where('status', 'processing')->count() > 0)
                                <span class="ml-auto bg-blue-500 text-white text-xs px-2 py-1 rounded-full">{{ auth()->user()->orders()->where('status', 'processing')->count() }}</span>
                            @endif
                        </a>
                        
                        <!-- Saved Builds -->
                        <a href="{{ route('dashboard') }}#builds" class="nav-item-hover flex items-center px-4 py-3 text-sm font-medium rounded-xl text-gray-700 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400">
                            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
                            </svg>
                            Saved Builds
                            @if(auth()->user()->builds()->count() > 0)
                                <span class="ml-auto bg-green-500 text-white text-xs px-2 py-1 rounded-full">{{ auth()->user()->builds()->count() }}</span>
                            @endif
                        </a>
                        
                        <!-- Payment History -->
                        <a href="{{ route('payment.create') }}" class="nav-item-hover flex items-center px-4 py-3 text-sm font-medium rounded-xl {{ request()->routeIs('payment.*') ? 'text-indigo-600 dark:text-indigo-400 bg-indigo-50 dark:bg-indigo-900/30' : 'text-gray-700 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400' }}">
                            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"/>
                            </svg>
                            Payments
                            @if(auth()->user()->transactions()->where('status', 'pending')->count() > 0)
                                <span class="ml-auto bg-yellow-500 text-white text-xs px-2 py-1 rounded-full">{{ auth()->user()->transactions()->where('status', 'pending')->count() }}</span>
                            @endif
                        </a>
                        
                        <!-- My Reviews -->
                        <a href="{{ route('dashboard') }}#reviews" class="nav-item-hover flex items-center px-4 py-3 text-sm font-medium rounded-xl text-gray-700 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400">
                            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l2.036 6.29a1 1 0 00.95.69h6.631c.969 0 1.371 1.24.588 1.81l-5.37 3.905a1 1 0 00-.364 1.118l2.036 6.29c.3.921-.755 1.688-1.54 1.118l-5.37-3.905a1 1 0 00-1.176 0l-5.37 3.905c-.784.57-1.838-.197-1.54-1.118l2.036-6.29a1 1 0 00-.364-1.118l-5.37-3.905c-.783-.57-.38-1.81.588-1.81h6.631a1 1 0 00.95-.69l2.036-6.29z"/>
                            </svg>
                            My Reviews
                        </a>
                        
                        <!-- Notifications -->
                        <a href="{{ route('dashboard') }}#notifications" class="nav-item-hover flex items-center px-4 py-3 text-sm font-medium rounded-xl text-gray-700 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400">
                            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V4a2 2 0 10-4 0v1.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"/>
                            </svg>
                            Notifications
                            <span class="ml-auto bg-red-500 text-white text-xs px-2 py-1 rounded-full animate-pulse">3</span>
                        </a>
                    </div>
                </div>

                <!-- Security & Privacy -->
                <div class="mb-4">
                    <h3 class="px-4 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider mb-2">Security</h3>
                    <div class="space-y-1">
                        <!-- Two-Factor Authentication -->
                        <a href="{{ route('profile.show') }}" class="nav-item-hover flex items-center px-4 py-3 text-sm font-medium rounded-xl {{ request()->routeIs('profile.show') ? 'text-indigo-600 dark:text-indigo-400 bg-indigo-50 dark:bg-indigo-900/30' : 'text-gray-700 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400' }}">
                            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"/>
                            </svg>
                            Security Settings
                            @if(!auth()->user()->two_factor_secret)
                                <span class="ml-auto bg-yellow-500 text-white text-xs px-2 py-1 rounded-full">!</span>
                            @endif
                        </a>
                        
                        <!-- Privacy Settings -->
                        <a href="{{ route('dashboard') }}#account" class="nav-item-hover flex items-center px-4 py-3 text-sm font-medium rounded-xl text-gray-700 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400">
                            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L8.464 8.464M9.878 9.878l-1.415-1.414M14.12 14.12l1.415 1.415M14.12 14.12L15.535 15.535M14.12 14.12l1.414 1.414"/>
                            </svg>
                            Privacy Settings
                        </a>
                    </div>
                </div>

                <!-- Additional Features -->
                <div class="mb-4">
                    <h3 class="px-4 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider mb-2">More</h3>
                    <div class="space-y-1">
                        <!-- Wishlist -->
                        <a href="#" class="nav-item-hover flex items-center px-4 py-3 text-sm font-medium rounded-xl text-gray-700 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400" title="Coming Soon">
                            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"/>
                            </svg>
                            Wishlist
                            <span class="ml-auto text-xs text-gray-400">Soon</span>
                        </a>
                        
                        <!-- Blog -->
                        <a href="{{ route('blog.index') }}" class="nav-item-hover flex items-center px-4 py-3 text-sm font-medium rounded-xl {{ request()->routeIs('blog.*') ? 'text-indigo-600 dark:text-indigo-400 bg-indigo-50 dark:bg-indigo-900/30' : 'text-gray-700 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400' }}">
                            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"/>
                            </svg>
                            Blog & News
                        </a>
                        
                        <!-- Support -->
                        <a href="#" class="nav-item-hover flex items-center px-4 py-3 text-sm font-medium rounded-xl text-gray-700 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400">
                            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-5 0a4 4 0 11-8 0 4 4 0 018 0z"/>
                            </svg>
                            Help Center
                        </a>
                    </div>
                </div>

                <!-- Support Section -->
                <div class="pt-8">
                    <div class="glass-card rounded-2xl p-4 border border-white/20">
                        <div class="flex items-center space-x-3 mb-3">
                            <div class="w-8 h-8 bg-gradient-to-br from-green-400 to-blue-500 rounded-lg flex items-center justify-center">
                                <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                </svg>
                            </div>
                            <div>
                                <h3 class="font-medium text-gray-900 dark:text-white">Need Help?</h3>
                                <p class="text-xs text-gray-600 dark:text-gray-400">Get support</p>
                            </div>
                        </div>
                        <button class="w-full bg-gradient-to-r from-indigo-500 to-purple-600 hover:from-indigo-600 hover:to-purple-700 text-white text-sm font-medium py-2 px-4 rounded-lg transition-all duration-200 transform hover:scale-105">
                            Contact Support
                        </button>
                    </div>
                </div>
            </nav>
        </aside>

        <!-- Main Content Area -->
        <div class="app-main">
            <!-- Sticky Top Navigation Bar -->    
            <div class="app-topbar flex h-16 sm:h-20 shrink-0 items-center gap-x-3 sm:gap-x-4 lg:gap-x-6 glass-card border-b border-white/20 dark:border-gray-700/50 backdrop-blur-xl px-3 sm:px-4 lg:px-6 xl:px-8">
                <!-- Mobile Sidebar Toggle -->
                <button type="button" class="group -m-2 p-2 text-gray-700 dark:text-gray-300 hover:bg-white/20 dark:hover:bg-gray-800/50 rounded-xl transition-all duration-200 lg:hidden touch-manipulation" @click="sidebarOpen = !sidebarOpen">
                    <span class="sr-only">Open sidebar</span>
                    <svg class="h-6 w-6 group-hover:scale-110 transition-transform" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5" />
                    </svg>
                </button>

                <!-- Page Title - Show on mobile/tablet -->
                <div class="lg:hidden flex-1 text-center">
                    <h1 class="text-lg font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">@yield('page-title', 'Dashboard')</h1>
                </div>

                <div class="flex flex-1 gap-x-4 self-stretch lg:gap-x-6 justify-end items-center">
                    <!-- Modern Search Bar - Hidden on mobile -->
                    <div class="hidden lg:flex lg:flex-1 lg:max-w-lg">
                        <div class="relative w-full group">
                            <div class="absolute inset-y-0 left-0 flex items-center pl-4 pointer-events-none">
                                <svg class="w-5 h-5 text-gray-500 dark:text-gray-400 group-focus-within:text-indigo-500 transition-colors" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 20">
                                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z"/>
                                </svg>
                            </div>
                            <input type="search" id="search" class="block w-full py-3 pl-12 pr-4 text-sm bg-white/60 dark:bg-gray-800/60 backdrop-blur-sm border border-white/30 dark:border-gray-700/50 rounded-2xl focus:ring-2 focus:ring-indigo-500/50 focus:border-transparent text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 transition-all duration-200" placeholder="Search anything...">
                        </div>
                    </div>

                    <!-- Modern Notification Bell -->
                    <div class="flex items-center">
                        <div class="relative">
                            <button type="button" class="relative p-3 text-gray-600 dark:text-gray-400 rounded-2xl hover:bg-white/20 dark:hover:bg-gray-800/50 transition-all duration-200 group">
                                <span class="sr-only">View notifications</span>
                                <svg class="w-6 h-6 group-hover:scale-110 transition-transform" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
                                </svg>
                                <div class="absolute -top-1 -right-1 flex items-center justify-center">
                                    <div class="w-5 h-5 bg-red-500 rounded-full flex items-center justify-center text-xs font-bold text-white animate-pulse">3</div>
                                    <div class="absolute w-5 h-5 bg-red-500 rounded-full pulse-ring"></div>
                                </div>
                            </button>
                        </div>
                    </div>

                    <!-- Modern Theme Toggle -->
                    <div class="flex items-center">
                        <button id="theme-toggle" type="button" class="p-3 text-gray-600 dark:text-gray-400 hover:bg-white/20 dark:hover:bg-gray-800/50 rounded-2xl transition-all duration-200 group" onclick="toggleTheme()">
                            <span class="dark:hidden">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 group-hover:scale-110 group-hover:rotate-12 transition-all" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
                                </svg>
                            </span>
                            <span class="hidden dark:inline">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 group-hover:scale-110 group-hover:rotate-12 transition-all" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
                                </svg>
                            </span>
                        </button>
                    </div>

                    <!-- Modern User Dropdown -->
                    <div x-data="{ userMenuOpen: false }" class="relative z-40">
                        <button type="button" class="flex items-center gap-x-3 p-2 rounded-2xl hover:bg-white/20 dark:hover:bg-gray-800/50 transition-all duration-200 group" id="user-menu-button" aria-expanded="false" aria-haspopup="true" @click="userMenuOpen = !userMenuOpen">
                            <span class="sr-only">Open user menu</span>
                            <div class="relative">
                                <div class="h-10 w-10 rounded-2xl bg-gradient-to-br from-indigo-500 to-purple-600 flex items-center justify-center text-white shadow-lg group-hover:scale-105 transition-transform glow-effect">
                                    {{ substr(auth()->user()->name ?? 'U', 0, 1) }}
                                </div>
                                <div class="absolute -bottom-1 -right-1 w-4 h-4 bg-green-400 border-2 border-white dark:border-gray-800 rounded-full"></div>
                            </div>
                            <span class="hidden lg:flex lg:items-center lg:space-x-2">
                                <div class="text-left">
                                    <span class="text-sm font-semibold text-gray-900 dark:text-white">{{ auth()->user()->name ?? 'User' }}</span>
                                    <p class="text-xs text-gray-600 dark:text-gray-400">Online</p>
                                </div>
                                <svg class="h-5 w-5 text-gray-400 group-hover:text-gray-600 dark:group-hover:text-gray-300 transition-colors" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                    <path fill-rule="evenodd" d="M5.23 7.21a.75.75 0 011.06.02L10 11.168l3.71-3.938a.75.75 0 111.08 1.04l-4.25 4.5a.75.75 0 01-1.08 0l-4.25-4.5a.75.75 0 01.02-1.06z" clip-rule="evenodd" />
                                </svg>
                            </span>
                        </button>

                        <!-- Modern User Dropdown Menu -->
                        <div x-show="userMenuOpen" @click.away="userMenuOpen = false" 
                            x-transition:enter="transition ease-out duration-200"
                            x-transition:enter-start="transform opacity-0 scale-95"
                            x-transition:enter-end="transform opacity-100 scale-100"
                            x-transition:leave="transition ease-in duration-150"
                            x-transition:leave-start="transform opacity-100 scale-100"
                            x-transition:leave-end="transform opacity-0 scale-95"
                            class="absolute right-0 z-10 mt-3 w-64 origin-top-right glass-card rounded-2xl modern-shadow border border-white/20 dark:border-gray-700/50 backdrop-blur-xl" role="menu" aria-orientation="vertical" aria-labelledby="user-menu-button" tabindex="-1">
                            
                            <!-- User Info Header -->
                            <div class="px-4 py-4 border-b border-white/10 dark:border-gray-700/50">
                                <div class="flex items-center space-x-3">
                                    <div class="h-12 w-12 rounded-2xl bg-gradient-to-br from-indigo-500 to-purple-600 flex items-center justify-center text-white font-semibold text-lg glow-effect">
                                        {{ substr(auth()->user()->name ?? 'U', 0, 1) }}
                                    </div>
                                    <div>
                                        <p class="text-sm font-semibold text-gray-900 dark:text-white">{{ auth()->user()->name ?? 'User' }}</p>
                                        <p class="text-xs text-gray-600 dark:text-gray-400">{{ auth()->user()->email ?? '<EMAIL>' }}</p>
                                        <div class="flex items-center mt-1">
                                            <div class="w-2 h-2 bg-green-400 rounded-full mr-2"></div>
                                            <span class="text-xs text-green-600 dark:text-green-400">Active now</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Menu Items -->
                            <div class="py-2">
                                <a href="#" class="flex items-center px-4 py-3 text-sm text-gray-700 dark:text-gray-300 hover:bg-white/10 dark:hover:bg-gray-800/50 transition-all duration-200 group" role="menuitem" tabindex="-1">
                                    <div class="w-8 h-8 bg-purple-100 dark:bg-purple-900/50 rounded-lg flex items-center justify-center mr-3 group-hover:scale-110 transition-transform">
                                        <svg class="w-4 h-4 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                        </svg>
                                    </div>
                                    <div>
                                        <p class="font-medium">Settings</p>
                                        <p class="text-xs text-gray-500 dark:text-gray-400">Preferences & privacy</p>
                                    </div>
                                </a>
                                
                                <a href="#" class="flex items-center px-4 py-3 text-sm text-gray-700 dark:text-gray-300 hover:bg-white/10 dark:hover:bg-gray-800/50 transition-all duration-200 group" role="menuitem" tabindex="-1">
                                    <div class="w-8 h-8 bg-green-100 dark:bg-green-900/50 rounded-lg flex items-center justify-center mr-3 group-hover:scale-110 transition-transform">
                                        <svg class="w-4 h-4 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                    </div>
                                    <div>
                                        <p class="font-medium">Help & Support</p>
                                        <p class="text-xs text-gray-500 dark:text-gray-400">Get assistance</p>
                                    </div>
                                </a>
                            </div>
                            
                            <div class="border-t border-white/10 dark:border-gray-700/50">
                                <form method="POST" action="{{ route('logout') }}">
                                    @csrf
                                    <button type="submit" class="flex w-full items-center px-4 py-3 text-sm text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 transition-all duration-200 group" role="menuitem" tabindex="-1">
                                        <div class="w-8 h-8 bg-red-100 dark:bg-red-900/50 rounded-lg flex items-center justify-center mr-3 group-hover:scale-110 transition-transform">
                                            <svg class="w-4 h-4 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                                            </svg>
                                        </div>
                                        <div>
                                            <p class="font-medium">Sign out</p>
                                            <p class="text-xs text-red-500 dark:text-red-400">End your session</p>
                                        </div>
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Scrollable Page Content -->
            <main id="main-content" class="app-content pt-6 sm:pt-8 relative z-10">
                <div class="container mx-auto px-3 sm:px-4 lg:px-6 xl:px-8 pb-6 sm:pb-8 lg:pb-12">
                    
                    <!-- Success/Error Messages with modern styling -->
                    @if (session('success'))
                        <div x-data="{ show: true }" x-show="show" x-init="setTimeout(() => show = false, 5000)"
                            x-transition:enter="transition ease-out duration-300"
                            x-transition:enter-start="opacity-0 transform scale-95"
                            x-transition:enter-end="opacity-100 transform scale-100"
                            x-transition:leave="transition ease-in duration-200"
                            x-transition:leave-start="opacity-100 transform scale-100"
                            x-transition:leave-end="opacity-0 transform scale-95"
                            class="mb-6 glass-card border-l-4 border-green-500 text-green-700 dark:text-green-300 p-6 rounded-2xl modern-shadow backdrop-blur-xl">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-green-100 dark:bg-green-900/50 rounded-xl flex items-center justify-center mr-4">
                                    <svg class="h-6 w-6 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                </div>
                                <div>
                                    <h3 class="font-semibold text-green-800 dark:text-green-300">Success!</h3>
                                    <p class="text-sm">{{ session('success') }}</p>
                                </div>
                                <button @click="show = false" class="ml-auto text-green-500 hover:text-green-700 transition-colors">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                    </svg>
                                </button>
                            </div>
                        </div>
                    @endif

                    @if (session('error'))
                        <div x-data="{ show: true }" x-show="show" x-init="setTimeout(() => show = false, 5000)"
                            x-transition:enter="transition ease-out duration-300"
                            x-transition:enter-start="opacity-0 transform scale-95"
                            x-transition:enter-end="opacity-100 transform scale-100"
                            x-transition:leave="transition ease-in duration-200"
                            x-transition:leave-start="opacity-100 transform scale-100"
                            x-transition:leave-end="opacity-0 transform scale-95"
                            class="mb-6 glass-card border-l-4 border-red-500 text-red-700 dark:text-red-300 p-6 rounded-2xl modern-shadow backdrop-blur-xl">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-red-100 dark:bg-red-900/50 rounded-xl flex items-center justify-center mr-4">
                                    <svg class="h-6 w-6 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                    </svg>
                                </div>
                                <div>
                                    <h3 class="font-semibold text-red-800 dark:text-red-300">Error!</h3>
                                    <p class="text-sm">{{ session('error') }}</p>
                                </div>
                                <button @click="show = false" class="ml-auto text-red-500 hover:text-red-700 transition-colors">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                    </svg>
                                </button>
                            </div>
                        </div>
                    @endif

                    <!-- Modern Content Card -->
                    <div class="glass-card rounded-3xl modern-shadow p-8 border border-white/20 dark:border-gray-700/50 backdrop-blur-xl">
                        @yield('content')
                    </div>
                </div>
            </main>

                <!-- Modern Footer -->
                <footer class="glass-card py-6 px-6 border-t border-white/20 dark:border-gray-700/50 backdrop-blur-xl relative z-10 mt-auto">
                    <div class="container mx-auto flex flex-col md:flex-row justify-between items-center">
                        <div class="text-center md:text-left mb-4 md:mb-0">
                            <div class="flex items-center justify-center md:justify-start space-x-2">
                                <div class="w-6 h-6 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-lg flex items-center justify-center">
                                    <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                                    </svg>
                                </div>
                                <span class="font-semibold text-gray-900 dark:text-white">{{ config('app.name') }}</span>
                            </div>
                            <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">&copy; {{ date('Y') }} All rights reserved.</p>
                        </div>
                        <div class="flex flex-wrap justify-center gap-6">
                            <a href="#" class="text-sm text-gray-600 dark:text-gray-400 hover:text-indigo-600 dark:hover:text-indigo-400 transition-colors duration-200 hover:scale-105 transform">
                                Privacy Policy
                            </a>
                            <a href="#" class="text-sm text-gray-600 dark:text-gray-400 hover:text-indigo-600 dark:hover:text-indigo-400 transition-colors duration-200 hover:scale-105 transform">
                                Terms of Service
                            </a>
                            <a href="#" class="text-sm text-gray-600 dark:text-gray-400 hover:text-indigo-600 dark:hover:text-indigo-400 transition-colors duration-200 hover:scale-105 transform">
                                Help Center
                            </a>
                            <a href="#" class="text-sm text-gray-600 dark:text-gray-400 hover:text-indigo-600 dark:hover:text-indigo-400 transition-colors duration-200 hover:scale-105 transform">
                                Contact Us
                            </a>
                        </div>
                    </div>
                </footer>
            </main>
        </div>
    </div>

    @stack('scripts')
    @livewireScripts

    <!-- Enhanced Theme Toggle Script -->
    <script>
        // Enhanced theme toggle functionality with smooth transitions
        function setThemeIcon(isDark) {
            // Icons are handled via CSS classes automatically
        }

        function toggleTheme() {
            const html = document.documentElement;
            const isDark = html.classList.contains('dark');
            
            // Add a subtle transition effect
            html.style.transition = 'all 0.3s ease-in-out';
            
            if (isDark) {
                html.classList.remove('dark');
                html.classList.add('light');
                html.setAttribute('data-theme', 'light');
                localStorage.setItem('theme', 'light');
            } else {
                html.classList.add('dark');
                html.classList.remove('light');
                html.setAttribute('data-theme', 'dark');
                localStorage.setItem('theme', 'dark');
            }
            
            // Remove transition after theme change is complete
            setTimeout(() => {
                html.style.transition = '';
            }, 300);
        }

        // Initialize on page load with enhanced animations
        document.addEventListener('DOMContentLoaded', function() {
            // Set initial state based on class
            const isDark = document.documentElement.classList.contains('dark');
            
            // Add subtle loading animation
            const mainContent = document.getElementById('main-content');
            if (mainContent) {
                mainContent.style.opacity = '0';
                mainContent.style.transform = 'translateY(20px)';
                
                setTimeout(() => {
                    mainContent.style.transition = 'all 0.6s cubic-bezier(0.4, 0, 0.2, 1)';
                    mainContent.style.opacity = '1';
                    mainContent.style.transform = 'translateY(0)';
                }, 100);
            }
            
            // Add smooth scroll behavior
            document.documentElement.style.scrollBehavior = 'smooth';
        });

        // Add intersection observer for subtle animations on scroll
        if ('IntersectionObserver' in window) {
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }
                });
            }, {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            });

            // Observe elements that should animate in
            document.querySelectorAll('.glass-card').forEach(el => {
                el.style.opacity = '0';
                el.style.transform = 'translateY(30px)';
                el.style.transition = 'all 0.6s cubic-bezier(0.4, 0, 0.2, 1)';
                observer.observe(el);
            });
        }

        // Add subtle parallax effect to background elements
        window.addEventListener('scroll', () => {
            const scrolled = window.pageYOffset;
            const parallaxElements = document.querySelectorAll('.floating-element');
            
            parallaxElements.forEach((element, index) => {
                const speed = 0.5 + (index * 0.1);
                element.style.transform = `translateY(${scrolled * speed}px) rotate(${scrolled * 0.01}deg)`;
            });
        });

        // Handle sidebar navigation active states based on hash
        function updateSidebarActiveStates() {
            const hash = window.location.hash.substring(1);
            const sidebarLinks = document.querySelectorAll('.app-sidebar a[href*="#"]');
            
            sidebarLinks.forEach(link => {
                const linkHash = link.getAttribute('href').split('#')[1];
                if (linkHash === hash) {
                    link.classList.add('text-indigo-600', 'dark:text-indigo-400', 'bg-indigo-50', 'dark:bg-indigo-900/30');
                    link.classList.remove('text-gray-700', 'dark:text-gray-300');
                } else {
                    link.classList.remove('text-indigo-600', 'dark:text-indigo-400', 'bg-indigo-50', 'dark:bg-indigo-900/30');
                    link.classList.add('text-gray-700', 'dark:text-gray-300');
                }
            });
        }

        // Update active states on hash change
        window.addEventListener('hashchange', updateSidebarActiveStates);
        
        // Update active states on page load
        document.addEventListener('DOMContentLoaded', updateSidebarActiveStates);
        
        // Mobile-specific improvements
        document.addEventListener('DOMContentLoaded', function() {
            // Add touch-friendly behavior for mobile
            if (window.innerWidth < 1024) {
                // Close sidebar when clicking outside on mobile
                document.addEventListener('click', function(e) {
                    const sidebar = document.querySelector('.app-sidebar');
                    const sidebarToggle = document.querySelector('[\\@click="sidebarOpen = !sidebarOpen"]');
                    
                    if (sidebar && !sidebar.contains(e.target) && !sidebarToggle.contains(e.target)) {
                        // Trigger Alpine.js to close sidebar
                        if (window.Alpine && window.Alpine.$data) {
                            try {
                                const alpineData = window.Alpine.$data(document.querySelector('[x-data]'));
                                if (alpineData && alpineData.sidebarOpen) {
                                    alpineData.sidebarOpen = false;
                                }
                            } catch (e) {
                                console.log('Alpine data access failed:', e);
                            }
                        }
                    }
                });
                
                // Prevent body scroll when sidebar is open
                const observer = new MutationObserver(function(mutations) {
                    mutations.forEach(function(mutation) {
                        if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                            const sidebar = document.querySelector('.app-sidebar');
                            if (sidebar && sidebar.classList.contains('open')) {
                                document.body.style.overflow = 'hidden';
                            } else {
                                document.body.style.overflow = '';
                            }
                        }
                    });
                });
                
                const sidebar = document.querySelector('.app-sidebar');
                if (sidebar) {
                    observer.observe(sidebar, { attributes: true });
                }
            }
        });
    </script>
</body>

</html>