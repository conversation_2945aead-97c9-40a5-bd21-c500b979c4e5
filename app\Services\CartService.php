<?php

namespace App\Services;

use App\Models\Cart;
use App\Models\CartItem;
use App\Models\Component;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\DB;

class CartService
{
    /**
     * Get the current cart for the user or session.
     *
     * @param User|null $user
     * @param string|null $sessionId
     * @return \App\Models\Cart
     */
    public function getCart(?User $user = null, ?string $sessionId = null)
    {
        $user = $user ?? Auth::user();
        $sessionId = $sessionId ?? Session::getId();
        
        // If user is logged in, get their cart
        if ($user) {
            $cart = Cart::firstOrCreate(
                ['user_id' => $user->id],
                ['session_id' => $sessionId]
            );
            
            // If there was a session cart, merge it with the user cart
            $this->mergeGuestCart($user, $sessionId);
            
            return $cart;
        }
        
        // Otherwise, get the session cart
        return Cart::firstOrCreate(
            ['session_id' => $sessionId],
            ['user_id' => null]
        );
    }
    
    /**
     * Merge guest cart with user cart when user logs in.
     *
     * @param User $user
     * @param string $sessionId
     * @return void
     */
    public function mergeGuestCart(User $user, string $sessionId): void
    {
        // Get the session cart
        $sessionCart = Cart::where('session_id', $sessionId)
            ->where('user_id', null)
            ->first();
        
        if (!$sessionCart || $sessionCart->items()->count() === 0) {
            return;
        }
        
        // Get or create user cart
        $userCart = Cart::firstOrCreate(['user_id' => $user->id]);
        
        DB::transaction(function () use ($sessionCart, $userCart) {
            // Move all items from session cart to user cart
            foreach ($sessionCart->items()->with('component')->get() as $item) {
                // Check if item already exists in user cart
                $existingItem = $userCart->items()->where('component_id', $item->component_id)->first();
                
                if ($existingItem) {
                    $existingItem->quantity += $item->quantity;
                    $existingItem->save();
                } else {
                    $userCart->items()->create([
                        'component_id' => $item->component_id,
                        'quantity' => $item->quantity,
                        'price' => $item->price,
                    ]);
                }
            }
            
            // Update user cart total and delete session cart
            $userCart->updateTotal();
            $sessionCart->delete();
        });
    }
    
    /**
     * Add a component to the cart.
     *
     * @param Component $component
     * @param int $quantity
     * @param User|null $user
     * @return CartItem
     * @throws \InvalidArgumentException
     */
    public function addToCart(Component $component, int $quantity, ?User $user = null): CartItem
    {
        if ($quantity <= 0) {
            throw new \InvalidArgumentException('Quantity must be greater than 0');
        }

        if (!$component->is_active) {
            throw new \InvalidArgumentException('Component is not available');
        }

        // Check stock availability
        if ($component->stock < $quantity) {
            throw new \InvalidArgumentException('Insufficient stock available');
        }

        $cart = $this->getCart($user);
        
        return DB::transaction(function () use ($cart, $component, $quantity) {
            // Check if item already exists in cart
            $existingItem = $cart->items()->where('component_id', $component->id)->first();
            
            if ($existingItem) {
                $newQuantity = $existingItem->quantity + $quantity;
                
                // Check total stock availability
                if ($component->stock < $newQuantity) {
                    throw new \InvalidArgumentException('Insufficient stock for requested quantity');
                }
                
                $existingItem->quantity = $newQuantity;
                $existingItem->save();
                
                $cart->updateTotal();
                return $existingItem;
            }
            
            // Create new cart item
            $item = $cart->items()->create([
                'component_id' => $component->id,
                'quantity' => $quantity,
                'price' => $component->price,
            ]);
            
            $cart->updateTotal();
            return $item;
        });
    }

    /**
     * Add an item to the cart (legacy method for backward compatibility).
     *
     * @param int $componentId
     * @param int $quantity
     * @return \App\Models\CartItem
     * @throws \InvalidArgumentException
     */
    public function addItem($componentId, $quantity = 1)
    {
        $component = Component::find($componentId);
        
        if (!$component) {
            throw new \InvalidArgumentException('Component not found');
        }
        
        // Check stock availability
        if ($component->stock < $quantity) {
            throw new \InvalidArgumentException("Insufficient stock. Only {$component->stock} available.");
        }
        
        return $this->addToCart($component, $quantity);
    }
    
    /**
     * Get the total price of the cart.
     *
     * @param User|null $user
     * @return float
     */
    public function getCartTotal(?User $user = null): float
    {
        $cart = $this->getCart($user);
        return (float) $cart->total;
    }
    
    /**
     * Calculate shipping cost for the cart.
     *
     * @param Cart $cart
     * @return float
     */
    public function calculateShipping(Cart $cart): float
    {
        // Free shipping for orders over $100
        if ($cart->total >= 100.00) {
            return 0.00;
        }
        
        // Base shipping rate
        $baseShipping = 10.00;
        
        // Calculate shipping based on item count and weight
        $itemCount = $cart->items()->sum('quantity');
        
        // Additional shipping for more items
        if ($itemCount > 5) {
            $baseShipping += 5.00;
        }
        
        return $baseShipping;
    }
    
    /**
     * Clear all items from the cart.
     *
     * @param User|null $user
     * @return bool
     */
    public function clearCart(?User $user = null): bool
    {
        $cart = $this->getCart($user);
        return $cart->clear();
    }
    
    /**
     * Update the quantity of an item in the cart.
     *
     * @param int $itemId
     * @param int $quantity
     * @param User|null $user
     * @return CartItem|bool
     * @throws \InvalidArgumentException
     */
    public function updateItemQuantity($itemId, $quantity, ?User $user = null)
    {
        if ($quantity < 0) {
            throw new \InvalidArgumentException('Quantity cannot be negative');
        }

        $cart = $this->getCart($user);
        $item = $cart->items()->find($itemId);

        if (!$item) {
            return false;
        }

        if ($quantity === 0) {
            return $this->removeFromCart($item, $user);
        }

        // Check stock availability
        if ($item->component->stock < $quantity) {
            throw new \InvalidArgumentException('Insufficient stock for requested quantity');
        }

        return DB::transaction(function () use ($item, $quantity, $cart) {
            $item->quantity = $quantity;
            $item->save();
            
            $cart->updateTotal();
            return $item;
        });
    }
    
    /**
     * Remove an item from the cart.
     *
     * @param CartItem|int $item
     * @param User|null $user
     * @return bool
     */
    public function removeFromCart($item, ?User $user = null): bool
    {
        $cart = $this->getCart($user);
        
        if (is_int($item)) {
            $item = $cart->items()->find($item);
        }
        
        if (!$item) {
            return false;
        }
        
        return DB::transaction(function () use ($item, $cart) {
            $result = $item->delete();
            $cart->updateTotal();
            return $result;
        });
    }

    /**
     * Remove an item from the cart (legacy method).
     *
     * @param int $itemId
     * @return bool
     */
    public function removeItem($itemId)
    {
        return $this->removeFromCart($itemId);
    }
    
    // Removed duplicate clearCart method
    
    /**
     * Add a build to the cart.
     *
     * @param int $buildId
     * @return bool
     */
    public function addBuild($buildId)
    {
        $build = \App\Models\Build::find($buildId);
        
        if (!$build) {
            return false;
        }
        
        $cart = $this->getCart();
        return $cart->addBuild($build);
    }
    
    /**
     * Get the total number of items in the cart.
     *
     * @param User|null $user
     * @return int
     */
    public function getItemCount(?User $user = null)
    {
        $cart = $this->getCart($user);
        
        if (!$cart) {
            return 0;
        }
        
        return $cart->items()->sum('quantity');
    }
    
    /**
     * Get the total price of the cart.
     *
     * @param User|null $user
     * @return float
     */
    public function getTotal(?User $user = null)
    {
        $cart = $this->getCart($user);
        
        if (!$cart) {
            return 0;
        }
        
        return $cart->total;
    }
    
    /**
     * Transfer a guest cart to a user after login.
     *
     * @param string $sessionId
     * @param int $userId
     * @return \App\Models\Cart|null
     */
    public function transferCartAfterLogin($sessionId, $userId)
    {
        // Find the session cart
        $sessionCart = Cart::where('session_id', $sessionId)
            ->where('user_id', null)
            ->first();
        
        if (!$sessionCart) {
            return null;
        }
        
        // Find or create the user cart
        $userCart = Cart::firstOrCreate(
            ['user_id' => $userId],
            ['session_id' => $sessionId]
        );
        
        // Move all items from session cart to user cart
        foreach ($sessionCart->items()->with('component')->get() as $item) {
            $userCart->addItem($item->component, $item->quantity, $item->price);
        }
        
        // Delete the session cart
        $sessionCart->delete();
        
        return $userCart;
    }

    /**
     * Calculate cart subtotal (before tax and shipping).
     *
     * @param User|null $user
     * @return float
     */
    public function getSubtotal(?User $user = null): float
    {
        $cart = $this->getCart($user);
        return $cart->total ?? 0;
    }

    /**
     * Calculate tax amount for the cart.
     *
     * @param User|null $user
     * @param float $taxRate
     * @return float
     */
    public function getTaxAmount(?User $user = null, float $taxRate = 0.08): float
    {
        $subtotal = $this->getSubtotal($user);
        return round($subtotal * $taxRate, 2);
    }

    /**
     * Calculate shipping cost for the cart.
     *
     * @param User|null $user
     * @param array $shippingAddress
     * @return float
     */
    public function getShippingCost(?User $user = null, array $shippingAddress = []): float
    {
        $cart = $this->getCart($user);
        $subtotal = $cart->total ?? 0;
        
        // Free shipping over $100
        if ($subtotal >= 100) {
            return 0;
        }
        
        // Standard shipping rate
        return 9.99;
    }

    /**
     * Calculate total cart amount including tax and shipping.
     *
     * @param User|null $user
     * @param float $taxRate
     * @param array $shippingAddress
     * @return array
     */
    public function getCartTotals(?User $user = null, float $taxRate = 0.08, array $shippingAddress = []): array
    {
        $subtotal = $this->getSubtotal($user);
        $tax = $this->getTaxAmount($user, $taxRate);
        $shipping = $this->getShippingCost($user, $shippingAddress);
        $total = $subtotal + $tax + $shipping;

        return [
            'subtotal' => $subtotal,
            'tax' => $tax,
            'shipping' => $shipping,
            'total' => round($total, 2),
        ];
    }

    /**
     * Validate cart item quantities against current stock levels.
     *
     * @param User|null $user
     * @return array
     */
    public function validateCartStock(?User $user = null): array
    {
        $cart = $this->getCart($user);
        $issues = [];

        foreach ($cart->items()->with('component')->get() as $item) {
            $component = $item->component;
            
            if (!$component->is_active) {
                $issues[] = [
                    'item_id' => $item->id,
                    'component_name' => $component->name,
                    'issue' => 'Component is no longer available',
                    'current_quantity' => $item->quantity,
                    'available_quantity' => 0,
                ];
                continue;
            }

            if ($component->stock < $item->quantity) {
                $issues[] = [
                    'item_id' => $item->id,
                    'component_name' => $component->name,
                    'issue' => 'Insufficient stock',
                    'current_quantity' => $item->quantity,
                    'available_quantity' => $component->stock,
                ];
            }
        }

        return $issues;
    }

    /**
     * Fix cart stock issues by adjusting quantities or removing items.
     *
     * @param User|null $user
     * @return array
     */
    public function fixCartStockIssues(?User $user = null): array
    {
        $issues = $this->validateCartStock($user);
        $fixed = [];

        foreach ($issues as $issue) {
            $itemId = $issue['item_id'];
            $availableQuantity = $issue['available_quantity'];

            if ($availableQuantity > 0) {
                // Adjust quantity to available stock
                $this->updateItemQuantity($itemId, $availableQuantity, $user);
                $fixed[] = [
                    'item_id' => $itemId,
                    'action' => 'quantity_adjusted',
                    'new_quantity' => $availableQuantity,
                ];
            } else {
                // Remove item completely
                $this->removeFromCart($itemId, $user);
                $fixed[] = [
                    'item_id' => $itemId,
                    'action' => 'item_removed',
                ];
            }
        }

        return $fixed;
    }

    /**
     * Clean up expired guest carts.
     *
     * @param int $daysOld
     * @return int
     */
    public function cleanupExpiredCarts(int $daysOld = 7): int
    {
        $cutoffDate = now()->subDays($daysOld);
        
        return Cart::where('user_id', null)
            ->where('updated_at', '<', $cutoffDate)
            ->delete();
    }

    /**
     * Clean up empty carts.
     *
     * @return int
     */
    public function cleanupEmptyCarts(): int
    {
        $emptyCarts = Cart::whereDoesntHave('items')->get();
        $count = $emptyCarts->count();
        
        foreach ($emptyCarts as $cart) {
            $cart->delete();
        }
        
        return $count;
    }

    /**
     * Update cart item prices to current component prices.
     *
     * @param User|null $user
     * @return array
     */
    public function updateCartPrices(?User $user = null): array
    {
        $cart = $this->getCart($user);
        $updated = [];

        foreach ($cart->items()->with('component')->get() as $item) {
            $currentPrice = $item->component->price;
            
            if ($item->price != $currentPrice) {
                $oldPrice = $item->price;
                $item->price = $currentPrice;
                $item->save();
                
                $updated[] = [
                    'item_id' => $item->id,
                    'component_name' => $item->component->name,
                    'old_price' => $oldPrice,
                    'new_price' => $currentPrice,
                ];
            }
        }

        if (!empty($updated)) {
            $cart->updateTotal();
        }

        return $updated;
    }

    /**
     * Check if cart has any items.
     *
     * @param User|null $user
     * @return bool
     */
    public function hasItems(?User $user = null): bool
    {
        return $this->getItemCount($user) > 0;
    }

    /**
     * Get cart weight for shipping calculations.
     *
     * @param User|null $user
     * @return float
     */
    public function getCartWeight(?User $user = null): float
    {
        $cart = $this->getCart($user);
        $weight = 0;

        foreach ($cart->items()->with('component')->get() as $item) {
            $componentWeight = $item->component->getSpec('weight', 1.0); // Default 1 lb
            $weight += $componentWeight * $item->quantity;
        }

        return $weight;
    }

    /**
     * Remove an item from the cart by component ID.
     *
     * @param int $componentId
     * @param User|null $user
     * @return bool
     */
    public function removeItemByComponentId($componentId, ?User $user = null): bool
    {
        $cart = $this->getCart($user);
        $item = $cart->items()->where('component_id', $componentId)->first();
        
        if (!$item) {
            throw new \InvalidArgumentException('Item not found in cart');
        }
        
        return $this->removeFromCart($item, $user);
    }

    /**
     * Apply a coupon to the cart.
     *
     * @param string $couponCode
     * @param User|null $user
     * @return array
     */
    public function applyCoupon($couponCode, ?User $user = null): array
    {
        $coupon = \App\Models\Coupon::findByCode($couponCode);

        if (!$coupon) {
            throw new \InvalidArgumentException('Invalid coupon code');
        }

        $userId = $user ? $user->id : auth()->id();
        if (!$coupon->canBeUsedBy($userId)) {
            throw new \InvalidArgumentException('This coupon cannot be used by this user');
        }

        $cart = $this->getCart($user);
        $subtotal = $cart->total;

        // Check minimum amount requirement
        if ($coupon->minimum_amount && $subtotal < $coupon->minimum_amount) {
            throw new \InvalidArgumentException(
                "Minimum order amount of ₹" . number_format($coupon->minimum_amount, 2) . " required."
            );
        }

        // For now, skip product applicability check for components
        // TODO: Implement proper component-coupon compatibility
        // Get cart items for product applicability check
        // $cartItems = $cart->items()->with('component')->get();
        // $productIds = $cartItems->pluck('component_id')->toArray();
        //
        // if (!$coupon->isApplicableToProducts($productIds)) {
        //     throw new \InvalidArgumentException('This coupon is not applicable to selected products');
        // }

        $discountAmount = $coupon->calculateDiscount((float) $subtotal);

        // Store coupon in session
        session(['applied_coupon' => $couponCode, 'discount_amount' => $discountAmount]);

        return [
            'discount_amount' => $discountAmount,
            'coupon_code' => $couponCode
        ];
    }
}