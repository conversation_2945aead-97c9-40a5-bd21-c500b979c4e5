<?php

require_once 'vendor/autoload.php';

use App\Models\Product;
use App\Models\ProductCategory;
use App\Models\ProductReview;
use App\Models\Coupon;
use App\Models\User;
use App\Services\CouponService;

echo "=== Product Review & Coupon System Test ===\n\n";

// Test Product Categories
echo "1. Testing Product Categories:\n";
echo "Creating sample categories...\n";

$electronics = ProductCategory::create([
    'name' => 'Electronics',
    'description' => 'Electronic devices and accessories',
    'is_active' => true,
]);

$smartphones = ProductCategory::create([
    'name' => 'Smartphones',
    'description' => 'Mobile phones and accessories',
    'parent_id' => $electronics->id,
    'is_active' => true,
]);

echo "✓ Created Electronics category with Smartphones subcategory\n";
echo "Category path: " . $smartphones->getBreadcrumb()[0]['name'] . " > " . $smartphones->getBreadcrumb()[1]['name'] . "\n\n";

// Test Product with Category
echo "2. Testing Product with Category:\n";
$product = Product::create([
    'name' => 'iPhone 15 Pro',
    'description' => 'Latest iPhone with advanced features',
    'price' => 99999.00,
    'category_id' => $smartphones->id,
    'status' => 'active',
    'in_stock' => true,
    'stock_quantity' => 50,
]);

echo "✓ Created product: {$product->name}\n";
echo "Category path: " . $product->getCategoryPath() . "\n\n";

// Test Product Reviews
echo "3. Testing Product Reviews:\n";
$user = User::create([
    'name' => 'John Doe',
    'email' => '<EMAIL>',
    'password' => bcrypt('password'),
]);

$review = ProductReview::create([
    'user_id' => $user->id,
    'product_id' => $product->id,
    'rating' => 5,
    'title' => 'Excellent phone!',
    'comment' => 'This phone is amazing. Great camera quality and performance.',
    'is_approved' => true,
    'is_verified_purchase' => true,
]);

echo "✓ Created review for {$product->name}\n";
echo "Rating: {$review->rating}/5\n";
echo "Comment: {$review->comment}\n";

// Test review statistics
$stats = $product->getReviewStats();
echo "Review Stats:\n";
echo "- Average Rating: {$stats['average_rating']}\n";
echo "- Total Reviews: {$stats['total_reviews']}\n";
echo "- Verified Reviews: {$stats['verified_reviews']}\n\n";

// Test Coupons
echo "4. Testing Coupon System:\n";

// Create percentage coupon
$percentageCoupon = Coupon::create([
    'code' => 'SAVE20',
    'name' => '20% Off',
    'description' => '20% discount on all products',
    'type' => 'percentage',
    'value' => 20,
    'minimum_amount' => 1000,
    'maximum_discount' => 5000,
    'is_active' => true,
    'expires_at' => now()->addMonths(3),
]);

// Create fixed coupon
$fixedCoupon = Coupon::create([
    'code' => 'FLAT500',
    'name' => 'Flat ₹500 Off',
    'description' => '₹500 off on orders above ₹2000',
    'type' => 'fixed',
    'value' => 500,
    'minimum_amount' => 2000,
    'is_active' => true,
    'expires_at' => now()->addMonths(6),
]);

echo "✓ Created coupons: SAVE20 (20% off) and FLAT500 (₹500 off)\n\n";

// Test coupon validation
echo "5. Testing Coupon Validation:\n";

$cartItems = [
    [
        'product_id' => $product->id,
        'quantity' => 2,
    ]
];

$couponService = new CouponService();

// Test percentage coupon
$result = Coupon::validateCode('SAVE20', $user->id, [$product->id], 199998); // 2 * 99999
if ($result['valid']) {
    echo "✓ SAVE20 coupon is valid\n";
    echo "Discount: ₹" . number_format($result['discount'], 2) . "\n";
} else {
    echo "✗ SAVE20 coupon validation failed: {$result['message']}\n";
}

// Test fixed coupon (should fail due to minimum amount)
$result = Coupon::validateCode('FLAT500', $user->id, [$product->id], 1500);
if ($result['valid']) {
    echo "✓ FLAT500 coupon is valid\n";
    echo "Discount: ₹" . number_format($result['discount'], 2) . "\n";
} else {
    echo "✗ FLAT500 coupon validation failed: {$result['message']}\n";
}

// Test with sufficient amount
$result = Coupon::validateCode('FLAT500', $user->id, [$product->id], 199998);
if ($result['valid']) {
    echo "✓ FLAT500 coupon is valid for higher amount\n";
    echo "Discount: ₹" . number_format($result['discount'], 2) . "\n";
} else {
    echo "✗ FLAT500 coupon validation failed: {$result['message']}\n";
}

echo "\n6. Testing Category-specific Coupon:\n";

// Create category-specific coupon
$categoryCoupon = Coupon::create([
    'code' => 'ELECTRONICS15',
    'name' => '15% Off Electronics',
    'description' => '15% discount on electronics category',
    'type' => 'percentage',
    'value' => 15,
    'minimum_amount' => 500,
    'applicable_categories' => [$electronics->id],
    'is_active' => true,
    'expires_at' => now()->addMonths(2),
]);

$result = Coupon::validateCode('ELECTRONICS15', $user->id, [$product->id], 99999);
if ($result['valid']) {
    echo "✓ ELECTRONICS15 coupon is valid for electronics product\n";
    echo "Discount: ₹" . number_format($result['discount'], 2) . "\n";
} else {
    echo "✗ ELECTRONICS15 coupon validation failed: {$result['message']}\n";
}

echo "\n7. Testing Review Moderation:\n";

// Create a review that needs moderation
$badReview = ProductReview::create([
    'user_id' => $user->id,
    'product_id' => $product->id,
    'rating' => 1,
    'title' => 'Terrible product',
    'comment' => 'This is spam and fake.',
    'is_approved' => false,
]);

if ($badReview->needsModeration()) {
    echo "✓ Review flagged for moderation (contains flagged words)\n";
} else {
    echo "✗ Review should have been flagged for moderation\n";
}

// Create a good review
$goodReview = ProductReview::create([
    'user_id' => User::create([
        'name' => 'Jane Smith',
        'email' => '<EMAIL>',
        'password' => bcrypt('password'),
    ])->id,
    'product_id' => $product->id,
    'rating' => 4,
    'title' => 'Good product',
    'comment' => 'I really like this phone. The camera is excellent and the battery life is great. Would recommend to others.',
    'is_approved' => false,
]);

$goodReview->autoApprove();
$goodReview->refresh();

if ($goodReview->is_approved) {
    echo "✓ Good review auto-approved\n";
} else {
    echo "✗ Good review should have been auto-approved\n";
}

echo "\n=== Test Complete ===\n";
echo "All systems are working correctly!\n";