<?php

namespace Tests\Unit;

use App\Models\ProductCategory;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class CategoryHierarchyTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function it_can_get_all_children()
    {
        $electronics = ProductCategory::create([
            'name' => 'Electronics',
            'slug' => 'electronics',
            'is_active' => true,
        ]);
        
        $smartphones = ProductCategory::create([
            'name' => 'Smartphones',
            'slug' => 'smartphones',
            'parent_id' => $electronics->id,
            'is_active' => true,
        ]);
        
        $children = $electronics->getAllChildren();
        
        $this->assertCount(1, $children);
        $this->assertEquals($smartphones->id, $children->first()->id);
    }
}
