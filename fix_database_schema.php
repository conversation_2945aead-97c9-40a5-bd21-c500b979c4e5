+++++++++++++++111111111++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++1+01+++<?php

/**
 * Fix Database Schema Script
 * 
 * This script identifies and reports database schema mismatches
 * between models and migrations.
 */

require_once 'vendor/autoload.php';

use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "🔧 Database Schema Analysis\n";
echo "===========================\n\n";

// Check database connection
try {
    DB::connection()->getPdo();
    echo "✅ Database connection successful\n\n";
} catch (Exception $e) {
    echo "❌ Database connection failed: " . $e->getMessage() . "\n";
    exit(1);
}

// Define expected table structures based on what seeders need
$expectedTables = [
    'users' => ['id', 'name', 'email', 'password', 'role', 'email_verified_at', 'created_at', 'updated_at'],
    'components' => ['id', 'name', 'slug', 'description', 'category_id', 'brand', 'model', 'price', 'stock', 'image', 'specs', 'is_featured', 'is_active', 'created_at', 'updated_at'],
    'component_categories' => ['id', 'name', 'slug', 'description', 'icon', 'display_order', 'is_required', 'created_at', 'updated_at'],
    'orders' => ['id', 'user_id', 'order_number', 'status', 'total', 'subtotal', 'tax', 'shipping', 'discount', 'billing_name', 'billing_email', 'billing_phone', 'billing_address', 'billing_city', 'billing_state', 'billing_zipcode', 'billing_country', 'shipping_name', 'shipping_email', 'shipping_phone', 'shipping_address', 'shipping_city', 'shipping_state', 'shipping_zipcode', 'shipping_country', 'notes', 'payment_status', 'created_at', 'updated_at'],
    'order_items' => ['id', 'order_id', 'component_id', 'quantity', 'price', 'name', 'options', 'created_at', 'updated_at'],
    'carts' => ['id', 'user_id', 'session_id', 'total', 'created_at', 'updated_at'],
    'cart_items' => ['id', 'cart_id', 'component_id', 'quantity', 'price', 'created_at', 'updated_at'],
    'transactions' => ['id', 'user_id', 'product_id', 'gateway_name', 'transaction_id', 'gateway_transaction_id', 'amount', 'currency', 'status', 'payment_details', 'webhook_data', 'processed_at', 'failed_at', 'failure_reason', 'created_at', 'updated_at'],
    'coupons' => ['id', 'code', 'name', 'description', 'type', 'value', 'minimum_amount', 'maximum_discount_amount', 'usage_limit', 'usage_limit_per_user', 'used_count', 'is_active', 'starts_at', 'expires_at', 'created_at', 'updated_at'],
    'coupon_usages' => ['id', 'coupon_id', 'user_id', 'order_id', 'discount_amount', 'created_at', 'updated_at'],
];

$issues = [];

foreach ($expectedTables as $tableName => $expectedColumns) {
    echo "🔍 Checking table: {$tableName}\n";
    
    if (!Schema::hasTable($tableName)) {
        echo "   ❌ Table does not exist\n";
        $issues[] = "Table '{$tableName}' does not exist";
        continue;
    }
    
    $actualColumns = Schema::getColumnListing($tableName);
    $missingColumns = array_diff($expectedColumns, $actualColumns);
    $extraColumns = array_diff($actualColumns, $expectedColumns);
    
    if (empty($missingColumns) && empty($extraColumns)) {
        echo "   ✅ Schema matches expectations\n";
    } else {
        if (!empty($missingColumns)) {
            echo "   ⚠️  Missing columns: " . implode(', ', $missingColumns) . "\n";
            $issues[] = "Table '{$tableName}' missing columns: " . implode(', ', $missingColumns);
        }
        
        if (!empty($extraColumns)) {
            echo "   ℹ️  Extra columns: " . implode(', ', $extraColumns) . "\n";
        }
    }
    
    echo "   📊 Total columns: " . count($actualColumns) . "\n\n";
}

// Summary
echo "📋 Summary\n";
echo "==========\n";

if (empty($issues)) {
    echo "✅ All tables have compatible schemas for seeders\n";
} else {
    echo "❌ Found " . count($issues) . " schema issues:\n\n";
    foreach ($issues as $issue) {
        echo "   • {$issue}\n";
    }
    
    echo "\n💡 Recommendations:\n";
    echo "   1. Run migrations: php artisan migrate\n";
    echo "   2. Check if migrations exist for missing tables\n";
    echo "   3. Update model fillable arrays to match actual columns\n";
    echo "   4. Update seeders to only use existing columns\n";
}

echo "\n✨ Analysis completed!\n";