<?php

namespace Tests\Unit\Models;

use App\Models\Product;
use App\Models\ProductCategory;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ProductCategoryTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        $this->artisan('migrate');
    }

    /** @test */
    public function it_can_create_a_category()
    {
        $category = ProductCategory::factory()->create([
            'name' => 'Electronics',
            'description' => 'Electronic devices',
        ]);

        $this->assertDatabaseHas('product_categories', [
            'name' => 'Electronics',
            'description' => 'Electronic devices',
        ]);
    }

    /** @test */
    public function it_automatically_generates_slug()
    {
        $category = ProductCategory::factory()->create([
            'name' => 'Electronics & Gadgets',
            'slug' => null,
        ]);

        $this->assertEquals('electronics-gadgets', $category->slug);
    }

    /** @test */
    public function it_has_parent_child_relationships()
    {
        $parent = ProductCategory::factory()->create(['name' => 'Electronics']);
        $child = ProductCategory::factory()->create([
            'name' => 'Smartphones',
            'parent_id' => $parent->id,
        ]);

        $this->assertEquals($parent->id, $child->parent->id);
        $this->assertCount(1, $parent->children);
        $this->assertEquals($child->id, $parent->children->first()->id);
    }

    /** @test */
    public function it_has_products_relationship()
    {
        $category = ProductCategory::factory()->create();
        Product::factory()->create(['category_id' => $category->id]);
        Product::factory()->create(['category_id' => $category->id]);

        $this->assertCount(2, $category->products);
    }

    /** @test */
    public function it_has_active_scope()
    {
        ProductCategory::factory()->create(['is_active' => true]);
        ProductCategory::factory()->create(['is_active' => false]);

        $activeCategories = ProductCategory::active()->get();

        $this->assertCount(1, $activeCategories);
        $this->assertTrue($activeCategories->first()->is_active);
    }

    /** @test */
    public function it_has_parents_scope()
    {
        $parent = ProductCategory::factory()->create(['parent_id' => null]);
        $child = ProductCategory::factory()->create(['parent_id' => $parent->id]);

        $parentCategories = ProductCategory::parents()->get();

        $this->assertCount(1, $parentCategories);
        $this->assertEquals($parent->id, $parentCategories->first()->id);
    }

    /** @test */
    public function it_has_children_scope()
    {
        $parent = ProductCategory::factory()->create(['parent_id' => null]);
        $child = ProductCategory::factory()->create(['parent_id' => $parent->id]);

        $childCategories = ProductCategory::query()->children()->get();

        $this->assertCount(1, $childCategories);
        $this->assertEquals($child->id, $childCategories->first()->id);
    }

    /** @test */
    public function it_gets_all_children_recursively()
    {
        $grandparent = ProductCategory::factory()->create(['name' => 'Electronics']);
        $parent = ProductCategory::factory()->create([
            'name' => 'Mobile',
            'parent_id' => $grandparent->id,
        ]);
        $child = ProductCategory::factory()->create([
            'name' => 'Smartphones',
            'parent_id' => $parent->id,
        ]);

        $allChildren = $grandparent->getAllChildren();

        $this->assertCount(2, $allChildren);
        $this->assertTrue($allChildren->contains('id', $parent->id));
        $this->assertTrue($allChildren->contains('id', $child->id));
    }

    /** @test */
    public function it_gets_ancestors()
    {
        $grandparent = ProductCategory::factory()->create(['name' => 'Electronics']);
        $parent = ProductCategory::factory()->create([
            'name' => 'Mobile',
            'parent_id' => $grandparent->id,
        ]);
        $child = ProductCategory::factory()->create([
            'name' => 'Smartphones',
            'parent_id' => $parent->id,
        ]);

        $ancestors = $child->getAncestors();

        $this->assertCount(2, $ancestors);
        $this->assertEquals($grandparent->id, $ancestors->first()->id);
        $this->assertEquals($parent->id, $ancestors->last()->id);
    }

    /** @test */
    public function it_generates_breadcrumb()
    {
        $grandparent = ProductCategory::factory()->create(['name' => 'Electronics']);
        $parent = ProductCategory::factory()->create([
            'name' => 'Mobile',
            'parent_id' => $grandparent->id,
        ]);
        $child = ProductCategory::factory()->create([
            'name' => 'Smartphones',
            'parent_id' => $parent->id,
        ]);

        $breadcrumb = $child->getBreadcrumb();

        $this->assertCount(3, $breadcrumb);
        $this->assertEquals('Electronics', $breadcrumb[0]['name']);
        $this->assertEquals('Mobile', $breadcrumb[1]['name']);
        $this->assertEquals('Smartphones', $breadcrumb[2]['name']);
    }

    /** @test */
    public function it_checks_if_has_children()
    {
        $parent = ProductCategory::factory()->create();
        $childless = ProductCategory::factory()->create();
        ProductCategory::factory()->create(['parent_id' => $parent->id]);

        $this->assertTrue($parent->hasChildren());
        $this->assertFalse($childless->hasChildren());
    }

    /** @test */
    public function it_gets_total_product_count_including_subcategories()
    {
        $parent = ProductCategory::factory()->create();
        $child = ProductCategory::factory()->create(['parent_id' => $parent->id]);

        // Create products in parent category
        Product::factory()->count(2)->create([
            'category_id' => $parent->id,
            'status' => 'active',
        ]);

        // Create products in child category
        Product::factory()->count(3)->create([
            'category_id' => $child->id,
            'status' => 'active',
        ]);

        // Create inactive product (shouldn't count)
        Product::factory()->create([
            'category_id' => $parent->id,
            'status' => 'inactive',
        ]);

        $this->assertEquals(5, $parent->getTotalProductCount());
        $this->assertEquals(3, $child->getTotalProductCount());
    }

    /** @test */
    public function it_gets_category_tree()
    {
        $parent1 = ProductCategory::factory()->create([
            'name' => 'Electronics',
            'sort_order' => 1,
            'is_active' => true,
        ]);
        $child1 = ProductCategory::factory()->create([
            'name' => 'Smartphones',
            'parent_id' => $parent1->id,
            'sort_order' => 1,
            'is_active' => true,
        ]);

        $parent2 = ProductCategory::factory()->create([
            'name' => 'Clothing',
            'sort_order' => 2,
            'is_active' => true,
        ]);

        // Inactive parent (shouldn't appear)
        ProductCategory::factory()->create([
            'name' => 'Inactive Category',
            'is_active' => false,
        ]);

        $tree = ProductCategory::getTree();

        $this->assertCount(2, $tree);
        $this->assertEquals('Electronics', $tree[0]['name']);
        $this->assertEquals('Clothing', $tree[1]['name']);
        $this->assertCount(1, $tree[0]['children']);
        $this->assertEquals('Smartphones', $tree[0]['children'][0]['name']);
    }
}