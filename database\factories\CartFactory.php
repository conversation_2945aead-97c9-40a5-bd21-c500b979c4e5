<?php

namespace Database\Factories;

use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Cart>
 */
class CartFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'user_id' => User::factory(),
            'session_id' => $this->faker->uuid(),
            'total' => $this->faker->randomFloat(2, 0, 5000),
        ];
    }

    /**
     * Indicate that the cart belongs to a guest user.
     *
     * @return static
     */
    public function guest()
    {
        return $this->state(function (array $attributes) {
            return [
                'user_id' => null,
                'session_id' => $this->faker->uuid(),
            ];
        });
    }
}