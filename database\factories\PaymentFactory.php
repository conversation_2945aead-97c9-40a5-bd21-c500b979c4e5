<?php

namespace Database\Factories;

use App\Models\Order;
use App\Models\Payment;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Payment>
 */
class PaymentFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'order_id' => Order::factory(),
            'payment_method' => $this->faker->randomElement([
                Payment::METHOD_CREDIT_CARD,
                Payment::METHOD_PAYPAL,
                Payment::METHOD_BANK_TRANSFER,
            ]),
            'transaction_id' => $this->faker->unique()->uuid(),
            'amount' => $this->faker->randomFloat(2, 50, 5000),
            'status' => $this->faker->randomElement([
                Payment::STATUS_PENDING,
                Payment::STATUS_COMPLETED,
                Payment::STATUS_FAILED,
                Payment::STATUS_REFUNDED,
            ]),
            'payment_data' => function (array $attributes) {
                $data = [
                    'processor' => $this->faker->company(),
                    'timestamp' => $this->faker->dateTimeThisMonth()->format('Y-m-d H:i:s'),
                ];
                
                // Add payment method specific data
                if ($attributes['payment_method'] === Payment::METHOD_CREDIT_CARD) {
                    $data['card_type'] = $this->faker->creditCardType();
                    $data['last_four'] = $this->faker->numerify('####');
                    $data['expiry'] = $this->faker->creditCardExpirationDateString();
                } elseif ($attributes['payment_method'] === Payment::METHOD_PAYPAL) {
                    $data['email'] = $this->faker->email();
                    $data['account_id'] = $this->faker->md5();
                } elseif ($attributes['payment_method'] === Payment::METHOD_BANK_TRANSFER) {
                    $data['bank_name'] = $this->faker->company();
                    $data['account_number'] = $this->faker->bankAccountNumber();
                }
                
                return $data;
            },
        ];
    }

    /**
     * Indicate that the payment is completed.
     *
     * @return static
     */
    public function completed()
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => Payment::STATUS_COMPLETED,
            ];
        });
    }

    /**
     * Indicate that the payment has failed.
     *
     * @return static
     */
    public function failed()
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => Payment::STATUS_FAILED,
                'payment_data' => function () {
                    return [
                        'processor' => $this->faker->company(),
                        'timestamp' => $this->faker->dateTimeThisMonth()->format('Y-m-d H:i:s'),
                        'error_code' => $this->faker->randomElement(['PAYMENT_REJECTED', 'INSUFFICIENT_FUNDS', 'CARD_EXPIRED']),
                        'error_message' => $this->faker->sentence()
                    ];
                },
            ];
        });
    }
}