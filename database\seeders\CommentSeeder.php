<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\BlogPost;
use App\Models\Comment;
use App\Models\User;

class CommentSeeder extends Seeder
{
    public function run()
    {
        // First, create some categories if they don't exist
        $categories = [
            'CPU' => 'Central Processing Units',
            'GPU' => 'Graphics Processing Units', 
            'RAM' => 'Random Access Memory',
            'Storage' => 'Storage Devices',
            'Motherboard' => 'Motherboards',
            'PSU' => 'Power Supply Units',
            'Case' => 'PC Cases',
            'Cooling' => 'Cooling Solutions'
        ];

        foreach ($categories as $name => $description) {
            ComponentCategory::firstOrCreate([
                'name' => $name,
                'slug' => Str::slug($name),
                'description' => $description
            ]);
        }

        // Sample components data
        $components = [
            // CPUs
            [
                'name' => 'AMD Ryzen 9 7950X',
                'slug' => 'amd-ryzen-9-7950x',
                'description' => 'High-performance 16-core processor for gaming and productivity',
                'category' => 'CPU',
                'brand' => 'AMD',
                'model' => 'Ryzen 9 7950X',
                'price' => 699.99,
                'stock' => 15,
                'specs' => [
                    'cores' => '16',
                    'threads' => '32',
                    'base_clock' => '4.5 GHz',
                    'boost_clock' => '5.7 GHz',
                    'socket' => 'AM5',
                    'power_consumption' => '170W'
                ],
                'is_featured' => true,
                'is_active' => true
            ],
            [
                'name' => 'Intel Core i7-13700K',
                'slug' => 'intel-core-i7-13700k',
                'description' => 'Powerful 13th gen processor with hybrid architecture',
                'category' => 'CPU',
                'brand' => 'Intel',
                'model' => 'Core i7-13700K',
                'price' => 409.99,
                'stock' => 20,
                'specs' => [
                    'cores' => '16',
                    'threads' => '24',
                    'base_clock' => '3.4 GHz',
                    'boost_clock' => '5.4 GHz',
                    'socket' => 'LGA1700',
                    'power_consumption' => '125W'
                ],
                'is_featured' => false,
                'is_active' => true
            ],

            // GPUs
            [
                'name' => 'NVIDIA GeForce RTX 4090',
                'slug' => 'nvidia-geforce-rtx-4090',
                'description' => 'Ultimate gaming GPU with ray tracing and DLSS 3',
                'category' => 'GPU',
                'brand' => 'NVIDIA',
                'model' => 'GeForce RTX 4090',
                'price' => 1599.99,
                'stock' => 8,
                'specs' => [
                    'memory' => '24GB GDDR6X',
                    'memory_bus' => '384-bit',
                    'boost_clock' => '2520 MHz',
                    'cuda_cores' => '16384',
                    'power_consumption' => '450W',
                    'ports' => '3x DisplayPort 1.4a, 1x HDMI 2.1'
                ],
                'is_featured' => true,
                'is_active' => true
            ],
            [
                'name' => 'AMD Radeon RX 7800 XT',
                'slug' => 'amd-radeon-rx-7800-xt',
                'description' => 'High-performance GPU for 1440p gaming',
                'category' => 'GPU',
                'brand' => 'AMD',
                'model' => 'Radeon RX 7800 XT',
                'price' => 499.99,
                'stock' => 12,
                'specs' => [
                    'memory' => '16GB GDDR6',
                    'memory_bus' => '256-bit',
                    'boost_clock' => '2430 MHz',
                    'stream_processors' => '3840',
                    'power_consumption' => '263W',
                    'ports' => '2x DisplayPort 2.1, 2x HDMI 2.1'
                ],
                'is_featured' => false,
                'is_active' => true
            ],

            // RAM
            [
                'name' => 'Corsair Vengeance LPX 32GB DDR4-3200',
                'slug' => 'corsair-vengeance-lpx-32gb-ddr4-3200',
                'description' => 'High-performance memory kit for gaming and productivity',
                'category' => 'RAM',
                'brand' => 'Corsair',
                'model' => 'Vengeance LPX',
                'price' => 89.99,
                'stock' => 25,
                'specs' => [
                    'capacity' => '32GB (2x16GB)',
                    'type' => 'DDR4',
                    'speed' => '3200MHz',
                    'latency' => 'CL16',
                    'voltage' => '1.35V',
                    'heat_spreader' => 'Yes'
                ],
                'is_featured' => false,
                'is_active' => true
            ],
            [
                'name' => 'G.Skill Trident Z5 32GB DDR5-6000',
                'slug' => 'gskill-trident-z5-32gb-ddr5-6000',
                'description' => 'Premium DDR5 memory for next-gen platforms',
                'category' => 'RAM',
                'brand' => 'G.Skill',
                'model' => 'Trident Z5',
                'price' => 179.99,
                'stock' => 18,
                'specs' => [
                    'capacity' => '32GB (2x16GB)',
                    'type' => 'DDR5',
                    'speed' => '6000MHz',
                    'latency' => 'CL36',
                    'voltage' => '1.35V',
                    'rgb' => 'Yes'
                ],
                'is_featured' => true,
                'is_active' => true
            ],

            // Storage
            [
                'name' => 'Samsung 980 PRO 2TB NVMe SSD',
                'slug' => 'samsung-980-pro-2tb-nvme-ssd',
                'description' => 'High-speed PCIe 4.0 NVMe SSD for gaming and professional work',
                'category' => 'Storage',
                'brand' => 'Samsung',
                'model' => '980 PRO',
                'price' => 149.99,
                'stock' => 30,
                'specs' => [
                    'capacity' => '2TB',
                    'interface' => 'PCIe 4.0 x4',
                    'form_factor' => 'M.2 2280',
                    'read_speed' => '7000 MB/s',
                    'write_speed' => '6900 MB/s',
                    'warranty' => '5 years'
                ],
                'is_featured' => true,
                'is_active' => true
            ],

            // Motherboards
            [
                'name' => 'ASUS ROG STRIX X670E-E',
                'slug' => 'asus-rog-strix-x670e-e',
                'description' => 'Premium AM5 motherboard with PCIe 5.0 and DDR5 support',
                'category' => 'Motherboard',
                'brand' => 'ASUS',
                'model' => 'ROG STRIX X670E-E',
                'price' => 449.99,
                'stock' => 10,
                'specs' => [
                    'socket' => 'AM5',
                    'chipset' => 'X670E',
                    'memory_support' => 'DDR5-5600+',
                    'pcie_slots' => '2x PCIe 5.0 x16',
                    'wifi' => 'Wi-Fi 6E',
                    'ethernet' => '2.5GbE'
                ],
                'is_featured' => false,
                'is_active' => true
            ],

            // PSU
            [
                'name' => 'Seasonic Focus GX-850',
                'slug' => 'seasonic-focus-gx-850',
                'description' => '80+ Gold certified modular power supply',
                'category' => 'PSU',
                'brand' => 'Seasonic',
                'model' => 'Focus GX-850',
                'price' => 139.99,
                'stock' => 15,
                'specs' => [
                    'wattage' => '850W',
                    'certification' => '80+ Gold',
                    'modular' => 'Fully Modular',
                    'fan_size' => '120mm',
                    'warranty' => '10 years',
                    'protection' => 'OVP/UVP/SCP/OCP/OTP'
                ],
                'is_featured' => false,
                'is_active' => true
            ]
        ];

        foreach ($components as $componentData) {
            $category = ComponentCategory::where('name', $componentData['category'])->first();
            
            if ($category) {
                Component::create([
                    'name' => $componentData['name'],
                    'slug' => $componentData['slug'],
                    'description' => $componentData['description'],
                    'category_id' => $category->id,
                    'brand' => $componentData['brand'],
                    'model' => $componentData['model'],
                    'price' => $componentData['price'],
                    'stock' => $componentData['stock'],
                    'specs' => $componentData['specs'],
                    'is_featured' => $componentData['is_featured'],
                    'is_active' => $componentData['is_active']
                ]);
            }
        }

        $this->command->info('Component seeder completed successfully!');
    }
}

// Don't forget to register this seeder in DatabaseSeeder.php:
/*
public function run()
{
    $this->call([
        ComponentSeeder::class,
    ]);
}
*/

// Run with: php artisan db:seed --class=ComponentSeeder