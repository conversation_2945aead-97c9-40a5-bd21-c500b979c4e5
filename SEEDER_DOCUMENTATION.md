# Database Seeders Documentation

This document provides comprehensive information about all database seeders in the PC Builder application.

## Overview

The seeders create realistic sample data for development and testing purposes. They are organized into logical groups and should be run in a specific order to maintain referential integrity.

## Seeder Groups

### 1. Core System Data

#### UserSeeder
- **Purpose**: Creates admin and regular users
- **Data Created**: 
  - 1 admin user (<EMAIL> / password)
  - 9 regular users with random data
- **Dependencies**: None
- **Key Features**: 
  - Different user roles
  - Email verification status
  - Realistic user profiles

#### SettingsSeeder
- **Purpose**: Sets up application configuration
- **Data Created**:
  - Site name and description
  - Logo and favicon placeholders
  - Maintenance mode settings
  - Login configuration
- **Dependencies**: None

#### GatewaySettingsSeeder
- **Purpose**: Configures payment gateways
- **Data Created**:
  - Razorpay configuration (test mode)
  - PayUmoney configuration (test mode)
  - Cashfree configuration (test mode)
- **Dependencies**: None
- **Note**: All gateways are set to test mode with placeholder credentials

### 2. Component & Product Data

#### ComponentCategorySeeder
- **Purpose**: Creates PC component categories
- **Data Created**:
  - CPU, Motherboard, RAM, GPU categories
  - Display order and requirement flags
  - Category icons and descriptions
- **Dependencies**: None

#### ComponentSeeder
- **Purpose**: Creates sample PC components
- **Data Created**:
  - Intel Core i7 CPU
  - ASUS Prime Z590 Motherboard
  - Compatible specifications
  - Pricing and stock information
- **Dependencies**: ComponentCategorySeeder

#### ComponentCompatibilitySeeder
- **Purpose**: Sets up compatibility rules
- **Data Created**:
  - Socket compatibility between CPU and Motherboard
  - Rule types and error messages
- **Dependencies**: ComponentSeeder

#### ProductCategorySeeder
- **Purpose**: Creates product categories (if using products)
- **Data Created**: Hierarchical product categories
- **Dependencies**: None

### 3. Build System

#### BuildSeeder
- **Purpose**: Creates sample PC builds
- **Data Created**:
  - Sample build with components
  - Public/private visibility settings
  - Share tokens
- **Dependencies**: UserSeeder

#### BuildComponentSeeder
- **Purpose**: Links components to builds
- **Data Created**:
  - Build-component relationships
  - Quantities and prices
- **Dependencies**: BuildSeeder, ComponentSeeder

### 4. E-commerce Data

#### CartSeeder
- **Purpose**: Creates shopping carts
- **Data Created**:
  - User carts with 2-4 components each
  - Guest carts (session-based)
  - Calculated totals
- **Dependencies**: UserSeeder, ComponentSeeder

#### OrderSeeder
- **Purpose**: Creates customer orders
- **Data Created**:
  - 1-3 orders per user
  - Complete billing/shipping information
  - Various order statuses
  - Indian addresses and phone numbers
  - Tracking information for shipped orders
- **Dependencies**: UserSeeder, ComponentSeeder, BuildSeeder

#### PaymentSeeder
- **Purpose**: Creates payment records
- **Data Created**:
  - Payments for completed orders
  - Various payment methods
  - Failed payment examples
  - Gateway-specific payment data
- **Dependencies**: OrderSeeder

#### TransactionSeeder
- **Purpose**: Creates payment transactions
- **Data Created**:
  - 2-5 transactions per user
  - Multiple payment gateways
  - Realistic transaction IDs
  - Webhook data simulation
  - Failure reasons for failed transactions
- **Dependencies**: UserSeeder, ProductSeeder (if products exist)

### 5. Coupon System

#### CouponSeeder
- **Purpose**: Creates discount coupons
- **Data Created**:
  - Percentage and fixed amount coupons
  - Usage limits and restrictions
  - Active and expired coupons
  - Minimum order requirements
- **Dependencies**: None (created by existing seeder)

#### CouponUsageSeeder
- **Purpose**: Creates coupon usage records
- **Data Created**:
  - Coupon applications to orders
  - Usage tracking
  - Discount calculations
  - User usage limits
- **Dependencies**: CouponSeeder, OrderSeeder

### 6. Inventory Management

#### PriceHistorySeeder
- **Purpose**: Creates price change history
- **Data Created**:
  - 6 months of price history per component
  - Price variations (±5% to ±15%)
  - Change reasons and sources
  - Market trend data
  - Supplier information
- **Dependencies**: ComponentSeeder

#### StockMovementSeeder
- **Purpose**: Creates inventory movement records
- **Data Created**:
  - Purchase, sale, adjustment movements
  - Initial stock entries
  - Order-based stock reductions
  - Transfer and damage records
  - Supplier and location metadata
- **Dependencies**: ComponentSeeder, ProductSeeder, OrderSeeder

#### InventoryAlertSeeder
- **Purpose**: Creates stock alerts
- **Data Created**:
  - Low stock alerts (threshold: 10)
  - Out of stock alerts
  - Overstock alerts (threshold: 100)
  - Historical resolved alerts
  - Alert metadata and priorities
- **Dependencies**: ComponentSeeder

### 7. Communication Logs

#### EmailLogSeeder
- **Purpose**: Creates email delivery logs
- **Data Created**:
  - Order confirmation emails
  - Status update notifications
  - Build-related emails
  - General system emails
  - Delivery status tracking
  - Error messages for failed emails
- **Dependencies**: OrderSeeder, BuildSeeder, UserSeeder

## Running Seeders

### Method 1: Run All Seeders
```bash
php artisan db:seed
```

### Method 2: Run Individual Seeder
```bash
php artisan db:seed --class=UserSeeder
```

### Method 3: Use Custom Runner Script
```bash
php run_seeders.php
```

The custom runner provides:
- Progress tracking
- Error handling
- Execution time monitoring
- Database statistics
- Detailed feedback

## Data Volumes

After running all seeders, you'll have approximately:
- 10 Users (1 admin + 9 regular)
- 4 Component Categories
- 2+ Components per category
- 5+ Builds
- 15+ Orders
- 50+ Transactions
- 10+ Coupons
- 100+ Price History Records
- 200+ Stock Movements
- 50+ Inventory Alerts
- 100+ Email Logs

## Customization

### Adding More Data
To increase data volume, modify the loop counts in seeders:
```php
// In OrderSeeder.php
$numOrders = rand(1, 5); // Increase max value
```

### Changing Data Types
Modify the arrays in seeders:
```php
// In OrderSeeder.php
private function getRandomCity(): string
{
    $cities = ['Your', 'Custom', 'Cities'];
    return $cities[array_rand($cities)];
}
```

### Regional Customization
Update address, phone, and currency formats in:
- OrderSeeder (addresses, phones)
- TransactionSeeder (currency)
- PaymentSeeder (payment methods)

## Testing Considerations

### Development Environment
- All payment gateways are in test mode
- Email logs simulate delivery without sending
- Stock levels are realistic but not production-ready

### Production Deployment
Before production:
1. Remove or modify seeders
2. Update payment gateway credentials
3. Configure real email settings
4. Set appropriate stock levels

## Troubleshooting

### Common Issues

1. **Column Not Found Errors**
   - Check that model fillable arrays match actual database columns
   - Run `php fix_database_schema.php` to analyze schema mismatches
   - Update seeders to only use existing columns

2. **Foreign Key Constraints**
   - Ensure seeders run in correct order
   - Check DatabaseSeeder.php order

3. **Random Selection Errors**
   - "You requested X items, but there are only Y items available"
   - Fixed in updated seeders with proper bounds checking
   - Run prerequisite seeders first (Users, Components, etc.)

4. **Duplicate Data**
   - Run `php artisan migrate:fresh --seed` for clean slate
   - Or manually truncate tables

5. **Memory Issues**
   - Reduce data volumes in seeders
   - Run seeders individually

6. **Missing Dependencies**
   - Ensure all required models exist
   - Check factory definitions

### Performance Tips

1. Use database transactions for large datasets
2. Disable query logging during seeding
3. Use bulk inserts where possible
4. Run seeders on local environment first

## Security Notes

- Default admin password is 'password' - change in production
- Payment gateway credentials are placeholders
- Email addresses are fake - update for real testing
- All data is for development/testing only

## Contributing

When adding new seeders:
1. Follow existing naming conventions
2. Add proper dependencies
3. Include realistic data
4. Update this documentation
5. Test with fresh database
6. Consider performance impact