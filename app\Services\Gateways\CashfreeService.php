<?php

namespace App\Services\Gateways;

use App\Contracts\PaymentGatewayInterface;
use App\Models\GatewaySetting;
use App\Exceptions\PaymentGatewayException;
use App\Exceptions\GatewayConfigurationException;
use Illuminate\Support\Facades\Http;

class CashfreeService implements PaymentGatewayInterface
{
    private array $config;
    private string $baseUrl;

    public function __construct()
    {
        $this->loadConfiguration();
        $this->baseUrl = $this->config['is_test_mode'] 
            ? 'https://sandbox.cashfree.com/pg'
            : 'https://api.cashfree.com/pg';
    }

    public function createPayment(array $data): array
    {
        try {
            $sessionData = [
                'order_id' => $data['transaction_id'],
                'order_amount' => $data['amount'],
                'order_currency' => $data['currency'],
                'customer_details' => [
                    'customer_id' => $data['user_id'],
                    'customer_name' => auth()->user()->name ?? 'Customer',
                    'customer_email' => auth()->user()->email ?? '',
                    'customer_phone' => auth()->user()->phone ?? '',
                ],
                'order_meta' => [
                    'return_url' => $data['callback_url']
                ]
            ];

            $response = Http::withHeaders([
                'accept' => 'application/json',
                'content-type' => 'application/json',
                'x-api-version' => '2023-08-01',
                'x-client-id' => $this->config['app_id'],
                'x-client-secret' => $this->config['secret_key']
            ])->post($this->baseUrl . '/orders', $sessionData);

            if ($response->successful()) {
                $responseData = $response->json();
                return [
                    'payment_session_id' => $responseData['payment_session_id'],
                    'order_id' => $responseData['order_id'],
                    'environment' => $this->config['is_test_mode'] ? 'sandbox' : 'production'
                ];
            }

            throw new \Exception('Cashfree API error: ' . $response->body());

        } catch (\Exception $e) {
            throw new PaymentGatewayException(
                'Cashfree payment session creation failed',
                'CASHFREE_SESSION_FAILED',
                $e->getMessage()
            );
        }
    }

    public function verifyPayment(string $transactionId, array $data): bool
    {
        try {
            $response = Http::withHeaders([
                'accept' => 'application/json',
                'x-api-version' => '2023-08-01',
                'x-client-id' => $this->config['app_id'],
                'x-client-secret' => $this->config['secret_key']
            ])->get($this->baseUrl . '/orders/' . $transactionId);

            if ($response->successful()) {
                $orderData = $response->json();
                return $orderData['order_status'] === 'PAID';
            }

            return false;

        } catch (\Exception $e) {
            return false;
        }
    }

    public function handleWebhook(array $payload): array
    {
        return [
            'order_id' => $payload['data']['order']['order_id'],
            'status' => $payload['data']['order']['order_status'],
            'amount' => $payload['data']['order']['order_amount']
        ];
    }

    public function getPaymentStatus(string $transactionId): string
    {
        return 'pending';
    }

    private function loadConfiguration(): void
    {
        $setting = GatewaySetting::byGateway('cashfree')->enabled()->first();
        
        if (!$setting) {
            throw new GatewayConfigurationException('Cashfree gateway not configured');
        }

        $this->config = $setting->settings;
    }
}