# Multi-Payment Gateway System Design

## Overview

The multi-payment gateway system provides a unified interface for processing payments through Razorpay, PayUmoney, and Cashfree. The system follows Laravel 11 best practices with a service-oriented architecture, secure credential management, and comprehensive admin controls. The frontend uses Tailwind CSS for responsive design and reusable components.

## Architecture

### High-Level Architecture

```mermaid
graph TB
    A[User Interface] --> B[Payment Controller]
    B --> C[Payment Service]
    C --> D[Gateway Factory]
    D --> E[Razorpay Service]
    D --> F[PayUmoney Service]
    D --> G[Cashfree Service]
    
    H[Webhook Controller] --> I[Webhook Service]
    I --> J[Transaction Service]
    
    K[Admin Controller] --> L[Gateway Settings Service]
    L --> M[Settings Repository]
    
    N[Database Layer]
    E --> N
    F --> N
    G --> N
    J --> N
    M --> N
```

### Service Layer Architecture

The system uses a service-oriented architecture with the following layers:

1. **Presentation Layer**: Controllers and Views
2. **Service Layer**: Business logic and gateway integrations
3. **Repository Layer**: Data access abstraction
4. **Model Layer**: Eloquent models and relationships

## Components and Interfaces

### Core Models

#### Transaction Model
```php
class Transaction extends Model
{
    protected $fillable = [
        'user_id', 'gateway_name', 'amount', 'currency', 'status',
        'transaction_id', 'gateway_transaction_id', 'payment_details',
        'webhook_verified', 'failure_reason'
    ];
    
    protected $casts = [
        'payment_details' => 'array',
        'webhook_verified' => 'boolean'
    ];
}
```

#### GatewaySetting Model
```php
class GatewaySetting extends Model
{
    protected $fillable = [
        'gateway_name', 'is_enabled', 'is_test_mode', 'settings'
    ];
    
    protected $casts = [
        'is_enabled' => 'boolean',
        'is_test_mode' => 'boolean',
        'settings' => 'encrypted:array'
    ];
}
```

### Service Interfaces

#### PaymentGatewayInterface
```php
interface PaymentGatewayInterface
{
    public function createPayment(array $data): array;
    public function verifyPayment(string $transactionId, array $data): bool;
    public function handleWebhook(array $payload): array;
    public function getPaymentStatus(string $transactionId): string;
}
```

#### Gateway Services

##### RazorpayService
- Uses Razorpay PHP SDK
- Handles order creation and payment verification
- Processes webhooks with signature verification
- Supports both test and live modes

##### PayUmoneyService  
- Implements form POST with hash generation
- Handles success/failure callbacks
- Verifies payment responses using hash validation
- Manages test and production endpoints

##### CashfreeService
- Uses Cashfree API v4
- Handles payment session creation
- Processes payment callbacks with signature verification
- Supports webhook notifications

### Gateway Factory Pattern

```php
class PaymentGatewayFactory
{
    public function create(string $gateway): PaymentGatewayInterface
    {
        return match($gateway) {
            'razorpay' => new RazorpayService(),
            'payumoney' => new PayUmoneyService(),
            'cashfree' => new CashfreeService(),
            default => throw new InvalidArgumentException("Unsupported gateway: $gateway")
        };
    }
}
```

## Data Models

### Database Schema

#### transactions table
```sql
CREATE TABLE transactions (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT UNSIGNED NOT NULL,
    gateway_name VARCHAR(50) NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'INR',
    status ENUM('pending', 'processing', 'completed', 'failed', 'cancelled') DEFAULT 'pending',
    transaction_id VARCHAR(100) UNIQUE NOT NULL,
    gateway_transaction_id VARCHAR(100) NULL,
    payment_details JSON NULL,
    webhook_verified BOOLEAN DEFAULT FALSE,
    failure_reason TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_user_id (user_id),
    INDEX idx_gateway_name (gateway_name),
    INDEX idx_status (status),
    INDEX idx_transaction_id (transaction_id),
    INDEX idx_created_at (created_at),
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);
```

#### gateway_settings table
```sql
CREATE TABLE gateway_settings (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    gateway_name VARCHAR(50) UNIQUE NOT NULL,
    is_enabled BOOLEAN DEFAULT TRUE,
    is_test_mode BOOLEAN DEFAULT TRUE,
    settings JSON NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_gateway_name (gateway_name),
    INDEX idx_is_enabled (is_enabled)
);
```

### Model Relationships

```php
// User.php
public function transactions()
{
    return $this->hasMany(Transaction::class);
}

// Transaction.php
public function user()
{
    return $this->belongsTo(User::class);
}
```

## Error Handling

### Exception Hierarchy

```php
abstract class PaymentException extends Exception {}

class PaymentGatewayException extends PaymentException {}
class InvalidPaymentDataException extends PaymentException {}
class WebhookVerificationException extends PaymentException {}
class GatewayConfigurationException extends PaymentException {}
```

### Error Response Format

```php
{
    "success": false,
    "error": {
        "code": "PAYMENT_FAILED",
        "message": "Payment processing failed",
        "details": "Gateway returned error: Insufficient funds"
    }
}
```

### Logging Strategy

- Payment attempts and results
- Webhook processing events
- Gateway API errors
- Security violations (invalid webhooks)
- Configuration changes

## Testing Strategy

### Unit Tests
- Gateway service implementations
- Payment validation logic
- Webhook signature verification
- Model relationships and scopes

### Integration Tests
- End-to-end payment flows
- Webhook processing
- Admin panel functionality
- Gateway switching logic

### Feature Tests
- Payment form submission
- Transaction status updates
- Admin settings management
- User payment history

### Test Data Management
- Use factory classes for test data
- Mock external API calls
- Test with various payment scenarios
- Validate security measures

## Security Considerations

### API Credential Management
- Store credentials encrypted in database
- Use Laravel's encryption for sensitive data
- Separate test and production credentials
- Environment-based configuration fallbacks

### Webhook Security
- Verify webhook signatures for all gateways
- Implement rate limiting on webhook endpoints
- Log all webhook attempts
- Validate payload structure before processing

### Payment Data Protection
- Never store complete card details
- Encrypt sensitive transaction data
- Use HTTPS for all payment communications
- Implement CSRF protection on forms

### Access Control
- Admin-only access to gateway settings
- User-specific transaction viewing
- Role-based permissions for payment management
- Audit logging for admin actions

## Frontend Components

### Tailwind CSS Components

#### Payment Gateway Selector
```html
<div class="grid grid-cols-1 md:grid-cols-3 gap-4">
    <div class="payment-gateway-card">
        <input type="radio" name="gateway" value="razorpay">
        <label class="gateway-label">Razorpay</label>
    </div>
</div>
```

#### Transaction Status Display
```html
<div class="status-card success|error|pending">
    <div class="status-icon"></div>
    <div class="status-message"></div>
    <div class="transaction-details"></div>
</div>
```

### Reusable Blade Components
- `<x-payment-gateway-card>`
- `<x-transaction-status>`
- `<x-payment-form>`
- `<x-admin-gateway-toggle>`

## API Endpoints

### Public Endpoints
- `POST /payment/initiate` - Start payment process
- `GET /payment/status/{transaction}` - Check payment status
- `POST /webhooks/{gateway}` - Handle gateway webhooks

### Admin Endpoints
- `GET /admin/gateways` - List gateway settings
- `PUT /admin/gateways/{gateway}` - Update gateway settings
- `GET /admin/transactions` - List all transactions
- `GET /admin/transactions/{transaction}` - View transaction details

## Configuration Management

### Gateway Configuration Structure
```php
// Razorpay
[
    'key_id' => 'rzp_test_...',
    'key_secret' => 'encrypted_secret',
    'webhook_secret' => 'encrypted_webhook_secret'
]

// PayUmoney
[
    'merchant_key' => 'merchant_key',
    'salt' => 'encrypted_salt',
    'auth_header' => 'encrypted_auth_header'
]

// Cashfree
[
    'app_id' => 'app_id',
    'secret_key' => 'encrypted_secret',
    'client_id' => 'client_id',
    'client_secret' => 'encrypted_client_secret'
]
```

### Environment Variables
```env
PAYMENT_DEFAULT_CURRENCY=INR
PAYMENT_WEBHOOK_TIMEOUT=30
PAYMENT_MAX_RETRY_ATTEMPTS=3
PAYMENT_LOG_LEVEL=info
```

## Performance Considerations

### Caching Strategy
- Cache enabled gateways list
- Cache gateway settings for active sessions
- Use Redis for session-based payment data

### Database Optimization
- Index frequently queried columns
- Partition transactions table by date
- Archive old transaction records
- Use read replicas for reporting

### Queue Processing
- Queue webhook processing
- Async transaction status updates
- Background payment verification
- Batch notification sending

## Monitoring and Analytics

### Metrics to Track
- Payment success rates by gateway
- Average transaction processing time
- Webhook processing failures
- Gateway downtime incidents

### Alerting
- Failed payment thresholds
- Webhook verification failures
- Gateway configuration errors
- Unusual transaction patterns

This design provides a robust, scalable, and secure foundation for the multi-payment gateway system while maintaining Laravel best practices and ensuring excellent user experience.