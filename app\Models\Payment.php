<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Payment extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'order_id',
        'payment_method',
        'transaction_id',
        'amount',
        'status',
        'payment_data',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'amount' => 'decimal:2',
        'payment_data' => 'array',
    ];

    /**
     * The payment statuses.
     */
    const STATUS_PENDING = 'pending';
    const STATUS_COMPLETED = 'completed';
    const STATUS_FAILED = 'failed';
    const STATUS_REFUNDED = 'refunded';

    /**
     * The payment methods.
     */
    const METHOD_CREDIT_CARD = 'credit_card';
    const METHOD_PAYPAL = 'paypal';
    const METHOD_BANK_TRANSFER = 'bank_transfer';

    /**
     * Get the order that owns the payment.
     */
    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    /**
     * Process a payment for an order.
     */
    public static function processPayment(Order $order, array $paymentData)
    {
        // This is a simplified version of payment processing
        // In a real application, this would integrate with a payment gateway
        
        $payment = self::create([
            'order_id' => $order->id,
            'payment_method' => $paymentData['payment_method'],
            'transaction_id' => 'TXN' . time(),
            'amount' => $order->total,
            'status' => self::STATUS_COMPLETED,
            'payment_data' => $paymentData,
        ]);
        
        // Update order status
        $order->status = Order::STATUS_PROCESSING;
        $order->save();
        
        return $payment;
    }
}