<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Validator;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Setting extends Model
{
    use HasFactory;

    protected $fillable = [
        'key',
        'value',
        'type',
        'group',
        'description',
        'is_public',
        'is_encrypted'
    ];

    protected $casts = [
        'is_public' => 'boolean',
        'is_encrypted' => 'boolean'
    ];

    protected $hidden = [
        'is_encrypted'
    ];

    public static function get($key, $default = null)
    {
        return Cache::rememberForever('setting.' . $key, function () use ($key, $default) {
            $setting = static::where('key', $key)->first();
            if (!$setting) {
                return $default;
            }

            if ($setting->is_encrypted) {
                return decrypt($setting->value);
            }

            return $setting->value;
        });
    }

    public static function set($key, $value)
    {
        $setting = static::firstOrNew(['key' => $key]);
        
        if ($setting->is_encrypted) {
            $value = encrypt($value);
        }

        // Ensure boolean values are properly stored
        if ($setting->type === 'boolean') {
            $value = filter_var($value, FILTER_VALIDATE_BOOLEAN);
        }

        $setting->value = $value;
        $setting->save();

        Cache::forget('setting.' . $key);
        Cache::forget('settings.all');
        Cache::forget('settings.public');

        return $setting;
    }

    public static function forget($key)
    {
        static::where('key', $key)->delete();
        Cache::forget('setting.' . $key);
        Cache::forget('settings.all');
        Cache::forget('settings.public');
    }

    public static function getAll()
    {
        return Cache::rememberForever('settings.all', function () {
            return static::all()->mapWithKeys(function ($setting) {
                $value = $setting->is_encrypted ? decrypt($setting->value) : $setting->value;
                return [$setting->key => $value];
            })->toArray();
        });
    }

    public static function getPublicSettings()
    {
        return Cache::rememberForever('settings.public', function () {
            return static::where('is_public', true)
                ->get()
                ->mapWithKeys(function ($setting) {
                    $value = $setting->is_encrypted ? decrypt($setting->value) : $setting->value;
                    return [$setting->key => $value];
                })
                ->toArray();
        });
    }

    public static function validateSetting($key, $value)
    {
        $setting = static::where('key', $key)->first();
        if (!$setting) {
            return true;
        }

        $rules = [
            'text' => 'string|max:255',
            'textarea' => 'string',
            'number' => 'numeric',
            'boolean' => 'boolean',
            'email' => 'email',
            'url' => 'url',
            'json' => 'json',
            'file' => 'file',
            'image' => 'image',
        ];

        if (isset($rules[$setting->type])) {
            $validator = Validator::make(['value' => $value], [
                'value' => $rules[$setting->type]
            ]);

            return $validator->passes();
        }

        return true;
    }

    public static function clearCache()
    {
        // The previous implementation using Cache::tags() will not work unless
        // you use a cache driver that supports tags (e.g., Redis) and all
        // setting items are cached with tags. This implementation works for all drivers.
        Cache::forget('settings.all');
        Cache::forget('settings.public');

        // To ensure all individually cached settings are cleared,
        // we fetch all setting keys and forget them one by one.
        $settings = static::select('key')->get();
        foreach ($settings as $setting) {
            Cache::forget('setting.' . $setting->key);
        }
    }

    /**
     * Alias for clearCache() for backward compatibility.
     */
    public static function flushCache()
    {
        return static::clearCache();
    }

    public function getValueAttribute($value)
    {
        // First handle encryption if needed
        if ($this->is_encrypted && $value) {
            try {
                $value = decrypt($value);
            } catch (\Exception $e) {
                // If decryption fails, return the original value
                return $value;
            }
        }
        
        // Handle different data types based on the setting type
        if ($this->type === 'boolean') {
            return filter_var($value, FILTER_VALIDATE_BOOLEAN);
        } elseif ($this->type === 'json' && $value) {
            try {
                return json_decode($value, true);
            } catch (\Exception $e) {
                return $value;
            }
        }
        
        // Return as is for text, textarea and other types
        return $value;
    }

    public function setValueAttribute($value)
    {
        // Handle different data types based on the setting type
        if ($this->type === 'json' && !is_string($value)) {
            $value = json_encode($value);
        } elseif ($this->type === 'boolean') {
            $value = filter_var($value, FILTER_VALIDATE_BOOLEAN) ? '1' : '0';
        }
        
        // Then handle encryption if needed
        if ($this->is_encrypted && $value) {
            $this->attributes['value'] = encrypt($value);
        } else {
            $this->attributes['value'] = $value;
        }
    }
}
