<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Component;
use App\Models\Order;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class AnalyticsController extends Controller
{
    /**
     * Get sales analytics data.
     */
    public function sales(Request $request)
    {
        $period = $request->get('period', 'last_30_days');
        
        $dateFrom = match($period) {
            'last_7_days' => now()->subDays(7),
            'last_30_days' => now()->subDays(30),
            'last_90_days' => now()->subDays(90),
            'last_year' => now()->subYear(),
            default => now()->subDays(30)
        };

        $orders = Order::where('created_at', '>=', $dateFrom)
            ->where('status', '!=', 'canceled')
            ->get();

        $totalSales = $orders->sum('total');
        $orderCount = $orders->count();
        $averageOrderValue = $orderCount > 0 ? $totalSales / $orderCount : 0;

        $dailySales = Order::where('created_at', '>=', $dateFrom)
            ->where('status', '!=', 'canceled')
            ->select(DB::raw('DATE(created_at) as date'), DB::raw('SUM(total) as total'))
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        $topSellingComponents = Component::withCount('orderItems')
            ->orderBy('order_items_count', 'desc')
            ->limit(10)
            ->get();

        return response()->json([
            'total_sales' => $totalSales,
            'order_count' => $orderCount,
            'average_order_value' => round($averageOrderValue, 2),
            'daily_sales' => $dailySales,
            'top_selling_components' => $topSellingComponents
        ]);
    }

    /**
     * Get inventory analytics data.
     */
    public function inventory()
    {
        $totalComponents = Component::count();
        $lowStockCount = Component::where('stock', '<=', 5)->count();
        $outOfStockCount = Component::where('stock', 0)->count();
        
        $inventoryValue = Component::where('is_active', true)
            ->sum(DB::raw('price * stock'));

        $categoryBreakdown = Component::join('component_categories', 'components.category_id', '=', 'component_categories.id')
            ->select('component_categories.name', DB::raw('COUNT(*) as count'), DB::raw('SUM(components.stock) as total_stock'))
            ->groupBy('component_categories.id', 'component_categories.name')
            ->get();

        return response()->json([
            'total_components' => $totalComponents,
            'low_stock_count' => $lowStockCount,
            'out_of_stock_count' => $outOfStockCount,
            'inventory_value' => $inventoryValue,
            'category_breakdown' => $categoryBreakdown
        ]);
    }

    /**
     * Get customer analytics data.
     */
    public function customers()
    {
        $totalCustomers = User::where('role', 'user')->count();
        $newCustomersThisMonth = User::where('role', 'user')
            ->where('created_at', '>=', now()->startOfMonth())
            ->count();

        $customerLifetimeValue = Order::where('status', '!=', 'canceled')
            ->avg('total');

        $topCustomers = User::withSum('orders', 'total')
            ->orderBy('orders_sum_total', 'desc')
            ->limit(10)
            ->get();

        return response()->json([
            'total_customers' => $totalCustomers,
            'new_customers_this_month' => $newCustomersThisMonth,
            'customer_lifetime_value' => round($customerLifetimeValue ?? 0, 2),
            'top_customers' => $topCustomers
        ]);
    }
}