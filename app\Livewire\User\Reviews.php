<?php

namespace App\Livewire\User;

use Livewire\Component;
use Illuminate\Support\Facades\Auth;
use App\Models\Review;

class Reviews extends Component
{
    public $reviews;
    public $editingReview = null;
    public $editContent = '';

    public function mount()
    {
        $this->reviews = Review::where('user_id', Auth::id())->orderByDesc('created_at')->get();
    }

    public function startEdit($reviewId)
    {
        $review = $this->reviews->where('id', $reviewId)->first();
        if ($review) {
            $this->editingReview = $review;
            $this->editContent = $review->content;
        }
    }

    public function saveEdit()
    {
        if ($this->editingReview) {
            $this->validate(['editContent' => 'required|string|min:5']);
            $this->editingReview->content = $this->editContent;
            $this->editingReview->save();
            $this->editingReview = null;
            $this->editContent = '';
            $this->mount(); // reload reviews
        }
    }

    public function cancelEdit()
    {
        $this->editingReview = null;
        $this->editContent = '';
    }

    public function deleteReview($reviewId)
    {
        $review = $this->reviews->where('id', $reviewId)->first();
        if ($review) {
            $review->delete();
            $this->mount(); // reload reviews
        }
    }

    public function render()
    {
        return view('livewire.user.reviews');
    }
} 