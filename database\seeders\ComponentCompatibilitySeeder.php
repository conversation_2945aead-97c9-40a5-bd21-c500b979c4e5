<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Component;
use App\Models\ComponentCompatibility;
use App\Models\ComponentCategory;

class ComponentCompatibilitySeeder extends Seeder
{
    public function run()
    {
        $cpu = Component::where('slug', 'intel-core-i7')->first();
        $mb = Component::where('slug', 'asus-prime-z590')->first();
        $cpuCat = ComponentCategory::where('slug', 'cpu')->first();
        $mbCat = ComponentCategory::where('slug', 'motherboard')->first();

        // Compatible if socket matches
        ComponentCompatibility::create([
            'component_id' => $cpu->id,
            'compatible_component_id' => $mb->id,
            'category_id' => $cpuCat->id,
            'compatible_category_id' => $mbCat->id,
            'rule_type' => ComponentCompatibility::RULE_TYPE_SPEC_MATCH,
            'rule_value' => json_encode(['key' => 'socket', 'operator' => '=']),
            'error_message' => 'CPU and Motherboard sockets do not match.',
        ]);
    }
}
