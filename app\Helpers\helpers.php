<?php

use App\Models\Setting;
use App\Models\State;
use Carbon\Carbon;

use Artesaos\SEOTools\Facades\SEOTools as SEO;



function setSEO($title, $description, $keywords, $imgPath)
{
    SEO::setTitle($title);
    SEO::setDescription($description);
    SEO::setCanonical(url()->current());
    SEO::metatags()->addMeta('keywords', $keywords, 'name');
    SEO::opengraph()->setUrl(url()->current());
    SEO::opengraph()->addProperty('type', 'article');
    SEO::opengraph()->setTitle($title);
    SEO::opengraph()->setDescription($description);
    SEO::opengraph()->addImage(asset($imgPath));
    SEO::twitter()->setSite('@digi_nsk');
    SEO::twitter()->setTitle($title);
    SEO::twitter()->setDescription($description);
    SEO::twitter()->setImage(asset($imgPath));
}

function getDynamicPath()
{
    $baseDir = config('defaultimg.default_upload_path');
    $currentYear = date('Y');
    $currentMonth = date('m');

    $fullDirectoryPath = "$baseDir/$currentYear/$currentMonth";

    if (!is_dir($fullDirectoryPath)) {
        if (@mkdir($fullDirectoryPath, 0755, true)) {
            return $fullDirectoryPath;
        }
        return "$baseDir/misc";
    }

    return $fullDirectoryPath;
}

function getYearMonthAsSlug()
{
    $currentYear = date('Y');
    $currentMonth = date('m');

    $fullDirectoryPath = "/$currentYear/$currentMonth/";

    return $fullDirectoryPath;
}



if (!function_exists('uploads_url')) {
    function uploads_url($path = '')
    {
        return url('wp-content/uploads/' . $path);
    }
}

if (!function_exists('generateSlug')) {
    function generateSlug(string $text): string
    {
        // Convert text to lowercase
        $text = mb_strtolower($text, 'UTF-8');

        // Remove special characters but keep Devanagari and Latin characters intact
        $text = preg_replace('/[^\p{Devanagari}\p{Latin}\d\s-]/u', '', $text);

        // Replace spaces with hyphens
        $text = preg_replace('/[\s]+/u', '-', trim($text));

        // Remove multiple hyphens
        $text = preg_replace('/-+/', '-', $text);

        return $text;
    }
}


if (!function_exists('capitalizeLetters')) {
    function capitalizeLetters($string)
    {
        return preg_replace_callback('/[a-z]/', function ($match) {
            return strtoupper($match[0]);
        }, $string);
    }
}

if (!function_exists('get_setting')) {
    function get_setting(string $key, $default = null)
    {
        return Setting::get($key, $default);
    }
}


if (!function_exists('typography')) {
    function typography($key, $default = null)
    {
        $prefix = 'typography_';
        return Setting::get($prefix . $key, $default);
    }
}

function can_use_cookies($type = 'essential')
{
    $level = config('cookie-consent.level', 'none');

    if ($level === 'all')
        return true;
    if ($level === 'essential' && $type === 'essential')
        return true;
    return false;
}

if (!function_exists('is_maintenance_mode')) {
    function is_maintenance_mode(): bool
    {
        return (bool) get_setting('maintenance_mode', false);
    }
}
