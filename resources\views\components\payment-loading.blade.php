@props([
    'show' => false,
    'message' => 'Processing Payment',
    'description' => 'Please wait while we process your payment...',
    'overlay' => true
])

<div 
    id="payment-loading" 
    class="payment-loading {{ $show ? '' : 'hidden' }} {{ $overlay ? 'fixed inset-0 bg-black bg-opacity-50 z-50' : '' }} flex items-center justify-center"
    {{ $attributes }}
>
    <div class="bg-white rounded-lg p-6 max-w-sm mx-4 shadow-xl">
        <div class="flex items-center space-x-4">
            <!-- Animated spinner -->
            <div class="flex-shrink-0">
                <svg class="animate-spin h-8 w-8 text-indigo-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
            </div>
            
            <div class="flex-1">
                <p class="font-semibold text-gray-900 text-lg">{{ $message }}</p>
                @if($description)
                    <p class="text-sm text-gray-600 mt-1">{{ $description }}</p>
                @endif
            </div>
        </div>
        
        <!-- Progress bar -->
        <div class="mt-4">
            <div class="bg-gray-200 rounded-full h-2">
                <div class="bg-indigo-600 h-2 rounded-full animate-pulse" style="width: 60%"></div>
            </div>
        </div>
        
        <!-- Security notice -->
        <div class="mt-4 flex items-center justify-center text-xs text-gray-500">
            <svg class="w-3 h-3 mr-1 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <span>Secure SSL encrypted connection</span>
        </div>
    </div>
</div>

<script>
// Payment loading utility functions
window.PaymentLoading = {
    show: function(message = 'Processing Payment', description = 'Please wait while we process your payment...') {
        const loadingElement = document.getElementById('payment-loading');
        if (loadingElement) {
            const messageElement = loadingElement.querySelector('.font-semibold');
            const descriptionElement = loadingElement.querySelector('.text-sm.text-gray-600');
            
            if (messageElement) messageElement.textContent = message;
            if (descriptionElement) descriptionElement.textContent = description;
            
            loadingElement.classList.remove('hidden');
            document.body.style.overflow = 'hidden'; // Prevent scrolling
        }
    },
    
    hide: function() {
        const loadingElement = document.getElementById('payment-loading');
        if (loadingElement) {
            loadingElement.classList.add('hidden');
            document.body.style.overflow = ''; // Restore scrolling
        }
    },
    
    updateMessage: function(message, description = null) {
        const loadingElement = document.getElementById('payment-loading');
        if (loadingElement) {
            const messageElement = loadingElement.querySelector('.font-semibold');
            const descriptionElement = loadingElement.querySelector('.text-sm.text-gray-600');
            
            if (messageElement) messageElement.textContent = message;
            if (description && descriptionElement) descriptionElement.textContent = description;
        }
    }
};

// Auto-hide loading on page unload
window.addEventListener('beforeunload', function() {
    PaymentLoading.hide();
});
</script>

<style>
.payment-loading {
    backdrop-filter: blur(2px);
}

.payment-loading .animate-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: .5;
    }
}
</style>