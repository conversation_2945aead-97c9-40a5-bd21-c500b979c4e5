<?php

namespace App\Livewire\Admin;

use Livewire\Component;
use App\Models\Order;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use App\Models\User;

class Notifications extends Component
{
    public $notifications = [];
    public $dismissed = [];
    protected $pollingInterval = 10000; // 10 seconds

    public function mount()
    {
        $this->loadNotifications();
    }

    public function loadNotifications()
    {
        $this->notifications = [];
        // Recent orders
        $recentOrders = Order::orderByDesc('created_at')->take(10)->get();
        foreach ($recentOrders as $order) {
            $this->notifications[] = [
                'id' => 'order-' . $order->id,
                'type' => 'order',
                'message' => "New order #{$order->order_number} ({$order->status})",
                'date' => $order->created_at,
            ];
        }
        // Failed jobs
        $failedJobs = DB::table('failed_jobs')->orderByDesc('failed_at')->take(10)->get();
        foreach ($failedJobs as $job) {
            $this->notifications[] = [
                'id' => 'failedjob-' . $job->id,
                'type' => 'failed_job',
                'message' => "Job failed: {$job->exception}",
                'date' => $job->failed_at,
            ];
        }
        // Low stock components
        $lowStock = DB::table('components')->where('stock_quantity', '<', 5)->orderBy('stock_quantity')->take(10)->get();
        foreach ($lowStock as $component) {
            $this->notifications[] = [
                'id' => 'lowstock-' . $component->id,
                'type' => 'low_stock',
                'message' => "Low stock: {$component->name} ({$component->stock_quantity} left)",
                'date' => $component->updated_at,
            ];
        }
        // New user registrations
        $newUsers = User::orderByDesc('created_at')->take(10)->get();
        foreach ($newUsers as $user) {
            $this->notifications[] = [
                'id' => 'user-' . $user->id,
                'type' => 'new_user',
                'message' => "New user registered: {$user->name} ({$user->email})",
                'date' => $user->created_at,
            ];
        }
        // Remove dismissed notifications
        $this->notifications = array_values(array_filter($this->notifications, function($n) {
            return !in_array($n['id'], $this->dismissed);
        }));
        usort($this->notifications, function($a, $b) {
            return strtotime($b['date']) <=> strtotime($a['date']);
        });
    }

    public function dismiss($id)
    {
        $this->dismissed[] = $id;
        $this->loadNotifications();
    }

    public function render()
    {
        return view('livewire.admin.notifications');
    }

    public function getListeners()
    {
        return [
            'refreshNotifications' => 'loadNotifications',
        ];
    }
} 