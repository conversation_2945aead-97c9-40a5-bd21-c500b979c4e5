<?php

namespace App\Http\Middleware;

use Closure;
use App\Services\ScriptActivationService;
use Illuminate\Support\Facades\Cache;
use Carbon\Carbon;

class CheckScriptActivation
{
    private $activationService;

    public function __construct(ScriptActivationService $activationService)
    {
        $this->activationService = $activationService;
    }

    public function handle($request, Closure $next)
    {
        if ($this->shouldSkipValidation($request)) {
            return $next($request);
        }

        $licenseKey = config('script-activation.license_key');
        
        // Check if we need to verify again based on frequency
        $lastCheck = Cache::get('license_last_check');
        $frequency = config('script-activation.verification_frequency', 24);
        
        if (!$lastCheck || Carbon::parse($lastCheck)->diffInHours(now()) >= $frequency) {
            $validationResult = $this->activationService->validateLicense($licenseKey);
            Cache::put('license_last_check', now(), Carbon::now()->addHours($frequency));
            
            if ($validationResult['status'] !== 'active') {
                if ($request->expectsJson()) {
                    return response()->json([
                        'error' => 'License validation failed',
                        'message' => $validationResult['message'] ?? 'Invalid or inactive license'
                    ], 403);
                }
                
                return redirect()->route('installer.license');
            }
        }

        return $next($request);
    }

    private function shouldSkipValidation($request)
    {
        $skipPaths = [
            'install/*',
            'installer/*',
            'api/v1/license/*',
            'assets/*',
            'css/*',
            'js/*',
        ];

        foreach ($skipPaths as $path) {
            if ($request->is($path)) {
                return true;
            }
        }

        return false;
    }
}
