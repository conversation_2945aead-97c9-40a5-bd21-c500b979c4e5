<?php

namespace Tests\Browser\Components;

use <PERSON><PERSON>\Dusk\Browser;
use <PERSON><PERSON>\Dusk\Component as BaseComponent;

class AdminSidebar extends BaseComponent
{
    /**
     * Get the root selector for the component.
     */
    public function selector(): string
    {
        return '.admin-sidebar';
    }

    /**
     * Assert that the browser page contains the component.
     */
    public function assert(Browser $browser): void
    {
        $browser->assertPresent($this->selector());
    }

    /**
     * Get the element shortcuts for the component.
     */
    public function elements(): array
    {
        return [
            '@dashboard' => 'a[href*="/admin/dashboard"]',
            '@transactions' => 'a[href*="/admin/transactions"]',
            '@gateways' => 'a[href*="/admin/gateways"]',
            '@products' => 'a[href*="/admin/products"]',
            '@users' => 'a[href*="/admin/users"]',
            '@settings' => 'a[href*="/admin/settings"]',
            '@reports' => 'a[href*="/admin/reports"]',
            '@activeLink' => '.nav-link.active',
            '@collapseToggle' => '.sidebar-toggle',
        ];
    }

    /**
     * Navigate to a specific admin section.
     */
    public function navigateTo(Browser $browser, string $section): void
    {
        $browser->click("@{$section}")
                ->waitForLocation("/admin/{$section}")
                ->assertPathIs("/admin/{$section}");
    }

    /**
     * Assert that a specific section is active.
     */
    public function assertSectionActive(Browser $browser, string $section): void
    {
        $browser->assertPresent("a[href*=\"/admin/{$section}\"].active");
    }

    /**
     * Toggle sidebar collapse.
     */
    public function toggleCollapse(Browser $browser): void
    {
        $browser->click('@collapseToggle');
    }
}