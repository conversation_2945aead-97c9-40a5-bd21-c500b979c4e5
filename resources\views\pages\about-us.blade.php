@extends('layouts.app')

@section('meta')
    <meta name="description" content="{{ $pageData['about']['content']['meta_description'] ?? 'Learn about our company, mission, vision, and the team behind our success. Discover how we\'re making a difference in the industry.' }}">
    <meta name="keywords" content="{{ $pageData['about']['content']['meta_keywords'] ?? 'about us, company profile, our mission, our vision, team members' }}">
    <meta property="og:title" content="{{ $pageData['about']['content']['heading'] ?? 'About Us' }}">
    <meta property="og:description" content="{{ $pageData['about']['content']['meta_description'] ?? 'Learn about our company, mission, vision, and the team behind our success.' }}">
    <meta property="og:type" content="website">
@endsection

@section('content')
    <!-- Hero Section -->
    <section
        class="relative py-24 md:py-32 overflow-hidden bg-gradient-to-br from-slate-50 to-slate-100 dark:from-gray-900 dark:to-gray-800"
        aria-labelledby="hero-heading">
        <div
            class="absolute inset-0 bg-grid-slate-100 dark:bg-grid-slate-800/25 [mask-image:linear-gradient(to_bottom,white,transparent)] opacity-20 animate-pulse">
        </div>
        <div class="absolute inset-0 bg-gradient-to-r from-primary/10 to-secondary/10 backdrop-blur-[1px]"></div>
        <div class="container relative z-10 mx-auto px-4 sm:px-6">
            <div class="max-w-3xl mx-auto text-center">
                <span
                    class="inline-flex items-center px-4 py-1.5 rounded-full text-sm font-medium bg-primary/10 text-primary dark:bg-primary/20 mb-6 animate-fade-in backdrop-blur-sm shadow-sm"
                    role="text">
                    {{ $pageData['about']['content']['badge_text'] ?? 'Our Story' }}
                </span>
                <h1
                    id="hero-heading"
                    class="text-4xl md:text-5xl lg:text-6xl font-extrabold tracking-tight mb-8 bg-clip-text text-transparent bg-gradient-to-r from-gray-900 via-primary/90 to-gray-700 dark:from-white dark:via-primary/80 dark:to-gray-300 leading-tight">
                    {{ $pageData['about']['content']['heading'] ?? 'About Us' }}
                </h1>
                <p class="text-lg md:text-xl text-gray-600 dark:text-gray-300 max-w-2xl mx-auto mb-10 leading-relaxed">
                    {{ $pageData['about']['content']['subheading'] ?? 'We\'re a team of passionate individuals dedicated to creating innovative solutions that make a difference.' }}
                </p>
                <a href="{{ $pageData['about']['content']['cta_link'] ?? '#' }}"
                    class="inline-flex items-center justify-center px-7 py-4 rounded-lg bg-primary hover:bg-primary/90 text-white font-medium transition-all duration-300 shadow-lg hover:shadow-primary/30 transform hover:-translate-y-1 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2"
                    aria-label="{{ $pageData['about']['content']['cta_text'] ?? 'Get Started' }}">
                    {{ $pageData['about']['content']['cta_text'] ?? 'Get Started' }}
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-2 animate-pulse" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                        <path fill-rule="evenodd"
                            d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z"
                            clip-rule="evenodd" />
                    </svg>
                </a>
            </div>
        </div>
    </section>

    <!-- Company Overview -->
    <section class="py-24 md:py-32" aria-labelledby="company-overview-heading">
        <div class="container mx-auto px-4 sm:px-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-16 lg:gap-24 items-center">
                <div class="space-y-8" data-aos="fade-right">
                    <span
                        class="inline-flex items-center px-4 py-1.5 rounded-full text-sm font-medium bg-primary/10 text-primary dark:bg-primary/20 shadow-sm"
                        role="text">
                        Our Journey
                    </span>
                    <h2 id="company-overview-heading" class="text-3xl md:text-4xl font-bold tracking-tight text-gray-900 dark:text-white leading-tight">
                        {{ $pageData['about']['content']['story_heading'] ?? 'Our Story' }}
                    </h2>
                    <div class="prose prose-lg dark:prose-invert max-w-none text-gray-600 dark:text-gray-300 leading-relaxed">
                        {!! nl2br(
                            e(
                                $pageData['about']['content']['content'] ??
                                    'Founded in 2015, our company has grown from a small startup to a leading provider in our industry. We believe in innovation, quality, and customer satisfaction.
                                                                    
                                                                    Our journey began with a simple idea: to create products that solve real problems. Today, we continue to build on that foundation, constantly improving and expanding our offerings.',
                            ),
                        ) !!}
                    </div>
                </div>
                <div class="relative group" data-aos="fade-left" data-aos-delay="200">
                    <div class="absolute -top-8 -left-8 w-40 h-40 bg-primary/10 rounded-2xl -z-10 blur-xl opacity-70 group-hover:opacity-90 transition-all duration-700"></div>
                    <div class="relative rounded-2xl overflow-hidden shadow-2xl transform transition-transform duration-500 group-hover:scale-[1.02] group-hover:shadow-primary/20">
                        <img src="{{ isset($pageData['about']['content']['image']) ? uploads_url(is_array($pageData['about']['content']['image']) ? $pageData['about']['content']['image'][0] : $pageData['about']['content']['image']) : asset('images/about/company-image.jpg') }}"
                            alt="{{ $pageData['about']['content']['image_alt'] ?? 'Our Company Office' }}"
                            class="w-full h-auto object-cover aspect-[4/3] transition-all duration-700 group-hover:brightness-105"
                            loading="lazy"
                            width="800"
                            height="600">
                    </div>
                    <div class="absolute -bottom-8 -right-8 w-40 h-40 bg-secondary/10 rounded-2xl -z-10 blur-xl opacity-70 group-hover:opacity-90 transition-all duration-700">
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Mission & Vision -->
    <section class="py-20 md:py-28 bg-gray-50 dark:bg-gray-900 relative overflow-hidden" aria-labelledby="mission-vision-heading">
        <div
            class="absolute inset-0 bg-grid-slate-100 dark:bg-grid-slate-800/25 [mask-image:linear-gradient(to_bottom,white,transparent)] opacity-20">
        </div>
        <div class="container mx-auto px-4 sm:px-6 relative z-10">
            <div class="text-center mb-16" data-aos="fade-up">
                <span
                    class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-secondary/10 text-secondary dark:bg-secondary/20 mb-4"
                    role="text">
                    What Drives Us
                </span>
                <h2 id="mission-vision-heading" class="text-3xl md:text-4xl font-bold tracking-tight text-gray-900 dark:text-white">
                    {{ $pageData['about']['content']['mission_vision_heading'] ?? 'Our Mission & Vision' }}
                </h2>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-8 lg:gap-12">
                <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-xl hover:shadow-2xl transition-all duration-300 group"
                    data-aos="fade-up" data-aos-delay="100" role="article">
                    <div class="p-8 md:p-10">
                        <div
                            class="w-16 h-16 flex items-center justify-center rounded-xl bg-primary/10 text-primary mb-6 group-hover:bg-primary group-hover:text-white transition-all duration-300"
                            aria-hidden="true">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                                stroke="currentColor" class="w-8 h-8">
                                <path stroke-linecap="round" stroke-linejoin="round"
                                    d="M3.75 13.5l10.5-11.25L12 10.5h8.25L9.75 21.75 12 13.5H3.75z" />
                            </svg>
                        </div>
                        <h3 class="text-2xl font-bold mb-4 text-gray-900 dark:text-white">
                            {{ $pageData['about']['content']['mission_heading'] ?? 'Our Mission' }}
                        </h3>
                        <p class="text-lg text-gray-600 dark:text-gray-300">
                            {{ $pageData['about']['content']['mission'] ?? 'To deliver exceptional products and services that improve people\'s lives while maintaining the highest standards of integrity and innovation.' }}
                        </p>
                    </div>
                </div>
                <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-xl hover:shadow-2xl transition-all duration-300 group"
                    data-aos="fade-up" data-aos-delay="200" role="article">
                    <div class="p-8 md:p-10">
                        <div
                            class="w-16 h-16 flex items-center justify-center rounded-xl bg-secondary/10 text-secondary mb-6 group-hover:bg-secondary group-hover:text-white transition-all duration-300"
                            aria-hidden="true">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                                stroke="currentColor" class="w-8 h-8">
                                <path stroke-linecap="round" stroke-linejoin="round"
                                    d="M2.036 12.322a1.012 1.012 0 010-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178z" />
                                <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                            </svg>
                        </div>
                        <h3 class="text-2xl font-bold mb-4 text-gray-900 dark:text-white">
                            {{ $pageData['about']['content']['vision_heading'] ?? 'Our Vision' }}
                        </h3>
                        <p class="text-lg text-gray-600 dark:text-gray-300">
                            {{ $pageData['about']['content']['vision'] ?? 'To be recognized globally as a leader in our industry, setting new standards for excellence and creating sustainable value for our customers and communities.' }}
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Core Values -->
    <section class="py-20 md:py-28">
        <div class="container mx-auto px-4 sm:px-6">
            <div class="text-center mb-16" data-aos="fade-up">
                <span
                    class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-primary/10 text-primary dark:bg-primary/20 mb-4">
                    Our Principles
                </span>
                <h2 class="text-3xl md:text-4xl font-bold tracking-tight text-gray-900 dark:text-white mb-4">
                    {{ $pageData['about']['content']['values_heading'] ?? 'Our Core Values' }}
                </h2>
                <p class="mt-4 max-w-2xl mx-auto text-lg text-gray-600 dark:text-gray-300">
                    {{ $pageData['about']['content']['values_subheading'] ?? 'These principles guide everything we do' }}
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                @if (isset($pageData['about']['content']['values']) && is_array($pageData['about']['content']['values']))
                    @foreach ($pageData['about']['content']['values'] as $key => $value)
                        <div class="bg-white dark:bg-gray-800 rounded-xl p-8 hover:shadow-xl transition-all duration-300 border border-gray-100 dark:border-gray-700 group"
                            data-aos="fade-up" data-aos-delay="{{ $key * 100 }}">
                            <div
                                class="w-20 h-20 flex items-center justify-center rounded-xl bg-gradient-to-br from-primary/10 to-secondary/10 text-primary mb-6 group-hover:from-primary group-hover:to-secondary group-hover:text-white transition-all duration-300">
                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                                    stroke-width="1.5" stroke="currentColor" class="w-10 h-10">
                                    <path stroke-linecap="round" stroke-linejoin="round"
                                        d="{{ $value['icon_path'] ?? 'M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z' }}" />
                                </svg>
                            </div>
                            <h3 class="text-2xl font-bold mb-3 text-gray-900 dark:text-white">{{ $value['title'] }}</h3>
                            <p class="text-lg text-gray-600 dark:text-gray-300">{{ $value['description'] }}</p>
                        </div>
                    @endforeach
                @else
                    <!-- Default values if not provided -->
                    <div class="bg-white dark:bg-gray-800 rounded-xl p-8 hover:shadow-xl transition-all duration-300 border border-gray-100 dark:border-gray-700 group"
                        data-aos="fade-up" data-aos-delay="0">
                        <div
                            class="w-20 h-20 flex items-center justify-center rounded-xl bg-gradient-to-br from-primary/10 to-secondary/10 text-primary mb-6 group-hover:from-primary group-hover:to-secondary group-hover:text-white transition-all duration-300">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                                stroke="currentColor" class="w-10 h-10">
                                <path stroke-linecap="round" stroke-linejoin="round"
                                    d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                        <h3 class="text-2xl font-bold mb-3 text-gray-900 dark:text-white">Innovation</h3>
                        <p class="text-lg text-gray-600 dark:text-gray-300">We embrace new ideas and technologies to
                            continuously improve our products and services.</p>
                    </div>
                    <div class="bg-white dark:bg-gray-800 rounded-xl p-8 hover:shadow-xl transition-all duration-300 border border-gray-100 dark:border-gray-700 group"
                        data-aos="fade-up" data-aos-delay="100">
                        <div
                            class="w-20 h-20 flex items-center justify-center rounded-xl bg-gradient-to-br from-primary/10 to-secondary/10 text-primary mb-6 group-hover:from-primary group-hover:to-secondary group-hover:text-white transition-all duration-300">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                                stroke-width="1.5" stroke="currentColor" class="w-10 h-10">
                                <path stroke-linecap="round" stroke-linejoin="round"
                                    d="M15.75 6a3.75 3.75 0 11-7.5 0 3.75 3.75 0 017.5 0zM4.501 20.118a7.5 7.5 0 0114.998 0A17.933 17.933 0 0112 21.75c-2.676 0-5.216-.584-7.499-1.632z" />
                            </svg>
                        </div>
                        <h3 class="text-2xl font-bold mb-3 text-gray-900 dark:text-white">Integrity</h3>
                        <p class="text-lg text-gray-600 dark:text-gray-300">We conduct our business with honesty,
                            transparency, and ethical standards.</p>
                    </div>
                    <div class="bg-white dark:bg-gray-800 rounded-xl p-8 hover:shadow-xl transition-all duration-300 border border-gray-100 dark:border-gray-700 group"
                        data-aos="fade-up" data-aos-delay="200">
                        <div
                            class="w-20 h-20 flex items-center justify-center rounded-xl bg-gradient-to-br from-primary/10 to-secondary/10 text-primary mb-6 group-hover:from-primary group-hover:to-secondary group-hover:text-white transition-all duration-300">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                                stroke-width="1.5" stroke="currentColor" class="w-10 h-10">
                                <path stroke-linecap="round" stroke-linejoin="round"
                                    d="M18 18.72a9.094 9.094 0 003.741-.479 3 3 0 00-4.682-2.72m.94 3.198l.001.031c0 .225-.012.447-.037.666A11.944 11.944 0 0112 21c-2.17 0-4.207-.576-5.963-1.584A6.062 6.062 0 016 18.719m12 0a5.971 5.971 0 00-.941-3.197m0 0A5.995 5.995 0 0012 12.75a5.995 5.995 0 00-5.058 2.772m0 0a3 3 0 00-4.681 2.72 8.986 8.986 0 003.74.477m.94-3.197a5.971 5.971 0 00-.94 3.197M15 6.75a3 3 0 11-6 0 3 3 0 016 0zm6 3a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0zm-13.5 0a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0z" />
                            </svg>
                        </div>
                        <h3 class="text-2xl font-bold mb-3 text-gray-900 dark:text-white">Collaboration</h3>
                        <p class="text-lg text-gray-600 dark:text-gray-300">We work together as a team, valuing diverse
                            perspectives to achieve common goals.</p>
                    </div>
                @endif
            </div>
        </div>
    </section>

    <!-- Team Section -->
    @if (isset($pageData['team']) && $pageData['team']['active'])
        <section class="py-20 md:py-28 bg-gray-50 dark:bg-gray-900">
            <div class="container mx-auto px-4 sm:px-6">
                <div class="text-center mb-16" data-aos="fade-up">
                    <h2 class="text-3xl md:text-4xl font-bold tracking-tight text-gray-900 dark:text-white mb-4">
                        {{ $pageData['team']['content']['heading'] ?? 'Meet Our Team' }}
                    </h2>
                    <p class="mt-4 text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto" data-aos="fade-up"
                        data-aos-delay="100">
                        {{ $pageData['team']['content']['subheading'] ?? 'The talented people behind our success' }}
                    </p>
                </div>

                @if (isset($pageData['team']['content']['team_members']))
                    <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
                        @foreach (is_string($pageData['team']['content']['team_members']) ? json_decode($pageData['team']['content']['team_members'], true) : $pageData['team']['content']['team_members'] as $key => $member)
                            <div class="bg-white dark:bg-gray-800 rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 group"
                                data-aos="fade-up" data-aos-delay="{{ $key * 100 }}">
                                <div class="aspect-[3/4] overflow-hidden">
                                    <img src="{{ uploads_url($member['photo'] ?? 'team/default.jpg') }}"
                                        alt="{{ $member['name'] }}"
                                        class="w-full h-full object-cover transition-transform duration-500 group-hover:scale-105" />
                                </div>
                                <div class="p-6 text-center">
                                    <h3 class="text-xl font-bold text-gray-900 dark:text-white">{{ $member['name'] }}</h3>
                                    <p class="text-primary font-medium mt-1">{{ $member['position'] }}</p>
                                    <p class="text-sm text-gray-500 dark:text-gray-400 mt-3">{{ $member['bio'] }}</p>

                                    <div class="flex justify-center gap-4 mt-5">
                                        @if (isset($member['social']['linkedin']))
                                            <a href="{{ $member['social']['linkedin'] }}" target="_blank" rel="noopener"
                                                class="text-gray-500 hover:text-primary transition-colors">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20"
                                                    fill="currentColor" class="bi bi-linkedin" viewBox="0 0 16 16">
                                                    <path
                                                        d="M0 1.146C0 .513.526 0 1.175 0h13.65C15.474 0 16 .513 16 1.146v13.708c0 .633-.526 1.146-1.175 1.146H1.175C.526 16 0 15.487 0 14.854V1.146zm4.943 12.248V6.169H2.542v7.225h2.401zm-1.2-8.212c.837 0 1.358-.554 1.358-1.248-.015-.709-.52-1.248-1.342-1.248-.822 0-1.359.54-1.359 1.248 0 .694.521 1.248 1.327 1.248h.016zm4.908 8.212V9.359c0-.216.016-.432.08-.586.173-.431.568-.878 1.232-.878.869 0 1.216.662 1.216 1.634v3.865h2.401V9.25c0-2.22-1.184-3.252-2.764-3.252-1.274 0-1.845.7-2.165 1.193v.025h-.016a5.54 5.54 0 0 1 .016-.025V6.169h-2.4c.03.678 0 7.225 0 7.225h2.4z" />
                                                </svg>
                                            </a>
                                        @endif
                                        @if (isset($member['social']['twitter']))
                                            <a href="{{ $member['social']['twitter'] }}" target="_blank" rel="noopener"
                                                class="text-gray-500 hover:text-primary transition-colors">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20"
                                                    fill="currentColor" class="bi bi-twitter" viewBox="0 0 16 16">
                                                    <path
                                                        d="M5.026 15c6.038 0 9.341-5.003 9.341-9.334 0-.14 0-.282-.006-.422A6.685 6.685 0 0 0 16 3.542a6.658 6.658 0 0 1-1.889.518 3.301 3.301 0 0 0 1.447-1.817 6.533 6.533 0 0 1-2.087.793A3.286 3.286 0 0 0 7.875 6.03a9.325 9.325 0 0 1-6.767-3.429 3.289 3.289 0 0 0 1.018 4.382A3.323 3.323 0 0 1 .64 6.575v.045a3.288 3.288 0 0 0 2.632 3.218 3.203 3.203 0 0 1-.865.115 3.23 3.23 0 0 1-.614-.057 3.283 3.283 0 0 0 3.067 2.277A6.588 6.588 0 0 1 .78 13.58a6.32 6.32 0 0 1-.78-.045A9.344 9.344 0 0 0 5.026 15z" />
                                                </svg>
                                            </a>
                                        @endif
                                        @if (isset($member['social']['github']))
                                            <a href="{{ $member['social']['github'] }}" target="_blank" rel="noopener"
                                                class="text-gray-500 hover:text-primary transition-colors">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20"
                                                    fill="currentColor" class="bi bi-github" viewBox="0 0 16 16">
                                                    <path
                                                        d="M8 0C3.58 0 0 3.58 0 8c0 3.54 2.29 6.53 5.47 7.59.4.07.55-.17.55-.38 0-.19-.01-.82-.01-1.49-2.01.37-2.53-.49-2.69-.94-.09-.23-.48-.94-.82-1.13-.28-.15-.68-.52-.01-.53.63-.01 1.08.58 1.23.82.72 1.21 1.87.87 2.33.66.07-.52.28-.87.51-1.07-1.78-.2-3.64-.89-3.64-3.95 0-.87.31-1.59.82-2.15-.08-.2-.36-1.02.08-2.12 0 0 .67-.21 2.2.82.64-.18 1.32-.27 2-.27.68 0 1.36.09 2 .27 1.53-1.04 2.2-.82 2.2-.82.44 1.1.16 1.92.08 2.12.51.56.82 1.27.82 2.15 0 3.07-1.87 3.75-3.65 3.95.29.25.54.73.54 1.48 0 1.07-.01 1.93-.01 2.2 0 .21.15.46.55.38A8.012 8.012 0 0 0 16 8c0-4.42-3.58-8-8-8z" />
                                                </svg>
                                            </a>
                                        @endif
                                        @if (isset($member['social']['instagram']))
                                            <a href="{{ $member['social']['instagram'] }}" target="_blank"
                                                rel="noopener" class="text-gray-500 hover:text-primary transition-colors">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20"
                                                    fill="currentColor" class="bi bi-instagram" viewBox="0 0 16 16">
                                                    <path
                                                        d="M8 0C5.829 0 5.556.01 4.703.048 3.85.088 3.269.222 2.76.42a3.917 3.917 0 0 0-1.417.923A3.927 3.927 0 0 0 .42 2.76C.222 3.268.087 3.85.048 4.7.01 5.555 0 5.827 0 8.001c0 2.172.01 2.444.048 3.297.04.852.174 1.433.372 1.942.205.526.478.972.923 1.417.444.445.89.719 1.416.923.51.198 1.09.333 1.942.372C5.555 15.99 5.827 16 8 16s2.444-.01 3.298-.048c.851-.04 1.434-.174 1.943-.372a3.916 3.916 0 0 0 1.416-.923c.445-.445.718-.891.923-1.417.197-.509.332-1.09.372-1.942C15.99 10.445 16 10.173 16 8s-.01-2.445-.048-3.299c-.04-.851-.175-1.433-.372-1.941a3.926 3.926 0 0 0-.923-1.417A3.911 3.911 0 0 0 13.24.42                                                        c-.51-.198-1.092-.333-1.943-.372C10.443.01 10.172 0 7.998 0h.003zm-.717 1.442h.718c2.136 0 2.389.007 3.232.046.78.035 1.204.166 1.486.275.373.145.64.319.92.599.28.28.453.546.598.92.11.281.24.705.275 1.485.039.843.047 1.096.047 3.231s-.008 2.389-.047 3.232c-.035.78-.166 1.203-.275 1.485a2.47 2.47 0 0 1-.599.919c-.28.28-.546.453-.92.598-.28.11-.704.24-1.485.276-.843.038-1.096.047-3.232.047s-2.39-.009-3.233-.047c-.78-.036-1.203-.166-1.485-.276a2.478 2.478 0 0 1-.92-.598 2.48 2.48 0 0 1-.6-.92c-.109-.281-.24-.705-.275-1.485-.038-.843-.046-1.096-.046-3.233 0-2.136.008-2.388.046-3.231.036-.78.166-1.204.276-1.486.145-.373.319-.64.599-.92.28-.28.546-.453.92-.598.282-.11.705-.24 1.485-.276.738-.034 1.024-.044 2.515-.045v.002zm4.988 1.328a.96.96 0 1 0 0 1.92.96.96 0 0 0 0-1.92zm-4.27 1.122a4.109 4.109 0 1 0 0 8.217 4.109 4.109 0 0 0 0-8.217zm0 1.441a2.667 2.667 0 1 1 0 5.334 2.667 2.667 0 0 1 0-5.334z" />
                                                </svg>
                                            </a>
                                        @endif
                                        <!-- Other social media icons remain the same -->
                                        @if (isset($member['social']['facebook']))
                                            <a href="{{ $member['social']['facebook'] }}" target="_blank" rel="noopener"
                                                class="text-gray-500 hover:text-primary transition-colors">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20"
                                                    fill="currentColor" class="bi bi-facebook" viewBox="0 0 16 16">
                                                    <path
                                                        d="M16 8.049c0-4.446-3.582-8.05-8-8.05C3.58 0-.002 3.603-.002 8.05c0 4.017 2.926 7.347 6.75 7.951v-5.625h-2.03V8.05H6.75V6.275c0-2.017 1.195-3.131 3.022-3.131.876 0 1.791.157 1.791.157v1.98h-1.009c-.993 0-1.303.621-1.303 1.258v1.51h2.218l-.354 2.326H9.25V16c3.824-.604 6.75-3.934 6.75-7.951z" />
                                                </svg>
                                            </a>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @endif
            </div>
        </section>
    @endif

    <!-- Stats Section -->
    <section class="py-20 md:py-28">
        <div class="container mx-auto px-4 sm:px-6">
            <div class="text-center mb-16" data-aos="fade-up">
                <span
                    class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-primary/10 text-primary dark:bg-primary/20 mb-4">
                    By The Numbers
                </span>
                <h2 class="text-3xl md:text-4xl font-bold tracking-tight text-gray-900 dark:text-white mb-4">
                    {{ $pageData['about']['content']['stats_heading'] ?? 'Our Impact' }}
                </h2>
                <p class="mt-4 max-w-2xl mx-auto text-lg text-gray-600 dark:text-gray-300">
                    {{ $pageData['about']['content']['stats_subheading'] ?? 'We measure our success by the impact we make' }}
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div class="bg-white dark:bg-gray-800 rounded-xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100 dark:border-gray-700 group"
                    data-aos="fade-up" data-aos-delay="0">
                    <div class="flex flex-col items-center text-center">
                        <div
                            class="w-16 h-16 flex items-center justify-center rounded-xl bg-primary/10 text-primary mb-6 group-hover:bg-primary group-hover:text-white transition-all duration-300">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                                stroke-width="1.5" stroke="currentColor" class="w-8 h-8">
                                <path stroke-linecap="round" stroke-linejoin="round"
                                    d="M15.75 6a3.75 3.75 0 11-7.5 0 3.75 3.75 0 017.5 0zM4.501 20.118a7.5 7.5 0 0114.998 0A17.933 17.933 0 0112 21.75c-2.676 0-5.216-.584-7.499-1.632z" />
                            </svg>
                        </div>
                        <div class="text-lg font-medium text-gray-600 dark:text-gray-300 mb-2">Happy Clients</div>
                        <div
                            class="text-5xl font-extrabold text-primary mb-2 bg-clip-text text-transparent bg-gradient-to-r from-primary to-secondary">
                            {{ $pageData['about']['content']['clients_count'] ?? '500' }}+
                        </div>
                        <div class="text-base text-gray-500 dark:text-gray-400">Since our founding</div>
                    </div>
                </div>

                <div class="bg-white dark:bg-gray-800 rounded-xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100 dark:border-gray-700 group"
                    data-aos="fade-up" data-aos-delay="100">
                    <div class="flex flex-col items-center text-center">
                        <div
                            class="w-16 h-16 flex items-center justify-center rounded-xl bg-secondary/10 text-secondary mb-6 group-hover:bg-secondary group-hover:text-white transition-all duration-300">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                                stroke-width="1.5" stroke="currentColor" class="w-8 h-8">
                                <path stroke-linecap="round" stroke-linejoin="round"
                                    d="M12 21a9.004 9.004 0 008.716-6.747M12 21a9.004 9.004 0 01-8.716-6.747M12 21c2.485 0 4.5-4.03 4.5-9S14.485 3 12 3m0 18c-2.485 0-4.5-4.03-4.5-9S9.515 3 12 3m0 0a8.997 8.997 0 017.843 4.582M12 3a8.997 8.997 0 00-7.843 4.582m15.686 0A11.953 11.953 0 0112 10.5c-2.998 0-5.74-1.1-7.843-2.918m15.686 0A8.959 8.959 0 0121 12c0 .778-.099 1.533-.284 2.253m0 0A17.919 17.919 0 0112 16.5c-3.162 0-6.133-.815-8.716-2.247m0 0A9.015 9.015 0 013 12c0-1.605.42-3.113 1.157-4.418" />
                            </svg>
                        </div>
                        <div class="text-lg font-medium text-gray-600 dark:text-gray-300 mb-2">Projects Completed</div>
                        <div
                            class="text-5xl font-extrabold text-secondary mb-2 bg-clip-text text-transparent bg-gradient-to-r from-secondary to-primary">
                            {{ $pageData['about']['content']['projects_count'] ?? '200' }}
                        </div>
                        <div class="text-base text-gray-500 dark:text-gray-400">Across
                            {{ $pageData['about']['content']['countries_count'] ?? '25' }} countries</div>
                    </div>
                </div>

                <div class="bg-white dark:bg-gray-800 rounded-xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100 dark:border-gray-700 group"
                    data-aos="fade-up" data-aos-delay="200">
                    <div class="flex flex-col items-center text-center">
                        <div
                            class="w-16 h-16 flex items-center justify-center rounded-xl bg-accent/10 text-accent mb-6 group-hover:bg-accent group-hover:text-white transition-all duration-300">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                                stroke-width="1.5" stroke="currentColor" class="w-8 h-8">
                                <path stroke-linecap="round" stroke-linejoin="round"
                                    d="M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 012.25-2.25h13.5A2.25 2.25 0 0121 7.5v11.25m-18 0A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75m-18 0v-7.5A2.25 2.25 0 015.25 9h13.5A2.25 2.25 0 0121 11.25v7.5" />
                            </svg>
                        </div>
                        <div class="text-lg font-medium text-gray-600 dark:text-gray-300 mb-2">Years of Experience</div>
                        <div
                            class="text-5xl font-extrabold text-accent mb-2 bg-clip-text text-transparent bg-gradient-to-r from-accent to-secondary">
                            {{ $pageData['about']['content']['years_count'] ?? '8' }}+
                        </div>
                        <div class="text-base text-gray-500 dark:text-gray-400">Growing stronger each year</div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact CTA -->
    <section
        class="py-16 md:py-24 bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 relative overflow-hidden">
        <div
            class="absolute inset-0 bg-grid-slate-100 dark:bg-grid-slate-800/25 [mask-image:linear-gradient(to_bottom,white,transparent)] opacity-20">
        </div>
        <div class="container relative z-10 mx-auto px-4 sm:px-6 text-center">
            <h2 class="text-3xl md:text-4xl font-bold tracking-tight text-gray-900 dark:text-white mb-6">
                {{ $pageData['about']['content']['cta_heading'] ?? 'Ready to Work With Us?' }}
            </h2>
            <p class="max-w-2xl mx-auto mb-8 text-lg text-gray-600 dark:text-gray-300">
                {{ $pageData['about']['content']['cta_text'] ?? 'We\'re always looking for new challenges and opportunities to make a difference.' }}
            </p>
            <a href="{{ $pageData['about']['content']['cta_button_link'] ?? route('pages.contact') }}"
                class="inline-flex items-center justify-center px-6 py-3 rounded-lg bg-primary hover:bg-primary/90 text-white font-medium transition-all shadow-lg hover:shadow-primary/30 transform hover:-translate-y-0.5">
                {{ $pageData['about']['content']['cta_button_text'] ?? 'Contact Us Today' }}
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-2" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd"
                        d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z"
                        clip-rule="evenodd" />
                </svg>
            </a>
        </div>
    </section>
@endsection
