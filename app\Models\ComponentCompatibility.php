<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ComponentCompatibility extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'component_id',
        'compatible_component_id',
        'category_id',
        'compatible_category_id',
        'rule_type',
        'rule_value',
        'error_message',
    ];

    /**
     * The rule types for compatibility.
     */
    const RULE_TYPE_COMPATIBLE = 'compatible';
    const RULE_TYPE_INCOMPATIBLE = 'incompatible';
    const RULE_TYPE_REQUIRES = 'requires';
    const RULE_TYPE_SPEC_MATCH = 'spec_match';

    /**
     * Get the component that owns the compatibility rule.
     */
    public function component(): BelongsTo
    {
        return $this->belongsTo(Component::class, 'component_id');
    }

    /**
     * Get the compatible component.
     */
    public function compatibleComponent(): BelongsTo
    {
        return $this->belongsTo(Component::class, 'compatible_component_id');
    }

    /**
     * Get the category of the component.
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(ComponentCategory::class, 'category_id');
    }

    /**
     * Get the compatible category.
     */
    public function compatibleCategory(): BelongsTo
    {
        return $this->belongsTo(ComponentCategory::class, 'compatible_category_id');
    }

    /**
     * Check if two components are compatible based on this rule.
     */
    public function checkCompatibility(Component $component, Component $otherComponent): bool
    {
        switch ($this->rule_type) {
            case self::RULE_TYPE_COMPATIBLE:
                return true;
            case self::RULE_TYPE_INCOMPATIBLE:
                return false;
            case self::RULE_TYPE_REQUIRES:
                // Check if the required component is present
                return true;
            case self::RULE_TYPE_SPEC_MATCH:
                // Check if the specs match according to rule_value
                $spec = json_decode($this->rule_value, true);
                $componentSpec = $component->specs[$spec['key']] ?? null;
                $otherComponentSpec = $otherComponent->specs[$spec['key']] ?? null;
                
                if ($componentSpec === null || $otherComponentSpec === null) {
                    return false;
                }
                
                switch ($spec['operator']) {
                    case '=':
                        return $componentSpec == $otherComponentSpec;
                    case '>':
                        return $componentSpec > $otherComponentSpec;
                    case '<':
                        return $componentSpec < $otherComponentSpec;
                    case '>=':
                        return $componentSpec >= $otherComponentSpec;
                    case '<=':
                        return $componentSpec <= $otherComponentSpec;
                    default:
                        return false;
                }
            default:
                return false;
        }
    }
}