<?php

namespace Tests\Feature\Api;

use App\Models\Component;
use App\Models\ComponentCategory;
use App\Models\User;
use App\Models\Cart;
use App\Models\CartItem;
use App\Models\Order;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Laravel\Sanctum\Sanctum;
use Tests\TestCase;

class OrderApiTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;
    protected ComponentCategory $category;
    protected Component $component;
    protected array $shippingAddress;
    protected array $paymentDetails;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->user = User::factory()->create();
        
        $this->category = ComponentCategory::factory()->create([
            'name' => 'CPU',
            'slug' => 'cpu'
        ]);
        
        $this->component = Component::factory()->create([
            'category_id' => $this->category->id,
            'price' => 299.99,
            'stock' => 10,
            'is_active' => true
        ]);

        $this->shippingAddress = [
            'name' => 'John <PERSON>',
            'address_line_1' => '123 Main St',
            'address_line_2' => 'Apt 4B',
            'city' => 'New York',
            'state' => 'NY',
            'postal_code' => '10001',
            'country' => 'USA',
            'email' => '<EMAIL>',
            'phone' => '************'
        ];

        $this->paymentDetails = [
            'card_number' => '****************',
            'expiry_month' => '12',
            'expiry_year' => '2025',
            'cvv' => '123'
        ];
    }

    public function test_authenticated_user_can_view_orders()
    {
        Sanctum::actingAs($this->user);

        // Create some orders for the user
        Order::factory()->count(3)->create([
            'user_id' => $this->user->id
        ]);

        $response = $this->getJson('/api/v1/orders');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'order_number',
                        'status',
                        'total',
                        'created_at',
                        'items'
                    ]
                ],
                'meta',
                'links'
            ]);
    }

    public function test_authenticated_user_can_create_order_from_cart()
    {
        Sanctum::actingAs($this->user);

        // Add item to cart
        $cart = Cart::factory()->create(['user_id' => $this->user->id]);
        CartItem::factory()->create([
            'cart_id' => $cart->id,
            'component_id' => $this->component->id,
            'quantity' => 2
        ]);

        $orderData = [
            'shipping_address' => [
                'name' => $this->shippingAddress['name'],
                'address_line_1' => $this->shippingAddress['address_line_1'],
                'address_line_2' => $this->shippingAddress['address_line_2'] ?? null,
                'city' => $this->shippingAddress['city'],
                'state' => $this->shippingAddress['state'],
                'postal_code' => $this->shippingAddress['postal_code'],
                'country' => $this->shippingAddress['country'],
                'email' => $this->shippingAddress['email'],
                'phone' => $this->shippingAddress['phone']
            ],
            'billing_address' => [
                'name' => $this->shippingAddress['name'],
                'address_line_1' => $this->shippingAddress['address_line_1'],
                'address_line_2' => $this->shippingAddress['address_line_2'] ?? null,
                'city' => $this->shippingAddress['city'],
                'state' => $this->shippingAddress['state'],
                'postal_code' => $this->shippingAddress['postal_code'],
                'country' => $this->shippingAddress['country'],
                'email' => $this->shippingAddress['email'],
                'phone' => $this->shippingAddress['phone']
            ],
            'payment_method' => 'credit_card',
            'payment_details' => $this->paymentDetails
        ];

        $response = $this->postJson('/api/v1/orders', $orderData);

        $response->assertStatus(201)
            ->assertJsonStructure([
                'data' => [
                    'id',
                    'order_number',
                    'status',
                    'total',
                    'items'
                ],
                'message'
            ]);

        $this->assertDatabaseHas('orders', [
            'user_id' => $this->user->id,
            'status' => 'pending'
        ]);
    }

    public function test_cannot_create_order_with_empty_cart()
    {
        Sanctum::actingAs($this->user);

        $orderData = [
            'shipping_address' => $this->shippingAddress,
            'payment_method' => 'credit_card',
            'payment_details' => $this->paymentDetails
        ];

        $response = $this->postJson('/api/v1/orders', $orderData);

        $response->assertStatus(422)
            ->assertJson([
                'message' => 'Cart is empty'
            ]);
    }

    public function test_cannot_create_order_with_insufficient_stock()
    {
        Sanctum::actingAs($this->user);

        // Set component stock to 1
        $this->component->update(['stock' => 1]);

        // Add 2 items to cart (more than stock)
        $cart = Cart::factory()->create(['user_id' => $this->user->id]);
        CartItem::factory()->create([
            'cart_id' => $cart->id,
            'component_id' => $this->component->id,
            'quantity' => 2
        ]);

        $orderData = [
            'shipping_address' => $this->shippingAddress,
            'payment_method' => 'credit_card',
            'payment_details' => $this->paymentDetails
        ];

        $response = $this->postJson('/api/v1/orders', $orderData);

        $response->assertStatus(422)
            ->assertJsonPath('message', "Insufficient stock for {$this->component->name}");
    }

    public function test_authenticated_user_can_view_specific_order()
    {
        Sanctum::actingAs($this->user);

        $order = Order::factory()->create([
            'user_id' => $this->user->id
        ]);

        $response = $this->getJson("/api/v1/orders/{$order->id}");

        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    'id',
                    'order_number',
                    'status',
                    'total',
                    'items'
                ]
            ]);
    }

    public function test_user_cannot_view_other_users_order()
    {
        Sanctum::actingAs($this->user);

        $otherUser = User::factory()->create();
        $order = Order::factory()->create([
            'user_id' => $otherUser->id
        ]);

        $response = $this->getJson("/api/v1/orders/{$order->id}");

        $response->assertStatus(404);
    }

    public function test_authenticated_user_can_cancel_pending_order()
    {
        Sanctum::actingAs($this->user);

        $order = Order::factory()->create([
            'user_id' => $this->user->id,
            'status' => 'pending'
        ]);

        $response = $this->putJson("/api/v1/orders/{$order->id}/cancel");

        $response->assertStatus(200)
            ->assertJson([
                'message' => 'Order canceled successfully'
            ]);

        $this->assertDatabaseHas('orders', [
            'id' => $order->id,
            'status' => 'canceled'
        ]);
    }

    public function test_cannot_cancel_shipped_order()
    {
        Sanctum::actingAs($this->user);

        $order = Order::factory()->create([
            'user_id' => $this->user->id,
            'status' => 'completed'
        ]);

        $response = $this->putJson("/api/v1/orders/{$order->id}/cancel");

        $response->assertStatus(422)
            ->assertJson([
                'message' => 'Order cannot be cancelled at this stage'
            ]);
    }

    public function test_can_track_order_by_order_number()
    {
        Sanctum::actingAs($this->user);
        
        $order = Order::factory()->create([
            'user_id' => $this->user->id,
            'order_number' => 'ORD-12345',
            'status' => 'completed',
            'tracking_number' => 'TRK-67890'
        ]);

        $response = $this->getJson('/api/v1/orders/track/ORD-12345');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    'order',
                    'tracking_info' => [
                        'status',
                        'tracking_number',
                        'tracking_url',
                        'estimated_delivery',
                        'status_history'
                    ]
                ]
            ]);
    }

    public function test_cannot_track_non_existent_order()
    {
        $response = $this->getJson('/api/v1/orders/track/NON-EXISTENT');

        $response->assertStatus(404);
    }

    public function test_can_filter_orders_by_status()
    {
        Sanctum::actingAs($this->user);

        Order::factory()->create([
            'user_id' => $this->user->id,
            'status' => 'pending'
        ]);

        Order::factory()->create([
            'user_id' => $this->user->id,
            'status' => 'completed'
        ]);

        $response = $this->getJson('/api/v1/orders?status=pending');

        $response->assertStatus(200);
        $data = $response->json('data');
        
        $this->assertCount(1, $data);
        $this->assertEquals('pending', $data[0]['status']);
    }

    public function test_can_sort_orders()
    {
        Sanctum::actingAs($this->user);

        Order::factory()->create([
            'user_id' => $this->user->id,
            'total' => 100.00
        ]);

        Order::factory()->create([
            'user_id' => $this->user->id,
            'total' => 200.00
        ]);

        $response = $this->getJson('/api/v1/orders?sort=total&direction=desc');

        $response->assertStatus(200);
        $data = $response->json('data');
        
        $this->assertEquals(200.00, $data[0]['total']);
        $this->assertEquals(100.00, $data[1]['total']);
    }

    public function test_validates_order_creation_request()
    {
        Sanctum::actingAs($this->user);

        // Missing shipping address
        $response = $this->postJson('/api/v1/orders', [
            'payment_method' => 'credit_card',
            'payment_details' => $this->paymentDetails
        ]);
        $response->assertStatus(422);

        // Invalid payment method
        $response = $this->postJson('/api/v1/orders', [
            'shipping_address' => $this->shippingAddress,
            'payment_method' => 'invalid_method',
            'payment_details' => $this->paymentDetails
        ]);
        $response->assertStatus(422);
    }

    public function test_unauthenticated_user_cannot_access_orders()
    {
        $response = $this->getJson('/api/v1/orders');
        $response->assertStatus(401);

        $response = $this->postJson('/api/v1/orders', [
            'shipping_address' => $this->shippingAddress,
            'payment_method' => 'credit_card',
            'payment_details' => $this->paymentDetails
        ]);
        $response->assertStatus(401);
    }
}