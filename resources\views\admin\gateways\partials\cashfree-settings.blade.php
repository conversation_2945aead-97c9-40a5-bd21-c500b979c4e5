<div class="space-y-6">
    <div class="bg-green-50 border border-green-200 rounded-lg p-4">
        <div class="flex">
            <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-green-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                </svg>
            </div>
            <div class="ml-3">
                <h3 class="text-sm font-medium text-green-800">Cashfree Configuration</h3>
                <div class="mt-2 text-sm text-green-700">
                    <p>Get your API credentials from the <a href="https://merchant.cashfree.com/" target="_blank" class="underline">Cashfree Merchant Dashboard</a>.</p>
                    <p class="mt-1">Webhook URL: <code class="bg-green-100 px-1 rounded">{{ url('/webhooks/cashfree') }}</code></p>
                </div>
            </div>
        </div>
    </div>

    <!-- App ID -->
    <div>
        <label for="app_id" class="block text-sm font-medium text-gray-700 mb-2">
            App ID <span class="text-red-500">*</span>
        </label>
        <input type="text" 
               id="app_id" 
               name="settings[app_id]" 
               value="{{ old('settings.app_id', $settings['app_id'] ?? '') }}"
               placeholder="Enter your Cashfree App ID"
               class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 @error('settings.app_id') border-red-500 @enderror"
               required>
        @error('settings.app_id')
            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
        @enderror
        <p class="mt-1 text-sm text-gray-500">Your Cashfree App ID from the dashboard</p>
    </div>

    <!-- Secret Key -->
    <div>
        <label for="secret_key" class="block text-sm font-medium text-gray-700 mb-2">
            Secret Key <span class="text-red-500">*</span>
        </label>
        <div class="relative">
            <input type="password" 
                   id="secret_key" 
                   name="settings[secret_key]" 
                   value="{{ old('settings.secret_key', $settings['secret_key'] ?? '') }}"
                   placeholder="Enter your Cashfree Secret Key"
                   class="w-full px-3 py-2 pr-10 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 @error('settings.secret_key') border-red-500 @enderror"
                   required>
            <button type="button" 
                    onclick="togglePasswordVisibility('secret_key')"
                    class="absolute inset-y-0 right-0 pr-3 flex items-center">
                <svg class="h-5 w-5 text-gray-400" id="secret_key_eye_open" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                </svg>
                <svg class="h-5 w-5 text-gray-400 hidden" id="secret_key_eye_closed" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                </svg>
            </button>
        </div>
        @error('settings.secret_key')
            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
        @enderror
        <p class="mt-1 text-sm text-gray-500">Your Cashfree Secret Key (keep this secure)</p>
    </div>

    <!-- Client ID -->
    <div>
        <label for="client_id" class="block text-sm font-medium text-gray-700 mb-2">
            Client ID <span class="text-red-500">*</span>
        </label>
        <input type="text" 
               id="client_id" 
               name="settings[client_id]" 
               value="{{ old('settings.client_id', $settings['client_id'] ?? '') }}"
               placeholder="Enter your Cashfree Client ID"
               class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 @error('settings.client_id') border-red-500 @enderror"
               required>
        @error('settings.client_id')
            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
        @enderror
        <p class="mt-1 text-sm text-gray-500">Your Cashfree Client ID for API authentication</p>
    </div>

    <!-- Client Secret -->
    <div>
        <label for="client_secret" class="block text-sm font-medium text-gray-700 mb-2">
            Client Secret <span class="text-red-500">*</span>
        </label>
        <div class="relative">
            <input type="password" 
                   id="client_secret" 
                   name="settings[client_secret]" 
                   value="{{ old('settings.client_secret', $settings['client_secret'] ?? '') }}"
                   placeholder="Enter your Cashfree Client Secret"
                   class="w-full px-3 py-2 pr-10 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 @error('settings.client_secret') border-red-500 @enderror"
                   required>
            <button type="button" 
                    onclick="togglePasswordVisibility('client_secret')"
                    class="absolute inset-y-0 right-0 pr-3 flex items-center">
                <svg class="h-5 w-5 text-gray-400" id="client_secret_eye_open" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                </svg>
                <svg class="h-5 w-5 text-gray-400 hidden" id="client_secret_eye_closed" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                </svg>
            </button>
        </div>
        @error('settings.client_secret')
            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
        @enderror
        <p class="mt-1 text-sm text-gray-500">Your Cashfree Client Secret for API authentication (keep this secure)</p>
    </div>

    <!-- Additional Configuration Info -->
    <div class="bg-gray-50 border border-gray-200 rounded-lg p-4">
        <h4 class="text-sm font-medium text-gray-900 mb-2">Important Notes:</h4>
        <ul class="text-sm text-gray-600 space-y-1">
            <li>• Cashfree uses API v4 for payment processing</li>
            <li>• Both App ID/Secret Key and Client ID/Secret are required</li>
            <li>• Webhook signature verification is automatically handled</li>
            <li>• Test and live environments use different API endpoints</li>
            <li>• Configure webhook URL in your Cashfree dashboard for real-time updates</li>
        </ul>
    </div>
</div>

<script>
function togglePasswordVisibility(fieldId) {
    const field = document.getElementById(fieldId);
    const eyeOpen = document.getElementById(fieldId + '_eye_open');
    const eyeClosed = document.getElementById(fieldId + '_eye_closed');
    
    if (field.type === 'password') {
        field.type = 'text';
        eyeOpen.classList.add('hidden');
        eyeClosed.classList.remove('hidden');
    } else {
        field.type = 'password';
        eyeOpen.classList.remove('hidden');
        eyeClosed.classList.add('hidden');
    }
}
</script>