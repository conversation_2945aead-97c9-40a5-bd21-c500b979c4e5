<footer
    class="relative bg-gradient-to-br from-gray-900 via-gray-800 to-black dark:from-gray-900 dark:via-gray-800 dark:to-black text-white overflow-hidden">

    <!-- Main Footer Content -->
    <div class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 lg:gap-12">

            <!-- Company Info -->
            <div class="col-span-1 lg:col-span-1">
                <div class="group">
                    <div class="flex items-center mb-6">
                        <div class="relative">
                            <div
                                class="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
                                <svg class="w-7 h-7 text-white" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5" />
                                </svg>
                            </div>
                            <div class="absolute -top-1 -right-1 w-4 h-4 bg-green-400 rounded-full"></div>
                        </div>
                        <div class="ml-4">
                            <span
                                class="text-2xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">NEXUS
                                PC</span>
                            <div class="text-xs text-gray-400 font-medium">PREMIUM BUILDS</div>
                        </div>
                    </div>
                    <p class="text-gray-300 mb-6 leading-relaxed">
                        Crafting the future of computing with precision-engineered custom PCs. Experience unparalleled
                        performance and reliability.
                    </p>

                    <!-- Social Media Icons -->
                    <div class="flex space-x-4">
                        <a href="#"
                            class="group relative w-12 h-12 bg-gray-800 hover:bg-blue-600 rounded-xl flex items-center justify-center">
                            <svg class="h-6 w-6 text-gray-300 group-hover:text-white"
                                fill="currentColor" viewBox="0 0 24 24">
                                <path fill-rule="evenodd"
                                    d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z"
                                    clip-rule="evenodd" />
                            </svg>
                        </a>
                        <a href="#"
                            class="group relative w-12 h-12 bg-gray-800 hover:bg-blue-400 rounded-xl flex items-center justify-center">
                            <svg class="h-6 w-6 text-gray-300 group-hover:text-white"
                                fill="currentColor" viewBox="0 0 24 24">
                                <path
                                    d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84" />
                            </svg>
                        </a>
                        <a href="#"
                            class="group relative w-12 h-12 bg-gray-800 hover:bg-gradient-to-r hover:from-purple-500 hover:to-pink-500 rounded-xl flex items-center justify-center">
                            <svg class="h-6 w-6 text-gray-300 group-hover:text-white"
                                fill="currentColor" viewBox="0 0 24 24">
                                <path fill-rule="evenodd"
                                    d="M12.315 2c2.43 0 2.784.013 3.808.06 1.064.049 1.791.218 2.427.465a4.902 4.902 0 011.772 1.153 4.902 4.902 0 011.153 1.772c.247.636.416 1.363.465 2.427.048 1.067.06 1.407.06 4.123v.08c0 2.643-.012 2.987-.06 4.043-.049 1.064-.218 1.791-.465 2.427a4.902 4.902 0 01-1.153 1.772 4.902 4.902 0 01-1.772 1.153c-.636.247-1.363.416-2.427.465-1.067.048-1.407.06-4.123.06h-.08c-2.643 0-2.987-.012-4.043-.06-1.064-.049-1.791-.218-2.427-.465a4.902 4.902 0 01-1.772-1.153 4.902 4.902 0 01-1.153-1.772c-.247-.636-.416-1.363-.465-2.427-.047-1.024-.06-1.379-.06-3.808v-.63c0-2.43.013-2.784.06-3.808.049-1.064.218-1.791.465-2.427a4.902 4.902 0 011.153-1.772A4.902 4.902 0 015.45 2.525c.636-.247 1.363-.416 2.427-.465C8.901 2.013 9.256 2 11.685 2h.63zm-.081 1.802h-.468c-2.456 0-2.784.011-3.807.058-.975.045-1.504.207-1.857.344-.467.182-.8.398-1.15.748-.35.35-.566.683-.748 1.15-.137.353-.3.882-.344 1.857-.047 1.023-.058 1.351-.058 3.807v.468c0 2.456.011 2.784.058 3.807.045.975.207 1.504.344 1.857.182.466.399.8.748 1.15.35.35.683.566 1.15.748.353.137.882.3 1.857.344 1.054.048 1.37.058 4.041.058h.08c2.597 0 2.917-.01 3.96-.058.976-.045 1.505-.207 1.858-.344.466-.182.8-.398 1.15-.748.35-.35.566-.683.748-1.15.137-.353.3-.882.344-1.857.048-1.055.058-1.37.058-4.041v-.08c0-2.597-.01-2.917-.058-3.96-.045-.976-.207-1.505-.344-1.858a3.097 3.097 0 00-.748-1.15 3.098 3.098 0 00-1.15-.748c-.353-.137-.882-.3-1.857-.344-1.023-.047-1.351-.058-3.807-.058zM12 6.865a5.135 5.135 0 110 10.27 5.135 5.135 0 010-10.27zm0 1.802a3.333 3.333 0 100 6.666 3.333 3.333 0 000-6.666zm5.338-3.205a1.2 1.2 0 110 2.4 1.2 1.2 0 010-2.4z"
                                    clip-rule="evenodd" />
                            </svg>
                        </a>
                        <a href="#"
                            class="group relative w-12 h-12 bg-gray-800 hover:bg-red-600 rounded-xl flex items-center justify-center">
                            <svg class="h-6 w-6 text-gray-300 group-hover:text-white"
                                fill="currentColor" viewBox="0 0 24 24">
                                <path
                                    d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z" />
                            </svg>
                        </a>
                    </div>
                </div>
            </div>

            <!-- Quick Links -->
            <div class="col-span-1">
                <h3 class="text-xl font-bold text-white mb-6 relative">
                    Quick Links
                    <div
                        class="absolute -bottom-2 left-0 w-12 h-1 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full">
                    </div>
                </h3>
                <ul class="space-y-4">
                    <li>
                        <a href="#"
                            class="group flex items-center text-gray-300 hover:text-white">
                            <svg class="w-4 h-4 mr-3"
                                fill="currentColor" viewBox="0 0 20 20">
                                <path
                                    d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z" />
                            </svg>
                            Dashboard
                        </a>
                    </li>
                    <li>
                        <a href="#"
                            class="group flex items-center text-gray-300 hover:text-white">
                            <svg class="w-4 h-4 mr-3"
                                fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 2L3 7v11a1 1 0 001 1h3v-6h6v6h3a1 1 0 001-1V7l-7-5z"
                                    clip-rule="evenodd" />
                            </svg>
                            Shop
                        </a>
                    </li>
                    <li>
                        <a href="#"
                            class="group flex items-center text-gray-300 hover:text-white">
                            <svg class="w-4 h-4 mr-3"
                                fill="currentColor" viewBox="0 0 20 20">
                                <path
                                    d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z" />
                            </svg>
                            PC Builder
                        </a>
                    </li>
                    <li>
                        <a href="#"
                            class="group flex items-center text-gray-300 hover:text-white transition-all duration-300 hover-lift">
                            <svg class="w-4 h-4 mr-3 group-hover:text-blue-400 transition-colors duration-300"
                                fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd"
                                    d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-2 0c0 .993-.241 1.929-.668 2.754l-1.524-1.525a3.997 3.997 0 00.078-2.183l1.562-1.562C15.802 8.249 16 9.1 16 10zm-5.165 3.913l1.58 1.58A5.98 5.98 0 0110 16a5.976 5.976 0 01-2.516-.552l1.562-1.562a4.006 4.006 0 001.789.027zm-4.677-2.796a4.002 4.002 0 01-.041-2.08l-1.588-1.588A5.98 5.98 0 004 10c0 .954.223 1.856.619 2.657l1.539-1.54zm.674-6.025L7.235 6.91a4.001 4.001 0 012.093.104l1.565-1.565A5.98 5.98 0 0010 4c-.954 0-1.856.223-2.657.619l1.539 1.54z"
                                    clip-rule="evenodd" />
                            </svg>
                            Support
                        </a>
                    </li>
                </ul>
            </div>

            <!-- Support -->
            <div class="col-span-1">
                <h3 class="text-xl font-bold text-white mb-6 relative">
                    Support
                    <div
                        class="absolute -bottom-2 left-0 w-12 h-1 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full">
                    </div>
                </h3>
                <ul class="space-y-4">
                    <li>
                        <a href="#"
                            class="group flex items-center text-gray-300 hover:text-white transition-all duration-300 hover-lift">
                            <svg class="w-4 h-4 mr-3 group-hover:text-blue-400 transition-colors duration-300"
                                fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd"
                                    d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z"
                                    clip-rule="evenodd" />
                            </svg>
                            FAQs
                        </a>
                    </li>
                    <li>
                        <a href="#"
                            class="group flex items-center text-gray-300 hover:text-white transition-all duration-300 hover-lift">
                            <svg class="w-4 h-4 mr-3 group-hover:text-blue-400 transition-colors duration-300"
                                fill="currentColor" viewBox="0 0 20 20">
                                <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                                <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
                            </svg>
                            Contact Us
                        </a>
                    </li>
                    <li>
                        <a href="#"
                            class="group flex items-center text-gray-300 hover:text-white transition-all duration-300 hover-lift">
                            <svg class="w-4 h-4 mr-3 group-hover:text-blue-400 transition-colors duration-300"
                                fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd"
                                    d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                                    clip-rule="evenodd" />
                            </svg>
                            Warranty
                        </a>
                    </li>
                    <li>
                        <a href="#"
                            class="group flex items-center text-gray-300 hover:text-white transition-all duration-300 hover-lift">
                            <svg class="w-4 h-4 mr-3 group-hover:text-blue-400 transition-colors duration-300"
                                fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd"
                                    d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z"
                                    clip-rule="evenodd" />
                            </svg>
                            Returns & Exchanges
                        </a>
                    </li>
                </ul>
            </div>

            <!-- Newsletter -->
            <div class="col-span-1 md:col-span-2 lg:col-span-1">
                <h3 class="text-xl font-bold text-white mb-6 relative">
                    Stay Connected
                    <div
                        class="absolute -bottom-2 left-0 w-12 h-1 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full">
                    </div>
                </h3>
                <p class="text-gray-300 mb-6 leading-relaxed">
                    Get exclusive deals, latest PC builds, and tech insights delivered to your inbox.
                </p>
                <form class="space-y-4" onsubmit="handleNewsletterSubmit(event)">
                    <div class="relative">
                        <input type="email" placeholder="Enter your email address"
                            class="newsletter-input w-full px-4 py-3 rounded-xl bg-gray-800 border border-gray-700 text-white placeholder-gray-400 focus:outline-none focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20"
                            required />
                        <div class="absolute inset-y-0 right-0 flex items-center pr-3">
                            <svg class="w-5 h-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                                <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
                            </svg>
                        </div>
                    </div>
                    <button type="submit"
                        class="w-full bg-gradient-to-r from-blue-500 to-purple-600 text-white font-semibold py-3 px-6 rounded-xl shadow-lg">
                        Subscribe Now
                    </button>
                </form>

                <!-- Trust Indicators -->
                <div class="mt-6 flex items-center space-x-4 text-sm text-gray-400">
                    <div class="flex items-center">
                        <svg class="w-4 h-4 mr-1 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd"
                                d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z"
                                clip-rule="evenodd" />
                        </svg>
                        Secure
                    </div>
                    <div class="flex items-center">
                        <svg class="w-4 h-4 mr-1 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        No Spam
                    </div>
                </div>
            </div>
        </div>

        <!-- Performance Stats -->
        <!-- <div class="mt-16 grid grid-cols-2 md:grid-cols-4 gap-8">
            <div class="text-center group">
                <div
                    class="text-3xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent mb-2">
                    24/7</div>
                <div class="text-gray-400 text-sm">Support</div>
            </div>
            <div class="text-center group">
                <div
                    class="text-3xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent mb-2">
                    5★</div>
                <div class="text-gray-400 text-sm">Rating</div>
            </div>
        </div> -->

        <!-- Bottom Bar -->
        <div class="relative mt-16 pt-8 border-t border-gray-700">
            <div class="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
                <div class="flex flex-col md:flex-row items-center space-y-2 md:space-y-0 md:space-x-6">
                    <p class="text-gray-400 text-sm">
                        &copy; <span id="currentYear">2025</span> NEXUS PC. All rights reserved.
                    </p>
                    <div class="flex items-center space-x-2 text-sm text-gray-400">
                        <svg class="w-4 h-4 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd"
                                d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                                clip-rule="evenodd" />
                        </svg>
                        <span>Trusted by 50,000+ Gamers</span>
                    </div>
                </div>

                <div class="flex flex-wrap justify-center md:justify-end items-center space-x-6">
                    <a href="#"
                        class="text-gray-400 hover:text-white text-sm relative group">
                        Privacy Policy
                        <span
                            class="absolute -bottom-1 left-0 w-0 h-0.5 bg-blue-400"></span>
                    </a>
                    <a href="#"
                        class="text-gray-400 hover:text-white text-sm relative group">
                        Terms of Service
                        <span
                            class="absolute -bottom-1 left-0 w-0 h-0.5 bg-blue-400"></span>
                    </a>
                    <a href="#"
                        class="text-gray-400 hover:text-white text-sm relative group">
                        Cookie Policy
                        <span
                            class="absolute -bottom-1 left-0 w-0 h-0.5 bg-blue-400"></span>
                    </a>
                    <a href="#"
                        class="text-gray-400 hover:text-white text-sm relative group">
                        Sitemap
                        <span
                            class="absolute -bottom-1 left-0 w-0 h-0.5 bg-blue-400"></span>
                    </a>
                </div>
            </div>

            <!-- Back to Top Button -->
            
        </div>
    </div>

    <!-- Decorative Elements -->
    <div class="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500"></div>
    <script>
        // Update current year
        document.getElementById('currentYear').textContent = new Date().getFullYear();

        // Newsletter form handler
        function handleNewsletterSubmit(event) {
            event.preventDefault();
            const email = event.target.querySelector('input[type="email"]').value;

            // Show success message (you can replace this with actual API call)
            const button = event.target.querySelector('button');
            const originalText = button.innerHTML;

            button.innerHTML = `
            <svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
            </svg>
            Subscribing...
        `;

            setTimeout(() => {
                button.innerHTML = `
                <svg class="w-5 h-5 inline mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                </svg>
                Subscribed!
            `;
                button.classList.add('bg-green-500');
                button.classList.remove('bg-gradient-to-r', 'from-blue-500', 'to-purple-600');

                setTimeout(() => {
                    button.innerHTML = originalText;
                    button.classList.remove('bg-green-500');
                    button.classList.add('bg-gradient-to-r', 'from-blue-500', 'to-purple-600');
                    event.target.reset();
                }, 2000);
            }, 1500);
        }

    </script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

        body {
            font-family: 'Inter', sans-serif;
        }

        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .glass-effect {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .dark .glass-effect {
            background: rgba(0, 0, 0, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .newsletter-input:focus {
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
        
        /* Removed animation-related styles */
    </style>
</footer>
<?php /**PATH C:\lara\www\pc-builder\resources\views/components/footer.blade.php ENDPATH**/ ?>