<?php

namespace App\Livewire\Shop;

use App\Models\Cart;
use App\Models\CartItem;
use App\Services\CartService;
use Livewire\Component;

class ShoppingCart extends Component
{
    public $cart;
    public $cartItems = [];
    public $totalItems = 0;
    public $subtotal = 0;
    public $tax = 0;
    public $shipping = 0;
    public $total = 0;
    
    protected $listeners = [
        'cartUpdated' => 'loadCart'
    ];
    
    public function mount()
    {
        $this->loadCart();
    }
    
    public function loadCart()
    {
        $cartService = app(CartService::class);
        $this->cart = $cartService->getCart();
        
        if ($this->cart) {
            $this->cartItems = $this->cart->items()->with('component')->get();
            $this->calculateTotals();
        }
    }
    
    public function calculateTotals()
    {
        $this->totalItems = $this->cartItems->sum('quantity');
        $this->subtotal = $this->cartItems->sum(function($item) {
            return $item->price * $item->quantity;
        });
        
        // Get discount if coupon is applied
        $discountAmount = session('discount_amount', 0);
        
        // Calculate tax (example: 10%)
        $this->tax = $this->subtotal * 0.1;
        
        // Calculate shipping (example: flat rate $5 for orders under $100)
        $this->shipping = $this->subtotal < 100 ? 5 : 0;
        
        // Calculate total with discount
        $this->total = $this->subtotal - $discountAmount + $this->tax + $this->shipping;
    }
    
    public function updateQuantity($cartItemId, $quantity)
    {
        $cartItem = CartItem::find($cartItemId);
        
        if (!$cartItem || $cartItem->cart_id != $this->cart->id) {
            return;
        }
        
        // Check if the requested quantity is valid
        if ($quantity < 1) {
            $this->removeItem($cartItemId);
            return;
        }
        
        // Check if we have enough stock
        if ($quantity > $cartItem->component->stock) {
            session()->flash('error', 'Not enough stock available.');
            return;
        }
        
        // Update the quantity
        $cartItem->quantity = $quantity;
        $cartItem->save();
        
        $this->loadCart();
        session()->flash('message', 'Cart updated successfully!');
    }
    
    public function incrementQuantity($cartItemId)
    {
        $cartItem = CartItem::find($cartItemId);
        
        if (!$cartItem || $cartItem->cart_id != $this->cart->id) {
            return;
        }
        
        // Check if we have enough stock
        if ($cartItem->quantity >= $cartItem->component->stock) {
            session()->flash('error', 'Not enough stock available.');
            return;
        }
        
        // Increment the quantity
        $cartItem->quantity++;
        $cartItem->save();
        
        $this->loadCart();
    }
    
    public function decrementQuantity($cartItemId)
    {
        $cartItem = CartItem::find($cartItemId);
        
        if (!$cartItem || $cartItem->cart_id != $this->cart->id) {
            return;
        }
        
        // If quantity is 1, remove the item
        if ($cartItem->quantity <= 1) {
            $this->removeItem($cartItemId);
            return;
        }
        
        // Decrement the quantity
        $cartItem->quantity--;
        $cartItem->save();
        
        $this->loadCart();
    }
    
    public function removeItem($cartItemId)
    {
        $cartItem = CartItem::find($cartItemId);
        
        if (!$cartItem || $cartItem->cart_id != $this->cart->id) {
            return;
        }
        
        // Remove the item
        $cartItem->delete();
        
        $this->loadCart();
        session()->flash('message', 'Item removed from cart!');
    }
    
    public function clearCart()
    {
        if ($this->cart) {
            // Remove all items
            $this->cart->items()->delete();
            
            $this->loadCart();
            session()->flash('message', 'Cart cleared successfully!');
        }
    }
    
    public function checkout()
    {
        if (!auth()->check()) {
            return redirect()->route('login', ['redirect' => 'checkout']);
        }
        
        if ($this->totalItems === 0) {
            session()->flash('error', 'Your cart is empty.');
            return;
        }
        
        // Check if all items are in stock
        foreach ($this->cartItems as $item) {
            if ($item->quantity > $item->component->stock) {
                session()->flash('error', 'Some items in your cart are out of stock.');
                return;
            }
        }
        
        return redirect()->route('shop.checkout');
    }
    
    public function render()
    {
        return view('livewire.shop.shopping-cart');
    }
}