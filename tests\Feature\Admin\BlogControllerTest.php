<?php

use App\Http\Controllers\Admin\BlogController;
use App\Models\BlogPost;
use App\Models\BlogPostCategory;
use App\Models\BlogPostTag;
use App\Models\Comment;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;

uses(RefreshDatabase::class);

beforeEach(function () {
    $this->user = User::factory()->create([
        'role' => 'admin',
        'status' => 'active'
    ]);
    $this->actingAs($this->user);
    
    $this->category = BlogPostCategory::factory()->create();
    $this->tag = BlogPostTag::factory()->create();
});

describe('BlogController Index', function () {
    it('displays blog posts index page', function () {
        $posts = BlogPost::factory()->count(3)->create([
            'blog_post_category_id' => $this->category->id,
            'user_id' => $this->user->id,
        ]);

        $response = $this->get(route('admin.blog.posts.index'));

        $response->assertStatus(200)
            ->assertViewIs('admin.blog.posts.blog-index')
            ->assertViewHas('posts')
            ->assertViewHas('categories');
    });

    it('filters posts by search term', function () {
        $post1 = BlogPost::factory()->create([
            'title' => 'Laravel Testing Guide',
            'blog_post_category_id' => $this->category->id,
            'user_id' => $this->user->id,
        ]);
        
        $post2 = BlogPost::factory()->create([
            'title' => 'PHP Best Practices',
            'blog_post_category_id' => $this->category->id,
            'user_id' => $this->user->id,
        ]);

        $response = $this->get(route('admin.blog.posts.index', ['search' => 'Laravel']));

        $response->assertStatus(200);
    });

    it('filters posts by category', function () {
        $category2 = BlogPostCategory::factory()->create();
        
        $post1 = BlogPost::factory()->create(['blog_post_category_id' => $this->category->id, 'user_id' => $this->user->id]);
        $post2 = BlogPost::factory()->create(['blog_post_category_id' => $category2->id, 'user_id' => $this->user->id]);

        $response = $this->get(route('admin.blog.posts.index', ['category' => $this->category->id]));

        $response->assertStatus(200);
    });

    it('filters posts by published status', function () {
        BlogPost::factory()->create([
            'is_published' => true,
            'blog_post_category_id' => $this->category->id,
            'user_id' => $this->user->id,
        ]);
        
        BlogPost::factory()->create([
            'is_published' => false,
            'blog_post_category_id' => $this->category->id,
            'user_id' => $this->user->id,
        ]);

        $response = $this->get(route('admin.blog.posts.index', ['status' => 'published']));
        $response->assertStatus(200);

        $response = $this->get(route('admin.blog.posts.index', ['status' => 'draft']));
        $response->assertStatus(200);
    });

    it('filters featured posts', function () {
        BlogPost::factory()->create([
            'featured' => true,
            'blog_post_category_id' => $this->category->id,
            'user_id' => $this->user->id,
        ]);

        $response = $this->get(route('admin.blog.posts.index', ['featured' => '1']));

        $response->assertStatus(200);
    });

    it('sorts posts by different fields', function () {
        BlogPost::factory()->count(3)->create([
            'blog_post_category_id' => $this->category->id,
            'user_id' => $this->user->id,
        ]);

        $response = $this->get(route('admin.blog.posts.index', [
            'sort' => 'title',
            'direction' => 'asc'
        ]));

        $response->assertStatus(200);
    });
});

describe('BlogController Create', function () {
    it('shows create blog post form', function () {
        $response = $this->get(route('admin.blog.posts.create'));

        $response->assertStatus(200)
            ->assertViewIs('admin.blog.posts.blog-create')
            ->assertViewHas('categories')
            ->assertViewHas('tags');
    });
});

describe('BlogController Store', function () {
    it('creates a new blog post successfully', function () {
        $postData = [
            'title' => 'Test Blog Post',
            'content' => 'This is test content for the blog post.',
            'excerpt' => 'This is a test excerpt.',
            'blog_post_category_id' => $this->category->id,
            'user_id' => $this->user->id,
            'meta_title' => 'Test Meta Title',
            'meta_description' => 'Test meta description',
            'meta_keywords' => 'test, blog, post',
            'is_published' => '1',
            'featured' => '1',
            'tags' => [$this->tag->id],
        ];

        $response = $this->post(route('admin.blog.posts.store'), $postData);

        $response->assertRedirect(route('admin.blog.index'))
            ->assertSessionHas('success', 'Blog post created successfully.');

        $this->assertDatabaseHas('blog_posts', [
            'title' => 'Test Blog Post',
            'content' => 'This is test content for the blog post.',
            'user_id' => $this->user->id,
            'is_published' => true,
            'featured' => true,
            'blog_post_category_id' => $this->category->id,
        ]);
    });

    it('creates blog post with featured image', function () {
        Storage::fake('public');

        $image = UploadedFile::fake()->image('test-image.jpg');

        $postData = [
            'title' => 'Test Blog Post with Image',
            'content' => 'Content with image.',
            'blog_post_category_id' => $this->category->id,
            'user_id' => $this->user->id,
            'featured_image' => $image,
        ];

        $response = $this->post(route('admin.blog.posts.store'), $postData);

        $response->assertRedirect(route('admin.blog.index'));
        
        Storage::disk('public')->assertExists('blog/' . $image->hashName());
    });

    it('validates required fields', function () {
        $response = $this->post(route('admin.blog.posts.store'), []);

        $response->assertSessionHasErrors(['title', 'content', 'blog_post_category_id']);
    });

    it('validates image file type and size', function () {
        Storage::fake('public');

        $invalidFile = UploadedFile::fake()->create('document.pdf', 1024);

        $response = $this->post(route('admin.blog.posts.store'), [
            'title' => 'Test Post',
            'content' => 'Test content',
            'blog_post_category_id' => $this->category->id,
            'user_id' => $this->user->id,
            'featured_image' => $invalidFile,
        ]);

        $response->assertSessionHasErrors(['featured_image']);
    });

    it('processes meta keywords correctly', function () {
        $postData = [
            'title' => 'Test Blog Post',
            'content' => 'Test content',
            'blog_post_category_id' => $this->category->id,
            'user_id' => $this->user->id,
            'meta_keywords' => 'laravel, php, testing, pest',
        ];

        $this->post(route('admin.blog.posts.store'), $postData);

        $post = BlogPost::where('title', 'Test Blog Post')->first();
        expect($post->meta_keywords)->toBeArray()
            ->and($post->meta_keywords)->toContain('laravel', 'php', 'testing', 'pest');
    });
});

describe('BlogController Edit', function () {
    it('shows edit form for existing blog post', function () {
        $post = BlogPost::factory()->create([
            'blog_post_category_id' => $this->category->id,
            'user_id' => $this->user->id,
        ]);

        $response = $this->get(route('admin.blog.posts.edit', $post));

        $response->assertStatus(200)
            ->assertViewIs('admin.blog.posts.blog-edit')
            ->assertViewHas('post')
            ->assertViewHas('categories')
            ->assertViewHas('tags');
    });
});

describe('BlogController Update', function () {
    it('updates blog post successfully', function () {
        $post = BlogPost::factory()->create([
            'blog_post_category_id' => $this->category->id,
            'user_id' => $this->user->id,
        ]);

        $updateData = [
            'title' => 'Updated Blog Post Title',
            'content' => 'Updated content',
            'blog_post_category_id' => $this->category->id,
            'user_id' => $this->user->id,
            'meta_title' => 'Updated Meta Title',
        ];

        $response = $this->put(route('admin.blog.posts.update', $post), $updateData);

        $response->assertRedirect(route('admin.blog.index'))
            ->assertSessionHas('success', 'Blog post updated successfully.');

        $this->assertDatabaseHas('blog_posts', [
            'id' => $post->id,
            'title' => 'Updated Blog Post Title',
            'content' => 'Updated content',
        ]);
    });

    it('updates featured image and deletes old one', function () {
        Storage::fake('public');

        $oldImage = UploadedFile::fake()->image('old-image.jpg');
        $oldImagePath = $oldImage->store('blog', 'public');

        $post = BlogPost::factory()->create([
            'blog_post_category_id' => $this->category->id,
            'user_id' => $this->user->id,
            'featured_image' => $oldImagePath,
        ]);

        $newImage = UploadedFile::fake()->image('new-image.jpg');

        $updateData = [
            'title' => $post->title,
            'content' => $post->content,
            'blog_post_category_id' => $this->category->id,
            'user_id' => $this->user->id,
            'featured_image' => $newImage,
        ];

        $this->put(route('admin.blog.posts.update', $post), $updateData);

        Storage::disk('public')->assertMissing($oldImagePath);
        Storage::disk('public')->assertExists('blog/' . $newImage->hashName());
    });

    it('sets published_at when publishing for first time', function () {
        $post = BlogPost::factory()->create([
            'blog_post_category_id' => $this->category->id,
            'user_id' => $this->user->id,
            'is_published' => false,
            'published_at' => null,
        ]);

        $updateData = [
            'title' => $post->title,
            'content' => $post->content,
            'blog_post_category_id' => $this->category->id,
            'user_id' => $this->user->id,
            'is_published' => '1',
        ];

        $this->put(route('admin.blog.posts.update', $post), $updateData);

        $post->refresh();
        expect($post->is_published)->toBeTrue()
            ->and($post->published_at)->not()->toBeNull();
    });

    it('syncs tags correctly', function () {
        $post = BlogPost::factory()->create([
            'blog_post_category_id' => $this->category->id,
            'user_id' => $this->user->id,
        ]);

        $tag2 = BlogPostTag::factory()->create();

        $updateData = [
            'title' => $post->title,
            'content' => $post->content,
            'blog_post_category_id' => $this->category->id,
            'user_id' => $this->user->id,
            'tags' => [$this->tag->id, $tag2->id],
        ];

        $this->put(route('admin.blog.posts.update', $post), $updateData);

        expect($post->tags()->count())->toBe(2);
    });
});

describe('BlogController Destroy', function () {
    it('deletes blog post successfully', function () {
        $post = BlogPost::factory()->create([
            'blog_post_category_id' => $this->category->id,
            'user_id' => $this->user->id,
        ]);

        $response = $this->delete(route('admin.blog.posts.destroy', $post));

        $response->assertRedirect(route('admin.blog.index'))
            ->assertSessionHas('success', 'Blog post deleted successfully.');

        $this->assertModelMissing($post);
    });

    it('deletes featured image when deleting post', function () {
        Storage::fake('public');

        $image = UploadedFile::fake()->image('test-image.jpg');
        $imagePath = $image->store('blog', 'public');

        $post = BlogPost::factory()->create([
            'blog_post_category_id' => $this->category->id,
            'user_id' => $this->user->id,
            'featured_image' => $imagePath,
        ]);

        $this->delete(route('admin.blog.posts.destroy', $post));

        Storage::disk('public')->assertMissing($imagePath);
    });

    it('deletes associated comments when deleting post', function () {
        $post = BlogPost::factory()->create([
            'blog_post_category_id' => $this->category->id,
            'user_id' => $this->user->id,
        ]);

        $comment = Comment::factory()->create([
            'blog_post_id' => $post->id,
            'is_approved' => true,
            'user_id' => $this->user->id,
        ]);

        $this->delete(route('admin.blog.posts.destroy', $post));

        $this->assertModelMissing($comment);
    });
});

describe('BlogController Upload Image', function () {
    it('uploads image for TinyMCE editor', function () {
        Storage::fake('public');

        $image = UploadedFile::fake()->image('editor-image.jpg');

        $response = $this->post(route('admin.blog.upload-image'), [
            'file' => $image,
        ]);

        $response->assertStatus(200)
            ->assertJsonStructure(['location']);

        Storage::disk('public')->assertExists('blog/content/' . $image->hashName());
    });

    it('validates image upload for editor', function () {
        $invalidFile = UploadedFile::fake()->create('document.pdf', 1024);

        $response = $this->post(route('admin.blog.upload-image'), [
            'file' => $invalidFile,
        ]);

        $response->assertSessionHasErrors(['file']);
    });
});

describe('BlogController Comments', function () {
    it('displays comments index page', function () {
        $post = BlogPost::factory()->create([
            'blog_post_category_id' => $this->category->id,
            'user_id' => $this->user->id,
        ]);

        Comment::factory()->count(3)->create([
            'blog_post_id' => $post->id,
            'is_approved' => true,
        ]);

        $response = $this->get(route('admin.comments.index'));

        $response->assertStatus(200)
            ->assertViewIs('admin.comments.index')
            ->assertViewHas('comments');
    });

    it('filters comments by status', function () {
        $post = BlogPost::factory()->create([
            'blog_post_category_id' => $this->category->id,
            'user_id' => $this->user->id,
        ]);

        Comment::factory()->create([
            'blog_post_id' => $post->id,
            'is_approved' => true,
        ]);

        $response = $this->get(route('admin.comments.index', ['status' => 'approved']));

        $response->assertStatus(200);
    });

    it('filters comments by post', function () {
        $post = BlogPost::factory()->create([
            'blog_post_category_id' => $this->category->id,
            'user_id' => $this->user->id,
        ]);

        Comment::factory()->create([
            'blog_post_id' => $post->id,
            'is_approved' => true,
        ]);

        $response = $this->get(route('admin.comments.index', ['post_id' => $post->id]));

        $response->assertStatus(200);
    });
});

describe('BlogController Comment Moderation', function () {
    it('approves comment successfully', function () {
        $post = BlogPost::factory()->create([
            'blog_post_category_id' => $this->category->id,
            'user_id' => $this->user->id,
        ]);

        $comment = Comment::factory()->create([
            'blog_post_id' => $post->id,
            'is_approved' => false,
        ]);

        $response = $this->patch(route('admin.comments.approve', $comment));

        $response->assertRedirect()
            ->assertSessionHas('success', 'Comment approved successfully.');

        $comment->refresh();
        expect($comment->is_approved)->toBeTrue()
            ->and($comment->moderated_at)->not()->toBeNull()
            ->and($comment->moderated_by)->toBe($this->user->id);
    });

    it('rejects comment with reason', function () {
        $post = BlogPost::factory()->create([
            'blog_post_category_id' => $this->category->id,
            'user_id' => $this->user->id,
        ]);

        $comment = Comment::factory()->create([
            'blog_post_id' => $post->id,
            'is_approved' => false,
        ]);

        $response = $this->patch(route('admin.comments.reject', $comment), [
            'rejected_reason' => 'Inappropriate content',
        ]);

        $response->assertRedirect()
            ->assertSessionHas('success', 'Comment rejected successfully.');

        $comment->refresh();
        expect($comment->is_approved)->toBeFalse()
            ->and($comment->rejected_reason)->toBe('Inappropriate content')
            ->and($comment->moderated_by)->toBe($this->user->id);
    });

    it('validates rejection reason', function () {
        $post = BlogPost::factory()->create([
            'blog_post_category_id' => $this->category->id,
            'user_id' => $this->user->id,
        ]);

        $comment = Comment::factory()->create([
            'blog_post_id' => $post->id,
            'is_approved' => false,
        ]);

        $response = $this->patch(route('admin.comments.reject', $comment), []);

        $response->assertSessionHasErrors(['rejected_reason']);
    });

    it('deletes comment and its replies', function () {
        $post = BlogPost::factory()->create([
            'blog_post_category_id' => $this->category->id,
            'user_id' => $this->user->id,
        ]);

        $comment = Comment::factory()->create([
            'blog_post_id' => $post->id,
            'is_approved' => true,
        ]);

        $reply = Comment::factory()->create([
            'blog_post_id' => $post->id,
            'parent_id' => $comment->id,
            'is_approved' => true,
        ]);

        $response = $this->delete(route('admin.comments.destroy', $comment));

        $response->assertRedirect()
            ->assertSessionHas('success', 'Comment deleted successfully.');

        $this->assertModelMissing($comment);
        $this->assertModelMissing($reply);
    });
});

describe('Authentication and Authorization', function () {
    it('requires authentication for all routes', function () {
        Auth::logout();

        $routes = [
            ['GET', route('admin.blog.posts.index')],
            ['GET', route('admin.blog.posts.create')],
            ['GET', route('admin.comments.index')],
        ];

        foreach ($routes as [$method, $route]) {
            $response = $this->call($method, $route);
            $response->assertRedirect(); // Should redirect to login
        }
    });
});