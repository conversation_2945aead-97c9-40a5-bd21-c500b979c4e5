<?php

namespace App\Services;

use App\Models\Cart;
use App\Models\Order;
use App\Models\User;
use App\Models\Payment;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;
use App\Jobs\SendOrderConfirmation;
use App\Jobs\SendOrderStatusUpdate;

class CheckoutService
{
    protected CartService $cartService;

    public function __construct(CartService $cartService)
    {
        $this->cartService = $cartService;
    }

    /**
     * Process checkout and create order from cart.
     *
     * @param Cart $cart
     * @param array $shippingData
     * @param array $paymentData
     * @return Order
     * @throws ValidationException
     */
    public function processCheckout(Cart $cart, array $shippingData, array $paymentData): Order
    {
        // Validate cart has items
        if ($cart->items()->count() === 0) {
            throw new \InvalidArgumentException('Cart is empty');
        }

        // Validate stock availability
        $stockIssues = $this->cartService->validateCartStock($cart->user);
        if (!empty($stockIssues)) {
            throw new \InvalidArgumentException('Some items in cart are no longer available');
        }

        // Validate shipping data
        $this->validateShippingData($shippingData);

        // Validate payment data
        $this->validatePaymentData($paymentData);

        return DB::transaction(function () use ($cart, $shippingData, $paymentData) {
            // Calculate totals
            $totals = $this->calculateOrderTotals($cart, $shippingData);

            // Create order
            $order = $this->createOrder($cart, $shippingData, $totals);

            // Create order items
            $this->createOrderItems($order, $cart);

            // Clear cart
            $cart->clear();

            // Dispatch order confirmation email
            \App\Jobs\SendOrderConfirmation::dispatch($order);

            return $order;
        });
    }

    /**
     * Calculate shipping cost for the given cart and address.
     *
     * @param Cart $cart
     * @param array $address
     * @return float
     */
    public function calculateShipping(Cart $cart, array $address): float
    {
        $subtotal = $cart->total ?? 0;
        
        // Free shipping over $100
        if ($subtotal >= 100) {
            return 0;
        }

        // Different rates based on location
        $state = strtoupper($address['state'] ?? '');
        $country = strtoupper($address['country'] ?? 'US');

        // International shipping
        if ($country !== 'US') {
            return 29.99;
        }

        // Standard shipping for all US addresses
        return 9.99;
    }

    /**
     * Validate shipping address data.
     *
     * @param array $address
     * @return bool
     * @throws ValidationException
     */
    public function validateAddress(array $address): bool
    {
        $validator = Validator::make($address, [
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'required|string|max:20',
            'address' => 'required|string|max:255',
            'city' => 'required|string|max:100',
            'state' => 'required|string|max:50',
            'zipcode' => 'required|string|max:20',
            'country' => 'required|string|max:50',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator);
        }

        return true;
    }

    /**
     * Generate a unique order number.
     *
     * @return string
     */
    public function generateOrderNumber(): string
    {
        do {
            $prefix = 'PCB';
            $timestamp = now()->format('YmdHis');
            $random = str_pad(mt_rand(1, 999), 3, '0', STR_PAD_LEFT);
            $orderNumber = $prefix . $timestamp . $random;
        } while (Order::where('order_number', $orderNumber)->exists());

        return $orderNumber;
    }

    /**
     * Update order status.
     *
     * @param Order $order
     * @param string $status
     * @param string|null $notes
     * @return Order
     */
    public function updateOrderStatus(Order $order, string $status, ?string $notes = null): Order
    {
        $validStatuses = [
            'pending',
            'processing', 
            'completed',
            'canceled',
            'refunded',
        ];

        if (!in_array($status, $validStatuses)) {
            throw new \InvalidArgumentException("Invalid order status: {$status}");
        }

        // Capture the previous status before updating
        $previousStatus = $order->status;
        
        $order->status = $status;
        
        if ($notes) {
            $order->notes = ($order->notes ? $order->notes . "\n" : '') . 
                           now()->format('Y-m-d H:i:s') . ': ' . $notes;
        }

        $order->save();

        // Dispatch order status update email with previous status
        \App\Jobs\SendOrderStatusUpdate::dispatch($order, $previousStatus, $notes);

        return $order;
    }

    /**
     * Calculate order totals including tax and shipping.
     *
     * @param Cart $cart
     * @param array $shippingData
     * @return array
     */
    protected function calculateOrderTotals(Cart $cart, array $shippingData): array
    {
        $subtotal = $cart->total ?? 0;
        $shipping = $this->calculateShipping($cart, $shippingData);

        // Calculate tax (8% default rate)
        $taxRate = 0.08;
        $tax = round($subtotal * $taxRate, 2);

        // Get discount from session if coupon is applied
        $discount = session('discount_amount', 0);

        $total = $subtotal + $tax + $shipping - $discount;

        return [
            'subtotal' => $subtotal,
            'tax' => $tax,
            'shipping' => $shipping,
            'discount' => $discount,
            'total' => round($total, 2),
        ];
    }

    /**
     * Create order from cart and shipping data.
     *
     * @param Cart $cart
     * @param array $shippingData
     * @param array $totals
     * @return Order
     */
    protected function createOrder(Cart $cart, array $shippingData, array $totals): Order
    {
        $billingData = $shippingData['billing'] ?? $shippingData['shipping'];

        return Order::create([
            'user_id' => $cart->user_id,
            'order_number' => $this->generateOrderNumber(),
            'status' => 'pending',
            'subtotal' => $totals['subtotal'],
            'tax' => $totals['tax'],
            'shipping' => $totals['shipping'],
            'discount' => $totals['discount'] ?? 0,
            'discount_amount' => $totals['discount'] ?? 0,
            'total' => $totals['total'],
            'coupon_code' => session('applied_coupon'),
            'billing_name' => $billingData['name'],
            'billing_email' => $billingData['email'],
            'billing_phone' => $billingData['phone'],
            'billing_address' => $billingData['address'],
            'billing_city' => $billingData['city'],
            'billing_state' => $billingData['state'],
            'billing_zipcode' => $billingData['zipcode'],
            'billing_country' => $billingData['country'],
            'shipping_name' => $shippingData['shipping']['name'],
            'shipping_email' => $shippingData['shipping']['email'],
            'shipping_phone' => $shippingData['shipping']['phone'],
            'shipping_address' => $shippingData['shipping']['address'],
            'shipping_city' => $shippingData['shipping']['city'],
            'shipping_state' => $shippingData['shipping']['state'],
            'shipping_zipcode' => $shippingData['shipping']['zipcode'],
            'shipping_country' => $shippingData['shipping']['country'],
            'payment_status' => 'pending',
        ]);
    }

    /**
     * Create order items from cart items.
     *
     * @param Order $order
     * @param Cart $cart
     * @return void
     */
    protected function createOrderItems(Order $order, Cart $cart): void
    {
        foreach ($cart->items()->with('component')->get() as $cartItem) {
            $order->items()->create([
                'component_id' => $cartItem->component_id,
                'quantity' => $cartItem->quantity,
                'price' => $cartItem->price,
                'name' => $cartItem->component->name,
                'options' => json_encode([
                    'brand' => $cartItem->component->brand,
                    'model' => $cartItem->component->model,
                    'category' => $cartItem->component->category->name ?? null,
                ]),
            ]);
        }
    }

    /**
     * Validate shipping data.
     *
     * @param array $shippingData
     * @return void
     * @throws ValidationException
     */
    protected function validateShippingData(array $shippingData): void
    {
        $validator = Validator::make($shippingData, [
            'shipping.name' => 'required|string|max:255',
            'shipping.email' => 'required|email|max:255',
            'shipping.phone' => 'required|string|max:20',
            'shipping.address' => 'required|string|max:255',
            'shipping.city' => 'required|string|max:100',
            'shipping.state' => 'required|string|max:50',
            'shipping.zipcode' => 'required|string|max:20',
            'shipping.country' => 'required|string|max:50',
            'billing.name' => 'sometimes|required|string|max:255',
            'billing.email' => 'sometimes|required|email|max:255',
            'billing.phone' => 'sometimes|required|string|max:20',
            'billing.address' => 'sometimes|required|string|max:255',
            'billing.city' => 'sometimes|required|string|max:100',
            'billing.state' => 'sometimes|required|string|max:50',
            'billing.zipcode' => 'sometimes|required|string|max:20',
            'billing.country' => 'sometimes|required|string|max:50',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator);
        }
    }

    /**
     * Validate payment data.
     *
     * @param array $paymentData
     * @return void
     * @throws ValidationException
     */
    protected function validatePaymentData(array $paymentData): void
    {
        $validator = Validator::make($paymentData, [
            'payment_method' => 'required|string|in:credit_card,paypal,bank_transfer',
            'card_number' => 'required_if:payment_method,credit_card|string',
            'card_expiry' => 'required_if:payment_method,credit_card|string',
            'card_cvv' => 'required_if:payment_method,credit_card|string|min:3|max:4',
            'card_name' => 'required_if:payment_method,credit_card|string|max:255',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator);
        }
    }

    /**
     * Get order by order number.
     *
     * @param string $orderNumber
     * @return Order|null
     */
    public function getOrderByNumber(string $orderNumber): ?Order
    {
        return Order::where('order_number', $orderNumber)->first();
    }

    /**
     * Cancel an order.
     *
     * @param Order $order
     * @param string|null $reason
     * @return Order
     */
    public function cancelOrder(Order $order, ?string $reason = null): Order
    {
        if ($order->status === 'completed') {
            throw new \InvalidArgumentException('Cannot cancel completed order');
        }

        if ($order->status === 'canceled') {
            throw new \InvalidArgumentException('Order is already canceled');
        }

        $notes = 'Order canceled';
        if ($reason) {
            $notes .= ': ' . $reason;
        }

        return $this->updateOrderStatus($order, 'canceled', $notes);
    }

    /**
     * Get shipping options for a cart.
     *
     * @param Cart $cart
     * @param array $address
     * @return array
     */
    public function getShippingOptions(Cart $cart, array $address): array
    {
        $subtotal = $cart->total ?? 0;
        $country = strtoupper($address['country'] ?? 'US');
        $state = strtoupper($address['state'] ?? '');

        $options = [];

        // Free shipping option
        if ($subtotal >= 100) {
            $options[] = [
                'id' => 'free',
                'name' => 'Free Shipping',
                'description' => 'Free standard shipping (5-7 business days)',
                'cost' => 0,
                'estimated_days' => '5-7',
            ];
        }

        // Standard shipping
        if ($country === 'US') {
            $cost = $subtotal >= 100 ? 0 : 9.99;
            $options[] = [
                'id' => 'standard',
                'name' => 'Standard Shipping',
                'description' => 'Standard shipping (5-7 business days)',
                'cost' => $cost,
                'estimated_days' => '5-7',
            ];

            // Express shipping for certain states
            $expressStates = ['CA', 'NY', 'TX', 'FL'];
            if (in_array($state, $expressStates)) {
                $options[] = [
                    'id' => 'express',
                    'name' => 'Express Shipping',
                    'description' => 'Express shipping (2-3 business days)',
                    'cost' => 14.99,
                    'estimated_days' => '2-3',
                ];
            }

            // Overnight shipping
            $options[] = [
                'id' => 'overnight',
                'name' => 'Overnight Shipping',
                'description' => 'Next business day delivery',
                'cost' => 24.99,
                'estimated_days' => '1',
            ];
        } else {
            // International shipping
            $options[] = [
                'id' => 'international',
                'name' => 'International Shipping',
                'description' => 'International shipping (10-14 business days)',
                'cost' => 29.99,
                'estimated_days' => '10-14',
            ];
        }

        return $options;
    }

    /**
     * Estimate delivery date based on shipping method.
     *
     * @param string $shippingMethod
     * @param array $address
     * @return \Carbon\Carbon
     */
    public function estimateDeliveryDate(string $shippingMethod, array $address): \Carbon\Carbon
    {
        $businessDays = match ($shippingMethod) {
            'overnight' => 1,
            'express' => 3,
            'standard', 'free' => 7,
            'international' => 14,
            default => 7,
        };

        return now()->addWeekdays($businessDays);
    }

    /**
     * Save shipping information to session.
     *
     * @param array $shippingInfo
     * @return void
     */
    public function saveShippingInfo(array $shippingInfo): void
    {
        session(['checkout.shipping' => $shippingInfo]);
    }

    /**
     * Save shipping method to session.
     *
     * @param string $shippingMethod
     * @return void
     */
    public function saveShippingMethod(string $shippingMethod): void
    {
        session(['checkout.shipping_method' => $shippingMethod]);
    }

    /**
     * Save billing information to session.
     *
     * @param array $billingInfo
     * @return void
     */
    public function saveBillingInfo(array $billingInfo): void
    {
        session(['checkout.billing' => $billingInfo]);
    }

    /**
     * Process payment and return result.
     *
     * @param array $paymentData
     * @return array
     */
    public function processPayment(array $paymentData): array
    {
        // Simulate payment processing
        $cardNumber = $paymentData['card_number'] ?? '';
        
        // Simulate declined card
        if ($cardNumber === '****************') {
            return [
                'success' => false,
                'error' => 'Your card was declined. Please try a different payment method.'
            ];
        }

        // Store payment info in session
        session(['checkout.payment' => $paymentData]);

        return ['success' => true];
    }

    /**
     * Complete the order using session data.
     *
     * @return Order
     */
    public function completeOrder(): Order
    {
        try {
            $cart = $this->cartService->getCart();
            
            if (!$cart || $cart->items()->count() === 0) {
                throw new \InvalidArgumentException('Cart is empty');
            }

            $shippingInfo = session('checkout.shipping');
            $shippingMethod = session('checkout.shipping_method', 'standard');
            $billingInfo = session('checkout.billing');
            $paymentData = session('checkout.payment');

            // Log session data for debugging
            \Log::debug('Checkout session data', [
                'cart' => $cart->toArray(),
                'cart_items' => $cart->items()->count(),
                'shipping_info' => $shippingInfo,
                'shipping_method' => $shippingMethod,
                'billing_info' => $billingInfo,
                'payment_data' => $paymentData
            ]);

            if (!$shippingInfo || !$paymentData) {
                throw new \InvalidArgumentException('Missing checkout information');
            }
        } catch (\Exception $e) {
            \Log::error('Error in completeOrder: ' . $e->getMessage(), [
                'exception' => $e,
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }

        return DB::transaction(function () use ($cart, $shippingInfo, $shippingMethod, $billingInfo, $paymentData) {
            // Calculate totals
            $totals = $this->calculateTotals();

            // Create order
            $order = Order::create([
                'user_id' => $cart->user_id,
                'order_number' => $this->generateOrderNumber(),
                'status' => 'processing',
                'payment_status' => 'paid',
                'subtotal' => $totals['subtotal'],
                'tax' => $totals['tax'],
                'shipping' => $totals['shipping'],
                'discount' => $totals['discount'] ?? 0,
                'discount_amount' => $totals['discount'] ?? 0,
                'total' => $totals['total'],
                'coupon_code' => session('applied_coupon'),
                'shipping_method' => $shippingMethod,
                'shipping_name' => $shippingInfo['name'],
                'shipping_email' => $shippingInfo['email'],
                'shipping_phone' => $shippingInfo['phone'],
                'shipping_address' => $shippingInfo['address'],
                'shipping_city' => $shippingInfo['city'],
                'shipping_state' => $shippingInfo['state'],
                'shipping_zipcode' => $shippingInfo['zipcode'],
                'shipping_country' => $shippingInfo['country'],
                'billing_name' => $billingInfo['same_as_shipping'] ?? true ? $shippingInfo['name'] : $billingInfo['billing_name'],
                'billing_email' => $billingInfo['same_as_shipping'] ?? true ? $shippingInfo['email'] : $billingInfo['billing_email'],
                'billing_phone' => $billingInfo['same_as_shipping'] ?? true ? $shippingInfo['phone'] : $billingInfo['billing_phone'],
                'billing_address' => $billingInfo['same_as_shipping'] ?? true ? $shippingInfo['address'] : $billingInfo['billing_address'],
                'billing_city' => $billingInfo['same_as_shipping'] ?? true ? $shippingInfo['city'] : $billingInfo['billing_city'],
                'billing_state' => $billingInfo['same_as_shipping'] ?? true ? $shippingInfo['state'] : $billingInfo['billing_state'],
                'billing_zipcode' => $billingInfo['same_as_shipping'] ?? true ? $shippingInfo['zipcode'] : $billingInfo['billing_zipcode'],
                'billing_country' => $billingInfo['same_as_shipping'] ?? true ? $shippingInfo['country'] : $billingInfo['billing_country'],
            ]);

            // Create order items
            foreach ($cart->items()->with('component')->get() as $cartItem) {
                $order->items()->create([
                    'component_id' => $cartItem->component_id,
                    'quantity' => $cartItem->quantity,
                    'price' => $cartItem->price,
                    'name' => $cartItem->component->name,
                ]);

                // Decrement stock
                $cartItem->component->decrement('stock', $cartItem->quantity);
            }

            // Create payment record
            Payment::create([
                'order_id' => $order->id,
                'payment_method' => $paymentData['payment_method'],
                'amount' => $order->total,
                'status' => 'completed',
                'transaction_id' => 'txn_' . uniqid(),
            ]);

            // Clear cart and session
            $cart->items()->delete();
            $cart->delete();
            session()->forget(['checkout', 'applied_coupon', 'discount_amount']);

            return $order;
        });
    }

    /**
     * Confirm price changes and update session.
     *
     * @return void
     */
    public function confirmPriceChanges(): void
    {
        // Update cart prices to current values
        $this->cartService->updateCartPrices();
        session(['checkout.price_changes_confirmed' => true]);
    }

    /**
     * Calculate checkout totals.
     *
     * @return array
     */
    public function calculateTotals(): array
    {
        $cart = $this->cartService->getCart();
        $subtotal = $cart->total ?? 0;
        
        // Get shipping method and calculate shipping cost
        $shippingMethod = session('checkout.shipping_method', 'standard');
        $shippingInfo = session('checkout.shipping', []);
        
        $shipping = match ($shippingMethod) {
            'express' => 19.99,
            'overnight' => 24.99,
            'standard' => $subtotal >= 100 ? 0 : 9.99,
            default => 9.99,
        };

        // Calculate tax based on shipping address
        $state = strtoupper($shippingInfo['state'] ?? '');
        $taxRate = match ($state) {
            'CA' => 0.0825,
            'NY' => 0.08,
            'TX' => 0.0625,
            default => 0.08,
        };
        
        $tax = round($subtotal * $taxRate, 2);
        
        // Apply discount if coupon is applied
        $discount = session('discount_amount', 0);
        
        $total = $subtotal + $tax + $shipping - $discount;

        return [
            'subtotal' => $subtotal,
            'tax' => $tax,
            'shipping' => $shipping,
            'discount' => $discount,
            'total' => round($total, 2),
        ];
    }
}