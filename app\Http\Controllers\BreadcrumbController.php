<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

class BreadcrumbController extends Controller
{
    public function generateBreadcrumb($path)
    {
        $segments = explode('/', $path);
        $breadcrumbs = [];
        $url = '';

        foreach ($segments as $index => $segment) {
            // if ($segment === '' || $segment === null) continue;
            $url .= '/' . $segment;

            // Special case for 'pincodes' segment
            $title = $segment;
            if ($segment === 'pincodes') {
                $title = 'State Wise Pin Codes';
            } else {
                $title = urldecode($segment);
            }

            $breadcrumbs[] = [
                'title' => $title,
                'name' => urldecode($segment),
                'url' => url($url)
            ];
        }

        return $breadcrumbs;
    }
}
