<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\EmailLog;
use App\Models\Order;
use App\Models\Build;
use App\Models\User;
use Carbon\Carbon;

class EmailLogSeeder extends Seeder
{
    public function run(): void
    {
        $orders = Order::all();
        $builds = Build::all();
        $users = User::all();

        // Create email logs for orders
        foreach ($orders as $order) {
            $this->createOrderEmailLogs($order);
        }

        // Create email logs for builds
        foreach ($builds->take(10) as $build) {
            $this->createBuildEmailLogs($build);
        }

        // Create some general email logs
        foreach ($users->take(20) as $user) {
            $this->createGeneralEmailLogs($user);
        }
    }

    private function createOrderEmailLogs(Order $order): void
    {
        $emailTypes = [
            'order_confirmation' => 'Order Confirmation - #' . $order->order_number,
            'order_status_update' => 'Order Status Update - #' . $order->order_number,
            'payment_confirmation' => 'Payment Confirmation - #' . $order->order_number,
            'shipping_notification' => 'Your Order Has Been Shipped - #' . $order->order_number,
            'delivery_confirmation' => 'Order Delivered - #' . $order->order_number,
        ];

        foreach ($emailTypes as $type => $subject) {
            // Skip certain emails based on order status
            if ($type === 'shipping_notification' && !in_array($order->status, [Order::STATUS_SHIPPED, Order::STATUS_DELIVERED, Order::STATUS_COMPLETED])) {
                continue;
            }
            if ($type === 'delivery_confirmation' && !in_array($order->status, [Order::STATUS_DELIVERED, Order::STATUS_COMPLETED])) {
                continue;
            }

            $status = $this->getRandomEmailStatus();
            $sentAt = $status === 'sent' ? $order->created_at->addMinutes(rand(1, 60)) : null;
            $deliveredAt = $status === 'sent' && rand(1, 100) <= 95 ? $sentAt->addMinutes(rand(1, 10)) : null;
            $failedAt = $status === 'failed' ? $order->created_at->addMinutes(rand(1, 30)) : null;

            EmailLog::create([
                'type' => $type,
                'recipient' => $order->billing_email,
                'subject' => $subject,
                'status' => $status,
                'related_id' => $order->id,
                'related_type' => Order::class,
                'attempts' => $status === 'failed' ? rand(1, 3) : 1,
                'error_message' => $status === 'failed' ? $this->getRandomErrorMessage() : null,
                'sent_at' => $sentAt,
                'delivered_at' => $deliveredAt,
                'failed_at' => $failedAt,
                'created_at' => $order->created_at->addMinutes(rand(1, 120)),
            ]);
        }
    }

    private function createBuildEmailLogs(Build $build): void
    {
        if (!$build->user) return;

        $emailTypes = [
            'build_shared' => 'Your PC Build Has Been Shared',
            'build_compatibility_alert' => 'Compatibility Issues Found in Your Build',
            'build_price_alert' => 'Price Changes in Your Build Components',
        ];

        foreach ($emailTypes as $type => $subject) {
            // Only create some emails randomly
            if (rand(1, 100) <= 40) {
                $status = $this->getRandomEmailStatus();
                $createdAt = $build->created_at->addDays(rand(1, 30));
                $sentAt = $status === 'sent' ? $createdAt->addMinutes(rand(1, 30)) : null;
                $deliveredAt = $status === 'sent' && rand(1, 100) <= 90 ? $sentAt->addMinutes(rand(1, 5)) : null;
                $failedAt = $status === 'failed' ? $createdAt->addMinutes(rand(1, 15)) : null;

                EmailLog::create([
                    'type' => $type,
                    'recipient' => $build->user->email,
                    'subject' => $subject,
                    'status' => $status,
                    'related_id' => $build->id,
                    'related_type' => Build::class,
                    'attempts' => $status === 'failed' ? rand(1, 2) : 1,
                    'error_message' => $status === 'failed' ? $this->getRandomErrorMessage() : null,
                    'sent_at' => $sentAt,
                    'delivered_at' => $deliveredAt,
                    'failed_at' => $failedAt,
                    'created_at' => $createdAt,
                ]);
            }
        }
    }

    private function createGeneralEmailLogs(User $user): void
    {
        $emailTypes = [
            'welcome' => 'Welcome to PC Builder!',
            'newsletter' => 'Weekly PC Building Tips',
            'promotional' => 'Special Offers on PC Components',
            'password_reset' => 'Password Reset Request',
            'account_verification' => 'Please Verify Your Account',
        ];

        // Create 1-3 general emails per user
        $numEmails = rand(1, 3);
        $selectedTypes = array_rand($emailTypes, min($numEmails, count($emailTypes)));
        if (!is_array($selectedTypes)) $selectedTypes = [$selectedTypes];

        foreach ($selectedTypes as $type) {
            $subject = $emailTypes[$type];
            
            $status = $this->getRandomEmailStatus();
            $createdAt = Carbon::now()->subDays(rand(1, 90));
            $sentAt = $status === 'sent' ? $createdAt->addMinutes(rand(1, 30)) : null;
            $deliveredAt = $status === 'sent' && rand(1, 100) <= 92 ? $sentAt->addMinutes(rand(1, 10)) : null;
            $failedAt = $status === 'failed' ? $createdAt->addMinutes(rand(1, 20)) : null;

            EmailLog::create([
                'type' => $type,
                'recipient' => $user->email,
                'subject' => $subject,
                'status' => $status,
                'related_id' => null,
                'related_type' => null,
                'attempts' => $status === 'failed' ? rand(1, 3) : 1,
                'error_message' => $status === 'failed' ? $this->getRandomErrorMessage() : null,
                'sent_at' => $sentAt,
                'delivered_at' => $deliveredAt,
                'failed_at' => $failedAt,
                'created_at' => $createdAt,
            ]);
        }
    }

    private function getRandomEmailStatus(): string
    {
        $statuses = ['sent', 'failed', 'pending'];
        $weights = [85, 10, 5]; // 85% sent, 10% failed, 5% pending
        
        $rand = rand(1, 100);
        $cumulative = 0;

        foreach ($weights as $index => $weight) {
            $cumulative += $weight;
            if ($rand <= $cumulative) {
                return $statuses[$index];
            }
        }

        return 'sent';
    }

    private function getRandomErrorMessage(): string
    {
        $errors = [
            'SMTP connection failed',
            'Invalid email address',
            'Mailbox full',
            'Email server timeout',
            'Recipient domain not found',
            'Message rejected by spam filter',
            'Daily sending limit exceeded',
            'Authentication failed',
            'Connection refused by server',
            'Temporary server error',
        ];

        return $errors[array_rand($errors)];
    }
}