<?php

namespace Tests\Feature\Integration;

use App\Models\Coupon;
use App\Models\CouponUsage;
use App\Models\Product;
use App\Models\ProductCategory;
use App\Models\User;
use App\Services\CouponService;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Auth;
use Tests\TestCase;

class CouponIntegrationTest extends TestCase
{
    use RefreshDatabase;

    protected CouponService $couponService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->artisan('migrate');
        $this->couponService = new CouponService();
    }

    /** @test */
    public function it_handles_complete_coupon_lifecycle()
    {
        $user = User::factory()->create();
        Auth::login($user);

        $product = Product::factory()->create(['sale_price' => null, 'price' => 1000, 'sale_price' => null]);

        // Create coupon
        $coupon = Coupon::create([
            'code' => 'LIFECYCLE20',
            'name' => 'Lifecycle Test',
            'type' => 'percentage',
            'value' => 20,
            'usage_limit' => 5,
            'usage_limit_per_user' => 2,
            'is_active' => true,
            'expires_at' => now()->addMonth(),
        ]);

        // Test initial state
        $this->assertTrue($coupon->isValid());
        $this->assertTrue($coupon->canBeUsedBy($user->id));
        $this->assertEquals('Active', $coupon->status);

        // Apply coupon
        $cartItems = [['product_id' => $product->id, 'quantity' => 1]];
        $result = $this->couponService->validateAndApplyCoupon('LIFECYCLE20', $cartItems);

        $this->assertTrue($result['valid']);
        $this->assertEquals(200, $result['discount']); // 20% of 1000
        $this->assertEquals(800, $result['final_total']);

        // Simulate actual usage (without order_id since orders don't exist in test)
        $discount = $coupon->apply($user->id, 1000);
        $this->assertEquals(200, $discount);
        $this->assertEquals(1, $coupon->fresh()->used_count);

        // Check usage record
        $this->assertDatabaseHas('coupon_usages', [
            'coupon_id' => $coupon->id,
            'user_id' => $user->id,
            'discount_amount' => 200,
        ]);

        // User can still use it once more
        $this->assertTrue($coupon->fresh()->canBeUsedBy($user->id));

        // Use it again
        $coupon->apply($user->id, 1000);
        $this->assertEquals(2, $coupon->fresh()->used_count);

        // User has reached per-user limit
        $this->assertFalse($coupon->fresh()->canBeUsedBy($user->id));

        // Other users can still use it
        $user2 = User::factory()->create();
        $this->assertTrue($coupon->fresh()->canBeUsedBy($user2->id));
    }

    /** @test */
    public function it_handles_category_based_coupons()
    {
        $user = User::factory()->create();
        Auth::login($user);

        // Create category hierarchy
        $electronics = ProductCategory::factory()->create(['name' => 'Electronics']);
        $smartphones = ProductCategory::factory()->create([
            'name' => 'Smartphones',
            'parent_id' => $electronics->id,
        ]);
        $clothing = ProductCategory::factory()->create(['name' => 'Clothing']);

        // Create products
        $phone = Product::factory()->create(['sale_price' => null, 
            'price' => 50000,
            'category_id' => $smartphones->id,
        ]);
        $laptop = Product::factory()->create(['sale_price' => null, 
            'price' => 80000,
            'category_id' => $electronics->id,
        ]);
        $shirt = Product::factory()->create(['sale_price' => null, 
            'price' => 2000,
            'category_id' => $clothing->id,
        ]);

        // Create category-specific coupon
        $electronicsCoupon = Coupon::create([
            'code' => 'ELECTRONICS15',
            'name' => 'Electronics 15% Off',
            'type' => 'percentage',
            'value' => 15,
            'applicable_categories' => [$electronics->id],
            'is_active' => true,
        ]);

        // Test on electronics product (should work)
        $cartItems = [['product_id' => $laptop->id, 'quantity' => 1]];
        $result = $this->couponService->validateAndApplyCoupon('ELECTRONICS15', $cartItems);
        $this->assertTrue($result['valid']);
        $this->assertEquals(12000, $result['discount']); // 15% of 80000

        // Test on smartphone (child category, should work)
        $cartItems = [['product_id' => $phone->id, 'quantity' => 1]];
        $result = $this->couponService->validateAndApplyCoupon('ELECTRONICS15', $cartItems);
        $this->assertTrue($result['valid']);
        $this->assertEquals(7500, $result['discount']); // 15% of 50000

        // Test on clothing (should fail)
        $cartItems = [['product_id' => $shirt->id, 'quantity' => 1]];
        $result = $this->couponService->validateAndApplyCoupon('ELECTRONICS15', $cartItems);
        $this->assertFalse($result['valid']);

        // Test mixed cart (should work if at least one item is eligible)
        $cartItems = [
            ['product_id' => $phone->id, 'quantity' => 1],
            ['product_id' => $shirt->id, 'quantity' => 1],
        ];
        $result = $this->couponService->validateAndApplyCoupon('ELECTRONICS15', $cartItems);
        $this->assertTrue($result['valid']);
    }

    /** @test */
    public function it_handles_product_exclusions()
    {
        $user = User::factory()->create();
        Auth::login($user);

        $category = ProductCategory::factory()->create();
        $product1 = Product::factory()->create(['sale_price' => null, 
            'price' => 1000,
            'category_id' => $category->id,
        ]);
        $product2 = Product::factory()->create(['sale_price' => null, 
            'price' => 1000,
            'category_id' => $category->id,
        ]);
        $product3 = Product::factory()->create(['sale_price' => null, 'price' => 1000, 'sale_price' => null]);

        // Coupon applicable to category but excluding specific product
        $coupon = Coupon::create([
            'code' => 'CATEXCLUDE',
            'name' => 'Category with Exclusion',
            'type' => 'fixed',
            'value' => 100,
            'applicable_categories' => [$category->id],
            'excluded_products' => [$product1->id],
            'is_active' => true,
        ]);

        // Test excluded product (should fail)
        $cartItems = [['product_id' => $product1->id, 'quantity' => 1]];
        $result = $this->couponService->validateAndApplyCoupon('CATEXCLUDE', $cartItems);
        $this->assertFalse($result['valid']);

        // Test included product in same category (should work)
        $cartItems = [['product_id' => $product2->id, 'quantity' => 1]];
        $result = $this->couponService->validateAndApplyCoupon('CATEXCLUDE', $cartItems);
        $this->assertTrue($result['valid']);

        // Test product not in category (should fail)
        $cartItems = [['product_id' => $product3->id, 'quantity' => 1]];
        $result = $this->couponService->validateAndApplyCoupon('CATEXCLUDE', $cartItems);
        $this->assertFalse($result['valid']);
    }

    /** @test */
    public function it_handles_time_based_coupon_validity()
    {
        $user = User::factory()->create();
        Auth::login($user);

        $product = Product::factory()->create(['sale_price' => null, 'price' => 1000, 'sale_price' => null]);
        $now = Carbon::now();

        // Future coupon (not started yet)
        $futureCoupon = Coupon::create([
            'code' => 'FUTURE',
            'name' => 'Future Coupon',
            'type' => 'fixed',
            'value' => 100,
            'starts_at' => $now->copy()->addDay(),
            'expires_at' => $now->copy()->addWeek(),
            'is_active' => true,
        ]);

        // Expired coupon
        $expiredCoupon = Coupon::create([
            'code' => 'EXPIRED',
            'name' => 'Expired Coupon',
            'type' => 'fixed',
            'value' => 100,
            'starts_at' => $now->copy()->subWeek(),
            'expires_at' => $now->copy()->subDay(),
            'is_active' => true,
        ]);

        // Active coupon
        $activeCoupon = Coupon::create([
            'code' => 'ACTIVE',
            'name' => 'Active Coupon',
            'type' => 'fixed',
            'value' => 100,
            'starts_at' => $now->copy()->subDay(),
            'expires_at' => $now->copy()->addDay(),
            'is_active' => true,
        ]);

        $cartItems = [['product_id' => $product->id, 'quantity' => 1]];

        // Test future coupon
        $result = $this->couponService->validateAndApplyCoupon('FUTURE', $cartItems);
        $this->assertFalse($result['valid']);
        $this->assertEquals('Scheduled', $futureCoupon->status);

        // Test expired coupon
        $result = $this->couponService->validateAndApplyCoupon('EXPIRED', $cartItems);
        $this->assertFalse($result['valid']);
        $this->assertEquals('Expired', $expiredCoupon->status);

        // Test active coupon
        $result = $this->couponService->validateAndApplyCoupon('ACTIVE', $cartItems);
        $this->assertTrue($result['valid']);
        $this->assertEquals('Active', $activeCoupon->status);
    }

    /** @test */
    public function it_handles_minimum_amount_requirements()
    {
        $user = User::factory()->create();
        Auth::login($user);

        $product = Product::factory()->create(['sale_price' => null, 'price' => 500]);

        $coupon = Coupon::create([
            'code' => 'MIN1000',
            'name' => 'Minimum 1000',
            'type' => 'percentage',
            'value' => 20,
            'minimum_amount' => 1000,
            'is_active' => true,
        ]);

        // Test below minimum (should fail)
        $cartItems = [['product_id' => $product->id, 'quantity' => 1]]; // 500
        $result = $this->couponService->validateAndApplyCoupon('MIN1000', $cartItems);
        $this->assertFalse($result['valid']);
        $this->assertStringContainsString('Minimum order amount', $result['message']);

        // Test at minimum (should work)
        $cartItems = [['product_id' => $product->id, 'quantity' => 2]]; // 1000
        $result = $this->couponService->validateAndApplyCoupon('MIN1000', $cartItems);
        $this->assertTrue($result['valid']);
        $this->assertEquals(200, $result['discount']); // 20% of 1000

        // Test above minimum (should work)
        $cartItems = [['product_id' => $product->id, 'quantity' => 3]]; // 1500
        $result = $this->couponService->validateAndApplyCoupon('MIN1000', $cartItems);
        $this->assertTrue($result['valid']);
        $this->assertEquals(300, $result['discount']); // 20% of 1500
    }

    /** @test */
    public function it_handles_maximum_discount_limits()
    {
        $user = User::factory()->create();
        Auth::login($user);

        $product = Product::factory()->create(['sale_price' => null, 'price' => 10000]);

        $coupon = Coupon::create([
            'code' => 'MAX500',
            'name' => 'Max Discount 500',
            'type' => 'percentage',
            'value' => 20,
            'maximum_discount' => 500,
            'is_active' => true,
        ]);

        // Test with small amount (no max limit hit)
        $cartItems = [['product_id' => $product->id, 'quantity' => 1]]; // 10000
        $result = $this->couponService->validateAndApplyCoupon('MAX500', $cartItems);
        $this->assertTrue($result['valid']);
        $this->assertEquals(500, $result['discount']); // Would be 2000 (20%), but capped at 500

        // Test with larger amount (max limit should still apply)
        $product2 = Product::factory()->create(['sale_price' => null, 'price' => 20000]);
        $cartItems = [['product_id' => $product2->id, 'quantity' => 1]]; // 20000
        $result = $this->couponService->validateAndApplyCoupon('MAX500', $cartItems);
        $this->assertTrue($result['valid']);
        $this->assertEquals(500, $result['discount']); // Would be 4000 (20%), but capped at 500
    }

    /** @test */
    public function it_handles_concurrent_coupon_usage()
    {
        $users = User::factory()->count(3)->create();
        $product = Product::factory()->create(['sale_price' => null, 'price' => 1000, 'sale_price' => null]);

        $coupon = Coupon::create([
            'code' => 'CONCURRENT',
            'name' => 'Concurrent Test',
            'type' => 'fixed',
            'value' => 100,
            'usage_limit' => 2,
            'usage_limit_per_user' => 1,
            'is_active' => true,
        ]);

        // Simulate concurrent usage
        $results = [];
        foreach ($users as $user) {
            try {
                $discount = $coupon->apply($user->id, 1000);
                $results[] = ['user_id' => $user->id, 'discount' => $discount, 'success' => true];
            } catch (\Exception $e) {
                $results[] = ['user_id' => $user->id, 'error' => $e->getMessage(), 'success' => false];
            }
        }

        // Only 2 users should succeed due to usage limit
        $successful = collect($results)->where('success', true);
        $failed = collect($results)->where('success', false);

        $this->assertEquals(2, $successful->count());
        $this->assertEquals(1, $failed->count());
        $this->assertEquals(2, $coupon->fresh()->used_count);
    }

    /** @test */
    public function it_provides_applicable_coupons_for_cart()
    {
        $user = User::factory()->create();
        Auth::login($user);

        $category = ProductCategory::factory()->create();
        $product1 = Product::factory()->create(['sale_price' => null, 
            'price' => 1000,
            'category_id' => $category->id,
        ]);
        $product2 = Product::factory()->create(['sale_price' => null, 'price' => 500]);

        // Create various coupons
        $generalCoupon = Coupon::factory()->create([
            'code' => 'GENERAL10',
            'name' => 'General 10%',
            'type' => 'percentage',
            'value' => 10,
            'is_active' => true,
        ]);

        $categoryCoupon = Coupon::factory()->create([
            'code' => 'CATEGORY20',
            'name' => 'Category 20%',
            'type' => 'percentage',
            'value' => 20,
            'applicable_categories' => [$category->id],
            'is_active' => true,
        ]);

        $minAmountCoupon = Coupon::factory()->create([
            'code' => 'BIG2000',
            'name' => 'Big Order',
            'type' => 'fixed',
            'value' => 200,
            'minimum_amount' => 2000,
            'is_active' => true,
        ]);

        $inactiveCoupon = Coupon::factory()->create([
            'code' => 'INACTIVE',
            'is_active' => false,
        ]);

        // Test with cart containing category product
        $applicable = $this->couponService->getApplicableCoupons([$product1->id], 1000);

        $codes = collect($applicable)->pluck('code')->toArray();
        $this->assertContains('GENERAL10', $codes);
        $this->assertContains('CATEGORY20', $codes);
        $this->assertNotContains('BIG2000', $codes); // Minimum amount not met
        $this->assertNotContains('INACTIVE', $codes); // Inactive

        // Test with higher cart value
        $applicable = $this->couponService->getApplicableCoupons([$product1->id, $product2->id], 1500);

        $codes = collect($applicable)->pluck('code')->toArray();
        $this->assertContains('GENERAL10', $codes);
        $this->assertContains('CATEGORY20', $codes);
        $this->assertNotContains('BIG2000', $codes); // Still below 2000

        // Test with cart meeting minimum amount
        $applicable = $this->couponService->getApplicableCoupons([$product1->id], 2000);

        $codes = collect($applicable)->pluck('code')->toArray();
        $this->assertContains('GENERAL10', $codes);
        $this->assertContains('CATEGORY20', $codes);
        $this->assertContains('BIG2000', $codes); // Now meets minimum
    }

    /** @test */
    public function it_tracks_coupon_usage_statistics()
    {
        $users = User::factory()->count(5)->create();
        $product = Product::factory()->create(['sale_price' => null, 'price' => 1000, 'sale_price' => null]);

        $coupon = Coupon::create([
            'code' => 'STATS',
            'name' => 'Statistics Test',
            'type' => 'percentage',
            'value' => 10,
            'usage_limit' => 10,
            'is_active' => true,
        ]);

        // Simulate multiple usages
        $totalDiscount = 0;
        foreach ($users as $index => $user) {
            $discount = $coupon->apply($user->id, 1000 + ($index * 100));
            $totalDiscount += $discount;
        }

        $stats = $this->couponService->getCouponStats($coupon);

        $this->assertEquals(5, $stats['total_usage']);
        $this->assertEquals(5, $stats['remaining_usage']); // 10 - 5
        $this->assertEquals(50.0, $stats['usage_percentage']); // 5/10 * 100
        $this->assertEquals($totalDiscount, $stats['total_discount_given']);
        $this->assertEquals(5, $stats['unique_users']);
        $this->assertCount(5, $stats['recent_usages']);
    }

    /** @test */
    public function it_generates_unique_coupon_codes()
    {
        // Create some existing codes
        Coupon::factory()->create(['code' => 'EXISTING1']);
        Coupon::factory()->create(['code' => 'EXISTING2']);

        $codes = [];
        for ($i = 0; $i < 10; $i++) {
            $code = $this->couponService->generateUniqueCode();
            $codes[] = $code;
            
            // Verify uniqueness
            $this->assertTrue($this->couponService->isCodeAvailable($code));
            $this->assertEquals(8, strlen($code)); // Default length
            $this->assertMatchesRegularExpression('/^[A-Z0-9]+$/', $code);
        }

        // All generated codes should be unique
        $this->assertEquals(10, count(array_unique($codes)));
    }
}