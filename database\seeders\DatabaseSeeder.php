<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        $this->call([
            // Core system data
            UserSeeder::class,
            SettingsSeeder::class,
            GatewaySettingsSeeder::class,
            
            // Component and product data
            ComponentCategorySeeder::class,
            ComponentSeeder::class,
            ComponentCompatibilitySeeder::class,
            ProductCategorySeeder::class,
            ProductSeeder::class,
            
            // Build system
            BuildSeeder::class,
            BuildComponentSeeder::class,
            
            // E-commerce data
            CartSeeder::class,
            OrderSeeder::class,
            PaymentSeeder::class,
            TransactionSeeder::class,
            
            // Coupon system
            CouponSeeder::class,
            CouponUsageSeeder::class,
            
            // Inventory management
            PriceHistorySeeder::class,
            StockMovementSeeder::class,
            InventoryAlertSeeder::class,
            
            // Communication logs
            EmailLogSeeder::class,
            
            // Blog system (optional - uncomment if needed)
            // BlogPostCategorySeeder::class,
            // BlogPostTagSeeder::class,
            // BlogPostSeeder::class,
            // CommentSeeder::class,
        ]);
    }
}
