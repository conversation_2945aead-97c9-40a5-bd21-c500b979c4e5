<?php

namespace Tests\Browser\Components;

use <PERSON><PERSON>\Dusk\Browser;
use <PERSON><PERSON>\Dusk\Component as BaseComponent;

class PaymentGatewaySelector extends BaseComponent
{
    /**
     * Get the root selector for the component.
     */
    public function selector(): string
    {
        return '.payment-gateway-selector';
    }

    /**
     * Assert that the browser page contains the component.
     */
    public function assert(Browser $browser): void
    {
        $browser->assertPresent($this->selector());
    }

    /**
     * Get the element shortcuts for the component.
     */
    public function elements(): array
    {
        return [
            '@razorpay' => '.gateway-option[data-gateway="razorpay"]',
            '@payumoney' => '.gateway-option[data-gateway="payumoney"]',
            '@cashfree' => '.gateway-option[data-gateway="cashfree"]',
            '@selectedGateway' => '.gateway-option.selected',
            '@gatewayLogo' => '.gateway-logo',
            '@gatewayName' => '.gateway-name',
            '@gatewayDescription' => '.gateway-description',
        ];
    }

    /**
     * Select a payment gateway.
     */
    public function selectGateway(Browser $browser, string $gateway): void
    {
        $browser->click("@{$gateway}")
                ->waitFor('@selectedGateway')
                ->assertPresent('@selectedGateway');
    }

    /**
     * Assert that a specific gateway is selected.
     */
    public function assertGatewaySelected(Browser $browser, string $gateway): void
    {
        $browser->assertPresent(".gateway-option[data-gateway=\"{$gateway}\"].selected");
    }
}