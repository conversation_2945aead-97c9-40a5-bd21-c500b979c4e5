<!-- Latest Builds Section -->
<section class="py-20 relative overflow-hidden">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div class="text-center mb-16">
            <h2 class="tech-font text-4xl font-bold mb-4 text-nexus-primary-400">LATEST CUSTOM BUILDS</h2>
            <p class="text-nexus-gray-400 max-w-3xl mx-auto">Check out some of our recent custom PC builds created for gamers, content creators, and professionals.</p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
            <!-- Build 1 -->
            <div class="build-card rounded-xl overflow-hidden bg-gradient-to-br from-nexus-dark-800 to-nexus-dark-900 border border-nexus-border-blue-light hover:border-nexus-border-blue-strong transition-all duration-300 hover:shadow-glow-blue group">
                <div class="relative">
                    <img src="https://images.unsplash.com/photo-1587202372775-e229f172b9d7?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=2070&q=80" alt="Gaming PC Build" class="w-full h-56 object-cover">
                    <div class="absolute top-3 right-3 bg-nexus-primary-600 text-white text-xs font-bold px-2 py-1 rounded tech-font">GAMING</div>
                </div>
                <div class="p-6">
                    <h3 class="tech-font text-xl font-bold mb-2 text-white">Frost Demon Gaming Rig</h3>
                    <div class="flex flex-wrap gap-2 mb-4">
                        <span class="text-xs bg-nexus-dark-700 text-nexus-primary-400 px-2 py-1 rounded">RTX 4080</span>
                        <span class="text-xs bg-nexus-dark-700 text-nexus-primary-400 px-2 py-1 rounded">i9-13900K</span>
                        <span class="text-xs bg-nexus-dark-700 text-nexus-primary-400 px-2 py-1 rounded">32GB DDR5</span>
                        <span class="text-xs bg-nexus-dark-700 text-nexus-primary-400 px-2 py-1 rounded">Custom Loop</span>
                    </div>
                    <p class="text-nexus-gray-400 text-sm mb-4">A high-performance gaming PC with custom water cooling, designed for 4K gaming and streaming.</p>
                    <button class="text-nexus-primary-400 text-sm tech-font flex items-center hover:text-nexus-primary-300 transition-colors">
                        VIEW DETAILS
                        <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
                        </svg>
                    </button>
                </div>
            </div>

            <!-- Build 2 -->
            <div class="build-card rounded-xl overflow-hidden bg-gradient-to-br from-nexus-dark-800 to-nexus-dark-900 border border-nexus-border-purple-light hover:border-nexus-border-purple-strong transition-all duration-300 hover:shadow-glow-purple group">
                <div class="relative">
                    <img src="https://images.unsplash.com/photo-1624705013726-8cb4f9415f40?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=2070&q=80" alt="Content Creator PC Build" class="w-full h-56 object-cover">
                    <div class="absolute top-3 right-3 bg-nexus-accent-600 text-white text-xs font-bold px-2 py-1 rounded tech-font">CREATOR</div>
                </div>
                <div class="p-6">
                    <h3 class="tech-font text-xl font-bold mb-2 text-white">Nebula Studio Workstation</h3>
                    <div class="flex flex-wrap gap-2 mb-4">
                        <span class="text-xs bg-nexus-dark-700 text-nexus-accent-400 px-2 py-1 rounded">RTX 4090</span>
                        <span class="text-xs bg-nexus-dark-700 text-nexus-accent-400 px-2 py-1 rounded">Ryzen 9 7950X</span>
                        <span class="text-xs bg-nexus-dark-700 text-nexus-accent-400 px-2 py-1 rounded">64GB DDR5</span>
                        <span class="text-xs bg-nexus-dark-700 text-nexus-accent-400 px-2 py-1 rounded">8TB Storage</span>
                    </div>
                    <p class="text-nexus-gray-400 text-sm mb-4">A powerhouse workstation optimized for video editing, 3D rendering, and content creation workflows.</p>
                    <button class="text-nexus-accent-400 text-sm tech-font flex items-center hover:text-nexus-accent-300 transition-colors">
                        VIEW DETAILS
                        <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
                        </svg>
                    </button>
                </div>
            </div>

            <!-- Build 3 -->
            <div class="build-card rounded-xl overflow-hidden bg-gradient-to-br from-nexus-dark-800 to-nexus-dark-900 border border-nexus-border-green-light hover:border-nexus-border-green-strong transition-all duration-300 hover:shadow-glow-green group">
                <div class="relative">
                    <img src="https://images.unsplash.com/photo-1593640408182-31c70c8268f5?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=2042&q=80" alt="Compact PC Build" class="w-full h-56 object-cover">
                    <div class="absolute top-3 right-3 bg-nexus-success-600 text-white text-xs font-bold px-2 py-1 rounded tech-font">COMPACT</div>
                </div>
                <div class="p-6">
                    <h3 class="tech-font text-xl font-bold mb-2 text-white">Quantum Mini ITX</h3>
                    <div class="flex flex-wrap gap-2 mb-4">
                        <span class="text-xs bg-nexus-dark-700 text-nexus-success-400 px-2 py-1 rounded">RTX 4070</span>
                        <span class="text-xs bg-nexus-dark-700 text-nexus-success-400 px-2 py-1 rounded">i7-13700K</span>
                        <span class="text-xs bg-nexus-dark-700 text-nexus-success-400 px-2 py-1 rounded">32GB DDR5</span>
                        <span class="text-xs bg-nexus-dark-700 text-nexus-success-400 px-2 py-1 rounded">SFF Case</span>
                    </div>
                    <p class="text-nexus-gray-400 text-sm mb-4">A compact yet powerful small form factor build that delivers high performance in a minimal footprint.</p>
                    <button class="text-nexus-success-400 text-sm tech-font flex items-center hover:text-nexus-success-300 transition-colors">
                        VIEW DETAILS
                        <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
                        </svg>
                    </button>
                </div>
            </div>
        </div>

        <div class="text-center mt-12">
            <button class="px-6 py-3 bg-transparent border-2 border-nexus-primary-500 rounded-lg font-semibold tech-font text-sm hover:bg-nexus-bg-blue-light transition-all duration-300 transform hover:scale-105">
                VIEW ALL BUILDS
            </button>
        </div>
    </div>
</section>