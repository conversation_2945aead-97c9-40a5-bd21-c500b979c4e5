/* Nexus PC Theme - Custom CSS Utilities */

/* Font Imports */
@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Inter:wght@300;400;500;600&display=swap');

/* Base Utility Classes */
.tech-font {
    font-family: 'Orbitron', monospace;
}

.content-font {
    font-family: 'Inter', sans-serif;
}

/* Cyber Grid Background */
.cyber-grid {
    background-image: 
        linear-gradient(rgba(59, 130, 246, 0.1) 1px, transparent 1px),
        linear-gradient(90deg, rgba(59, 130, 246, 0.1) 1px, transparent 1px);
    background-size: 20px 20px;
    animation: grid-move 20s linear infinite;
}

/* Glow Effects */
.glow {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
}

.glow-cyan {
    box-shadow: 0 0 20px rgba(6, 182, 212, 0.3);
}

.glow-purple {
    box-shadow: 0 0 20px rgba(168, 85, 247, 0.3);
}

.glow-green {
    box-shadow: 0 0 20px rgba(34, 197, 94, 0.3);
}

/* Neon Text Effect */
.neon-text {
    text-shadow: 0 0 10px rgba(59, 130, 246, 0.5);
}

.neon-text-cyan {
    text-shadow: 0 0 10px rgba(6, 182, 212, 0.5);
}

.neon-text-purple {
    text-shadow: 0 0 10px rgba(168, 85, 247, 0.5);
}

/* Component Hover Effects */
.component-icon {
    transition: all 0.3s ease;
    position: relative;
}

.component-icon:hover {
    transform: scale(1.1);
    filter: drop-shadow(0 0 15px rgba(59, 130, 246, 0.6));
}

/* Card Backgrounds */
.stat-card {
    background: linear-gradient(135deg, rgba(15, 23, 42, 0.8) 0%, rgba(30, 41, 59, 0.8) 100%);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(59, 130, 246, 0.2);
}

.feature-card {
    background: linear-gradient(135deg, rgba(30, 41, 59, 1) 0%, rgba(15, 23, 42, 1) 100%);
}

.testimonial-card {
    background: linear-gradient(135deg, rgba(30, 41, 59, 0.5) 0%, rgba(15, 23, 42, 0.5) 100%);
}

.tech-card {
    background: linear-gradient(135deg, rgba(30, 41, 59, 1) 0%, rgba(15, 23, 42, 1) 100%);
}

.build-card {
    background: linear-gradient(135deg, rgba(30, 41, 59, 1) 0%, rgba(15, 23, 42, 1) 100%);
}

/* Tooltip Styling */
.tooltip {
    position: absolute;
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 12px 16px;
    border-radius: 8px;
    font-size: 12px;
    border: 1px solid rgba(59, 130, 246, 0.3);
    opacity: 0;
    pointer-events: none;
    transform: translateY(10px);
    transition: all 0.3s ease;
    z-index: 1000;
    min-width: 200px;
}

.tooltip.show {
    opacity: 1;
    transform: translateY(0);
}

/* Animation Classes */
.floating {
    animation: float 6s ease-in-out infinite;
}

.pulse-ring {
    animation: pulse-ring 2s infinite;
}

/* Keyframe Animations */
@keyframes grid-move {
    0% {
        background-position: 0 0;
    }
    100% {
        background-position: 20px 20px;
    }
}

@keyframes pulse-ring {
    0% {
        transform: scale(0.8);
        opacity: 1;
    }
    100% {
        transform: scale(1.2);
        opacity: 0;
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-20px);
    }
}

/* Responsive Design Helpers */
@media (max-width: 768px) {
    .cyber-grid {
        background-size: 15px 15px;
    }
    
    .glow {
        box-shadow: 0 0 15px rgba(59, 130, 246, 0.2);
    }
}

/* Dark Mode Specific Styles */
.dark .stat-card {
    background: linear-gradient(135deg, rgba(10, 15, 28, 0.8) 0%, rgba(30, 41, 59, 0.8) 100%);
}

.dark .feature-card {
    background: linear-gradient(135deg, rgba(30, 41, 59, 1) 0%, rgba(10, 15, 28, 1) 100%);
}

/* Utility Classes for Common Patterns */
.nexus-gradient-primary {
    background: linear-gradient(to right, theme('colors.nexus.primary.600'), theme('colors.nexus.secondary.600'));
}

.nexus-gradient-accent {
    background: linear-gradient(to right, theme('colors.nexus.accent.600'), theme('colors.nexus.primary.600'));
}

.nexus-text-gradient {
    background: linear-gradient(to right, theme('colors.nexus.primary.400'), theme('colors.nexus.secondary.400'), theme('colors.nexus.primary.600'));
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
}

.nexus-border-glow {
    border: 1px solid rgba(59, 130, 246, 0.3);
    box-shadow: 0 0 10px rgba(59, 130, 246, 0.1);
}

.nexus-hover-glow {
    transition: all 0.3s ease;
}

.nexus-hover-glow:hover {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.4);
    border-color: rgba(59, 130, 246, 0.6);
}