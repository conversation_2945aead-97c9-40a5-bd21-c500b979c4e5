{{-- Blog Sidebar --}}
<div class="space-y-8">
    {{-- Search Widget --}}
    <div class="bg-white dark:bg-bg-dark rounded-lg shadow-sm border border-border-light dark:border-border-dark p-6">
        <h3 class="text-lg font-semibold text-text-primary-light dark:text-text-primary-dark mb-4">Search Posts</h3>
        <form action="{{ route('blog.index') }}" method="GET" class="space-y-3" id="blog-search-form">
            <div class="relative">
                <input 
                    type="text" 
                    name="search" 
                    id="blog-search-input"
                    value="{{ request('search') }}"
                    placeholder="Search blog posts..." 
                    class="w-full px-4 py-2 border border-border-light dark:border-border-dark rounded-lg focus:ring-2 focus:ring-primary-light dark:focus:ring-primary-dark focus:border-transparent bg-white dark:bg-bg-dark text-text-primary-light dark:text-text-primary-dark"
                    autocomplete="off"
                >
                <button 
                    type="submit" 
                    class="absolute right-2 top-1/2 transform -translate-y-1/2 text-text-secondary-light dark:text-text-secondary-dark hover:text-primary-light dark:hover:text-primary-dark"
                >
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                </button>
                <div id="blog-search-results" class="absolute z-10 w-full bg-white dark:bg-bg-dark border border-border-light dark:border-border-dark rounded-lg mt-1 shadow-lg hidden"></div>
            </div>
        </form>
    </div>

    {{-- Featured Posts --}}
    @php
        $featuredPosts = \App\Models\BlogPost::published()
            ->featured()
            ->withBasicRelations()
            ->latest('published_at')
            ->take(3)
            ->get();
    @endphp
    
    @if($featuredPosts->count() > 0)
    <div class="bg-white dark:bg-bg-dark rounded-lg shadow-sm border border-border-light dark:border-border-dark p-6">
        <h3 class="text-lg font-semibold text-text-primary-light dark:text-text-primary-dark mb-4">Featured Posts</h3>
        <div class="space-y-4">
            @foreach($featuredPosts as $post)
            <article class="flex space-x-3">
                @if($post->featured_image)
                <div class="flex-shrink-0">
                    <img 
                        src="{{ asset('storage/' . $post->featured_image) }}" 
                        alt="{{ $post->title }}"
                        class="w-16 h-16 object-cover rounded-lg"
                    >
                </div>
                @endif
                <div class="flex-1 min-w-0">
                    <h4 class="text-sm font-medium text-text-primary-light dark:text-text-primary-dark hover:text-primary-light dark:hover:text-primary-dark transition-colors">
                        <a href="{{ route('blog.show', $post->slug) }}" class="line-clamp-2">
                            {{ $post->title }}
                        </a>
                    </h4>
                    <p class="text-xs text-text-secondary-light dark:text-text-secondary-dark mt-1">
                        {{ $post->published_at->format('M j, Y') }}
                    </p>
                    @if($post->category)
                    <span class="inline-block px-2 py-1 text-xs text-white rounded-full mt-1 {{ $post->category->badge_color }}">
                        {{ $post->category->name }}
                    </span>
                    @endif
                </div>
            </article>
            @endforeach
        </div>
    </div>
    @endif

    {{-- Recent Posts --}}
    @php
        $recentPosts = \App\Models\BlogPost::published()
            ->withBasicRelations()
            ->latest('published_at')
            ->take(5)
            ->get();
    @endphp
    
    @if($recentPosts->count() > 0)
    <div class="bg-white dark:bg-bg-dark rounded-lg shadow-sm border border-border-light dark:border-border-dark p-6">
        <h3 class="text-lg font-semibold text-text-primary-light dark:text-text-primary-dark mb-4">Recent Posts</h3>
        <div class="space-y-4">
            @foreach($recentPosts as $post)
            <article class="border-b border-border-light dark:border-border-dark last:border-b-0 pb-4 last:pb-0">
                <h4 class="text-sm font-medium text-text-primary-light dark:text-text-primary-dark hover:text-primary-light dark:hover:text-primary-dark transition-colors mb-1">
                    <a href="{{ route('blog.show', $post->slug) }}" class="line-clamp-2">
                        {{ $post->title }}
                    </a>
                </h4>
                <div class="flex items-center justify-between text-xs text-text-secondary-light dark:text-text-secondary-dark">
                    <span>{{ $post->published_at->format('M j, Y') }}</span>
                    <span>{{ $post->reading_time }} min read</span>
                </div>
                @if($post->excerpt)
                <p class="text-xs text-text-secondary-light dark:text-text-secondary-dark mt-2 line-clamp-2">{{ $post->excerpt }}</p>
                @endif
            </article>
            @endforeach
        </div>
    </div>
    @endif

    {{-- Categories --}}
    @php
        $categories = \App\Models\BlogPostCategory::withCount(['posts' => function($query) {
            $query->published();
        }])
        ->orderBy('display_order')
        ->orderBy('name')
        ->get()
        ->filter(function($category) {
            return $category->posts_count > 0;
        });
    @endphp
    
    @if($categories->count() > 0)
    <div class="bg-white dark:bg-bg-dark rounded-lg shadow-sm border border-border-light dark:border-border-dark p-6">
        <h3 class="text-lg font-semibold text-text-primary-light dark:text-text-primary-dark mb-4">Categories</h3>
        <div class="space-y-2">
            @foreach($categories as $category)
            <div class="flex items-center justify-between">
                <a 
                    href="{{ route('blog.category', $category->slug) }}" 
                    class="flex items-center space-x-2 text-sm text-text-primary-light dark:text-text-primary-dark hover:text-primary-light dark:hover:text-primary-dark transition-colors"
                >
                    <span class="w-3 h-3 rounded-full {{ $category->badge_color }}"></span>
                    <span>{{ $category->name }}</span>
                </a>
                <span class="text-xs text-text-secondary-light dark:text-text-secondary-dark bg-gray-100 dark:bg-bg-darker px-2 py-1 rounded-full">
                    {{ $category->posts_count }}
                </span>
            </div>
            @endforeach
        </div>
    </div>
    @endif

    {{-- Popular Tags --}}
    @php
        $popularTags = \App\Models\BlogPostTag::withCount(['posts' => function($query) {
            $query->published();
        }])
        ->orderByDesc('posts_count')
        ->get()
        ->filter(function($tag) {
            return $tag->posts_count > 0;
        })
        ->take(15);
    @endphp
    
    @if($popularTags->count() > 0)
    <div class="bg-white dark:bg-bg-dark rounded-lg shadow-sm border border-border-light dark:border-border-dark p-6">
        <h3 class="text-lg font-semibold text-text-primary-light dark:text-text-primary-dark mb-4">Popular Tags</h3>
        <div class="flex flex-wrap gap-2">
            @foreach($popularTags as $tag)
            <a 
                href="{{ route('blog.tag', $tag->slug) }}" 
                class="inline-flex items-center px-3 py-1 text-xs font-medium text-text-primary-light dark:text-text-primary-dark bg-bg-light dark:bg-bg-dark hover:bg-primary-light/10 dark:hover:bg-primary-dark/10 hover:text-primary-light dark:hover:text-primary-dark rounded-full transition-colors border border-border-light dark:border-border-dark"
            >
                {{ $tag->name }}
                <span class="ml-1 text-text-primary-light dark:text-text-primary-dark">({{ $tag->posts_count }})</span>
            </a>
            @endforeach
        </div>
    </div>
    @endif

    {{-- Archive --}}
    @php
        // Database-agnostic archive query
        $dbDriver = config('database.default');
        $connection = config("database.connections.{$dbDriver}.driver");
        
        if ($connection === 'sqlite') {
            $archives = \App\Models\BlogPost::published()
                ->selectRaw('strftime("%Y", published_at) as year, strftime("%m", published_at) as month, COUNT(*) as count')
                ->groupByRaw('strftime("%Y", published_at), strftime("%m", published_at)')
                ->orderByRaw('strftime("%Y", published_at) DESC, strftime("%m", published_at) DESC')
                ->take(12)
                ->get();
        } else {
            // MySQL, PostgreSQL, etc.
            $archives = \App\Models\BlogPost::published()
                ->selectRaw('YEAR(published_at) as year, MONTH(published_at) as month, COUNT(*) as count')
                ->groupByRaw('YEAR(published_at), MONTH(published_at)')
                ->orderByRaw('YEAR(published_at) DESC, MONTH(published_at) DESC')
                ->take(12)
                ->get();
        }
    @endphp
    
    @if($archives->count() > 0)
    <div class="bg-white dark:bg-bg-dark rounded-lg shadow-sm border border-border-light dark:border-border-dark p-6">
        <h3 class="text-lg font-semibold text-text-primary-light dark:text-text-primary-dark mb-4">Archive</h3>
        <div class="space-y-2">
            @foreach($archives as $archive)
            <div class="flex items-center justify-between">
                <a 
                    href="{{ route('blog.index', ['year' => $archive->year, 'month' => $archive->month]) }}" 
                    class="text-sm text-text-primary-light dark:text-text-primary-dark hover:text-primary-light dark:hover:text-primary-dark transition-colors"
                >
                    {{ \Carbon\Carbon::createFromDate($archive->year, $archive->month, 1)->format('F Y') }}
                </a>
                <span class="text-xs text-text-secondary-light dark:text-text-secondary-dark bg-gray-100 dark:bg-bg-darker px-2 py-1 rounded-full">
                    {{ $archive->count }}
                </span>
            </div>
            @endforeach
        </div>
    </div>
    @endif
    
</div>

{{-- Custom Styles --}}
<style>
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.autocomplete-item {
    padding: 10px;
    cursor: pointer;
    border-bottom: 1px solid var(--border-light, #f0f0f0);
}

.dark .autocomplete-item {
    border-bottom: 1px solid var(--border-dark, #2d3748);
}

.autocomplete-item:hover {
    background-color: var(--bg-lighter, #f8f9fa);
}

.dark .autocomplete-item:hover {
    background-color: var(--bg-darker, #1a202c);
}

.autocomplete-item:last-child {
    border-bottom: none;
}
</style>

{{-- Autocomplete Script --}}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const searchInput = document.getElementById('blog-search-input');
        const searchResults = document.getElementById('blog-search-results');
        const searchForm = document.getElementById('blog-search-form');
        let debounceTimer;

        // Function to fetch autocomplete results
        const fetchAutocomplete = (query) => {
            if (query.length < 2) {
                searchResults.innerHTML = '';
                searchResults.classList.add('hidden');
                return;
            }

            fetch(`{{ route('blog.autocomplete') }}?query=${encodeURIComponent(query)}`)
                .then(response => response.json())
                .then(data => {
                    searchResults.innerHTML = '';
                    
                    if (data.length === 0) {
                        searchResults.classList.add('hidden');
                        return;
                    }

                    data.forEach(post => {
                        const item = document.createElement('div');
                        item.className = 'autocomplete-item';
                        item.textContent = post.title;
                        item.addEventListener('click', () => {
                            window.location.href = `{{ url('/blog') }}/${post.slug}`;
                        });
                        searchResults.appendChild(item);
                    });

                    searchResults.classList.remove('hidden');
                })
                .catch(error => {
                    console.error('Error fetching autocomplete results:', error);
                });
        };

        // Add event listener for input changes with debounce
        searchInput.addEventListener('input', function() {
            clearTimeout(debounceTimer);
            debounceTimer = setTimeout(() => {
                fetchAutocomplete(this.value);
            }, 300);
        });

        // Hide results when clicking outside
        document.addEventListener('click', function(e) {
            if (!searchInput.contains(e.target) && !searchResults.contains(e.target)) {
                searchResults.classList.add('hidden');
            }
        });

        // Show results when input is focused and has value
        searchInput.addEventListener('focus', function() {
            if (this.value.length >= 2) {
                fetchAutocomplete(this.value);
            }
        });

        // Prevent form submission when selecting an autocomplete item
        searchResults.addEventListener('click', function(e) {
            e.preventDefault();
        });
    });
</script>