# Comprehensive Test Suite

This directory contains a comprehensive test suite for the PC Builder E-commerce application, covering all aspects of functionality, performance, and user experience.

## Test Structure

### Integration Tests (`tests/Feature/Integration/`)

End-to-end workflow tests that verify complete user journeys:

- **PcBuilderWorkflowTest.php** - Complete PC building workflow with compatibility checking
- **EcommercePurchaseFlowTest.php** - Full e-commerce purchase flow from browsing to order completion
- **AdminManagementWorkflowTest.php** - Admin management workflows for components, orders, and users

### Performance Tests (`tests/Performance/`)

Performance and load testing to ensure the application scales:

- **PcBuilderPerformanceTest.php** - PC builder performance with large component datasets
- **EcommercePerformanceTest.php** - E-commerce operations performance testing
- **LoadTestingTest.php** - High-load scenarios and concurrent user testing
- **DatabasePerformanceTest.php** - Database query optimization and performance
- **CartCheckoutStressTest.php** - Cart and checkout stress testing

### Browser Tests (`tests/Browser/`)

Laravel Dusk browser tests for UI/UX validation:

- **PcBuilderBrowserTest.php** - PC builder interface browser testing
- **EcommerceBrowserTest.php** - E-commerce shopping experience browser testing
- **AdminBrowserTest.php** - Admin interface browser testing

## Running Tests

### Quick Start

Run all tests using the comprehensive test runner:

```bash
php run-comprehensive-tests.php
```

### Individual Test Suites

#### Integration Tests
```bash
php artisan test tests/Feature/Integration/
```

#### Performance Tests
```bash
php artisan test tests/Performance/
```

#### Browser Tests
```bash
php artisan dusk
```

#### Specific Test Files
```bash
# Run PC Builder workflow tests
php artisan test tests/Feature/Integration/PcBuilderWorkflowTest.php

# Run performance tests
php artisan test tests/Performance/PcBuilderPerformanceTest.php

# Run browser tests
php artisan dusk tests/Browser/PcBuilderBrowserTest.php
```

## Test Requirements

### Prerequisites

1. **Laravel Dusk Setup**
   ```bash
   composer require --dev laravel/dusk
   php artisan dusk:install
   ```

2. **Chrome/Chromium Browser**
   - Chrome or Chromium browser installed
   - ChromeDriver (automatically managed by Dusk)

3. **Test Database**
   - Separate test database configured
   - Database migrations run for testing

4. **Environment Setup**
   ```bash
   # Copy environment file for testing
   cp .env .env.testing
   
   # Configure test database in .env.testing
   DB_CONNECTION=sqlite
   DB_DATABASE=:memory:
   ```

### Performance Test Requirements

Performance tests expect certain thresholds:

- **Component Loading**: < 500ms per category
- **Compatibility Checking**: < 1000ms for 1000 components
- **Build Creation**: < 50ms average per build
- **Search Operations**: < 200ms per search
- **Cart Operations**: < 50ms per operation
- **Checkout Process**: < 1000ms per checkout

### Browser Test Requirements

Browser tests require:

- **Headless Chrome** (default)
- **Screen Resolution**: Tests run at various resolutions
- **JavaScript Enabled**: Full JavaScript support
- **Local Development Server**: Application running locally

## Test Data

### Factories

Tests use Laravel factories to generate realistic test data:

- **ComponentFactory** - Creates components with specifications
- **UserFactory** - Creates users with different roles
- **OrderFactory** - Creates orders with various statuses
- **CartFactory** - Creates shopping carts

### Seeders

Some tests may require specific seeders:

```bash
php artisan db:seed --class=TestDataSeeder
```

## Performance Benchmarks

### Expected Performance Metrics

| Operation | Target Time | Stress Test |
|-----------|-------------|-------------|
| Component Loading | < 500ms | 1000+ components |
| PC Build Creation | < 50ms | 100 concurrent builds |
| Cart Operations | < 50ms | 200 items in cart |
| Checkout Process | < 1000ms | 100 concurrent checkouts |
| Search Queries | < 200ms | Large product catalog |
| Database Queries | < 100ms | Complex joins |

### Load Testing Scenarios

- **Concurrent Users**: 50-100 simultaneous users
- **High Traffic**: 500+ requests per minute
- **Large Datasets**: 10,000+ components, 5,000+ orders
- **Memory Usage**: Stable under extended load
- **Database Connections**: Efficient connection pooling

## Continuous Integration

### GitHub Actions

Example workflow for CI/CD:

```yaml
name: Comprehensive Tests

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v2
    
    - name: Setup PHP
      uses: shivammathur/setup-php@v2
      with:
        php-version: '8.2'
        
    - name: Install Dependencies
      run: composer install
      
    - name: Run Integration Tests
      run: php artisan test tests/Feature/Integration/
      
    - name: Run Performance Tests
      run: php artisan test tests/Performance/
      
    - name: Run Browser Tests
      run: php artisan dusk
```

## Troubleshooting

### Common Issues

1. **Browser Tests Failing**
   - Ensure Chrome/Chromium is installed
   - Check ChromeDriver version compatibility
   - Verify application is running locally

2. **Performance Tests Timing Out**
   - Increase PHP memory limit
   - Optimize database indexes
   - Check system resources

3. **Database Connection Issues**
   - Verify test database configuration
   - Ensure migrations are run
   - Check database permissions

### Debug Mode

Run tests with verbose output:

```bash
php artisan test --verbose tests/Feature/Integration/
```

Enable Dusk debugging:

```bash
php artisan dusk --browse
```

## Contributing

When adding new tests:

1. **Follow Naming Conventions**
   - Use descriptive test method names
   - Group related tests in the same file
   - Add appropriate doc blocks

2. **Performance Considerations**
   - Set realistic performance expectations
   - Clean up test data after tests
   - Use database transactions where appropriate

3. **Browser Test Best Practices**
   - Use page objects for complex interactions
   - Add appropriate waits for dynamic content
   - Test responsive design at different screen sizes

4. **Documentation**
   - Update this README when adding new test suites
   - Document any special setup requirements
   - Include performance benchmarks for new features

## Test Coverage

The comprehensive test suite covers:

- ✅ **PC Builder Functionality** - Component selection, compatibility checking, build creation
- ✅ **E-commerce Features** - Product browsing, cart management, checkout process
- ✅ **Admin Management** - Component management, order processing, user administration
- ✅ **Performance** - Load testing, stress testing, database optimization
- ✅ **User Experience** - Browser testing, responsive design, accessibility
- ✅ **API Endpoints** - REST API testing, authentication, rate limiting
- ✅ **Security** - Authentication, authorization, input validation
- ✅ **Integration** - Third-party services, payment processing, email notifications

This comprehensive test suite ensures the PC Builder E-commerce application is robust, performant, and ready for production deployment.