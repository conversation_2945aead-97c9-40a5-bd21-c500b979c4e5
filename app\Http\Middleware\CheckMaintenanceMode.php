<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\App;

class CheckMaintenanceMode
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        // Skip maintenance mode check in testing environment
        if (App::environment('testing')) {
            return $next($request);
        }
        
        if (is_maintenance_mode() && !$this->isAllowedWhileInMaintenance($request)) {
            return response()->view('errors.maintenance', [], 503);
        }

        return $next($request);
    }

    private function isAllowedWhileInMaintenance(Request $request): bool
    {
        // Allow admin users to access the site
        if (Auth::check() && Auth::user()->isAdmin()) {
            return true;
        }

        // Allow access to the login page
        if ($request->is('login') || $request->is('admin/login')) {
            return true;
        }

        return false;
    }
}