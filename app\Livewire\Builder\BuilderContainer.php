<?php

namespace App\Livewire\Builder;

use App\Models\Build;
use App\Models\Component;
use App\Models\ComponentCategory;
use App\Services\BuilderService;
use Livewire\Component as LivewireComponent;

class BuilderContainer extends LivewireComponent
{
    public $build;
    public $categories = [];
    public $selectedCategory = null;
    public $components = [];
    public $selectedComponents = [];
    public $totalPrice = 0;
    public $isComplete = false;
    public $compatibilityIssues = [];
    
    // Filtering properties
    public $searchTerm = '';
    public $selectedBrands = [];
    public $priceRange = [];
    public $inStockOnly = false;
    public $sortBy = 'price';
    public $sortDirection = 'asc';
    
    protected $builderService;
    
    protected $listeners = [
        'componentSelected' => 'addComponent',
        'componentRemoved' => 'removeComponent',
        'buildSaved' => 'loadBuild',
    ];
    
    public function boot(BuilderService $builderService)
    {
        $this->builderService = $builderService;
    }
    
    public function mount($buildId = null)
    {
        // Load categories
        $this->categories = $this->builderService->getComponentCategories();
        
        // Set default selected category if available
        if ($this->categories->isNotEmpty()) {
            $this->selectedCategory = $this->categories->first()->slug;
            $this->loadComponentsByCategory();
        }
        
        // Load existing build if ID provided
        if ($buildId) {
            $this->loadBuild($buildId);
        } else {
            // Initialize new build
            $this->build = new Build();
            $this->selectedComponents = [];
        }
    }
    
    public function loadBuild($buildId)
    {
        $this->build = Build::with('components.component')->findOrFail($buildId);
        
        // Load selected components
        $this->selectedComponents = [];
        foreach ($this->build->components as $buildComponent) {
            $this->selectedComponents[$buildComponent->component->category->slug] = [
                'component_id' => $buildComponent->component_id,
                'name' => $buildComponent->component->name,
                'price' => $buildComponent->price,
                'quantity' => $buildComponent->quantity,
            ];
        }
        
        $this->totalPrice = $this->build->total_price;
        $this->isComplete = $this->build->is_complete;
        $this->compatibilityIssues = $this->build->compatibility_issues ?? [];
    }
    
    public function selectCategory($categorySlug)
    {
        $this->selectedCategory = $categorySlug;
        $this->loadComponentsByCategory();
    }
    
    public function loadComponentsByCategory()
    {
        if ($this->selectedCategory) {
            $filters = [
                'search' => $this->searchTerm,
                'brand' => $this->selectedBrands,
                'price_min' => is_array($this->priceRange) ? ($this->priceRange['min'] ?? null) : null,
                'price_max' => is_array($this->priceRange) ? ($this->priceRange['max'] ?? null) : null,
                'in_stock' => $this->inStockOnly,
                'sort_by' => $this->sortBy,
                'sort_dir' => $this->sortDirection,
            ];
            
            $this->components = $this->builderService->getComponentsByCategory($this->selectedCategory, $filters);
            
            // Check compatibility for each component with current build
            $this->checkComponentCompatibility();
        }
    }
    
    public function updatedSearchTerm()
    {
        $this->loadComponentsByCategory();
    }
    
    public function updatedSelectedBrands()
    {
        $this->loadComponentsByCategory();
    }
    
    public function updatedPriceRange()
    {
        $this->loadComponentsByCategory();
    }
    
    public function updatedInStockOnly()
    {
        $this->loadComponentsByCategory();
    }
    
    public function updatedSortBy()
    {
        $this->loadComponentsByCategory();
    }
    
    public function updatedSortDirection()
    {
        $this->loadComponentsByCategory();
    }
    
    public function checkComponentCompatibility()
    {
        // TODO: Implement real-time compatibility checking
        // For now, we'll mark all components as compatible
        if (!$this->components->isEmpty()) {
            foreach ($this->components as $component) {
                $component->is_compatible = true;
                $component->compatibility_warnings = [];
            }
        }
    }
    
    public function addComponent($componentId, $categorySlug)
    {
        $component = Component::findOrFail($componentId);
        
        $this->selectedComponents[$categorySlug] = [
            'component_id' => $component->id,
            'name' => $component->name,
            'price' => $component->price,
            'quantity' => 1,
        ];
        
        $this->calculateTotalPrice();
        $this->checkBuildStatus();
    }
    
    public function removeComponent($categorySlug)
    {
        if (isset($this->selectedComponents[$categorySlug])) {
            unset($this->selectedComponents[$categorySlug]);
            $this->calculateTotalPrice();
            $this->checkBuildStatus();
        }
    }
    
    public function calculateTotalPrice()
    {
        $this->totalPrice = collect($this->selectedComponents)->sum(function ($component) {
            return $component['price'] * $component['quantity'];
        });
    }
    
    public function checkBuildStatus()
    {
        // Check if all required categories have components
        $requiredCategories = $this->builderService->getComponentCategories(true);
        $selectedCategorySlugs = array_keys($this->selectedComponents);
        
        $this->isComplete = $requiredCategories->every(function ($category) use ($selectedCategorySlugs) {
            return in_array($category->slug, $selectedCategorySlugs);
        });
        
        // Check compatibility if we have components
        $this->compatibilityIssues = [];
        // TODO: Implement compatibility checking
        // For now, we'll skip this to avoid issues
    }
    
    public function render()
    {
        return view('livewire.builder.builder-container');
    }
}