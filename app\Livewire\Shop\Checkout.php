<?php

namespace App\Livewire\Shop;

use App\Models\Cart;
use App\Models\Order;
use App\Services\CartService;
use App\Services\CheckoutService;
use App\Services\PaymentGatewayService;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\ValidationException;
use Livewire\Attributes\Computed;
use Livewire\Component;

class Checkout extends Component
{
    public $currentStep = 1;
    public $maxSteps = 4;

    // Shipping Information
    public $shipping = [
        'name' => '',
        'email' => '',
        'phone' => '',
        'address' => '',
        'city' => '',
        'state' => '',
        'zipcode' => '',
        'country' => 'US',
    ];

    // Billing Information
    public $billing = [
        'same_as_shipping' => true,
        'name' => '',
        'email' => '',
        'phone' => '',
        'address' => '',
        'city' => '',
        'state' => '',
        'zipcode' => '',
        'country' => 'US',
    ];

    // Shipping Options
    public $selectedShippingMethod = 'standard';
    public $shippingOptions = [];

    // Payment Information
    public $payment = [
        'payment_method' => 'credit_card',
        'card_number' => '',
        'card_expiry' => '',
        'card_cvv' => '',
        'card_name' => '',
        'paypal_email' => '',
        'bank_name' => '',
        'account_number' => '',
        'routing_number' => '',
    ];

    // Order Summary
    public $orderTotals = [];
    public $processingPayment = false;
    public $orderComplete = false;
    public $completedOrder = null;

    protected CartService $cartService;
    protected CheckoutService $checkoutService;
    protected PaymentGatewayService $paymentService;

    public function boot(
        CartService $cartService,
        CheckoutService $checkoutService,
        PaymentGatewayService $paymentService
    ) {
        $this->cartService = $cartService;
        $this->checkoutService = $checkoutService;
        $this->paymentService = $paymentService;
    }

    public function mount()
    {
        // Redirect if cart is empty
        if (!$this->cartService->hasItems()) {
            return redirect()->route('shop.cart');
        }

        // Pre-fill user information if logged in
        if (Auth::check()) {
            $user = Auth::user();
            $this->shipping['name'] = $user->name;
            $this->shipping['email'] = $user->email;
            $this->billing['name'] = $user->name;
            $this->billing['email'] = $user->email;
        }

        $this->calculateTotals();
    }

    #[Computed]
    public function cart()
    {
        return $this->cartService->getCart();
    }

    #[Computed]
    public function cartItems()
    {
        return $this->cart->items()->with('component')->get();
    }

    public function nextStep()
    {
        $this->validateCurrentStep();
        
        if ($this->currentStep < $this->maxSteps) {
            $this->currentStep++;
            
            // Load shipping options when moving to shipping step
            if ($this->currentStep === 3) {
                $this->loadShippingOptions();
            }
            
            // Calculate final totals when moving to review step
            if ($this->currentStep === 4) {
                $this->calculateTotals();
            }
        }
    }

    public function previousStep()
    {
        if ($this->currentStep > 1) {
            $this->currentStep--;
        }
    }

    public function goToStep($step)
    {
        if ($step >= 1 && $step <= $this->maxSteps && $step <= $this->currentStep + 1) {
            $this->currentStep = $step;
        }
    }

    public function updatedBillingSameAsShipping()
    {
        if ($this->billing['same_as_shipping']) {
            $this->billing = array_merge($this->billing, $this->shipping);
            $this->billing['same_as_shipping'] = true;
        }
    }

    public function updatedSelectedShippingMethod()
    {
        $this->calculateTotals();
    }

    public function loadShippingOptions()
    {
        try {
            $this->shippingOptions = $this->checkoutService->getShippingOptions(
                $this->cart,
                $this->shipping
            );
            
            // Set default shipping method if not already selected
            if (empty($this->selectedShippingMethod) && !empty($this->shippingOptions)) {
                $this->selectedShippingMethod = $this->shippingOptions[0]['id'];
            }
        } catch (\Exception $e) {
            $this->addError('shipping', 'Unable to load shipping options: ' . $e->getMessage());
        }
    }

    public function calculateTotals()
    {
        try {
            $cart = $this->cart;
            $subtotal = $cart->total ?? 0;
            
            // Calculate shipping
            $shipping = 0;
            if ($this->selectedShippingMethod && !empty($this->shippingOptions)) {
                $selectedOption = collect($this->shippingOptions)
                    ->firstWhere('id', $this->selectedShippingMethod);
                $shipping = $selectedOption['cost'] ?? 0;
            } else {
                // Default shipping calculation if no method selected yet
                $shipping = $this->checkoutService->calculateShipping($cart, $this->shipping);
            }
            
            // Calculate tax (8% default rate)
            $tax = round($subtotal * 0.08, 2);
            
            $total = $subtotal + $tax + $shipping;
            
            $this->orderTotals = [
                'subtotal' => $subtotal,
                'tax' => $tax,
                'shipping' => $shipping,
                'total' => round($total, 2),
            ];
        } catch (\Exception $e) {
            $this->addError('totals', 'Unable to calculate order totals: ' . $e->getMessage());
        }
    }

    public function processOrder()
    {
        $this->processingPayment = true;
        $this->resetErrorBag();

        try {
            DB::transaction(function () {
                // Validate final order data
                $this->validateOrderData();
                
                // Prepare shipping data
                $shippingData = [
                    'shipping' => $this->shipping,
                    'billing' => $this->billing['same_as_shipping'] ? $this->shipping : $this->billing,
                ];
                
                // Create order through checkout service
                $order = $this->checkoutService->processCheckout(
                    $this->cart,
                    $shippingData,
                    $this->payment
                );
                
                // Process payment
                $payment = $this->paymentService->processPayment($order, $this->payment);
                
                if ($payment->status === 'failed') {
                    throw new \Exception('Payment processing failed. Please try again.');
                }
                
                $this->completedOrder = $order->fresh(['items.component', 'payment']);
                $this->orderComplete = true;
            });
        } catch (ValidationException $e) {
            $this->processingPayment = false;
            throw $e;
        } catch (\Exception $e) {
            $this->processingPayment = false;
            $this->addError('payment', $e->getMessage());
        }
    }

    protected function validateCurrentStep()
    {
        switch ($this->currentStep) {
            case 1:
                $this->validateShippingInfo();
                break;
            case 2:
                $this->validateBillingInfo();
                break;
            case 3:
                $this->validateShippingMethod();
                break;
            case 4:
                $this->validatePaymentInfo();
                break;
        }
    }

    protected function validateShippingInfo()
    {
        $this->validate([
            'shipping.name' => 'required|string|max:255',
            'shipping.email' => 'required|email|max:255',
            'shipping.phone' => 'required|string|max:20',
            'shipping.address' => 'required|string|max:255',
            'shipping.city' => 'required|string|max:100',
            'shipping.state' => 'required|string|max:50',
            'shipping.zipcode' => 'required|string|max:20',
            'shipping.country' => 'required|string|max:50',
        ], [], [
            'shipping.name' => 'Name',
            'shipping.email' => 'Email',
            'shipping.phone' => 'Phone',
            'shipping.address' => 'Address',
            'shipping.city' => 'City',
            'shipping.state' => 'State',
            'shipping.zipcode' => 'ZIP Code',
            'shipping.country' => 'Country',
        ]);
    }

    protected function validateBillingInfo()
    {
        if (!$this->billing['same_as_shipping']) {
            $this->validate([
                'billing.name' => 'required|string|max:255',
                'billing.email' => 'required|email|max:255',
                'billing.phone' => 'required|string|max:20',
                'billing.address' => 'required|string|max:255',
                'billing.city' => 'required|string|max:100',
                'billing.state' => 'required|string|max:50',
                'billing.zipcode' => 'required|string|max:20',
                'billing.country' => 'required|string|max:50',
            ], [], [
                'billing.name' => 'Billing Name',
                'billing.email' => 'Billing Email',
                'billing.phone' => 'Billing Phone',
                'billing.address' => 'Billing Address',
                'billing.city' => 'Billing City',
                'billing.state' => 'Billing State',
                'billing.zipcode' => 'Billing ZIP Code',
                'billing.country' => 'Billing Country',
            ]);
        }
    }

    protected function validateShippingMethod()
    {
        $this->validate([
            'selectedShippingMethod' => 'required|string',
        ], [], [
            'selectedShippingMethod' => 'Shipping Method',
        ]);
    }

    protected function validatePaymentInfo()
    {
        $rules = [
            'payment.payment_method' => 'required|string|in:credit_card,paypal,bank_transfer',
        ];

        $attributes = [
            'payment.payment_method' => 'Payment Method',
        ];

        // Add method-specific validation
        switch ($this->payment['payment_method']) {
            case 'credit_card':
                $rules = array_merge($rules, [
                    'payment.card_number' => 'required|string|regex:/^\d{13,19}$/',
                    'payment.card_expiry' => 'required|string|regex:/^\d{2}\/\d{2}$/',
                    'payment.card_cvv' => 'required|string|regex:/^\d{3,4}$/',
                    'payment.card_name' => 'required|string|max:255',
                ]);
                $attributes = array_merge($attributes, [
                    'payment.card_number' => 'Card Number',
                    'payment.card_expiry' => 'Expiry Date',
                    'payment.card_cvv' => 'CVV',
                    'payment.card_name' => 'Cardholder Name',
                ]);
                break;
                
            case 'paypal':
                $rules['payment.paypal_email'] = 'sometimes|email|max:255';
                $attributes['payment.paypal_email'] = 'PayPal Email';
                break;
                
            case 'bank_transfer':
                $rules = array_merge($rules, [
                    'payment.bank_name' => 'required|string|max:255',
                    'payment.account_number' => 'required|string|max:50',
                    'payment.routing_number' => 'required|string|max:20',
                ]);
                $attributes = array_merge($attributes, [
                    'payment.bank_name' => 'Bank Name',
                    'payment.account_number' => 'Account Number',
                    'payment.routing_number' => 'Routing Number',
                ]);
                break;
        }

        $this->validate($rules, [], $attributes);
    }

    protected function validateOrderData()
    {
        // Validate cart still has items
        if (!$this->cartService->hasItems()) {
            throw new \Exception('Your cart is empty. Please add items before checkout.');
        }

        // Validate stock availability
        $stockIssues = $this->cartService->validateCartStock();
        if (!empty($stockIssues)) {
            throw new \Exception('Some items in your cart are no longer available. Please review your cart.');
        }

        // Validate all step data
        $this->validateShippingInfo();
        $this->validateBillingInfo();
        $this->validateShippingMethod();
        $this->validatePaymentInfo();
    }

    public function render()
    {
        return view('livewire.shop.checkout');
    }
}