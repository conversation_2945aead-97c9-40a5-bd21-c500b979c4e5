@props([
    'action' => '',
    'method' => 'POST',
    'showGatewaySelector' => true,
    'gateways' => [],
    'selectedGateway' => null,
    'amount' => '',
    'currency' => 'INR',
    'description' => '',
    'loading' => false
])

<form 
    id="payment-form" 
    action="{{ $action }}" 
    method="{{ $method }}"
    class="payment-form space-y-6"
    novalidate
>
    @csrf
    
    @if($showGatewaySelector && count($gateways) > 0)
        <div class="gateway-selection">
            <x-payment-gateway-selector 
                :gateways="$gateways" 
                :selectedGateway="$selectedGateway"
                :error="$errors->first('gateway')"
            />
        </div>
    @endif
    
    <div class="payment-details space-y-4">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="form-group">
                <x-label for="amount" value="Amount *" />
                <div class="relative">
                    <x-input 
                        id="amount" 
                        name="amount" 
                        type="number" 
                        step="0.01" 
                        min="1" 
                        :value="old('amount', $amount)"
                        placeholder="0.00"
                        class="pl-12 {{ $errors->has('amount') ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : '' }}"
                        required 
                    />
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <span class="currency-symbol text-gray-500 text-sm font-medium">₹</span>
                    </div>
                </div>
                @error('amount')
                    <x-input-error :messages="$message" class="mt-1" />
                @enderror
            </div>
            
            <div class="form-group">
                <x-label for="currency" value="Currency" />
                <select 
                    id="currency" 
                    name="currency" 
                    class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 {{ $errors->has('currency') ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : '' }}"
                >
                    <option value="INR" {{ old('currency', $currency) === 'INR' ? 'selected' : '' }}>INR - Indian Rupee</option>
                    <option value="USD" {{ old('currency', $currency) === 'USD' ? 'selected' : '' }}>USD - US Dollar</option>
                    <option value="EUR" {{ old('currency', $currency) === 'EUR' ? 'selected' : '' }}>EUR - Euro</option>
                </select>
                @error('currency')
                    <x-input-error :messages="$message" class="mt-1" />
                @enderror
            </div>
        </div>
        
        <div class="form-group">
            <x-label for="description" value="Description (Optional)" />
            <x-input 
                id="description" 
                name="description" 
                type="text" 
                :value="old('description', $description)"
                placeholder="Payment for order #12345"
                class="{{ $errors->has('description') ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : '' }}"
                maxlength="255"
            />
            @error('description')
                <x-input-error :messages="$message" class="mt-1" />
            @enderror
        </div>
    </div>
    
    <div class="form-actions">
        <button 
            type="submit" 
            class="payment-submit-btn w-full bg-indigo-600 text-white py-3 px-6 rounded-lg font-semibold hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
            :disabled="$loading"
        >
            <span class="btn-text">
                @if($loading)
                    <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Processing Payment...
                @else
                    <svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                    </svg>
                    Pay Securely
                @endif
            </span>
        </button>
        
        <div class="mt-4 text-center">
            <div class="flex items-center justify-center space-x-2 text-sm text-gray-600">
                <svg class="w-4 h-4 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <span>256-bit SSL encrypted payment</span>
            </div>
        </div>
    </div>
    
    <!-- Loading overlay -->
    <div id="payment-loading-overlay" class="hidden fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center">
        <div class="bg-white rounded-lg p-6 max-w-sm mx-4">
            <div class="flex items-center space-x-3">
                <svg class="animate-spin h-6 w-6 text-indigo-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <div>
                    <p class="font-medium text-gray-900">Processing Payment</p>
                    <p class="text-sm text-gray-600">Please wait while we process your payment...</p>
                </div>
            </div>
        </div>
    </div>
</form>

<!-- Include payment validation script -->
<script src="{{ asset('js/payment-validation.js') }}"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('payment-form');
    const submitBtn = form.querySelector('.payment-submit-btn');
    const btnText = submitBtn.querySelector('.btn-text');
    const loadingOverlay = document.getElementById('payment-loading-overlay');
    const amountInput = document.getElementById('amount');
    const currencySelect = document.getElementById('currency');
    
    // Real-time form validation
    form.addEventListener('input', function(e) {
        validateField(e.target);
    });
    
    // Amount formatting
    amountInput.addEventListener('input', function() {
        let value = this.value;
        if (value && !isNaN(value)) {
            // Format to 2 decimal places on blur
            this.addEventListener('blur', function() {
                if (this.value) {
                    this.value = parseFloat(this.value).toFixed(2);
                }
            }, { once: true });
        }
    });
    
    // Currency symbol update
    currencySelect.addEventListener('change', function() {
        const symbol = getCurrencySymbol(this.value);
        const symbolElement = form.querySelector('.absolute span');
        if (symbolElement) {
            symbolElement.textContent = symbol;
        }
    });
    
    // Form submission handling
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        
        if (!validateForm()) {
            return;
        }
        
        showLoadingState();
        processPayment();
    });
    
    function validateField(field) {
        const fieldGroup = field.closest('.form-group');
        const errorElement = fieldGroup?.querySelector('.text-red-600');
        
        // Remove existing error styling
        field.classList.remove('border-red-500', 'focus:border-red-500', 'focus:ring-red-500');
        if (errorElement) {
            errorElement.remove();
        }
        
        let isValid = true;
        let errorMessage = '';
        
        switch (field.name) {
            case 'amount':
                const amount = parseFloat(field.value);
                if (!field.value) {
                    isValid = false;
                    errorMessage = 'Amount is required';
                } else if (isNaN(amount) || amount <= 0) {
                    isValid = false;
                    errorMessage = 'Please enter a valid amount';
                } else if (amount < 1) {
                    isValid = false;
                    errorMessage = 'Minimum amount is ₹1.00';
                }
                break;
                
            case 'gateway':
                if (!document.querySelector('input[name="gateway"]:checked')) {
                    isValid = false;
                    errorMessage = 'Please select a payment gateway';
                }
                break;
        }
        
        if (!isValid) {
            field.classList.add('border-red-500', 'focus:border-red-500', 'focus:ring-red-500');
            const errorDiv = document.createElement('div');
            errorDiv.className = 'text-red-600 text-sm mt-1';
            errorDiv.textContent = errorMessage;
            fieldGroup.appendChild(errorDiv);
        }
        
        return isValid;
    }
    
    function validateForm() {
        let isValid = true;
        
        // Validate gateway selection
        const gatewaySelected = document.querySelector('input[name="gateway"]:checked');
        if (!gatewaySelected) {
            isValid = false;
            showError('Please select a payment gateway');
        }
        
        // Validate amount
        const amountField = document.getElementById('amount');
        if (!validateField(amountField)) {
            isValid = false;
        }
        
        return isValid;
    }
    
    function showLoadingState() {
        submitBtn.disabled = true;
        loadingOverlay.classList.remove('hidden');
        
        btnText.innerHTML = `
            <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Processing Payment...
        `;
    }
    
    function hideLoadingState() {
        submitBtn.disabled = false;
        loadingOverlay.classList.add('hidden');
        
        btnText.innerHTML = `
            <svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
            </svg>
            Pay Securely
        `;
    }
    
    async function processPayment() {
        const formData = new FormData(form);
        const data = Object.fromEntries(formData);
        
        try {
            const response = await fetch(form.action || '{{ route("payment.store") }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                },
                body: JSON.stringify(data)
            });
            
            const result = await response.json();
            
            if (result.success) {
                // Dispatch custom event for payment processing
                const event = new CustomEvent('paymentInitiated', {
                    detail: {
                        gateway: data.gateway,
                        amount: data.amount,
                        result: result
                    }
                });
                document.dispatchEvent(event);
                
                // Handle different gateways
                if (data.gateway === 'razorpay') {
                    handleRazorpayPayment(result.payment_data, result.transaction_id);
                } else if (data.gateway === 'cashfree') {
                    handleCashfreePayment(result.payment_data);
                } else if (data.gateway === 'payumoney') {
                    // PayUmoney will redirect to form submission page
                    window.location.href = result.redirect_url || '/payment/payumoney-redirect';
                }
            } else {
                hideLoadingState();
                showError(result.error?.message || 'Payment initiation failed');
            }
        } catch (error) {
            hideLoadingState();
            showError('Network error: ' + error.message);
        }
    }
    
    function getCurrencySymbol(currency) {
        return {
            'INR': '₹',
            'USD': '$',
            'EUR': '€'
        }[currency] || '₹';
    }
    
    function showError(message) {
        // Create or update error alert
        let errorAlert = document.getElementById('payment-error-alert');
        if (!errorAlert) {
            errorAlert = document.createElement('div');
            errorAlert.id = 'payment-error-alert';
            errorAlert.className = 'mb-4 p-4 bg-red-50 border border-red-200 rounded-lg';
            form.insertBefore(errorAlert, form.firstChild);
        }
        
        errorAlert.innerHTML = `
            <div class="flex items-center">
                <svg class="w-5 h-5 text-red-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <span class="text-red-800 text-sm">${message}</span>
                <button type="button" class="ml-auto text-red-400 hover:text-red-600" onclick="this.parentElement.parentElement.remove()">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
        `;
        
        // Auto-hide after 5 seconds
        setTimeout(() => {
            if (errorAlert.parentElement) {
                errorAlert.remove();
            }
        }, 5000);
    }
    
    // Gateway-specific payment handlers (to be implemented by parent page)
    window.handleRazorpayPayment = window.handleRazorpayPayment || function(paymentData, transactionId) {
        console.log('Razorpay payment handler not implemented');
    };
    
    window.handleCashfreePayment = window.handleCashfreePayment || function(paymentData) {
        console.log('Cashfree payment handler not implemented');
    };
});
</script>

<style>
.payment-form .form-group {
    @apply relative;
}

.payment-form .form-group label {
    @apply block text-sm font-medium text-gray-700 mb-1;
}

.payment-form input:focus,
.payment-form select:focus {
    @apply outline-none ring-2 ring-indigo-500 ring-offset-2;
}

.payment-form .payment-submit-btn:hover:not(:disabled) {
    @apply transform -translate-y-0.5 shadow-lg;
}

@media (max-width: 640px) {
    .payment-form .grid-cols-2 {
        @apply grid-cols-1;
    }
}
</style>