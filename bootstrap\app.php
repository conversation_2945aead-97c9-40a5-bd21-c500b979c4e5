<?php

use App\Http\Middleware\AdminMiddleware;
use App\Http\Middleware\InstallationMiddleware;
use App\Http\Middleware\RedirectIfAdminAuthenticated;
use App\Http\Middleware\WebhookRateLimit;
use App\Http\Middleware\WebhookSignatureValidation;
use App\Http\Middleware\WebhookSecurityFilter;
use App\Http\Middleware\PaymentAdminMiddleware;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        api: __DIR__.'/../routes/api.php',
        commands: __DIR__.'/../routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware) {
        // Global middleware - runs first
        $middleware->append([
            InstallationMiddleware::class, // Installation check should run first

        ]);

        // Named middleware aliases
        $middleware->alias([
            'install' => InstallationMiddleware::class,
            'admin' => AdminMiddleware::class,
            'redirect.admin.if.authenticated' => RedirectIfAdminAuthenticated::class,
            'webhook.rate.limit' => WebhookRateLimit::class,
            'webhook.signature' => WebhookSignatureValidation::class,
            'webhook.security' => WebhookSecurityFilter::class,
            'payment.admin' => PaymentAdminMiddleware::class,
        ]);
    })
    ->withExceptions(function (Exceptions $exceptions) {
        //
    })->create();
