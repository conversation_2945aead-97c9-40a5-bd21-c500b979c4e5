@extends('layouts.app')

@section('content')
<div class="min-h-screen flex items-center justify-center relative overflow-hidden bg-bg-light text-primary-light dark:bg-bg-dark dark:text-primary-dark">
    <!-- Animated particles -->
    <div class="particle" style="left: 10%; animation-delay: 0s;"></div>
    <div class="particle" style="left: 20%; animation-delay: 1s;"></div>
    <div class="particle" style="left: 30%; animation-delay: 2s;"></div>
    <div class="particle" style="left: 40%; animation-delay: 3s;"></div>
    <div class="particle" style="left: 50%; animation-delay: 4s;"></div>
    <div class="particle" style="left: 60%; animation-delay: 5s;"></div>
    <div class="particle" style="left: 70%; animation-delay: 6s;"></div>
    <div class="particle" style="left: 80%; animation-delay: 7s;"></div>
    <div class="particle" style="left: 90%; animation-delay: 8s;"></div>

    <div class="text-center z-10 px-4 max-w-4xl mx-auto">
        <!-- Main 404 display -->
        <div class="fade-in-up" style="animation-delay: 0.2s; opacity: 0;">
            <div class="float mb-8">
                <h1 class="text-9xl md:text-[12rem] font-black text-gradient mb-4 select-none">
                    404
                </h1>
            </div>
        </div>

        <!-- Error message -->
        <div class="fade-in-up" style="animation-delay: 0.4s; opacity: 0;">
            <h2 class="text-3xl md:text-5xl font-bold text-text-primary-light dark:text-text-primary-dark mb-4">
                Oops! Page Not Found
            </h2>
            <p class="text-lg md:text-xl text-text-secondary-light dark:text-text-secondary-dark mb-8 max-w-2xl mx-auto leading-relaxed">
                The page you're looking for seems to have vanished into the digital void.
                But don't worry, even the best explorers sometimes take wrong turns in cyberspace.
            </p>
        </div>

        <!-- Action buttons -->
        <div class="fade-in-up flex flex-col sm:flex-row gap-4 justify-center items-center" style="animation-delay: 0.6s; opacity: 0;">
            <button onclick="window.history.back()"
                class="card-hover glow bg-gradient-to-r from-primary-light to-primary-dark dark:from-primary-dark dark:to-primary-light hover:from-primary-dark hover:to-primary-light dark:hover:from-primary-light dark:hover:to-primary-dark text-white font-semibold py-4 px-8 rounded-full shadow-lg transition-all duration-300 text-lg">
                <span class="flex items-center gap-2">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                    Go Back
                </span>
            </button>

            <button onclick="window.location.href='/'"
                class="card-hover bg-border-light dark:bg-border-dark hover:bg-border-dark dark:hover:bg-border-light text-primary-light dark:text-primary-dark font-semibold py-4 px-8 rounded-full border-2 border-border-light dark:border-border-dark hover:border-primary-light dark:hover:border-primary-dark transition-all duration-300 text-lg">
                <span class="flex items-center gap-2">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
                    </svg>
                    Home Page
                </span>
            </button>
        </div>

        <!-- Additional help section -->
        <div class="fade-in-up mt-16 p-8 bg-black bg-opacity-30 dark:bg-white dark:bg-opacity-10 backdrop-blur-sm rounded-2xl border border-border-light dark:border-border-dark card-hover" style="animation-delay: 0.8s; opacity: 0;">
            <h3 class="text-xl font-semibold text-primary-light dark:text-primary-dark mb-4">Need Help Finding Something?</h3>
            <div class="grid md:grid-cols-3 gap-6 text-left">
                <div class="flex items-start gap-3">
                    <div class="bg-primary-light dark:bg-primary-dark p-2 rounded-lg">
                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                    </div>
                    <div>
                        <h4 class="text-primary-light dark:text-primary-dark font-medium mb-1">Search</h4>
                        <p class="text-text-secondary-light dark:text-text-secondary-dark text-sm">Try using the search feature to find what you need.</p>
                    </div>
                </div>

                <div class="flex items-start gap-3">
                    <div class="bg-accent-light dark:bg-accent-dark p-2 rounded-lg">
                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div>
                        <h4 class="text-primary-light dark:text-primary-dark font-medium mb-1">Support</h4>
                        <p class="text-text-secondary-light dark:text-text-secondary-dark text-sm">Contact our support team for assistance.</p>
                    </div>
                </div>

                <div class="flex items-start gap-3">
                    <div class="bg-border-light dark:bg-border-dark p-2 rounded-lg">
                        <svg class="w-5 h-5 text-primary-light dark:text-primary-dark" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                    </div>
                    <div>
                        <h4 class="text-primary-light dark:text-primary-dark font-medium mb-1">Sitemap</h4>
                        <p class="text-text-secondary-light dark:text-text-secondary-dark text-sm">Browse our complete site structure.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Add some interactivity without external dependencies
        document.addEventListener('DOMContentLoaded', function() {
            // Trigger animations on load
            const elements = document.querySelectorAll('.fade-in-up');
            elements.forEach((el, index) => {
                setTimeout(() => {
                    el.style.opacity = '1';
                }, index * 200);
            });

            // Add mouse move parallax effect
            document.addEventListener('mousemove', function(e) {
                const particles = document.querySelectorAll('.particle');
                const x = e.clientX / window.innerWidth;
                const y = e.clientY / window.innerHeight;

                particles.forEach((particle, index) => {
                    const speed = (index + 1) * 0.5;
                    particle.style.transform =
                        `translateX(${x * speed}px) translateY(${y * speed}px)`;
                });
            });
        });
    </script>
</div>
@endsection

@push('styles')
    <style>
        @keyframes float {
            0%,
            100% {
                transform: translateY(0px) rotate(0deg);
            }
            50% {
                transform: translateY(-20px) rotate(5deg);
            }
        }
        @keyframes glow {
            0%,
            100% {
                box-shadow: 0 0 20px rgba(139, 92, 246, 0.3);
            }
            50% {
                box-shadow: 0 0 40px rgba(139, 92, 246, 0.6);
            }
        }
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        .float {
            animation: float 6s ease-in-out infinite;
        }
        .glow {
            animation: glow 2s ease-in-out infinite alternate;
        }
        .fade-in-up {
            animation: fadeInUp 0.8s ease-out forwards;
        }
        .gradient-bg {
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 25%, #16213e 50%, #0f3460 75%, #533483 100%);
        }
        .text-gradient {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        .card-hover {
            transition: all 0.3s ease;
        }
        .card-hover:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
        }
        .particle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: rgba(139, 92, 246, 0.6);
            border-radius: 50%;
            animation: particle 8s linear infinite;
        }
        @keyframes particle {
            0% {
                transform: translateY(100vh) translateX(0);
                opacity: 0;
            }
            10% {
                opacity: 1;
            }
            90% {
                opacity: 1;
            }
            100% {
                transform: translateY(-100px) translateX(100px);
                opacity: 0;
            }
        }
    </style>
@endpush
