<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Transaction;
use App\Models\Order;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Carbon;

class StatsController extends Controller
{
    /**
     * Get dashboard metrics for the payment dashboard.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function dashboardMetrics(Request $request)
    {
        $period = $request->get('period', 'month');
        
        $dateFrom = $this->getDateFromByPeriod($period);
        
        // Get transactions for the current period
        $transactions = Transaction::where('created_at', '>=', $dateFrom)->get();
        
        // Calculate metrics
        $totalRevenue = $transactions->sum('amount');
        $totalTransactions = $transactions->count();
        $successfulTransactions = $transactions->where('status', 'completed')->count();
        $successRate = $totalTransactions > 0 ? ($successfulTransactions / $totalTransactions) * 100 : 0;
        
        // Calculate average processing time (in seconds)
        $avgProcessingTime = $transactions
            ->where('status', 'completed')
            ->avg(function ($transaction) {
                if ($transaction->completed_at && $transaction->created_at) {
                    return Carbon::parse($transaction->completed_at)->diffInSeconds($transaction->created_at);
                }
                return null;
            }) ?? 0;
        
        return response()->json([
            'total_revenue' => round($totalRevenue, 2),
            'currency' => 'INR', // Default currency
            'total_transactions' => $totalTransactions,
            'success_rate' => round($successRate, 1),
            'avg_processing_time' => round($avgProcessingTime, 1),
            'period' => $period
        ]);
    }
    
    /**
     * Get live transaction feed data.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function liveTransactions()
    {
        // Get the 10 most recent transactions
        $transactions = Transaction::with('user')
            ->latest()
            ->take(10)
            ->get()
            ->map(function ($transaction) {
                return [
                    'id' => $transaction->id,
                    'transaction_id' => $transaction->transaction_id,
                    'user_name' => $transaction->user ? $transaction->user->name : 'Guest',
                    'gateway_name' => $transaction->gateway_name,
                    'amount' => $transaction->amount,
                    'currency' => $transaction->currency,
                    'status' => $transaction->status,
                    'created_at' => $transaction->created_at->diffForHumans(),
                    'timestamp' => $transaction->created_at->timestamp
                ];
            });
        
        return response()->json($transactions);
    }
    
    /**
     * Get payment statistics.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function paymentStats(Request $request)
    {
        $period = $request->get('period', 'month');
        $dateFrom = $this->getDateFromByPeriod($period);
        
        // Get previous period for comparison
        $previousDateFrom = $this->getPreviousPeriod($period, $dateFrom);
        $previousDateTo = $dateFrom;
        
        // Current period transactions
        $transactions = Transaction::where('created_at', '>=', $dateFrom)->get();
        $totalRevenue = $transactions->sum('amount');
        $totalTransactions = $transactions->count();
        $successfulTransactions = $transactions->where('status', 'completed')->count();
        $successRate = $totalTransactions > 0 ? ($successfulTransactions / $totalTransactions) * 100 : 0;
        
        // Previous period transactions for comparison
        $previousTransactions = Transaction::whereBetween('created_at', [$previousDateFrom, $previousDateTo])->get();
        $previousRevenue = $previousTransactions->sum('amount');
        $previousTransactionCount = $previousTransactions->count();
        
        // Calculate changes
        $revenueChange = $previousRevenue > 0 ? (($totalRevenue - $previousRevenue) / $previousRevenue) * 100 : 0;
        $transactionChange = $previousTransactionCount > 0 ? 
            (($totalTransactions - $previousTransactionCount) / $previousTransactionCount) * 100 : 0;
        
        // Get gateway statistics
        $gatewayStats = $this->getGatewayStats($dateFrom);
        
        return response()->json([
            'total_revenue' => round($totalRevenue, 2),
            'currency' => 'INR', // Default currency
            'total_transactions' => $totalTransactions,
            'success_rate' => round($successRate, 1),
            'revenue_change' => round($revenueChange, 1),
            'transaction_change' => round($transactionChange, 1),
            'gateway_stats' => $gatewayStats,
            'period' => $period
        ]);
    }
    
    /**
     * Get recent transactions for display.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function recentTransactions()
    {
        $transactions = Transaction::with('user')
            ->latest()
            ->take(5)
            ->get()
            ->map(function ($transaction) {
                return [
                    'id' => $transaction->id,
                    'transaction_id' => $transaction->transaction_id,
                    'user_name' => $transaction->user ? $transaction->user->name : 'Guest',
                    'gateway_name' => $transaction->gateway_name,
                    'amount' => $transaction->amount,
                    'currency' => $transaction->currency,
                    'status' => $transaction->status,
                    'created_at' => $transaction->created_at->format('M d, Y h:i A'),
                    'created_at_human' => $transaction->created_at->diffForHumans()
                ];
            });
        
        return response()->json($transactions);
    }
    
    /**
     * Get payment chart data.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function paymentChart(Request $request)
    {
        $period = $request->get('period', 'month');
        $dateFrom = $this->getDateFromByPeriod($period);
        
        // Group by day for week/month, by month for year
        $groupBy = $period === 'year' ? 'month' : 'day';
        
        $chartData = Transaction::where('created_at', '>=', $dateFrom)
            ->select(
                DB::raw($groupBy === 'day' ? 'DATE(created_at) as label' : 'MONTH(created_at) as label'),
                DB::raw('SUM(amount) as revenue'),
                DB::raw('COUNT(*) as count')
            )
            ->groupBy('label')
            ->orderBy('label')
            ->get()
            ->map(function ($item) use ($groupBy) {
                // Format the label based on grouping
                if ($groupBy === 'month') {
                    $monthNames = [
                        1 => 'Jan', 2 => 'Feb', 3 => 'Mar', 4 => 'Apr', 5 => 'May', 6 => 'Jun',
                        7 => 'Jul', 8 => 'Aug', 9 => 'Sep', 10 => 'Oct', 11 => 'Nov', 12 => 'Dec'
                    ];
                    $item->label = $monthNames[(int)$item->label] ?? $item->label;
                }
                
                return [
                    'label' => $item->label,
                    'revenue' => round($item->revenue, 2),
                    'count' => $item->count
                ];
            });
        
        return response()->json([
            'chart_data' => $chartData,
            'period' => $period
        ]);
    }
    
    /**
     * Export payment report as CSV.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Symfony\Component\HttpFoundation\StreamedResponse
     */
    public function exportPaymentReport(Request $request)
    {
        $period = $request->get('period', 'month');
        $dateFrom = $this->getDateFromByPeriod($period);
        
        $transactions = Transaction::with('user')
            ->where('created_at', '>=', $dateFrom)
            ->get();
        
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="payment-report-' . date('Y-m-d') . '.csv"',
        ];
        
        $callback = function() use ($transactions) {
            $file = fopen('php://output', 'w');
            
            // Add CSV headers
            fputcsv($file, [
                'Transaction ID', 'Gateway Transaction ID', 'User', 'Gateway', 
                'Amount', 'Currency', 'Status', 'Created At', 'Completed At'
            ]);
            
            // Add transaction data
            foreach ($transactions as $transaction) {
                fputcsv($file, [
                    $transaction->transaction_id,
                    $transaction->gateway_transaction_id,
                    $transaction->user ? $transaction->user->name : 'Guest',
                    $transaction->gateway_name,
                    $transaction->amount,
                    $transaction->currency,
                    $transaction->status,
                    $transaction->created_at->format('Y-m-d H:i:s'),
                    $transaction->completed_at ? $transaction->completed_at->format('Y-m-d H:i:s') : 'N/A'
                ]);
            }
            
            fclose($file);
        };
        
        return response()->stream($callback, 200, $headers);
    }
    
    /**
     * Get gateway statistics.
     *
     * @param  \Carbon\Carbon  $dateFrom
     * @return array
     */
    private function getGatewayStats($dateFrom)
    {
        $gateways = ['razorpay', 'payumoney', 'cashfree'];
        $stats = [];
        
        foreach ($gateways as $gateway) {
            $gatewayTransactions = Transaction::where('gateway_name', $gateway)
                ->where('created_at', '>=', $dateFrom)
                ->get();
            
            $totalTransactions = $gatewayTransactions->count();
            $successfulTransactions = $gatewayTransactions->where('status', 'completed')->count();
            $successRate = $totalTransactions > 0 ? ($successfulTransactions / $totalTransactions) * 100 : 0;
            
            $stats[$gateway] = [
                'name' => ucfirst($gateway),
                'total_transactions' => $totalTransactions,
                'success_rate' => round($successRate, 1),
                'revenue' => round($gatewayTransactions->sum('amount'), 2)
            ];
        }
        
        return $stats;
    }
    
    /**
     * Get date from based on period.
     *
     * @param  string  $period
     * @return \Carbon\Carbon
     */
    private function getDateFromByPeriod($period)
    {
        return match($period) {
            'today' => Carbon::today(),
            'week' => Carbon::now()->subWeek(),
            'month' => Carbon::now()->subMonth(),
            'year' => Carbon::now()->subYear(),
            default => Carbon::now()->subMonth()
        };
    }
    
    /**
     * Get previous period date range.
     *
     * @param  string  $period
     * @param  \Carbon\Carbon  $currentPeriodStart
     * @return \Carbon\Carbon
     */
    private function getPreviousPeriod($period, $currentPeriodStart)
    {
        return match($period) {
            'today' => Carbon::yesterday(),
            'week' => Carbon::parse($currentPeriodStart)->subWeek(),
            'month' => Carbon::parse($currentPeriodStart)->subMonth(),
            'year' => Carbon::parse($currentPeriodStart)->subYear(),
            default => Carbon::parse($currentPeriodStart)->subMonth()
        };
    }
}