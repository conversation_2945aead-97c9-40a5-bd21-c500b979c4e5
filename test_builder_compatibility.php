<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "Testing BuilderService compatibility methods...\n";

// Create component categories
$cpuCategory = App\Models\ComponentCategory::firstOrCreate(
    ['slug' => 'cpu'],
    [
        'name' => 'CPU',
        'description' => 'Central Processing Unit',
        'icon' => 'cpu',
        'display_order' => 1,
        'is_required' => true,
    ]
);

$motherboardCategory = App\Models\ComponentCategory::firstOrCreate(
    ['slug' => 'motherboard'],
    [
        'name' => 'Motherboard',
        'description' => 'Motherboard',
        'icon' => 'motherboard',
        'display_order' => 2,
        'is_required' => true,
    ]
);

$ramCategory = App\Models\ComponentCategory::firstOrCreate(
    ['slug' => 'memory-ram'],
    [
        'name' => 'Memory (RAM)',
        'description' => 'System Memory',
        'icon' => 'memory',
        'display_order' => 3,
        'is_required' => true,
    ]
);

$psuCategory = App\Models\ComponentCategory::firstOrCreate(
    ['slug' => 'power-supply'],
    [
        'name' => 'Power Supply',
        'description' => 'Power Supply Unit',
        'icon' => 'psu',
        'display_order' => 4,
        'is_required' => true,
    ]
);

$gpuCategory = App\Models\ComponentCategory::firstOrCreate(
    ['slug' => 'graphics-card'],
    [
        'name' => 'Graphics Card',
        'description' => 'Graphics Processing Unit',
        'icon' => 'gpu',
        'display_order' => 5,
        'is_required' => false,
    ]
);

echo "Created component categories\n";

// Create compatible components
$cpu = App\Models\Component::factory()->create([
    'name' => 'Intel Core i7-13700K',
    'category_id' => $cpuCategory->id,
    'specs' => [
        'socket' => 'LGA1700',
        'power_consumption' => '125W',
    ],
    'stock' => 10,
]);

$motherboard = App\Models\Component::factory()->create([
    'name' => 'ASUS Z790 Motherboard',
    'category_id' => $motherboardCategory->id,
    'specs' => [
        'socket' => 'LGA1700',
        'memory_type' => 'DDR5',
        'max_memory_speed' => '5600',
    ],
    'stock' => 5,
]);

$ram = App\Models\Component::factory()->create([
    'name' => 'Corsair DDR5-5600 32GB',
    'category_id' => $ramCategory->id,
    'specs' => [
        'memory_type' => 'DDR5',
        'speed' => '5600',
        'power_consumption' => '10W',
    ],
    'stock' => 20,
]);

$psu = App\Models\Component::factory()->create([
    'name' => 'Corsair RM850x 850W',
    'category_id' => $psuCategory->id,
    'specs' => [
        'wattage' => '850W',
    ],
    'stock' => 8,
]);

$gpu = App\Models\Component::factory()->create([
    'name' => 'NVIDIA RTX 4070',
    'category_id' => $gpuCategory->id,
    'specs' => [
        'power_consumption' => '200W',
    ],
    'stock' => 3,
]);

echo "Created compatible components\n";

// Create user and build
$user = App\Models\User::factory()->create(['name' => 'Test Builder']);
$build = App\Models\Build::factory()->create([
    'user_id' => $user->id,
    'name' => 'Gaming Build Test',
    'description' => 'Testing compatibility validation',
]);

echo "Created user and build: {$build->name}\n";

// Initialize BuilderService
$builderService = new App\Services\BuilderService();

// Test 1: Add CPU to build
echo "\n=== Test 1: Adding CPU to build ===\n";
$result = $builderService->addComponentToBuild($build, $cpu, 1, null, true);
echo "Add CPU result: " . ($result['success'] ? 'SUCCESS' : 'FAILED') . "\n";
echo "Message: {$result['message']}\n";
if (!empty($result['warnings'])) {
    echo "Warnings: " . count($result['warnings']) . "\n";
}

// Test 2: Add compatible motherboard
echo "\n=== Test 2: Adding compatible motherboard ===\n";
$result = $builderService->addComponentToBuild($build, $motherboard, 1, null, true);
echo "Add Motherboard result: " . ($result['success'] ? 'SUCCESS' : 'FAILED') . "\n";
echo "Message: {$result['message']}\n";

// Test 3: Add compatible RAM
echo "\n=== Test 3: Adding compatible RAM ===\n";
$result = $builderService->addComponentToBuild($build, $ram, 1, null, true);
echo "Add RAM result: " . ($result['success'] ? 'SUCCESS' : 'FAILED') . "\n";
echo "Message: {$result['message']}\n";

// Test 4: Add PSU
echo "\n=== Test 4: Adding PSU ===\n";
$result = $builderService->addComponentToBuild($build, $psu, 1, null, true);
echo "Add PSU result: " . ($result['success'] ? 'SUCCESS' : 'FAILED') . "\n";
echo "Message: {$result['message']}\n";

// Test 5: Add GPU
echo "\n=== Test 5: Adding GPU ===\n";
$result = $builderService->addComponentToBuild($build, $gpu, 1, null, true);
echo "Add GPU result: " . ($result['success'] ? 'SUCCESS' : 'FAILED') . "\n";
echo "Message: {$result['message']}\n";

// Test 6: Validate complete build
echo "\n=== Test 6: Validating complete build ===\n";
$validation = $builderService->validateBuild($build);
echo "Build is valid: " . ($validation->isValid() ? 'YES' : 'NO') . "\n";
echo "Power consumption: {$validation->getPowerConsumption()}W\n";
echo "PSU capacity: {$validation->getPsuCapacity()}W\n";
echo "Power efficiency: " . round($validation->getPowerEfficiency(), 1) . "%\n";
echo "Power recommendation: {$validation->getPowerRecommendation()}\n";

if ($validation->getCompatibilityResult()->hasWarnings()) {
    echo "Compatibility warnings:\n";
    foreach ($validation->getCompatibilityResult()->getWarnings() as $warning) {
        echo "- {$warning['message']}\n";
    }
}

// Test 7: Calculate build power breakdown
echo "\n=== Test 7: Power consumption breakdown ===\n";
$powerInfo = $builderService->calculateBuildPower($build);
echo "Total power: {$powerInfo['total_power']}W\n";
echo "PSU power: {$powerInfo['psu_power']}W\n";
echo "Efficiency: " . round($powerInfo['efficiency'], 1) . "%\n";
echo "Recommendation: {$powerInfo['recommendation']}\n";
echo "Power breakdown:\n";
foreach ($powerInfo['breakdown'] as $item) {
    echo "- {$item['component']} ({$item['category']}): {$item['power']}W\n";
}

// Test 8: Build completion status
echo "\n=== Test 8: Build completion status ===\n";
$completion = $builderService->getBuildCompletionStatus($build);
echo "Build complete: " . ($completion['is_complete'] ? 'YES' : 'NO') . "\n";
echo "Completion: {$completion['completion_percentage']}%\n";
echo "Required components: {$completion['completed_required']}/{$completion['total_required']}\n";

if (!empty($completion['missing_categories'])) {
    echo "Missing categories:\n";
    foreach ($completion['missing_categories'] as $category) {
        echo "- {$category->name}\n";
    }
}

// Test 9: Get compatible components for a category
echo "\n=== Test 9: Getting compatible motherboards for CPU ===\n";
$compatibleMbs = $builderService->getCompatibleComponentsForBuild($build, 'motherboard');
echo "Compatible motherboards found: {$compatibleMbs->count()}\n";

// Test 10: Try to add incompatible component
echo "\n=== Test 10: Testing incompatible component ===\n";
$incompatibleMb = App\Models\Component::factory()->create([
    'name' => 'AMD B550 Motherboard',
    'category_id' => $motherboardCategory->id,
    'specs' => [
        'socket' => 'AM4',
        'memory_type' => 'DDR4',
    ],
    'stock' => 5,
]);

// Remove existing motherboard first
$builderService->removeComponentFromBuild($build, $motherboard->id);

$result = $builderService->addComponentToBuild($build, $incompatibleMb, 1, null, true);
echo "Add incompatible MB result: " . ($result['success'] ? 'SUCCESS' : 'FAILED') . "\n";
echo "Message: {$result['message']}\n";

// Test 11: Try to add component with insufficient stock
echo "\n=== Test 11: Testing insufficient stock ===\n";
$noStockComponent = App\Models\Component::factory()->create([
    'name' => 'Out of Stock Component',
    'category_id' => $cpuCategory->id,
    'stock' => 0,
]);

$result = $builderService->addComponentToBuild($build, $noStockComponent, 1, null, true);
echo "Add no-stock component result: " . ($result['success'] ? 'SUCCESS' : 'FAILED') . "\n";
echo "Message: {$result['message']}\n";

echo "\nBuilderService compatibility testing completed!\n";