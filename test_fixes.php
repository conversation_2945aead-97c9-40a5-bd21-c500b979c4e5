<?php

require_once 'vendor/autoload.php';

use App\Models\Product;
use App\Models\ProductCategory;
use App\Models\Coupon;
use App\Models\User;
use App\Services\CouponService;
use Illuminate\Support\Facades\Auth;

echo "=== Testing Product Integration Fixes ===\n\n";

try {
    // Test 1: Product creation with required fields
    echo "1. Testing Product Creation:\n";
    $product = Product::create([
        'name' => 'Test iPhone',
        'sku' => 'TEST001',
        'price' => 50000.00,
        'sale_price' => null,
        'status' => 'active',
        'in_stock' => true,
        'stock_quantity' => 10,
    ]);
    echo "✓ Product created successfully with SKU: {$product->sku}\n";
    echo "✓ Effective price: {$product->effective_price}\n\n";

    // Test 2: Category creation
    echo "2. Testing Category Creation:\n";
    $category = ProductCategory::create([
        'name' => 'Electronics',
        'is_active' => true,
    ]);
    echo "✓ Category created: {$category->name}\n\n";

    // Test 3: Product with category
    echo "3. Testing Product with Category:\n";
    $productWithCategory = Product::create([
        'name' => 'Smartphone',
        'sku' => 'PHONE001',
        'price' => 30000.00,
        'sale_price' => null,
        'category_id' => $category->id,
        'status' => 'active',
        'in_stock' => true,
        'stock_quantity' => 5,
    ]);
    echo "✓ Product with category created\n";
    echo "✓ Category path: " . $productWithCategory->getCategoryPath() . "\n\n";

    // Test 4: Coupon creation and validation
    echo "4. Testing Coupon System:\n";
    $user = User::create([
        'name' => 'Test User',
        'email' => '<EMAIL>',
        'password' => bcrypt('password'),
    ]);
    
    Auth::login($user);
    
    $coupon = Coupon::create([
        'code' => 'TEST20',
        'name' => 'Test 20% Off',
        'type' => 'percentage',
        'value' => 20,
        'is_active' => true,
    ]);
    
    $couponService = new CouponService();
    $cartItems = [['product_id' => $product->id, 'quantity' => 1]];
    $result = $couponService->validateAndApplyCoupon('TEST20', $cartItems);
    
    echo "✓ Coupon created: {$coupon->code}\n";
    echo "✓ Coupon validation: " . ($result['valid'] ? 'VALID' : 'INVALID') . "\n";
    if ($result['valid']) {
        echo "✓ Subtotal: {$result['subtotal']}\n";
        echo "✓ Discount: {$result['discount']}\n";
        echo "✓ Final total: {$result['final_total']}\n";
    }
    echo "\n";

    // Test 5: Sale price calculation
    echo "5. Testing Sale Price Calculation:\n";
    $saleProduct = Product::create([
        'name' => 'Sale Product',
        'sku' => 'SALE001',
        'price' => 1000.00,
        'sale_price' => 800.00,
        'status' => 'active',
        'in_stock' => true,
        'stock_quantity' => 3,
    ]);
    
    echo "✓ Original price: {$saleProduct->price}\n";
    echo "✓ Sale price: {$saleProduct->sale_price}\n";
    echo "✓ Effective price: {$saleProduct->effective_price}\n";
    echo "✓ Is on sale: " . ($saleProduct->is_on_sale ? 'YES' : 'NO') . "\n";
    echo "✓ Discount percentage: {$saleProduct->discount_percentage}%\n\n";

    echo "🎉 All tests passed! The fixes are working correctly.\n";

} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . ":" . $e->getLine() . "\n";
}

echo "\n=== Test Complete ===\n";