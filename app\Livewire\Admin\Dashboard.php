<?php

namespace App\Livewire\Admin;

use App\Models\Component;
use App\Models\Order;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Livewire\Component as LivewireComponent;

class Dashboard extends LivewireComponent
{
    public $totalSales;
    public $totalOrders;
    public $totalUsers;
    public $totalComponents;
    public $lowStockComponents;
    public $recentOrders;
    public $salesByMonth;
    public $chartData;
    public $topProductsByQuantity;
    public $topProductsByRevenue;
    public $revenueByCategory;
    public $userGrowthByMonth;
    public $conversionRate;
    public $pollingInterval = 10000; // 10 seconds

    public function mount()
    {
        $this->loadDashboardData();
    }

    public function loadDashboardData()
    {
        // Get total sales
        $this->totalSales = Order::where('status', 'completed')->sum('total');
        
        // Get total orders
        $this->totalOrders = Order::count();
        
        // Get total users
        $this->totalUsers = User::count();
        
        // Get total components
        $this->totalComponents = Component::count();
        
        // Get low stock components
        $this->lowStockComponents = Component::where('stock', '<', 10)->get();
        
        // Get recent orders
        $this->recentOrders = Order::with('user')->latest()->take(5)->get();
        
        // Get sales by month for the current year
        $this->salesByMonth = Order::where('status', 'completed')
            ->whereYear('created_at', date('Y'))
            ->select(DB::raw('MONTH(created_at) as month'), DB::raw('SUM(total) as total'))
            ->groupBy('month')
            ->get()
            ->keyBy('month')
            ->map(function ($item) {
                return $item->total;
            })
            ->toArray(); // <-- Convert to array
        // Fill in missing months with zero
        for ($i = 1; $i <= 12; $i++) {
            if (!isset($this->salesByMonth[$i])) {
                $this->salesByMonth[$i] = 0;
            }
        }
        // Sort by month
        ksort($this->salesByMonth);

        // Prepare chart data
        $this->prepareChartData();

        // Top 5 products by quantity
        $this->topProductsByQuantity = \App\Models\Order::join('order_items', 'orders.id', '=', 'order_items.order_id')
            ->join('components', 'order_items.component_id', '=', 'components.id')
            ->select('components.name', \DB::raw('SUM(order_items.quantity) as total_quantity'))
            ->groupBy('components.name')
            ->orderByDesc('total_quantity')
            ->take(5)
            ->get();
        // Top 5 products by revenue
        $this->topProductsByRevenue = \App\Models\Order::join('order_items', 'orders.id', '=', 'order_items.order_id')
            ->join('components', 'order_items.component_id', '=', 'components.id')
            ->select('components.name', \DB::raw('SUM(order_items.price * order_items.quantity) as total_revenue'))
            ->groupBy('components.name')
            ->orderByDesc('total_revenue')
            ->take(5)
            ->get();
        // Revenue by category
        $this->revenueByCategory = \App\Models\Order::join('order_items', 'orders.id', '=', 'order_items.order_id')
            ->join('components', 'order_items.component_id', '=', 'components.id')
            ->join('component_categories', 'components.category_id', '=', 'component_categories.id')
            ->select('component_categories.name as category', \DB::raw('SUM(order_items.price * order_items.quantity) as total_revenue'))
            ->groupBy('component_categories.name')
            ->orderByDesc('total_revenue')
            ->get();
        // User growth by month (current year)
        $this->userGrowthByMonth = \App\Models\User::whereYear('created_at', date('Y'))
            ->select(\DB::raw('MONTH(created_at) as month'), \DB::raw('COUNT(*) as total'))
            ->groupBy('month')
            ->orderBy('month')
            ->get()
            ->keyBy('month')
            ->map(function ($item) {
                return $item->total;
            })
            ->toArray(); // <-- Convert to array
        // Fill in missing months with zero
        for ($i = 1; $i <= 12; $i++) {
            if (!isset($this->userGrowthByMonth[$i])) {
                $this->userGrowthByMonth[$i] = 0;
            }
        }
        ksort($this->userGrowthByMonth);

        // Conversion rate: orders/users (as percentage)
        $userCount = \App\Models\User::count();
        $orderCount = \App\Models\Order::count();
        $this->conversionRate = $userCount > 0 ? round(($orderCount / $userCount) * 100, 2) : 0;
    }

    private function prepareChartData()
    {
        $months = [
            1 => 'Jan', 2 => 'Feb', 3 => 'Mar', 4 => 'Apr', 5 => 'May', 6 => 'Jun',
            7 => 'Jul', 8 => 'Aug', 9 => 'Sep', 10 => 'Oct', 11 => 'Nov', 12 => 'Dec'
        ];

        $labels = [];
        $data = [];

        foreach ($this->salesByMonth as $month => $total) {
            $labels[] = $months[$month];
            $data[] = $total;
        }

        $this->chartData = [
            'labels' => $labels,
            'datasets' => [
                [
                    'label' => 'Monthly Sales',
                    'data' => $data,
                    'backgroundColor' => 'rgba(75, 192, 192, 0.2)',
                    'borderColor' => 'rgba(75, 192, 192, 1)',
                    'borderWidth' => 1
                ]
            ]
        ];
    }

    public function render()
    {
        // Prepare monthly sales data
        $months = [
            1 => 'Jan', 2 => 'Feb', 3 => 'Mar', 4 => 'Apr', 5 => 'May', 6 => 'Jun',
            7 => 'Jul', 8 => 'Aug', 9 => 'Sep', 10 => 'Oct', 11 => 'Nov', 12 => 'Dec'
        ];
        $labels = [];
        $data = [];
        foreach ($this->salesByMonth as $month => $total) {
            $labels[] = $months[$month];
            $data[] = $total;
        }
        $monthlySalesData = [
            'labels' => $labels,
            'data' => $data
        ];

        // Prepare order status data
        $statuses = ['pending', 'processing', 'completed', 'cancelled', 'refunded'];
        $orderStatusCounts = [];
        foreach ($statuses as $status) {
            $orderStatusCounts[$status] = Order::where('status', $status)->count();
        }
        $orderStatusData = [
            'labels' => array_map('ucfirst', $statuses),
            'data' => array_values($orderStatusCounts)
        ];

        return view('livewire.admin.dashboard', [
            'totalSales' => $this->totalSales,
            'totalOrders' => $this->totalOrders,
            'totalUsers' => $this->totalUsers,
            'totalComponents' => $this->totalComponents,
            'lowStockComponents' => $this->lowStockComponents,
            'recentOrders' => $this->recentOrders,
            'monthlySalesData' => $monthlySalesData,
            'orderStatusData' => $orderStatusData,
            'topProductsByQuantity' => $this->topProductsByQuantity,
            'topProductsByRevenue' => $this->topProductsByRevenue,
            'revenueByCategory' => $this->revenueByCategory,
            'userGrowthByMonth' => $this->userGrowthByMonth,
            'conversionRate' => $this->conversionRate,
        ]);
    }
}