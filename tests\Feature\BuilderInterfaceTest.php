<?php

namespace Tests\Feature;

use App\Livewire\Builder\BuilderContainer;
use App\Models\Component;
use App\Models\ComponentCategory;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Livewire\Livewire;
use Tests\TestCase;

class BuilderInterfaceTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test categories
        $this->motherboardCategory = ComponentCategory::factory()->create([
            'name' => 'Motherboard',
            'slug' => 'motherboard',
            'is_required' => true,
            'display_order' => 1,
        ]);
        
        $this->cpuCategory = ComponentCategory::factory()->create([
            'name' => 'CPU',
            'slug' => 'cpu',
            'is_required' => true,
            'display_order' => 2,
        ]);
        
        $this->ramCategory = ComponentCategory::factory()->create([
            'name' => 'RAM',
            'slug' => 'ram',
            'is_required' => true,
            'display_order' => 3,
        ]);
        
        // Create test components
        $this->motherboard = Component::factory()->create([
            'category_id' => $this->motherboardCategory->id,
            'name' => 'ASUS ROG Strix B550-F',
            'brand' => 'ASUS',
            'price' => 189.99,
            'stock' => 10,
            'is_active' => true,
        ]);
        
        $this->cpu = Component::factory()->create([
            'category_id' => $this->cpuCategory->id,
            'name' => 'AMD Ryzen 5 5600X',
            'brand' => 'AMD',
            'price' => 299.99,
            'stock' => 5,
            'is_active' => true,
        ]);
        
        $this->ram = Component::factory()->create([
            'category_id' => $this->ramCategory->id,
            'name' => 'Corsair Vengeance LPX 16GB',
            'brand' => 'Corsair',
            'price' => 79.99,
            'stock' => 0, // Out of stock
            'is_active' => true,
        ]);
    }

    /** @test */
    public function it_displays_component_categories_in_sidebar()
    {
        Livewire::test(BuilderContainer::class)
            ->assertSee('Motherboard')
            ->assertSee('CPU')
            ->assertSee('RAM')
            ->assertSee('Components');
    }

    /** @test */
    public function it_shows_required_category_indicators()
    {
        Livewire::test(BuilderContainer::class)
            ->assertSeeHtml('text-red-500'); // Required indicator should be present
    }

    /** @test */
    public function it_can_select_a_category()
    {
        Livewire::test(BuilderContainer::class)
            ->call('selectCategory', 'motherboard')
            ->assertSet('selectedCategory', 'motherboard')
            ->assertSee('Select Motherboard');
    }

    /** @test */
    public function it_displays_components_when_category_is_selected()
    {
        Livewire::test(BuilderContainer::class)
            ->call('selectCategory', 'motherboard')
            ->assertSee('ASUS ROG Strix B550-F')
            ->assertSee('$189.99')
            ->assertSee('ASUS');
    }

    /** @test */
    public function it_can_add_component_to_build()
    {
        Livewire::test(BuilderContainer::class)
            ->call('selectCategory', 'motherboard')
            ->call('addComponent', $this->motherboard->id, 'motherboard')
            ->assertSet('selectedComponents.motherboard.component_id', $this->motherboard->id)
            ->assertSet('selectedComponents.motherboard.name', 'ASUS ROG Strix B550-F')
            ->assertSet('selectedComponents.motherboard.price', 189.99);
    }

    /** @test */
    public function it_calculates_total_price_when_components_added()
    {
        Livewire::test(BuilderContainer::class)
            ->call('addComponent', $this->motherboard->id, 'motherboard')
            ->call('addComponent', $this->cpu->id, 'cpu')
            ->assertSet('totalPrice', 489.98); // 189.99 + 299.99
    }

    /** @test */
    public function it_can_remove_component_from_build()
    {
        Livewire::test(BuilderContainer::class)
            ->call('addComponent', $this->motherboard->id, 'motherboard')
            ->assertSet('totalPrice', 189.99)
            ->call('removeComponent', 'motherboard')
            ->assertSet('totalPrice', 0)
            ->assertCount('selectedComponents', 0);
    }

    /** @test */
    public function it_shows_build_completion_status()
    {
        $component = Livewire::test(BuilderContainer::class);
        
        // Initially incomplete
        $component->assertSet('isComplete', false);
        
        // Add all required components
        $component->call('addComponent', $this->motherboard->id, 'motherboard')
                 ->call('addComponent', $this->cpu->id, 'cpu')
                 ->call('addComponent', $this->ram->id, 'ram')
                 ->assertSet('isComplete', true);
    }

    /** @test */
    public function it_displays_stock_status_correctly()
    {
        Livewire::test(BuilderContainer::class)
            ->call('selectCategory', 'motherboard')
            ->assertSee('In Stock (10)')
            ->call('selectCategory', 'ram')
            ->assertSee('Out of Stock');
    }

    /** @test */
    public function it_disables_out_of_stock_components()
    {
        Livewire::test(BuilderContainer::class)
            ->call('selectCategory', 'ram')
            ->assertSeeHtml('disabled')
            ->assertSee('Out of Stock');
    }

    /** @test */
    public function it_can_search_components()
    {
        Livewire::test(BuilderContainer::class)
            ->call('selectCategory', 'motherboard')
            ->set('searchTerm', 'ASUS')
            ->assertSee('ASUS ROG Strix B550-F');
    }

    /** @test */
    public function it_can_filter_by_stock_status()
    {
        Livewire::test(BuilderContainer::class)
            ->call('selectCategory', 'ram')
            ->set('inStockOnly', true)
            ->assertDontSee('Corsair Vengeance LPX 16GB');
    }

    /** @test */
    public function it_can_sort_components()
    {
        // Create another motherboard with different price
        $cheaperMotherboard = Component::factory()->create([
            'category_id' => $this->motherboardCategory->id,
            'name' => 'MSI B450 Tomahawk',
            'brand' => 'MSI',
            'price' => 99.99,
            'stock' => 3,
        ]);

        Livewire::test(BuilderContainer::class)
            ->call('selectCategory', 'motherboard')
            ->set('sortBy', 'price')
            ->set('sortDirection', 'asc')
            ->assertSeeInOrder(['MSI B450 Tomahawk', 'ASUS ROG Strix B550-F']);
    }

    /** @test */
    public function it_shows_compatibility_status_when_components_selected()
    {
        Livewire::test(BuilderContainer::class)
            ->call('addComponent', $this->motherboard->id, 'motherboard')
            ->call('selectCategory', 'cpu')
            ->assertSeeHtml('Compatible'); // Should show compatibility indicators
    }

    /** @test */
    public function it_displays_build_summary_table()
    {
        Livewire::test(BuilderContainer::class)
            ->call('addComponent', $this->motherboard->id, 'motherboard')
            ->assertSee('Your Build')
            ->assertSee('ASUS ROG Strix B550-F')
            ->assertSee('$189.99')
            ->assertSee('Remove');
    }

    /** @test */
    public function it_shows_save_build_form_when_complete()
    {
        Livewire::test(BuilderContainer::class)
            ->call('addComponent', $this->motherboard->id, 'motherboard')
            ->call('addComponent', $this->cpu->id, 'cpu')
            ->call('addComponent', $this->ram->id, 'ram')
            ->assertSee('Add All to Cart'); // Should show when complete
    }

    /** @test */
    public function it_shows_incomplete_message_when_missing_required_components()
    {
        Livewire::test(BuilderContainer::class)
            ->call('addComponent', $this->motherboard->id, 'motherboard')
            ->assertSee('Please select all required components to complete your build.');
    }

    /** @test */
    public function it_displays_compatibility_issues_when_present()
    {
        // This test would need mock compatibility issues
        // For now, we'll test the UI structure
        Livewire::test(BuilderContainer::class)
            ->call('addComponent', $this->motherboard->id, 'motherboard')
            ->call('addComponent', $this->cpu->id, 'cpu')
            ->assertDontSeeHtml('<h3 class="text-lg font-medium text-red-800">Compatibility Issues</h3>'); // Should not show when no issues
    }

    /** @test */
    public function authenticated_user_can_access_builder()
    {
        $user = User::factory()->create();
        
        $this->actingAs($user);
        
        Livewire::test(BuilderContainer::class)
            ->assertSuccessful();
    }

    /** @test */
    public function guest_user_can_access_builder()
    {
        Livewire::test(BuilderContainer::class)
            ->assertSuccessful();
    }

    /** @test */
    public function it_maintains_filter_state_when_switching_categories()
    {
        Livewire::test(BuilderContainer::class)
            ->set('searchTerm', 'test')
            ->set('inStockOnly', true)
            ->call('selectCategory', 'motherboard')
            ->assertSet('searchTerm', 'test')
            ->assertSet('inStockOnly', true);
    }

    /** @test */
    public function it_resets_components_when_category_changes()
    {
        Livewire::test(BuilderContainer::class)
            ->call('selectCategory', 'motherboard')
            ->assertSee('ASUS ROG Strix B550-F')
            ->call('selectCategory', 'cpu')
            ->assertSee('AMD Ryzen 5 5600X')
            ->assertDontSee('ASUS ROG Strix B550-F');
    }
}