# PC Builder Web Application with E-commerce - Directory Structure

project-root/
│
├── app/                             # Application core
│   ├── Console/                     # Artisan commands
│   │   └── Commands/
│   │       ├── ImportComponents.php
│   │       └── UpdatePrices.php
│   │
│   ├── Exceptions/                  # Exception handlers
│   │
│   ├── Http/                        # HTTP layer
│   │   ├── Controllers/             # Controllers
│   │   │   ├── Admin/               # Admin controllers
│   │   │   │   ├── ComponentController.php
│   │   │   │   ├── OrderController.php
│   │   │   │   ├── DashboardController.php
│   │   │   │   └── StatsController.php
│   │   │   │
│   │   │   ├── Auth/                # Auth controllers if needed beyond Fortify
│   │   │   ├── BuildController.php
│   │   │   ├── CartController.php
│   │   │   ├── CheckoutController.php
│   │   │   ├── ComponentController.php
│   │   │   ├── OrderController.php
│   │   │   └── PageController.php
│   │   │
│   │   ├── Livewire/               # Livewire components
│   │   │   ├── Admin/              # Admin Livewire components
│   │   │   │   ├── ComponentForm.php
│   │   │   │   ├── ComponentsList.php
│   │   │   │   ├── OrderManagement.php
│   │   │   │   └── Dashboard.php
│   │   │   │
│   │   │   ├── Builder/            # PC Builder specific components
│   │   │   │   ├── BuilderContainer.php
│   │   │   │   ├── ComponentSelector.php
│   │   │   │   ├── CompatibilityChecker.php
│   │   │   │   ├── BuildSummary.php
│   │   │   │   └── SaveBuildForm.php
│   │   │   │
│   │   │   ├── Shop/               # E-commerce components
│   │   │   │   ├── Cart.php
│   │   │   │   ├── CartIcon.php
│   │   │   │   ├── Checkout.php
│   │   │   │   ├── ProductCard.php
│   │   │   │   ├── ProductFilter.php
│   │   │   │   ├── ProductList.php
│   │   │   │   └── RelatedProducts.php
│   │   │   │
│   │   │   ├── User/               # User-related components
│   │   │   │   ├── AccountSettings.php
│   │   │   │   ├── OrderHistory.php
│   │   │   │   └── SavedBuilds.php
│   │   │   │
│   │   │   └── CommentSection.php
│   │   │
│   │   ├── Middleware/             # Custom middleware
│   │   └── Requests/               # Form requests
│   │
│   ├── Jobs/                        # Queue jobs
│   │   ├── ProcessOrder.php
│   │   ├── SendOrderConfirmation.php
│   │   └── UpdateComponentPrices.php
│   │
│   ├── Listeners/                   # Event listeners
│   │
│   ├── Mail/                        # Mail classes
│   │   ├── OrderConfirmation.php
│   │   └── BuildShared.php
│   │
│   ├── Models/                      # Eloquent models
│   │   ├── User.php
│   │   ├── Component.php
│   │   ├── ComponentCategory.php
│   │   ├── ComponentCompatibility.php
│   │   ├── Build.php
│   │   ├── BuildComponent.php
│   │   ├── Cart.php
│   │   ├── CartItem.php
│   │   ├── Order.php
│   │   ├── OrderItem.php
│   │   ├── Payment.php
│   │   └── Review.php
│   │
│   ├── Notifications/               # Notification classes
│   │
│   ├── Policies/                    # Access control policies
│   │
│   ├── Providers/                   # Service providers
│   │
│   ├── Rules/                       # Custom validation rules
│   │   └── ComponentCompatibilityRule.php
│   │
│   └── Services/                    # Service classes
│       ├── BuilderService.php
│       ├── CompatibilityService.php
│       ├── CartService.php
│       ├── CheckoutService.php
│       ├── PaymentGatewayService.php
│       └── PriceTrackingService.php
│
├── bootstrap/                       # App bootstrapping
│
├── config/                          # Configuration files
│   ├── builder.php                  # PC Builder specific config
│   ├── shop.php                     # E-commerce specific config
│   └── compatibility.php            # Component compatibility rules
│
├── database/                        # Database
│   ├── factories/                   # Model factories
│   ├── migrations/                  # Migrations
│   └── seeders/                     # Database seeders
│       ├── ComponentCategorySeeder.php
│       ├── ComponentSeeder.php
│       └── CompatibilityRulesSeeder.php
│
├── public/                          # Publicly accessible files
│   ├── css/
│   ├── js/
│   ├── images/
│   └── components/                  # Component images
│
├── resources/                       # Resources
│   ├── css/                         # CSS/SCSS files
│   │   └── app.css
│   │
│   ├── js/                          # JavaScript files
│   │   └── app.js
│   │
│   ├── views/                       # Blade templates
│   │   ├── admin/                   # Admin views
│   │   │   ├── dashboard.blade.php
│   │   │   ├── components/
│   │   │   ├── orders/
│   │   │   └── settings/
│   │   │
│   │   ├── auth/                    # Authentication views
│   │   │
│   │   ├── builder/                 # PC Builder views
│   │   │   ├── index.blade.php
│   │   │   ├── saved-builds.blade.php
│   │   │   └── build-details.blade.php
│   │   │
│   │   ├── components/              # Reusable Blade components
│   │   │   ├── buttons/
│   │   │   ├── cards/
│   │   │   ├── forms/
│   │   │   └── layout/
│   │   │
│   │   ├── layouts/                 # Layout templates
│   │   │   ├── app.blade.php
│   │   │   ├── admin.blade.php
│   │   │   └── builder.blade.php
│   │   │
│   │   ├── livewire/                # Livewire component views
│   │   │   ├── admin/
│   │   │   ├── builder/
│   │   │   ├── shop/
│   │   │   └── user/
│   │   │
│   │   ├── shop/                    # E-commerce views
│   │   │   ├── index.blade.php
│   │   │   ├── product.blade.php
│   │   │   ├── category.blade.php
│   │   │   ├── cart.blade.php
│   │   │   └── checkout.blade.php
│   │   │
│   │   ├── user/                    # User account views
│   │   │   ├── profile.blade.php
│   │   │   ├── orders.blade.php
│   │   │   └── builds.blade.php
│   │   │
│   │   ├── emails/                  # Email templates
│   │   │
│   │   └── pages/                   # Static pages
│   │       ├── home.blade.php
│   │       ├── about.blade.php
│   │       └── contact.blade.php
│   │
│   └── lang/                        # Localization files
│
├── routes/                          # Routes
│   ├── web.php                      # Web routes
│   ├── admin.php                    # Admin routes
│   ├── api.php                      # API routes
│   └── channels.php                 # Broadcast channels
│
├── storage/                         # Storage
│
├── tests/                           # Tests
│   ├── Feature/                     # Feature tests
│   │   ├── Builder/
│   │   ├── Shop/
│   │   └── Admin/
│   │
│   └── Unit/                        # Unit tests
│       ├── Services/
│       └── Models/
│
├── vendor/                          # Composer dependencies
│
├── .env                             # Environment variables
├── .env.example                     # Example environment file
├── .gitignore                       # Git ignore file
├── composer.json                    # Composer dependencies
├── package.json                     # NPM dependencies
├── phpunit.xml                      # PHPUnit configuration
├── README.md                        # Project documentation
├── artisan                          # Laravel Artisan CLI
└── vite.config.js                   # Vite configuration