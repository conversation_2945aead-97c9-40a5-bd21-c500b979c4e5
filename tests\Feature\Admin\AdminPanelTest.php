<?php

namespace Tests\Feature\Admin;

use App\Models\GatewaySetting;
use App\Models\Transaction;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Hash;
use Tests\TestCase;

class AdminPanelTest extends TestCase
{
    use RefreshDatabase;

    private User $adminUser;
    private User $regularUser;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create admin user
        $this->adminUser = User::factory()->create([
            'email' => '<EMAIL>',
            'role' => User::ROLE_ADMIN ?? 'admin',
            'password' => Hash::make('password')
        ]);

        // Create regular user
        $this->regularUser = User::factory()->create([
            'email' => '<EMAIL>',
            'role' => User::ROLE_USER ?? 'user'
        ]);

        // Create gateway settings
        $this->createGatewaySettings();
    }

    private function createGatewaySettings(): void
    {
        GatewaySetting::create([
            'gateway_name' => 'razorpay',
            'is_enabled' => true,
            'is_test_mode' => true,
            'settings' => [
                'key_id' => 'rzp_test_key',
                'key_secret' => 'test_secret',
                'webhook_secret' => 'webhook_secret'
            ]
        ]);

        GatewaySetting::create([
            'gateway_name' => 'payumoney',
            'is_enabled' => false,
            'is_test_mode' => true,
            'settings' => [
                'merchant_key' => 'test_merchant_key',
                'salt' => 'test_salt',
                'auth_header' => 'test_auth'
            ]
        ]);

        GatewaySetting::create([
            'gateway_name' => 'cashfree',
            'is_enabled' => true,
            'is_test_mode' => false,
            'settings' => [
                'app_id' => 'live_app_id',
                'secret_key' => 'live_secret',
                'client_id' => 'live_client_id',
                'client_secret' => 'live_client_secret'
            ]
        ]);
    }

    /** @test */
    public function admin_can_view_gateway_settings_index()
    {
        // Requirements: 4.1
        $response = $this->actingAs($this->adminUser)
                        ->get(route('admin.gateways.index'));

        $response->assertStatus(200);
        $response->assertViewIs('admin.gateways.index');
        $response->assertViewHas('gateways');
        
        $gateways = $response->viewData('gateways');
        $this->assertCount(3, $gateways);
        
        // Check that all gateways are present
        $gatewayNames = $gateways->pluck('gateway_name')->toArray();
        $this->assertContains('razorpay', $gatewayNames);
        $this->assertContains('payumoney', $gatewayNames);
        $this->assertContains('cashfree', $gatewayNames);
    }

    /** @test */
    public function admin_can_view_specific_gateway_settings()
    {
        // Requirements: 4.1
        $response = $this->actingAs($this->adminUser)
                        ->get(route('admin.gateways.show', 'razorpay'));

        $response->assertStatus(200);
        $response->assertViewIs('admin.gateways.show');
        $response->assertViewHas('gateway');
        $response->assertViewHas('gatewayName', 'razorpay');
        
        $gateway = $response->viewData('gateway');
        $this->assertEquals('razorpay', $gateway->gateway_name);
        $this->assertTrue($gateway->is_enabled);
        $this->assertTrue($gateway->is_test_mode);
    }

    /** @test */
    public function admin_can_enable_disabled_gateway()
    {
        // Requirements: 4.2
        $response = $this->actingAs($this->adminUser)
                        ->postJson(route('admin.gateways.toggle', 'payumoney'), [
                            'enabled' => true
                        ]);

        $response->assertStatus(200);
        $response->assertJson([
            'success' => true,
            'data' => [
                'gateway' => 'payumoney',
                'is_enabled' => true
            ]
        ]);

        $this->assertDatabaseHas('gateway_settings', [
            'gateway_name' => 'payumoney',
            'is_enabled' => true
        ]);
    }

    /** @test */
    public function admin_can_disable_enabled_gateway()
    {
        // Requirements: 4.2
        $response = $this->actingAs($this->adminUser)
                        ->postJson(route('admin.gateways.toggle', 'razorpay'), [
                            'enabled' => false
                        ]);

        $response->assertStatus(200);
        $response->assertJson([
            'success' => true,
            'data' => [
                'gateway' => 'razorpay',
                'is_enabled' => false
            ]
        ]);

        $this->assertDatabaseHas('gateway_settings', [
            'gateway_name' => 'razorpay',
            'is_enabled' => false
        ]);
    }

    /** @test */
    public function admin_can_switch_gateway_to_test_mode()
    {
        // Requirements: 4.4
        $response = $this->actingAs($this->adminUser)
                        ->postJson(route('admin.gateways.switch-mode', 'cashfree'), [
                            'test_mode' => true
                        ]);

        $response->assertStatus(200);
        $response->assertJson([
            'success' => true,
            'data' => [
                'gateway' => 'cashfree',
                'is_test_mode' => true
            ]
        ]);

        $this->assertDatabaseHas('gateway_settings', [
            'gateway_name' => 'cashfree',
            'is_test_mode' => true
        ]);
    }

    /** @test */
    public function admin_can_switch_gateway_to_live_mode()
    {
        // Requirements: 4.4
        $response = $this->actingAs($this->adminUser)
                        ->postJson(route('admin.gateways.switch-mode', 'razorpay'), [
                            'test_mode' => false
                        ]);

        $response->assertStatus(200);
        $response->assertJson([
            'success' => true,
            'data' => [
                'gateway' => 'razorpay',
                'is_test_mode' => false
            ]
        ]);

        $this->assertDatabaseHas('gateway_settings', [
            'gateway_name' => 'razorpay',
            'is_test_mode' => false
        ]);
    }

    /** @test */
    public function admin_can_update_razorpay_credentials()
    {
        // Requirements: 4.3
        $newSettings = [
            'is_enabled' => true,
            'is_test_mode' => false,
            'settings' => [
                'key_id' => 'rzp_live_new_key',
                'key_secret' => 'new_live_secret',
                'webhook_secret' => 'new_webhook_secret'
            ]
        ];

        $response = $this->actingAs($this->adminUser)
                        ->putJson(route('admin.gateways.update', 'razorpay'), $newSettings);

        $response->assertStatus(200);
        $response->assertJson([
            'success' => true,
            'message' => 'Gateway settings updated successfully'
        ]);

        $this->assertDatabaseHas('gateway_settings', [
            'gateway_name' => 'razorpay',
            'is_enabled' => true,
            'is_test_mode' => false
        ]);

        $gateway = GatewaySetting::where('gateway_name', 'razorpay')->first();
        $this->assertEquals('rzp_live_new_key', $gateway->settings['key_id']);
        $this->assertEquals('new_live_secret', $gateway->settings['key_secret']);
        $this->assertEquals('new_webhook_secret', $gateway->settings['webhook_secret']);
    }

    /** @test */
    public function admin_can_update_payumoney_credentials()
    {
        // Requirements: 4.3
        $newSettings = [
            'is_enabled' => true,
            'is_test_mode' => false,
            'settings' => [
                'merchant_key' => 'live_merchant_key',
                'salt' => 'live_salt',
                'auth_header' => 'live_auth_header'
            ]
        ];

        $response = $this->actingAs($this->adminUser)
                        ->putJson(route('admin.gateways.update', 'payumoney'), $newSettings);

        $response->assertStatus(200);
        $response->assertJson([
            'success' => true,
            'message' => 'Gateway settings updated successfully'
        ]);

        $this->assertDatabaseHas('gateway_settings', [
            'gateway_name' => 'payumoney',
            'is_enabled' => true,
            'is_test_mode' => false
        ]);

        $gateway = GatewaySetting::where('gateway_name', 'payumoney')->first();
        $this->assertEquals('live_merchant_key', $gateway->settings['merchant_key']);
        $this->assertEquals('live_salt', $gateway->settings['salt']);
        $this->assertEquals('live_auth_header', $gateway->settings['auth_header']);
    }

    /** @test */
    public function admin_can_update_cashfree_credentials()
    {
        // Requirements: 4.3
        $newSettings = [
            'is_enabled' => true,
            'is_test_mode' => true,
            'settings' => [
                'app_id' => 'test_app_id',
                'secret_key' => 'test_secret_key',
                'client_id' => 'test_client_id',
                'client_secret' => 'test_client_secret'
            ]
        ];

        $response = $this->actingAs($this->adminUser)
                        ->putJson(route('admin.gateways.update', 'cashfree'), $newSettings);

        $response->assertStatus(200);
        $response->assertJson([
            'success' => true,
            'message' => 'Gateway settings updated successfully'
        ]);

        $this->assertDatabaseHas('gateway_settings', [
            'gateway_name' => 'cashfree',
            'is_enabled' => true,
            'is_test_mode' => true
        ]);

        $gateway = GatewaySetting::where('gateway_name', 'cashfree')->first();
        $this->assertEquals('test_app_id', $gateway->settings['app_id']);
        $this->assertEquals('test_secret_key', $gateway->settings['secret_key']);
        $this->assertEquals('test_client_id', $gateway->settings['client_id']);
        $this->assertEquals('test_client_secret', $gateway->settings['client_secret']);
    }

    /** @test */
    public function regular_user_cannot_access_admin_gateway_settings()
    {
        // Requirements: 4.5 - Security
        $response = $this->actingAs($this->regularUser)
                        ->get(route('admin.gateways.index'));

        $response->assertStatus(403);
    }

    /** @test */
    public function unauthenticated_user_cannot_access_admin_gateway_settings()
    {
        // Requirements: 4.5 - Security
        $response = $this->get(route('admin.gateways.index'));

        $response->assertRedirect(route('admin.login'));
    }

    /** @test */
    public function admin_can_view_transaction_list()
    {
        // Requirements: 4.6
        // Create some test transactions
        Transaction::factory()->count(5)->completed()->create([
            'user_id' => $this->regularUser->id
        ]);

        Transaction::factory()->count(3)->create([
            'user_id' => $this->regularUser->id,
            'status' => Transaction::STATUS_PENDING
        ]);

        $response = $this->actingAs($this->adminUser)
                        ->get(route('admin.transactions.index'));

        $response->assertStatus(200);
        $response->assertViewIs('admin.transactions.index');
        $response->assertViewHas('transactions');
        
        $transactions = $response->viewData('transactions');
        $this->assertCount(8, $transactions);
    }

    /** @test */
    public function admin_can_view_specific_transaction()
    {
        // Requirements: 4.6
        $transaction = Transaction::factory()->completed()->create([
            'user_id' => $this->regularUser->id,
            'amount' => 1000.00,
            'gateway_name' => 'razorpay'
        ]);

        $response = $this->actingAs($this->adminUser)
                        ->get(route('admin.transactions.show', $transaction->id));

        $response->assertStatus(200);
        $response->assertViewIs('admin.transactions.show');
        $response->assertViewHas('transaction');
        
        $viewTransaction = $response->viewData('transaction');
        $this->assertEquals($transaction->id, $viewTransaction->id);
        $this->assertEquals(Transaction::STATUS_COMPLETED, $viewTransaction->status);
        $this->assertEquals(1000.00, $viewTransaction->amount);
        $this->assertEquals('razorpay', $viewTransaction->gateway_name);
    }

    /** @test */
    public function admin_can_filter_transactions_by_status()
    {
        // Requirements: 4.6
        Transaction::factory()->count(3)->completed()->create();
        Transaction::factory()->count(2)->create(['status' => Transaction::STATUS_PENDING]);
        Transaction::factory()->count(1)->failed()->create();

        $response = $this->actingAs($this->adminUser)
                        ->get(route('admin.transactions.index', ['status' => Transaction::STATUS_COMPLETED]));

        $response->assertStatus(200);
        $transactions = $response->viewData('transactions');
        $this->assertCount(3, $transactions);
        
        foreach ($transactions as $transaction) {
            $this->assertEquals(Transaction::STATUS_COMPLETED, $transaction->status);
        }
    }

    /** @test */
    public function admin_can_filter_transactions_by_gateway()
    {
        // Requirements: 4.6
        Transaction::factory()->count(2)->create(['gateway_name' => 'razorpay']);
        Transaction::factory()->count(3)->create(['gateway_name' => 'cashfree']);
        Transaction::factory()->count(1)->create(['gateway_name' => 'payumoney']);

        $response = $this->actingAs($this->adminUser)
                        ->get(route('admin.transactions.index', ['gateway' => 'cashfree']));

        $response->assertStatus(200);
        $transactions = $response->viewData('transactions');
        $this->assertCount(3, $transactions);
        
        foreach ($transactions as $transaction) {
            $this->assertEquals('cashfree', $transaction->gateway_name);
        }
    }

    /** @test */
    public function admin_cannot_update_nonexistent_gateway()
    {
        // Requirements: 4.7 - Error handling
        $response = $this->actingAs($this->adminUser)
                        ->putJson(route('admin.gateways.update', 'nonexistent'), [
                            'is_enabled' => true,
                            'settings' => []
                        ]);

        $response->assertStatus(404);
    }

    /** @test */
    public function admin_cannot_toggle_nonexistent_gateway()
    {
        // Requirements: 4.7 - Error handling
        $response = $this->actingAs($this->adminUser)
                        ->postJson(route('admin.gateways.toggle', 'nonexistent'), [
                            'enabled' => true
                        ]);

        $response->assertStatus(404);
    }

    /** @test */
    public function admin_update_gateway_validates_required_fields()
    {
        // Requirements: 4.7 - Validation
        $response = $this->actingAs($this->adminUser)
                        ->putJson(route('admin.gateways.update', 'razorpay'), [
                            'is_enabled' => true,
                            'settings' => [
                                'key_id' => '', // Empty required field
                                'key_secret' => 'secret'
                            ]
                        ]);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['settings.key_id']);
    }
}