<?php

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Hash;

uses(RefreshDatabase::class);

beforeEach(function () {
    // Create an admin user for testing
    $this->admin = User::factory()->create([
        'role' => User::ROLE_ADMIN,
        'status' => User::STATUS_ACTIVE,
    ]);
    
    // Create some regular users for testing
    $this->users = User::factory()->count(3)->create([
        'role' => User::ROLE_USER,
        'status' => User::STATUS_ACTIVE,
    ]);
});

test('admin can view users index page', function () {
    $response = $this->actingAs($this->admin)
        ->get(route('admin.users.index'));
    
    $response->assertStatus(200);
    $response->assertViewIs('admin.users.index');
    $response->assertViewHas('users');
});

test('non-admin cannot access users index page', function () {
    $regularUser = User::factory()->create([
        'role' => User::ROLE_USER,
        'status' => User::STATUS_ACTIVE,
    ]);
    
    $response = $this->actingAs($regularUser)
        ->get(route('admin.users.index'));
    
    $response->assertStatus(403);
});

test('admin can search users by name or email', function () {
    // Create a user with a unique name for testing search
    $searchUser = User::factory()->create([
        'name' => 'SearchableUserName',
        'email' => '<EMAIL>',
    ]);
    
    // Search by name
    $response = $this->actingAs($this->admin)
        ->get(route('admin.users.index', ['search' => 'SearchableUserName']));
    
    $response->assertStatus(200);
    $response->assertViewHas('users', function ($users) use ($searchUser) {
        return $users->contains($searchUser);
    });
    
    // Search by email
    $response = $this->actingAs($this->admin)
        ->get(route('admin.users.index', ['search' => 'searchable@example']));
    
    $response->assertStatus(200);
    $response->assertViewHas('users', function ($users) use ($searchUser) {
        return $users->contains($searchUser);
    });
});

test('admin can filter users by role', function () {
    $response = $this->actingAs($this->admin)
        ->get(route('admin.users.index', ['role' => User::ROLE_USER]));
    
    $response->assertStatus(200);
    $response->assertViewHas('users', function ($users) {
        return $users->every(fn ($user) => $user->role === User::ROLE_USER);
    });
});

test('admin can filter users by status', function () {
    // Create an inactive user
    $inactiveUser = User::factory()->create([
        'status' => User::STATUS_INACTIVE,
    ]);
    
    $response = $this->actingAs($this->admin)
        ->get(route('admin.users.index', ['status' => User::STATUS_INACTIVE]));
    
    $response->assertStatus(200);
    $response->assertViewHas('users', function ($users) {
        return $users->every(fn ($user) => $user->status === User::STATUS_INACTIVE);
    });
});

test('admin can view create user form', function () {
    $response = $this->actingAs($this->admin)
        ->get(route('admin.users.create'));
    
    $response->assertStatus(200);
    $response->assertViewIs('admin.users.create');
});

test('admin can create a new user', function () {
    $userData = [
        'name' => 'New Test User',
        'email' => '<EMAIL>',
        'password' => 'password123',
        'password_confirmation' => 'password123',
        'role' => User::ROLE_USER,
        'status' => User::STATUS_ACTIVE,
    ];
    
    $response = $this->actingAs($this->admin)
        ->post(route('admin.users.store'), $userData);
    
    $response->assertStatus(302);
    $response->assertJson(['message' => 'User created successfully']);
    
    $this->assertDatabaseHas('users', [
        'name' => 'New Test User',
        'email' => '<EMAIL>',
        'role' => User::ROLE_USER,
        'status' => User::STATUS_ACTIVE,
    ]);
});

test('admin can view edit user form', function () {
    $user = $this->users->first();
    
    $response = $this->actingAs($this->admin)
        ->get(route('admin.users.edit', $user));
    
    $response->assertStatus(200);
    $response->assertViewIs('admin.users.edit');
    $response->assertViewHas('user', $user);
});

test('admin can update a user', function () {
    $user = $this->users->first();
    
    $userData = [
        'name' => 'Updated User Name',
        'email' => $user->email,
        'role' => User::ROLE_USER,
        'status' => User::STATUS_ACTIVE,
    ];
    
    $response = $this->actingAs($this->admin)
        ->put(route('admin.users.update', $user), $userData);
    
    $response->assertStatus(302);
    $response->assertJson(['message' => 'User updated successfully']);
    
    $this->assertDatabaseHas('users', [
        'id' => $user->id,
        'name' => 'Updated User Name',
    ]);
});

test('admin can update user password', function () {
    $user = $this->users->first();
    
    $userData = [
        'name' => $user->name,
        'email' => $user->email,
        'role' => $user->role,
        'status' => $user->status,
        'password' => 'newpassword123',
        'password_confirmation' => 'newpassword123',
    ];
    
    $response = $this->actingAs($this->admin)
        ->put(route('admin.users.update', $user), $userData);
    
    $response->assertStatus(302);
    $response->assertJson(['message' => 'User updated successfully']);
    
    // Instead of testing login directly, verify the password was updated
    $this->assertTrue(Hash::check('newpassword123', $user->fresh()->password));
});

test('admin can delete a user', function () {
    $user = $this->users->first();
    
    $response = $this->actingAs($this->admin)
        ->delete(route('admin.users.destroy', $user));
    
    $response->assertStatus(302);
    $response->assertJson(['message' => 'User deleted successfully']);
    
    // Since User model uses soft deletes, check for deleted_at field
    $this->assertDatabaseHas('users', [
        'id' => $user->id,
        'deleted_at' => now()->toDateTimeString(),
    ]);
});

test('admin cannot delete their own account', function () {
    $response = $this->actingAs($this->admin)
        ->delete(route('admin.users.destroy', $this->admin));
    
    $response->assertStatus(302);
    $response->assertJson(['error' => 'You cannot delete your own account.']);
    
    $this->assertDatabaseHas('users', ['id' => $this->admin->id]);
});

test('user creation validates required fields', function () {
    $response = $this->actingAs($this->admin)
        ->post(route('admin.users.store'), []);
    
    $response->assertSessionHasErrors(['name', 'email', 'password', 'role', 'status']);
});

test('user update validates required fields', function () {
    $user = $this->users->first();
    
    $response = $this->actingAs($this->admin)
        ->put(route('admin.users.update', $user), []);
    
    $response->assertSessionHasErrors(['name', 'email', 'role', 'status']);
});