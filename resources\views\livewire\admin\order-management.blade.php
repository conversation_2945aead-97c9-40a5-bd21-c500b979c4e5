<div>
    <!-- Search and Filters -->
    <div class="mb-6 grid grid-cols-1 md:grid-cols-3 gap-4">
        <div class="col-span-1">
            <label for="search" class="block text-sm font-medium text-gray-700 mb-1">Search</label>
            <div class="relative rounded-md shadow-sm">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <svg class="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                        <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd" />
                    </svg>
                </div>
                <input type="text" wire:model.debounce.300ms="search" id="search" class="focus:ring-indigo-500 focus:border-indigo-500 block w-full pl-10 sm:text-sm border-gray-300 rounded-md" placeholder="Search by order number or customer...">
            </div>
        </div>

        <div>
            <label for="statusFilter" class="block text-sm font-medium text-gray-700 mb-1">Status</label>
            <select id="statusFilter" wire:model="statusFilter" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md">
                <option value="">All Status</option>
                <option value="pending">Pending</option>
                <option value="processing">Processing</option>
                <option value="completed">Completed</option>
                <option value="canceled">Cancelled</option>
                <option value="refunded">Refunded</option>
            </select>
        </div>

        <div>
            <label for="dateFilter" class="block text-sm font-medium text-gray-700 mb-1">Date</label>
            <select id="dateFilter" wire:model="dateFilter" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md">
                <option value="">All Time</option>
                <option value="today">Today</option>
                <option value="yesterday">Yesterday</option>
                <option value="this_week">This Week</option>
                <option value="this_month">This Month</option>
                <option value="last_month">Last Month</option>
            </select>
        </div>
    </div>

    <!-- Orders Table -->
    <div class="bg-white shadow overflow-hidden border-b border-gray-200 sm:rounded-lg">
        <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer" wire:click="sortBy('order_number')">
                        Order #
                        @if($sortField === 'order_number')
                            @if($sortDirection === 'asc')
                                <svg class="inline-block w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"></path></svg>
                            @else
                                <svg class="inline-block w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path></svg>
                            @endif
                        @endif
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Customer
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer" wire:click="sortBy('created_at')">
                        Date
                        @if($sortField === 'created_at')
                            @if($sortDirection === 'asc')
                                <svg class="inline-block w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"></path></svg>
                            @else
                                <svg class="inline-block w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path></svg>
                            @endif
                        @endif
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer" wire:click="sortBy('total')">
                        Total
                        @if($sortField === 'total')
                            @if($sortDirection === 'asc')
                                <svg class="inline-block w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"></path></svg>
                            @else
                                <svg class="inline-block w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path></svg>
                            @endif
                        @endif
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Actions
                    </th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                @forelse($orders as $order)
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            {{ $order->order_number }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-gray-900">{{ $order->user->name }}</div>
                            <div class="text-sm text-gray-500">{{ $order->user->email }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {{ $order->created_at->format('M d, Y') }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            ${{ number_format($order->total, 2) }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full 
                                @if($order->status == 'pending') bg-yellow-100 text-yellow-800
                                @elseif($order->status == 'processing') bg-blue-100 text-blue-800
                                @elseif($order->status == 'completed') bg-green-100 text-green-800
                                @elseif($order->status == 'canceled') bg-red-100 text-red-800
                                @elseif($order->status == 'refunded') bg-purple-100 text-purple-800
                                @endif">
                                {{ ucfirst($order->status) }}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <button wire:click="viewOrder({{ $order->id }})" class="text-indigo-600 hover:text-indigo-900">View</button>
                        </td>
                    </tr>
                @empty
                    <tr>
                        <td colspan="6" class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">
                            No orders found.
                        </td>
                    </tr>
                @endforelse
            </tbody>
        </table>
    </div>

    <!-- Pagination -->
    <div class="mt-4">
        {{ $orders->links() }}
    </div>

    <!-- Order Details Modal -->
    @if($showingOrderDetails)
        <div class="fixed z-10 inset-0 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
            <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
                <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>

                <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

                <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full">
                    <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                        <div class="sm:flex sm:items-start">
                            <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                                <div class="flex justify-between items-center">
                                    <h3 class="text-lg leading-6 font-medium text-gray-900" id="modal-title">
                                        Order #{{ $selectedOrder->order_number }}
                                    </h3>
                                    <button type="button" wire:click="closeOrderDetails" class="text-gray-400 hover:text-gray-500">
                                        <span class="sr-only">Close</span>
                                        <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                        </svg>
                                    </button>
                                </div>
                                
                                <div class="mt-4">
                                    <!-- Order Info -->
                                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                                        <div class="bg-gray-50 p-4 rounded-lg">
                                            <h4 class="font-medium text-gray-900 mb-2">Order Information</h4>
                                            <p class="text-sm text-gray-600">Date: {{ $selectedOrder->created_at->format('M d, Y g:i A') }}</p>
                                            <p class="text-sm text-gray-600">Status: 
                                                <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full 
                                                    @if($selectedOrder->status == 'pending') bg-yellow-100 text-yellow-800
                                                    @elseif($selectedOrder->status == 'processing') bg-blue-100 text-blue-800
                                                    @elseif($selectedOrder->status == 'completed') bg-green-100 text-green-800
                                                    @elseif($selectedOrder->status == 'canceled') bg-red-100 text-red-800
                                                    @elseif($selectedOrder->status == 'refunded') bg-purple-100 text-purple-800
                                                    @endif">
                                                    {{ ucfirst($selectedOrder->status) }}
                                                </span>
                                            </p>
                                            <p class="text-sm text-gray-600">Total: ${{ number_format($selectedOrder->total, 2) }}</p>
                                        </div>
                                        
                                        <div class="bg-gray-50 p-4 rounded-lg">
                                            <h4 class="font-medium text-gray-900 mb-2">Customer Information</h4>
                                            <p class="text-sm text-gray-600">Name: {{ $selectedOrder->user->name }}</p>
                                            <p class="text-sm text-gray-600">Email: {{ $selectedOrder->user->email }}</p>
                                        </div>
                                        
                                        <div class="bg-gray-50 p-4 rounded-lg">
                                            <h4 class="font-medium text-gray-900 mb-2">Shipping Information</h4>
                                            <p class="text-sm text-gray-600">Address: {{ $selectedOrder->shipping_address ?? 'N/A' }}</p>
                                            <p class="text-sm text-gray-600">City: {{ $selectedOrder->shipping_city ?? 'N/A' }}</p>
                                            <p class="text-sm text-gray-600">State: {{ $selectedOrder->shipping_state ?? 'N/A' }}</p>
                                            <p class="text-sm text-gray-600">Zip: {{ $selectedOrder->shipping_zipcode ?? 'N/A' }}</p>
                                            <p class="text-sm text-gray-600">Country: {{ $selectedOrder->shipping_country ?? 'N/A' }}</p>
                                        </div>
                                    </div>
                                    
                                    <!-- Order Items -->
                                    <div class="mb-6">
                                        <h4 class="font-medium text-gray-900 mb-2">Order Items</h4>
                                        <div class="bg-gray-50 rounded-lg overflow-hidden">
                                            <table class="min-w-full divide-y divide-gray-200">
                                                <thead class="bg-gray-100">
                                                    <tr>
                                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item</th>
                                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Price</th>
                                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quantity</th>
                                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total</th>
                                                    </tr>
                                                </thead>
                                                <tbody class="bg-white divide-y divide-gray-200">
                                                    @foreach($selectedOrder->items as $item)
                                                    <tr>
                                                        <td class="px-6 py-4 whitespace-nowrap">
                                                            <div class="flex items-center">
                                                                @if($item->component && $item->component->image)
                                                                    <div class="flex-shrink-0 h-10 w-10 mr-4">
                                                                        <img class="h-10 w-10 rounded-full object-cover" src="{{ asset('storage/' . $item->component->image) }}" alt="{{ $item->component->name }}">
                                                                    </div>
                                                                @endif
                                                                <div>
                                                                    <div class="text-sm font-medium text-gray-900">
                                                                        {{ $item->component ? $item->component->name : $item->name }}
                                                                    </div>
                                                                    @if($item->component)
                                                                    <div class="text-sm text-gray-500">
                                                                        {{ $item->component->category->name }}
                                                                    </div>
                                                                    @endif
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                            ${{ number_format($item->price, 2) }}
                                                        </td>
                                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                            {{ $item->quantity }}
                                                        </td>
                                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                            ${{ number_format($item->price * $item->quantity, 2) }}
                                                        </td>
                                                    </tr>
                                                    @endforeach
                                                </tbody>
                                                <tfoot class="bg-gray-50">
                                                    <tr>
                                                        <td colspan="3" class="px-6 py-4 text-right text-sm font-medium text-gray-900">Subtotal:</td>
                                                        <td class="px-6 py-4 text-sm text-gray-900">${{ number_format($selectedOrder->subtotal ?? $selectedOrder->total, 2) }}</td>
                                                    </tr>
                                                    @if(isset($selectedOrder->tax))
                                                    <tr>
                                                        <td colspan="3" class="px-6 py-4 text-right text-sm font-medium text-gray-900">Tax:</td>
                                                        <td class="px-6 py-4 text-sm text-gray-900">${{ number_format($selectedOrder->tax, 2) }}</td>
                                                    </tr>
                                                    @endif
                                                    @if(isset($selectedOrder->shipping))
                                                    <tr>
                                                        <td colspan="3" class="px-6 py-4 text-right text-sm font-medium text-gray-900">Shipping:</td>
                                                        <td class="px-6 py-4 text-sm text-gray-900">${{ number_format($selectedOrder->shipping, 2) }}</td>
                                                    </tr>
                                                    @endif
                                                    <tr>
                                                        <td colspan="3" class="px-6 py-4 text-right text-sm font-bold text-gray-900">Total:</td>
                                                        <td class="px-6 py-4 text-sm font-bold text-gray-900">${{ number_format($selectedOrder->total, 2) }}</td>
                                                    </tr>
                                                </tfoot>
                                            </table>
                                        </div>
                                    </div>
                                    
                                    <!-- Order Management -->
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                        <!-- Update Status -->
                                        <div class="bg-gray-50 p-4 rounded-lg">
                                            <h4 class="font-medium text-gray-900 mb-2">Update Status</h4>
                                            <div class="mb-4">
                                                <label for="newStatus" class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                                                <select id="newStatus" wire:model="newStatus" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md">
                                                    <option value="pending">Pending</option>
                                                    <option value="processing">Processing</option>
                                                    <option value="completed">Completed</option>
                                                    <option value="canceled">Cancelled</option>
                                                    <option value="refunded">Refunded</option>
                                                </select>
                                            </div>
                                            <button wire:click="updateOrderStatus" class="w-full inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                                Update Status
                                            </button>
                                        </div>
                                        
                                        <!-- Tracking Information -->
                                        <div class="bg-gray-50 p-4 rounded-lg">
                                            <h4 class="font-medium text-gray-900 mb-2">Tracking Information</h4>
                                            <div class="space-y-3">
                                                <div>
                                                    <label for="trackingNumber" class="block text-sm font-medium text-gray-700 mb-1">Tracking Number</label>
                                                    <input type="text" id="trackingNumber" wire:model="trackingNumber" class="mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
                                                </div>
                                                <div>
                                                    <label for="trackingCarrier" class="block text-sm font-medium text-gray-700 mb-1">Carrier</label>
                                                    <input type="text" id="trackingCarrier" wire:model="trackingCarrier" class="mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
                                                </div>
                                                <div>
                                                    <label for="trackingUrl" class="block text-sm font-medium text-gray-700 mb-1">Tracking URL</label>
                                                    <input type="url" id="trackingUrl" wire:model="trackingUrl" class="mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
                                                </div>
                                                <button wire:click="updateTracking" class="w-full inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                                    Update Tracking
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- Order Notes -->
                                    <div class="mt-6 bg-gray-50 p-4 rounded-lg">
                                        <h4 class="font-medium text-gray-900 mb-2">Order Notes</h4>
                                        <div class="mb-3">
                                            <textarea id="orderNote" wire:model="orderNote" rows="3" class="mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"></textarea>
                                        </div>
                                        <button wire:click="updateNote" class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                            Update Note
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                        <button type="button" wire:click="closeOrderDetails" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                            Close
                        </button>
                    </div>
                </div>
            </div>
        </div>
    @endif

    <!-- Flash Message -->
    @if (session()->has('message'))
        <div x-data="{ show: true }" x-init="setTimeout(() => show = false, 3000)" x-show="show" class="fixed bottom-0 right-0 m-6 p-4 bg-green-500 text-white rounded shadow-lg">
            {{ session('message') }}
        </div>
    @endif
</div>