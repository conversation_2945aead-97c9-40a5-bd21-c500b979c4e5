<?php

namespace Tests\Feature\Models;

use App\Models\BlogPost;
use App\Models\BlogPostCategory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

// uses(TestCase::class, RefreshDatabase::class); // This is defined globally for Feature tests.

it('can be created with all attributes', function () {
    $data = [
        'name' => 'Tech',
        'slug' => 'tech',
        'description' => 'All about technology.',
        'display_order' => 1,
    ];

    $category = BlogPostCategory::factory()->create($data);

    expect($category)->toBeInstanceOf(BlogPostCategory::class);
    $this->assertDatabaseHas('blog_post_categories', $data);
    expect($category->name)->toBe('Tech');
    expect($category->slug)->toBe('tech');
    expect($category->display_order)->toBe(1);
});

it('does not auto-generate a slug', function () {
    // The boot method for slug generation is commented out, so the slug should be null if not provided.
    $category = BlogPostCategory::factory()->create(['name' => 'No Slug Test', 'slug' => null]);
    
    expect($category->slug)->toBeNull();
});

it('has a posts relationship', function () {
    $category = BlogPostCategory::factory()->create();
    
    // Create posts associated with this category
    BlogPost::factory()->count(3)->create(['blog_post_category_id' => $category->id]);

    // Create an unrelated post
    BlogPost::factory()->create();

    // Assert the relationship type
    expect($category->posts())->toBeInstanceOf(HasMany::class);

    // Assert the posts are correctly associated
    expect($category->posts)->toHaveCount(3);
    foreach($category->posts as $post) {
        expect($post->blog_post_category_id)->toBe($category->id);
    }
}); 