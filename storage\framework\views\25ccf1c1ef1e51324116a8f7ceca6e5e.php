<?php $__env->startSection('content'); ?>
    <div class="bg-nexus-dark-900 dark:bg-nexus-dark-950 text-white transition-colors duration-300">
        <!-- Hero Section -->
        <section class="relative min-h-screen flex items-center justify-center cyber-grid">
            <!-- Animated Background Elements -->
            <div class="absolute inset-0 overflow-hidden">
                <div class="absolute top-20 left-20 w-96 h-96 bg-nexus-primary-500 dark:bg-nexus-primary-600 rounded-full opacity-10 floating"></div>
                <div class="absolute bottom-20 right-20 w-80 h-80 bg-nexus-secondary-500 dark:bg-nexus-secondary-600 rounded-full opacity-10 floating"
                    style="animation-delay: 2s;"></div>
                <div
                    class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[600px] h-[600px] border border-nexus-primary-500 dark:border-nexus-primary-600 rounded-full opacity-20 pulse-ring">
                </div>
            </div>

            <div class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
                <!-- Main Content -->
                <div class="text-center mb-16">
                    <!-- Headline -->
                    <h1 class="tech-font text-6xl md:text-8xl font-black mb-6 neon-text" id="headline">
                        <span class="nexus-text-gradient">
                            NEXUS
                        </span>
                        <span class="text-white dark:text-nexus-gray-100 transition-colors duration-300">PC</span>
                    </h1>

                    <!-- Dynamic Tagline -->
                    <div class="h-16 flex items-center justify-center mb-8">
                        <p class="tech-font text-2xl md:text-3xl font-bold text-nexus-primary-400 dark:text-nexus-primary-300 transition-colors duration-300" id="tagline">
                            Silent. Powerful. Smart.
                        </p>
                    </div>

                    <!-- Performance Stats -->
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12 max-w-4xl mx-auto">
                        <!-- FPS Benchmark -->
                        <div class="stat-card p-6 rounded-xl glow dark:bg-nexus-dark-800 transition-colors duration-300">
                            <div class="flex items-center justify-between mb-3">
                                <h3 class="tech-font text-sm font-semibold text-nexus-primary-400 dark:text-nexus-primary-300 transition-colors duration-300">FPS BENCHMARK</h3>
                                <div class="w-2 h-2 bg-nexus-success-400 dark:bg-nexus-success-500 rounded-full animate-pulse transition-colors duration-300"></div>
                            </div>
                            <div class="text-3xl font-bold text-white dark:text-nexus-gray-100 mb-2 transition-colors duration-300">
                                <span id="fps-counter">0</span>
                                <span class="text-lg text-nexus-gray-400 dark:text-nexus-gray-300 transition-colors duration-300">FPS</span>
                            </div>
                            <p class="text-xs text-nexus-gray-400 dark:text-nexus-gray-300 transition-colors duration-300">4K Ultra Settings</p>
                        </div>

                        <!-- Thermal Output -->
                        <div class="stat-card p-6 rounded-xl glow dark:bg-nexus-dark-800 transition-colors duration-300">
                            <div class="flex items-center justify-between mb-3">
                                <h3 class="tech-font text-sm font-semibold text-nexus-primary-400 dark:text-nexus-primary-300 transition-colors duration-300">THERMAL OUTPUT</h3>
                                <div class="w-2 h-2 bg-nexus-primary-400 dark:bg-nexus-primary-500 rounded-full animate-pulse transition-colors duration-300"></div>
                            </div>
                            <div class="text-3xl font-bold text-white dark:text-nexus-gray-100 mb-2 transition-colors duration-300">
                                <span id="temp-counter">0</span>
                                <span class="text-lg text-nexus-gray-400 dark:text-nexus-gray-300 transition-colors duration-300">°C</span>
                            </div>
                            <p class="text-xs text-nexus-gray-400 dark:text-nexus-gray-300 transition-colors duration-300">Under Load</p>
                        </div>

                        <!-- Watts Saved -->
                        <div class="stat-card p-6 rounded-xl glow dark:bg-nexus-dark-800 transition-colors duration-300">
                            <div class="flex items-center justify-between mb-3">
                                <h3 class="tech-font text-sm font-semibold text-nexus-primary-400 dark:text-nexus-primary-300 transition-colors duration-300">EFFICIENCY</h3>
                                <div class="w-2 h-2 bg-nexus-warning-400 dark:bg-nexus-warning-500 rounded-full animate-pulse transition-colors duration-300"></div>
                            </div>
                            <div class="text-3xl font-bold text-white dark:text-nexus-gray-100 mb-2 transition-colors duration-300">
                                <span id="watts-counter">0</span>
                                <span class="text-lg text-nexus-gray-400 dark:text-nexus-gray-300 transition-colors duration-300">W</span>
                            </div>
                            <p class="text-xs text-nexus-gray-400 dark:text-nexus-gray-300 transition-colors duration-300">Power Saved</p>
                        </div>
                    </div>
                </div>

                <!-- Component Showcase -->
                <div class="max-w-5xl mx-auto">
                    <h2 class="tech-font text-2xl font-bold text-center mb-8 text-nexus-primary-400 dark:text-nexus-primary-300 transition-colors duration-300">COMPONENT PERFORMANCE</h2>

                    <div class="grid grid-cols-2 md:grid-cols-4 gap-8 justify-items-center">
                        <!-- CPU -->
                        <div class="component-icon cursor-pointer" data-tooltip="cpu">
                            <div
                                class="w-20 h-20 bg-gradient-to-br from-nexus-primary-500 to-nexus-secondary-500 rounded-lg flex items-center justify-center relative">
                                <svg class="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 24 24">
                                    <path
                                        d="M4 4h4v4H4V4zm6 0h4v4h-4V4zm6 0h4v4h-4V4zM4 10h4v4H4v-4zm6 0h4v4h-4v-4zm6 0h4v4h-4v-4zM4 16h4v4H4v-4zm6 0h4v4h-4v-4zm6 0h4v4h-4v-4z" />
                                </svg>
                                <div
                                    class="absolute inset-0 rounded-lg border-2 border-nexus-primary-400 opacity-0 hover:opacity-100 transition-opacity duration-300">
                                </div>
                            </div>
                            <p class="tech-font text-sm mt-3 text-center text-nexus-gray-400 dark:text-nexus-gray-300 transition-colors duration-300">CPU</p>
                        </div>

                        <!-- GPU -->
                        <div class="component-icon cursor-pointer" data-tooltip="gpu">
                            <div
                                class="w-20 h-20 bg-gradient-to-br from-nexus-success-500 to-teal-500 rounded-lg flex items-center justify-center relative">
                                <svg class="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 24 24">
                                    <path
                                        d="M21 16.5c0 .38-.21.71-.53.88l-7.9 4.44c-.16.12-.36.18-.57.18-.21 0-.41-.06-.57-.18l-7.9-4.44A.991.991 0 0 1 3 16.5v-9c0-.38.21-.71.53-.88l7.9-4.44c.16-.12.36-.18.57-.18.21 0 .41.06.57.18l7.9 4.44c.**********.53.88v9z" />
                                </svg>
                                <div
                                    class="absolute inset-0 rounded-lg border-2 border-nexus-success-400 opacity-0 hover:opacity-100 transition-opacity duration-300">
                                </div>
                            </div>
                            <p class="tech-font text-sm mt-3 text-center text-nexus-gray-400">GPU</p>
                        </div>

                        <!-- RAM -->
                        <div class="component-icon cursor-pointer" data-tooltip="ram">
                            <div
                                class="w-20 h-20 bg-gradient-to-br from-nexus-accent-500 to-pink-500 rounded-lg flex items-center justify-center relative">
                                <svg class="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M6 7h12v2H6V7zm0 4h12v2H6v-2zm0 4h12v2H6v-2z" />
                                </svg>
                                <div
                                    class="absolute inset-0 rounded-lg border-2 border-purple-400 opacity-0 hover:opacity-100 transition-opacity duration-300">
                                </div>
                            </div>
                            <p class="tech-font text-sm mt-3 text-center text-gray-400">RAM</p>
                        </div>

                        <!-- Storage -->
                        <div class="component-icon cursor-pointer" data-tooltip="storage">
                            <div
                                class="w-20 h-20 bg-gradient-to-br from-orange-500 to-red-500 rounded-lg flex items-center justify-center relative">
                                <svg class="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 24 24">
                                    <path
                                        d="M6 2c-1.1 0-1.99.9-1.99 2L4 20c0 1.1.89 2 2 2h10v-2H6V4h7v5h5v1.67l2-2V8l-6-6H6z" />
                                </svg>
                                <div
                                    class="absolute inset-0 rounded-lg border-2 border-orange-400 opacity-0 hover:opacity-100 transition-opacity duration-300">
                                </div>
                            </div>
                            <p class="tech-font text-sm mt-3 text-center text-gray-400">SSD</p>
                        </div>
                    </div>
                </div>

                <!-- CTA Button -->
                <div class="text-center mt-16">
                    <button
                        class="px-12 py-4 bg-gradient-to-r from-blue-600 to-cyan-600 rounded-lg font-semibold tech-font text-lg hover:from-blue-700 hover:to-cyan-700 transition-all duration-300 transform hover:scale-105 glow">
                        CONFIGURE YOUR BUILD
                    </button>
                </div>
            </div>

            <!-- Tooltips -->
            <div id="tooltip" class="tooltip"></div>
        </section>

        <!-- Features Section -->
        <section class="py-20 relative overflow-hidden">
            <div class="absolute inset-0 overflow-hidden">
                <div class="absolute top-40 right-20 w-96 h-96 bg-purple-500 rounded-full opacity-5 floating" style="animation-delay: 1s;"></div>
                <div class="absolute bottom-40 left-20 w-80 h-80 bg-blue-500 rounded-full opacity-5 floating" style="animation-delay: 3s;"></div>
            </div>

            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
                <div class="text-center mb-16">
                    <h2 class="tech-font text-4xl font-bold mb-4 text-blue-400">ADVANCED FEATURES</h2>
                    <p class="text-gray-400 max-w-3xl mx-auto">Our custom PC builds come with cutting-edge technology and premium features designed for maximum performance and reliability.</p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                    <!-- Feature 1 -->
                    <div class="feature-card p-8 rounded-xl bg-gradient-to-br from-slate-800 to-slate-900 border border-blue-900/50 hover:border-blue-500/50 transition-all duration-300 hover:shadow-lg hover:shadow-blue-500/20">
                        <div class="w-14 h-14 bg-blue-500/20 rounded-lg flex items-center justify-center mb-6">
                            <svg class="w-8 h-8 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                            </svg>
                        </div>
                        <h3 class="tech-font text-xl font-bold mb-3 text-white">Overclocking Ready</h3>
                        <p class="text-gray-400">All our systems are built with premium components that support safe and stable overclocking for maximum performance gains.</p>
                    </div>

                    <!-- Feature 2 -->
                    <div class="feature-card p-8 rounded-xl bg-gradient-to-br from-slate-800 to-slate-900 border border-cyan-900/50 hover:border-cyan-500/50 transition-all duration-300 hover:shadow-lg hover:shadow-cyan-500/20">
                        <div class="w-14 h-14 bg-cyan-500/20 rounded-lg flex items-center justify-center mb-6">
                            <svg class="w-8 h-8 text-cyan-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                            </svg>
                        </div>
                        <h3 class="tech-font text-xl font-bold mb-3 text-white">Advanced Cooling</h3>
                        <p class="text-gray-400">Custom liquid cooling solutions and precision airflow management keep your system running cool even under the most demanding workloads.</p>
                    </div>

                    <!-- Feature 3 -->
                    <div class="feature-card p-8 rounded-xl bg-gradient-to-br from-slate-800 to-slate-900 border border-purple-900/50 hover:border-purple-500/50 transition-all duration-300 hover:shadow-lg hover:shadow-purple-500/20">
                        <div class="w-14 h-14 bg-purple-500/20 rounded-lg flex items-center justify-center mb-6">
                            <svg class="w-8 h-8 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                            </svg>
                        </div>
                        <h3 class="tech-font text-xl font-bold mb-3 text-white">Lifetime Support</h3>
                        <p class="text-gray-400">Every build comes with lifetime technical support and a comprehensive 3-year warranty on all parts and labor.</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Testimonials Section -->
        <section class="py-20 relative overflow-hidden bg-slate-950">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
                <div class="text-center mb-16">
                    <h2 class="tech-font text-4xl font-bold mb-4 text-blue-400">CUSTOMER EXPERIENCES</h2>
                    <p class="text-gray-400 max-w-3xl mx-auto">Hear what our customers have to say about their custom PC builds and experience with our service.</p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                    <!-- Testimonial 1 -->
                    <div class="testimonial-card p-8 rounded-xl bg-gradient-to-br from-slate-800/50 to-slate-900/50 border border-blue-900/30 hover:border-blue-500/30 transition-all duration-300">
                        <div class="flex items-center mb-6">
                            <div class="mr-4">
                                <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-full flex items-center justify-center text-white font-bold">JD</div>
                            </div>
                            <div>
                                <h4 class="text-white font-semibold">James Donovan</h4>
                                <div class="flex text-yellow-400">
                                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path></svg>
                                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path></svg>
                                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path></svg>
                                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path></svg>
                                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path></svg>
                                </div>
                            </div>
                        </div>
                        <p class="text-gray-300 italic">"The custom gaming PC I received exceeded all my expectations. The build quality is exceptional, and the performance is incredible. I'm getting consistent 165+ FPS in all my favorite games at max settings."</p>
                    </div>

                    <!-- Testimonial 2 -->
                    <div class="testimonial-card p-8 rounded-xl bg-gradient-to-br from-slate-800/50 to-slate-900/50 border border-cyan-900/30 hover:border-cyan-500/30 transition-all duration-300">
                        <div class="flex items-center mb-6">
                            <div class="mr-4">
                                <div class="w-12 h-12 bg-gradient-to-br from-cyan-500 to-blue-500 rounded-full flex items-center justify-center text-white font-bold">SL</div>
                            </div>
                            <div>
                                <h4 class="text-white font-semibold">Sarah Lin</h4>
                                <div class="flex text-yellow-400">
                                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path></svg>
                                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path></svg>
                                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path></svg>
                                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path></svg>
                                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path></svg>
                                </div>
                            </div>
                        </div>
                        <p class="text-gray-300 italic">"As a 3D artist, I needed a workstation that could handle complex renders without breaking a sweat. This PC delivers exactly that. The custom cooling system keeps everything running cool even during 12+ hour render sessions."</p>
                    </div>

                    <!-- Testimonial 3 -->
                    <div class="testimonial-card p-8 rounded-xl bg-gradient-to-br from-slate-800/50 to-slate-900/50 border border-purple-900/30 hover:border-purple-500/30 transition-all duration-300">
                        <div class="flex items-center mb-6">
                            <div class="mr-4">
                                <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-blue-500 rounded-full flex items-center justify-center text-white font-bold">MR</div>
                            </div>
                            <div>
                                <h4 class="text-white font-semibold">Michael Rodriguez</h4>
                                <div class="flex text-yellow-400">
                                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path></svg>
                                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path></svg>
                                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path></svg>
                                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path></svg>
                                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path></svg>
                                </div>
                            </div>
                        </div>
                        <p class="text-gray-300 italic">"The customer service was just as impressive as the PC itself. They guided me through every step of the configuration process and even helped me optimize my setup after delivery. Truly a premium experience."</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Comparison Table Section -->
        <section class="py-20 relative overflow-hidden">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
                <div class="text-center mb-16">
                    <h2 class="tech-font text-4xl font-bold mb-4 text-blue-400">BUILD COMPARISON</h2>
                    <p class="text-gray-400 max-w-3xl mx-auto">Compare our different build options to find the perfect PC for your needs and budget.</p>
                </div>

                <div class="overflow-x-auto">
                    <table class="w-full border-collapse">
                        <thead>
                            <tr>
                                <th class="p-4 text-left text-gray-400 font-normal"></th>
                                <th class="p-4 text-center">
                                    <div class="tech-font text-xl font-bold text-blue-400 mb-2">STARTER</div>
                                    <div class="text-white text-2xl font-bold mb-1">$1,299</div>
                                    <div class="text-gray-400 text-sm">Entry-level gaming</div>
                                </th>
                                <th class="p-4 text-center bg-slate-800 rounded-t-xl">
                                    <div class="tech-font text-xl font-bold text-cyan-400 mb-2">ENTHUSIAST</div>
                                    <div class="text-white text-2xl font-bold mb-1">$2,499</div>
                                    <div class="text-gray-400 text-sm">High-performance gaming</div>
                                </th>
                                <th class="p-4 text-center">
                                    <div class="tech-font text-xl font-bold text-purple-400 mb-2">EXTREME</div>
                                    <div class="text-white text-2xl font-bold mb-1">$3,999</div>
                                    <div class="text-gray-400 text-sm">Ultimate workstation</div>
                                </th>
                            </tr>
                        </thead>
                        <tbody class="divide-y divide-gray-800">
                            <!-- CPU Row -->
                            <tr class="hover:bg-slate-800/50 transition-colors duration-150">
                                <td class="p-4 text-left text-gray-300 font-medium">CPU</td>
                                <td class="p-4 text-center text-gray-400">Intel Core i5-13600K</td>
                                <td class="p-4 text-center text-white bg-slate-800">Intel Core i7-13700K</td>
                                <td class="p-4 text-center text-gray-400">Intel Core i9-13900KS</td>
                            </tr>
                            <!-- GPU Row -->
                            <tr class="hover:bg-slate-800/50 transition-colors duration-150">
                                <td class="p-4 text-left text-gray-300 font-medium">GPU</td>
                                <td class="p-4 text-center text-gray-400">RTX 4060 Ti 8GB</td>
                                <td class="p-4 text-center text-white bg-slate-800">RTX 4080 16GB</td>
                                <td class="p-4 text-center text-gray-400">RTX 4090 24GB</td>
                            </tr>
                            <!-- RAM Row -->
                            <tr class="hover:bg-slate-800/50 transition-colors duration-150">
                                <td class="p-4 text-left text-gray-300 font-medium">RAM</td>
                                <td class="p-4 text-center text-gray-400">16GB DDR5-5200</td>
                                <td class="p-4 text-center text-white bg-slate-800">32GB DDR5-5600</td>
                                <td class="p-4 text-center text-gray-400">64GB DDR5-6000</td>
                            </tr>
                            <!-- Storage Row -->
                            <tr class="hover:bg-slate-800/50 transition-colors duration-150">
                                <td class="p-4 text-left text-gray-300 font-medium">Storage</td>
                                <td class="p-4 text-center text-gray-400">1TB NVMe SSD</td>
                                <td class="p-4 text-center text-white bg-slate-800">2TB NVMe SSD</td>
                                <td class="p-4 text-center text-gray-400">4TB NVMe SSD + 4TB HDD</td>
                            </tr>
                            <!-- Cooling Row -->
                            <tr class="hover:bg-slate-800/50 transition-colors duration-150">
                                <td class="p-4 text-left text-gray-300 font-medium">Cooling</td>
                                <td class="p-4 text-center text-gray-400">Air Cooling</td>
                                <td class="p-4 text-center text-white bg-slate-800">240mm AIO Liquid</td>
                                <td class="p-4 text-center text-gray-400">Custom Loop Liquid</td>
                            </tr>
                            <!-- Warranty Row -->
                            <tr class="hover:bg-slate-800/50 transition-colors duration-150">
                                <td class="p-4 text-left text-gray-300 font-medium">Warranty</td>
                                <td class="p-4 text-center text-gray-400">1 Year</td>
                                <td class="p-4 text-center text-white bg-slate-800">3 Years</td>
                                <td class="p-4 text-center text-gray-400">5 Years</td>
                            </tr>
                            <!-- Action Row -->
                            <tr>
                                <td class="p-4"></td>
                                <td class="p-4 text-center">
                                    <button class="px-6 py-2 bg-blue-600 rounded-lg font-semibold tech-font text-sm hover:bg-blue-700 transition-all duration-300 transform hover:scale-105">SELECT</button>
                                </td>
                                <td class="p-4 text-center bg-slate-800 rounded-b-xl">
                                    <button class="px-6 py-2 bg-gradient-to-r from-blue-600 to-cyan-600 rounded-lg font-semibold tech-font text-sm hover:from-blue-700 hover:to-cyan-700 transition-all duration-300 transform hover:scale-105 glow">SELECT</button>
                                </td>
                                <td class="p-4 text-center">
                                    <button class="px-6 py-2 bg-purple-600 rounded-lg font-semibold tech-font text-sm hover:bg-purple-700 transition-all duration-300 transform hover:scale-105">SELECT</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </section>

        <!-- Technology Showcase Section -->
        <section class="py-20 relative overflow-hidden">
            <div class="absolute inset-0 overflow-hidden">
                <div class="absolute bottom-40 right-20 w-96 h-96 bg-blue-500 rounded-full opacity-5 floating" style="animation-delay: 1.5s;"></div>
                <div class="absolute top-40 left-20 w-80 h-80 bg-purple-500 rounded-full opacity-5 floating" style="animation-delay: 2.5s;"></div>
            </div>

            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
                <div class="text-center mb-16">
                    <h2 class="tech-font text-4xl font-bold mb-4 text-blue-400">CUTTING-EDGE TECHNOLOGY</h2>
                    <p class="text-gray-400 max-w-3xl mx-auto">Our systems are built with the latest technologies to ensure maximum performance, reliability, and future-proofing.</p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                    <!-- Technology 1: DDR5 Memory -->
                    <div class="tech-card p-6 rounded-xl bg-gradient-to-br from-slate-800 to-slate-900 border border-blue-900/30 hover:border-blue-500/50 transition-all duration-300 hover:shadow-lg hover:shadow-blue-500/20 group">
                        <div class="w-16 h-16 bg-blue-500/20 rounded-lg flex items-center justify-center mb-6 group-hover:bg-blue-500/30 transition-all duration-300">
                            <svg class="w-10 h-10 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z"></path>
                            </svg>
                        </div>
                        <h3 class="tech-font text-xl font-bold mb-3 text-white">DDR5 Memory</h3>
                        <p class="text-gray-400 mb-4">Next-gen memory with higher bandwidth, improved power efficiency, and enhanced stability for multitasking.</p>
                        <div class="text-sm text-blue-400 tech-font flex items-center">
                            <span>UP TO 6000MHz</span>
                            <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
                            </svg>
                        </div>
                    </div>

                    <!-- Technology 2: PCIe Gen 5 -->
                    <div class="tech-card p-6 rounded-xl bg-gradient-to-br from-slate-800 to-slate-900 border border-green-900/30 hover:border-green-500/50 transition-all duration-300 hover:shadow-lg hover:shadow-green-500/20 group">
                        <div class="w-16 h-16 bg-green-500/20 rounded-lg flex items-center justify-center mb-6 group-hover:bg-green-500/30 transition-all duration-300">
                            <svg class="w-10 h-10 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                            </svg>
                        </div>
                        <h3 class="tech-font text-xl font-bold mb-3 text-white">PCIe Gen 5</h3>
                        <p class="text-gray-400 mb-4">Double the bandwidth of PCIe 4.0, enabling faster data transfer for GPUs, SSDs, and networking components.</p>
                        <div class="text-sm text-green-400 tech-font flex items-center">
                            <span>128 GB/s BANDWIDTH</span>
                            <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
                            </svg>
                        </div>
                    </div>

                    <!-- Technology 3: AI Acceleration -->
                    <div class="tech-card p-6 rounded-xl bg-gradient-to-br from-slate-800 to-slate-900 border border-purple-900/30 hover:border-purple-500/50 transition-all duration-300 hover:shadow-lg hover:shadow-purple-500/20 group">
                        <div class="w-16 h-16 bg-purple-500/20 rounded-lg flex items-center justify-center mb-6 group-hover:bg-purple-500/30 transition-all duration-300">
                            <svg class="w-10 h-10 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                            </svg>
                        </div>
                        <h3 class="tech-font text-xl font-bold mb-3 text-white">AI Acceleration</h3>
                        <p class="text-gray-400 mb-4">Dedicated AI processing units for faster machine learning tasks, content creation, and real-time image enhancement.</p>
                        <div class="text-sm text-purple-400 tech-font flex items-center">
                            <span>TENSOR CORES</span>
                            <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
                            </svg>
                        </div>
                    </div>

                    <!-- Technology 4: Ray Tracing -->
                    <div class="tech-card p-6 rounded-xl bg-gradient-to-br from-slate-800 to-slate-900 border border-cyan-900/30 hover:border-cyan-500/50 transition-all duration-300 hover:shadow-lg hover:shadow-cyan-500/20 group">
                        <div class="w-16 h-16 bg-cyan-500/20 rounded-lg flex items-center justify-center mb-6 group-hover:bg-cyan-500/30 transition-all duration-300">
                            <svg class="w-10 h-10 text-cyan-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                            </svg>
                        </div>
                        <h3 class="tech-font text-xl font-bold mb-3 text-white">Ray Tracing</h3>
                        <p class="text-gray-400 mb-4">Advanced lighting simulation technology for photorealistic reflections, shadows, and global illumination in games.</p>
                        <div class="text-sm text-cyan-400 tech-font flex items-center">
                            <span>REAL-TIME RENDERING</span>
                            <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- FAQ Section -->
        <section class="py-20 relative overflow-hidden bg-slate-950">
            <div class="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
                <div class="text-center mb-16">
                    <h2 class="tech-font text-4xl font-bold mb-4 text-blue-400">FREQUENTLY ASKED QUESTIONS</h2>
                    <p class="text-gray-400 max-w-3xl mx-auto">Everything you need to know about our custom PC building service.</p>
                </div>

                <div class="space-y-6" x-data="{activeTab: null}">
                    <!-- FAQ Item 1 -->
                    <div class="bg-gradient-to-br from-slate-800/50 to-slate-900/50 border border-blue-900/30 rounded-lg overflow-hidden">
                        <button 
                            @click="activeTab = (activeTab === 1) ? null : 1" 
                            class="flex justify-between items-center w-full px-6 py-4 text-left focus:outline-none"
                        >
                            <span class="text-lg font-medium text-white tech-font">What is the build time for a custom PC?</span>
                            <svg 
                                class="w-5 h-5 text-blue-400 transform transition-transform duration-300" 
                                :class="{'rotate-180': activeTab === 1}"
                                fill="none" 
                                stroke="currentColor" 
                                viewBox="0 0 24 24"
                            >
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </button>
                        <div 
                            x-show="activeTab === 1" 
                            x-transition:enter="transition ease-out duration-300" 
                            x-transition:enter-start="opacity-0 transform -translate-y-4" 
                            x-transition:enter-end="opacity-100 transform translate-y-0" 
                            x-transition:leave="transition ease-in duration-300" 
                            x-transition:leave-start="opacity-100 transform translate-y-0" 
                            x-transition:leave-end="opacity-0 transform -translate-y-4" 
                            class="px-6 pb-4 text-gray-300"
                        >
                            <p>Our standard build time is 7-10 business days from order confirmation to shipping. For complex custom builds with advanced cooling solutions, it may take 14-21 days. Rush options are available for an additional fee.</p>
                        </div>
                    </div>

                    <!-- FAQ Item 2 -->
                    <div class="bg-gradient-to-br from-slate-800/50 to-slate-900/50 border border-blue-900/30 rounded-lg overflow-hidden">
                        <button 
                            @click="activeTab = (activeTab === 2) ? null : 2" 
                            class="flex justify-between items-center w-full px-6 py-4 text-left focus:outline-none"
                        >
                            <span class="text-lg font-medium text-white tech-font">What warranty do you offer?</span>
                            <svg 
                                class="w-5 h-5 text-blue-400 transform transition-transform duration-300" 
                                :class="{'rotate-180': activeTab === 2}"
                                fill="none" 
                                stroke="currentColor" 
                                viewBox="0 0 24 24"
                            >
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </button>
                        <div 
                            x-show="activeTab === 2" 
                            x-transition:enter="transition ease-out duration-300" 
                            x-transition:enter-start="opacity-0 transform -translate-y-4" 
                            x-transition:enter-end="opacity-100 transform translate-y-0" 
                            x-transition:leave="transition ease-in duration-300" 
                            x-transition:leave-start="opacity-100 transform translate-y-0" 
                            x-transition:leave-end="opacity-0 transform -translate-y-4" 
                            class="px-6 pb-4 text-gray-300"
                        >
                            <p>All our custom PCs come with a 3-year warranty on parts and labor. Premium builds include a 5-year warranty. We also offer lifetime technical support for all customers, ensuring your system continues to perform optimally for years to come.</p>
                        </div>
                    </div>

                    <!-- FAQ Item 3 -->
                    <div class="bg-gradient-to-br from-slate-800/50 to-slate-900/50 border border-blue-900/30 rounded-lg overflow-hidden">
                        <button 
                            @click="activeTab = (activeTab === 3) ? null : 3" 
                            class="flex justify-between items-center w-full px-6 py-4 text-left focus:outline-none"
                        >
                            <span class="text-lg font-medium text-white tech-font">Do you ship internationally?</span>
                            <svg 
                                class="w-5 h-5 text-blue-400 transform transition-transform duration-300" 
                                :class="{'rotate-180': activeTab === 3}"
                                fill="none" 
                                stroke="currentColor" 
                                viewBox="0 0 24 24"
                            >
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </button>
                        <div 
                            x-show="activeTab === 3" 
                            x-transition:enter="transition ease-out duration-300" 
                            x-transition:enter-start="opacity-0 transform -translate-y-4" 
                            x-transition:enter-end="opacity-100 transform translate-y-0" 
                            x-transition:leave="transition ease-in duration-300" 
                            x-transition:leave-start="opacity-100 transform translate-y-0" 
                            x-transition:leave-end="opacity-0 transform -translate-y-4" 
                            class="px-6 pb-4 text-gray-300"
                        >
                            <p>Yes, we ship to most countries worldwide. International shipping costs and delivery times vary by location. All international orders include special packaging to ensure your PC arrives safely. Import duties and taxes may apply depending on your country's regulations.</p>
                        </div>
                    </div>

                    <!-- FAQ Item 4 -->
                    <div class="bg-gradient-to-br from-slate-800/50 to-slate-900/50 border border-blue-900/30 rounded-lg overflow-hidden">
                        <button 
                            @click="activeTab = (activeTab === 4) ? null : 4" 
                            class="flex justify-between items-center w-full px-6 py-4 text-left focus:outline-none"
                        >
                            <span class="text-lg font-medium text-white tech-font">Can I upgrade my PC later?</span>
                            <svg 
                                class="w-5 h-5 text-blue-400 transform transition-transform duration-300" 
                                :class="{'rotate-180': activeTab === 4}"
                                fill="none" 
                                stroke="currentColor" 
                                viewBox="0 0 24 24"
                            >
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </button>
                        <div 
                            x-show="activeTab === 4" 
                            x-transition:enter="transition ease-out duration-300" 
                            x-transition:enter-start="opacity-0 transform -translate-y-4" 
                            x-transition:enter-end="opacity-100 transform translate-y-0" 
                            x-transition:leave="transition ease-in duration-300" 
                            x-transition:leave-start="opacity-100 transform translate-y-0" 
                            x-transition:leave-end="opacity-0 transform -translate-y-4" 
                            class="px-6 pb-4 text-gray-300"
                        >
                            <p>Absolutely! All our PCs are built with upgradeability in mind. We use standard components and high-quality motherboards that support future upgrades. Our technical support team can provide guidance on compatible upgrade paths for your specific build.</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Latest Builds Section -->
        <section class="py-20 relative overflow-hidden">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
                <div class="text-center mb-16">
                    <h2 class="tech-font text-4xl font-bold mb-4 text-blue-400">LATEST CUSTOM BUILDS</h2>
                    <p class="text-gray-400 max-w-3xl mx-auto">Check out some of our recent custom PC builds created for gamers, content creators, and professionals.</p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                    <!-- Build 1 -->
                    <div class="build-card rounded-xl overflow-hidden bg-gradient-to-br from-slate-800 to-slate-900 border border-blue-900/30 hover:border-blue-500/50 transition-all duration-300 hover:shadow-lg hover:shadow-blue-500/20 group">
                        <div class="relative">
                            <img src="https://images.unsplash.com/photo-1587202372775-e229f172b9d7?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=2070&q=80" alt="Gaming PC Build" class="w-full h-56 object-cover">
                            <div class="absolute top-3 right-3 bg-blue-600 text-white text-xs font-bold px-2 py-1 rounded tech-font">GAMING</div>
                        </div>
                        <div class="p-6">
                            <h3 class="tech-font text-xl font-bold mb-2 text-white">Frost Demon Gaming Rig</h3>
                            <div class="flex flex-wrap gap-2 mb-4">
                                <span class="text-xs bg-slate-700 text-blue-400 px-2 py-1 rounded">RTX 4080</span>
                                <span class="text-xs bg-slate-700 text-blue-400 px-2 py-1 rounded">i9-13900K</span>
                                <span class="text-xs bg-slate-700 text-blue-400 px-2 py-1 rounded">32GB DDR5</span>
                                <span class="text-xs bg-slate-700 text-blue-400 px-2 py-1 rounded">Custom Loop</span>
                            </div>
                            <p class="text-gray-400 text-sm mb-4">A high-performance gaming PC with custom water cooling, designed for 4K gaming and streaming.</p>
                            <button class="text-blue-400 text-sm tech-font flex items-center hover:text-blue-300 transition-colors">
                                VIEW DETAILS
                                <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
                                </svg>
                            </button>
                        </div>
                    </div>

                    <!-- Build 2 -->
                    <div class="build-card rounded-xl overflow-hidden bg-gradient-to-br from-slate-800 to-slate-900 border border-purple-900/30 hover:border-purple-500/50 transition-all duration-300 hover:shadow-lg hover:shadow-purple-500/20 group">
                        <div class="relative">
                            <img src="https://images.unsplash.com/photo-1624705013726-8cb4f9415f40?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=2070&q=80" alt="Content Creator PC Build" class="w-full h-56 object-cover">
                            <div class="absolute top-3 right-3 bg-purple-600 text-white text-xs font-bold px-2 py-1 rounded tech-font">CREATOR</div>
                        </div>
                        <div class="p-6">
                            <h3 class="tech-font text-xl font-bold mb-2 text-white">Nebula Studio Workstation</h3>
                            <div class="flex flex-wrap gap-2 mb-4">
                                <span class="text-xs bg-slate-700 text-purple-400 px-2 py-1 rounded">RTX 4090</span>
                                <span class="text-xs bg-slate-700 text-purple-400 px-2 py-1 rounded">Ryzen 9 7950X</span>
                                <span class="text-xs bg-slate-700 text-purple-400 px-2 py-1 rounded">64GB DDR5</span>
                                <span class="text-xs bg-slate-700 text-purple-400 px-2 py-1 rounded">8TB Storage</span>
                            </div>
                            <p class="text-gray-400 text-sm mb-4">A powerhouse workstation optimized for video editing, 3D rendering, and content creation workflows.</p>
                            <button class="text-purple-400 text-sm tech-font flex items-center hover:text-purple-300 transition-colors">
                                VIEW DETAILS
                                <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
                                </svg>
                            </button>
                        </div>
                    </div>

                    <!-- Build 3 -->
                    <div class="build-card rounded-xl overflow-hidden bg-gradient-to-br from-slate-800 to-slate-900 border border-green-900/30 hover:border-green-500/50 transition-all duration-300 hover:shadow-lg hover:shadow-green-500/20 group">
                        <div class="relative">
                            <img src="https://images.unsplash.com/photo-1593640408182-31c70c8268f5?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=2042&q=80" alt="Compact PC Build" class="w-full h-56 object-cover">
                            <div class="absolute top-3 right-3 bg-green-600 text-white text-xs font-bold px-2 py-1 rounded tech-font">COMPACT</div>
                        </div>
                        <div class="p-6">
                            <h3 class="tech-font text-xl font-bold mb-2 text-white">Quantum Mini ITX</h3>
                            <div class="flex flex-wrap gap-2 mb-4">
                                <span class="text-xs bg-slate-700 text-green-400 px-2 py-1 rounded">RTX 4070</span>
                                <span class="text-xs bg-slate-700 text-green-400 px-2 py-1 rounded">i7-13700K</span>
                                <span class="text-xs bg-slate-700 text-green-400 px-2 py-1 rounded">32GB DDR5</span>
                                <span class="text-xs bg-slate-700 text-green-400 px-2 py-1 rounded">SFF Case</span>
                            </div>
                            <p class="text-gray-400 text-sm mb-4">A compact yet powerful small form factor build that delivers high performance in a minimal footprint.</p>
                            <button class="text-green-400 text-sm tech-font flex items-center hover:text-green-300 transition-colors">
                                VIEW DETAILS
                                <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>

                <div class="text-center mt-12">
                    <button class="px-6 py-3 bg-transparent border-2 border-blue-500 rounded-lg font-semibold tech-font text-sm hover:bg-blue-500/10 transition-all duration-300 transform hover:scale-105">
                        VIEW ALL BUILDS
                    </button>
                </div>
            </div>
        </section>

        <!-- Final CTA Section -->
        <section class="py-20 relative overflow-hidden bg-gradient-to-b from-slate-900 to-slate-950">
            <div class="absolute inset-0 overflow-hidden">
                <div class="absolute top-0 left-0 w-full h-1/2 bg-gradient-to-b from-blue-500/10 to-transparent"></div>
            </div>

            <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10 text-center">
                <h2 class="tech-font text-5xl font-bold mb-6 text-white">Ready to Build Your <span class="bg-gradient-to-r from-blue-400 via-cyan-400 to-blue-600 bg-clip-text text-transparent">Dream PC?</span></h2>
                <p class="text-gray-300 text-xl mb-10 max-w-3xl mx-auto">Our expert team is ready to help you create the perfect custom PC tailored to your specific needs and budget.</p>
                
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <button class="px-8 py-4 bg-gradient-to-r from-blue-600 to-cyan-600 rounded-lg font-semibold tech-font text-lg hover:from-blue-700 hover:to-cyan-700 transition-all duration-300 transform hover:scale-105 glow">
                        START YOUR BUILD
                    </button>
                    <button class="px-8 py-4 bg-transparent border-2 border-blue-500 rounded-lg font-semibold tech-font text-lg hover:bg-blue-500/10 transition-all duration-300 transform hover:scale-105">
                        SPEAK TO AN EXPERT
                    </button>
                </div>
            </div>
        </section>
            </div>

            <!-- Tooltips -->
            <div id="tooltip" class="tooltip"></div>
        </section>
    </div>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('styles'); ?>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Inter:wght@300;400;500;600&display=swap');

        .tech-font {
            font-family: 'Orbitron', monospace;
        }

        .content-font {
            font-family: 'Inter', sans-serif;
        }

        .cyber-grid {
            background-image:
                linear-gradient(rgba(59, 130, 246, 0.1) 1px, transparent 1px),
                linear-gradient(90deg, rgba(59, 130, 246, 0.1) 1px, transparent 1px);
            background-size: 20px 20px;
            animation: grid-move 20s linear infinite;
        }

        @keyframes grid-move {
            0% {
                background-position: 0 0;
            }

            100% {
                background-position: 20px 20px;
            }
        }

        .glow {
            box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
        }

        .neon-text {
            text-shadow: 0 0 10px rgba(59, 130, 246, 0.5);
        }

        .component-icon {
            transition: all 0.3s ease;
            position: relative;
        }

        .component-icon:hover {
            transform: scale(1.1);
            filter: drop-shadow(0 0 15px rgba(59, 130, 246, 0.6));
        }

        .tooltip {
            position: absolute;
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 12px 16px;
            border-radius: 8px;
            font-size: 12px;
            border: 1px solid rgba(59, 130, 246, 0.3);
            opacity: 0;
            pointer-events: none;
            transform: translateY(10px);
            transition: all 0.3s ease;
            z-index: 1000;
            min-width: 200px;
        }

        .tooltip.show {
            opacity: 1;
            transform: translateY(0);
        }

        .stat-card {
            background: linear-gradient(135deg, rgba(15, 23, 42, 0.8) 0%, rgba(30, 41, 59, 0.8) 100%);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(59, 130, 246, 0.2);
        }

        .pulse-ring {
            animation: pulse-ring 2s infinite;
        }

        @keyframes pulse-ring {
            0% {
                transform: scale(0.8);
                opacity: 1;
            }

            100% {
                transform: scale(1.2);
                opacity: 0;
            }
        }

        .floating {
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {

            0%,
            100% {
                transform: translateY(0px);
            }

            50% {
                transform: translateY(-20px);
            }
        }
    </style>
<?php $__env->stopPush(); ?>
<?php $__env->startPush('scripts'); ?>
    <script>
        // Initialize GSAP animations
        gsap.registerPlugin();

        // Animated counter for performance stats
        function animateCounter(id, target, duration = 2000, suffix = '') {
            const element = document.getElementById(id);
            const increment = target / (duration / 16);
            let current = 0;

            const timer = setInterval(() => {
                current += increment;
                if (current >= target) {
                    current = target;
                    clearInterval(timer);
                }
                element.textContent = Math.floor(current) + suffix;
            }, 16);
        }

        // Rotating taglines
        const taglines = [
            "Silent. Powerful. Smart.",
            "Performance Redefined.",
            "Future-Ready Computing.",
            "Precision Engineering.",
            "Next-Gen Performance."
        ];

        let currentTagline = 0;

        function rotateTagline() {
            const taglineEl = document.getElementById('tagline');

            gsap.to(taglineEl, {
                opacity: 0,
                y: -20,
                duration: 0.5,
                ease: "power2.out",
                onComplete: () => {
                    currentTagline = (currentTagline + 1) % taglines.length;
                    taglineEl.textContent = taglines[currentTagline];
                    gsap.to(taglineEl, {
                        opacity: 1,
                        y: 0,
                        duration: 0.5,
                        ease: "power2.out"
                    });
                }
            });
        }

        // Component tooltip data
        const tooltipData = {
            cpu: {
                title: "Intel Core i9-13900KS",
                specs: [
                    "24 Cores, 32 Threads",
                    "Base: 3.2GHz, Boost: 6.0GHz",
                    "Performance: 98,500 PassMark",
                    "TDP: 150W"
                ]
            },
            gpu: {
                title: "NVIDIA RTX 4090",
                specs: [
                    "16,384 CUDA Cores",
                    "24GB GDDR6X Memory",
                    "4K Gaming: 120+ FPS",
                    "Ray Tracing: Ultimate"
                ]
            },
            ram: {
                title: "DDR5-6000 32GB",
                specs: [
                    "32GB (2x16GB) Kit",
                    "Speed: 6000 MT/s",
                    "Latency: CL30",
                    "RGB Lighting"
                ]
            },
            storage: {
                title: "Samsung 990 PRO 2TB",
                specs: [
                    "PCIe 4.0 NVMe SSD",
                    "Read: 7,450 MB/s",
                    "Write: 6,900 MB/s",
                    "Endurance: 1,200 TBW"
                ]
            }
        };

        // Tooltip functionality
        const tooltip = document.getElementById('tooltip');
        const componentIcons = document.querySelectorAll('.component-icon');

        componentIcons.forEach(icon => {
            icon.addEventListener('mouseenter', (e) => {
                const componentType = e.currentTarget.dataset.tooltip;
                const data = tooltipData[componentType];

                if (data) {
                    const specsHTML = data.specs.map(spec => `<div class="mb-1">${spec}</div>`).join('');
                    tooltip.innerHTML = `
                <div class="font-semibold text-blue-400 mb-2">${data.title}</div>
                <div class="text-gray-300">${specsHTML}</div>
            `;

                    const rect = e.currentTarget.getBoundingClientRect();
                    tooltip.style.left = rect.left + rect.width / 2 - tooltip.offsetWidth / 2 + 'px';
                    tooltip.style.top = rect.top - tooltip.offsetHeight - 10 + 'px';
                    tooltip.classList.add('show');
                }
            });

            icon.addEventListener('mouseleave', () => {
                tooltip.classList.remove('show');
            });
        });

        // Real-time performance simulation
        function simulatePerformance() {
            const fpsBase = 165;
            const tempBase = 68;
            const wattsBase = 125;

            // Simulate fluctuations
            const fpsVariation = Math.random() * 20 - 10;
            const tempVariation = Math.random() * 10 - 5;
            const wattsVariation = Math.random() * 30 - 15;

            const targetFPS = Math.max(30, fpsBase + fpsVariation);
            const targetTemp = Math.max(35, tempBase + tempVariation);
            const targetWatts = Math.max(50, wattsBase + wattsVariation);

            // Animate to new values
            gsap.to({
                fps: parseInt(document.getElementById('fps-counter').textContent)
            }, {
                fps: targetFPS,
                duration: 2,
                ease: "power2.out",
                onUpdate: function() {
                    document.getElementById('fps-counter').textContent = Math.floor(this.targets()[0].fps);
                }
            });

            gsap.to({
                temp: parseInt(document.getElementById('temp-counter').textContent)
            }, {
                temp: targetTemp,
                duration: 2,
                ease: "power2.out",
                onUpdate: function() {
                    document.getElementById('temp-counter').textContent = Math.floor(this.targets()[0].temp);
                }
            });

            gsap.to({
                watts: parseInt(document.getElementById('watts-counter').textContent)
            }, {
                watts: targetWatts,
                duration: 2,
                ease: "power2.out",
                onUpdate: function() {
                    document.getElementById('watts-counter').textContent = Math.floor(this.targets()[0].watts);
                }
            });
        }

        // Initialize animations on page load
        document.addEventListener('DOMContentLoaded', () => {
            // Animate headline entrance
            gsap.from('#headline', {
                opacity: 0,
                y: 50,
                duration: 1.5,
                ease: "power3.out"
            });

            // Animate tagline entrance
            gsap.from('#tagline', {
                opacity: 0,
                y: 30,
                duration: 1,
                delay: 0.5,
                ease: "power2.out"
            });

            // Animate stat cards
            gsap.from('.stat-card', {
                opacity: 0,
                y: 30,
                duration: 0.8,
                stagger: 0.2,
                delay: 1,
                ease: "power2.out"
            });

            // Animate component icons
            gsap.from('.component-icon', {
                opacity: 0,
                scale: 0.8,
                duration: 0.6,
                stagger: 0.1,
                delay: 1.5,
                ease: "back.out(1.7)"
            });

            // Start initial counter animations
            setTimeout(() => {
                animateCounter('fps-counter', 165);
                animateCounter('temp-counter', 68);
                animateCounter('watts-counter', 125);
            }, 2000);

            // Start periodic updates
            setInterval(simulatePerformance, 5000);
            setInterval(rotateTagline, 4000);
        });
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\lara\www\pc-builder\resources\views/welcome.blade.php ENDPATH**/ ?>