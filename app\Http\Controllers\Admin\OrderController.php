<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Order;
use Illuminate\Http\Request;

class OrderController extends Controller
{
    /**
     * Display a listing of the orders.
     */
    public function index()
    {
        return view('admin.orders.index');
    }

    /**
     * Display the specified order.
     */
    public function show(Order $order)
    {
        $order->load(['user', 'orderItems.component']);
        return view('admin.orders.show', compact('order'));
    }

    /**
     * Update the order status.
     */
    public function updateStatus(Request $request, Order $order)
    {
        $request->validate([
            'status' => 'required|string|in:pending,processing,shipped,delivered,canceled',
            'notes' => 'nullable|string'
        ]);

        $order->update([
            'status' => $request->status,
            'notes' => $request->notes
        ]);

        return response()->json(['success' => true, 'message' => 'Order status updated successfully']);
    }

    /**
     * Add tracking information to the order.
     */
    public function addTracking(Request $request, Order $order)
    {
        $request->validate([
            'tracking_number' => 'required|string',
            'carrier' => 'required|string',
            'status' => 'sometimes|string|in:pending,processing,shipped,delivered,canceled'
        ]);

        $updateData = [
            'tracking_number' => $request->tracking_number,
            'tracking_carrier' => $request->carrier
        ];

        if ($request->has('status')) {
            $updateData['status'] = $request->status;
        }

        $order->update($updateData);

        return response()->json(['success' => true, 'message' => 'Tracking information updated successfully']);
    }

    /**
     * Bulk update orders.
     */
    public function bulkUpdate(Request $request)
    {
        $request->validate([
            'order_ids' => 'required|array',
            'order_ids.*' => 'exists:orders,id',
            'status' => 'required|string|in:pending,processing,shipped,delivered,canceled',
            'send_notification' => 'boolean'
        ]);

        Order::whereIn('id', $request->order_ids)
            ->update(['status' => $request->status]);

        return response()->json(['success' => true, 'message' => 'Orders updated successfully']);
    }

    /**
     * Cancel an order.
     */
    public function cancel(Request $request, Order $order)
    {
        $request->validate([
            'reason' => 'required|string',
            'refund_amount' => 'required|numeric|min:0'
        ]);

        $order->update([
            'status' => 'canceled',
            'cancellation_reason' => $request->reason,
            'refund_amount' => $request->refund_amount
        ]);

        return response()->json(['success' => true, 'message' => 'Order canceled successfully']);
    }

    /**
     * Process refund for an order.
     */
    public function processRefund(Request $request, Order $order)
    {
        $request->validate([
            'refund_amount' => 'required|numeric|min:0',
            'reason' => 'required|string'
        ]);

        $order->update([
            'payment_status' => 'refunded',
            'refund_amount' => $request->refund_amount,
            'refund_reason' => $request->reason,
            'refunded_at' => now()
        ]);

        return response()->json(['success' => true, 'message' => 'Refund processed successfully']);
    }

    /**
     * Export orders data.
     */
    public function export(Request $request)
    {
        $dateFrom = $request->get('date_from', now()->subDays(30)->format('Y-m-d'));
        $dateTo = $request->get('date_to', now()->format('Y-m-d'));

        $orders = Order::with(['user', 'orderItems'])
            ->whereBetween('created_at', [$dateFrom, $dateTo])
            ->get();

        $csvData = "Order Number,Customer,Status,Total,Created At\n";
        
        foreach ($orders as $order) {
            $csvData .= sprintf(
                "%s,%s,%s,%.2f,%s\n",
                $order->order_number,
                $order->user->name ?? 'Guest',
                $order->status,
                $order->total,
                $order->created_at->format('Y-m-d H:i:s')
            );
        }

        $response = response($csvData, 200, [
            'Content-Disposition' => 'attachment; filename="orders.csv"'
        ]);
        $response->header('Content-Type', 'text/csv');
        return $response;
    }
}