<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Component;
use App\Models\ComponentCategory;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class ComponentController extends Controller
{
    /**
     * Display a listing of components.
     */
    public function index(Request $request): JsonResponse
    {
        $request->validate([
            'category' => 'sometimes|string|exists:component_categories,slug',
            'search' => 'sometimes|string|max:100',
            'min_price' => 'sometimes|numeric|min:0',
            'max_price' => 'sometimes|numeric|min:0',
            'sort' => 'sometimes|string|in:name,price,created_at',
            'direction' => 'sometimes|string|in:asc,desc',
            'per_page' => 'sometimes|integer|min:1|max:100',
        ]);

        $query = Component::with('category')->active();

        // Apply category filter
        if ($request->has('category')) {
            $query->whereHas('category', function ($q) use ($request) {
                $q->where('slug', $request->category);
            });
        }

        // Apply search filter
        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('brand', 'like', "%{$search}%")
                  ->orWhere('model', 'like', "%{$search}%");
            });
        }

        // Apply price range filter
        if ($request->has('min_price')) {
            $query->where('price', '>=', $request->min_price);
        }

        if ($request->has('max_price')) {
            $query->where('price', '<=', $request->max_price);
        }

        // Apply sorting
        $sort = $request->get('sort', 'created_at');
        $direction = $request->get('direction', 'desc');
        $query->orderBy($sort, $direction);

        // Paginate results
        $perPage = $request->get('per_page', 15);
        $components = $query->paginate($perPage);

        return response()->json([
            'data' => $components->items(),
            'meta' => [
                'current_page' => $components->currentPage(),
                'last_page' => $components->lastPage(),
                'per_page' => $components->perPage(),
                'total' => $components->total()
            ],
            'links' => [
                'first' => $components->url(1),
                'last' => $components->url($components->lastPage()),
                'prev' => $components->previousPageUrl(),
                'next' => $components->nextPageUrl(),
            ]
        ]);
    }

    /**
     * Display the specified component.
     */
    public function show(Component $component): JsonResponse
    {
        $component->load(['category']);

        return response()->json([
            'data' => $component->append(['average_rating', 'review_count'])
        ]);
    }

    /**
     * Get compatible components for a given component.
     */
    public function compatible(Component $component, Request $request): JsonResponse
    {
        $request->validate([
            'category' => 'required|string|exists:component_categories,slug',
        ]);

        $compatibleComponents = $component->getCompatibleComponents($request->category);

        return response()->json([
            'data' => $compatibleComponents->values()
        ]);
    }

    /**
     * Get component categories.
     */
    public function categories(): JsonResponse
    {
        $categories = ComponentCategory::with(['components' => function ($query) {
            $query->active()->limit(5);
        }])->get();

        return response()->json([
            'data' => $categories
        ]);
    }

    /**
     * Get component brands for a category.
     */
    public function brands(Request $request): JsonResponse
    {
        $request->validate([
            'category' => 'sometimes|string|exists:component_categories,slug',
        ]);

        $query = Component::active();

        if ($request->has('category')) {
            $query->whereHas('category', function ($q) use ($request) {
                $q->where('slug', $request->category);
            });
        }

        $brands = $query->distinct()
            ->pluck('brand')
            ->filter()
            ->sort()
            ->values();

        return response()->json([
            'data' => $brands
        ]);
    }
}