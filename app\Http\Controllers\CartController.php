<?php

namespace App\Http\Controllers;

use App\Services\CartService;
use Illuminate\Http\Request;

class CartController extends Controller
{
    protected $cartService;
    
    public function __construct(CartService $cartService)
    {   
        $this->cartService = $cartService;
    }
    
    /**
     * Display the cart page.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {   
        return view('shop.cart');
    }
    
    /**
     * Add an item to the cart.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse|\Illuminate\Http\RedirectResponse
     */
    public function addItem(Request $request)
    {   
        $validated = $request->validate([
            'component_id' => 'required|exists:components,id',
            'quantity' => 'required|integer|min:1',
        ]);
        
        try {
            $result = $this->cartService->addItem(
                $validated['component_id'], 
                $validated['quantity']
            );
            
            if ($request->wantsJson()) {
                return response()->json(['success' => true, 'message' => 'Item added to cart successfully!']);
            }
            
            return redirect()->back()->with('message', 'Item added to cart successfully!');
        } catch (\Exception $e) {
            if ($request->wantsJson()) {
                return response()->json([
                    'success' => false,
                    'error' => $e->getMessage()
                ], 422);
            }
            
            return redirect()->back()->with('error', $e->getMessage());
        }
    }
    
    /**
     * Update multiple items in the cart.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse|\Illuminate\Http\RedirectResponse
     */
    public function updateItems(Request $request)
    {   
        $validated = $request->validate([
            'items' => 'required|array',
            'items.*.component_id' => 'required|exists:components,id',
            'items.*.quantity' => 'required|integer|min:1',
        ]);
        
        try {
            $errors = [];
            foreach ($validated['items'] as $item) {
                try {
                    $this->cartService->updateItemQuantity($item['component_id'], $item['quantity']);
                } catch (\Exception $e) {
                    $errors[$item['component_id']] = $e->getMessage();
                }
            }
            
            if (!empty($errors)) {
                if ($request->expectsJson()) {
                    return response()->json([
                        'success' => false,
                        'errors' => $errors
                    ], 422);
                }
                
                return redirect()->back()->with('error', 'Some items could not be updated.');
            }
            
            if ($request->expectsJson()) {
                return response()->json(['success' => true, 'message' => 'Cart updated successfully!']);
            }
            
            return redirect()->back()->with('message', 'Cart updated successfully!');
        } catch (\Exception $e) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'error' => $e->getMessage()
                ], 422);
            }
            
            return redirect()->back()->with('error', $e->getMessage());
        }
    }
    
    /**
     * Update the quantity of a single item in the cart.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $itemId
     * @return \Illuminate\Http\RedirectResponse
     */
    public function updateItem(Request $request, $itemId)
    {   
        $validated = $request->validate([
            'quantity' => 'required|integer|min:1',
        ]);
        
        $result = $this->cartService->updateItemQuantity($itemId, $validated['quantity']);
        
        if ($result) {
            return redirect()->back()->with('message', 'Cart updated successfully!');
        }
        
        return redirect()->back()->with('error', 'Failed to update cart.');
    }
    
    /**
     * Remove an item from the cart.
     *
     * @param  int  $componentId
     * @return \Illuminate\Http\JsonResponse|\Illuminate\Http\RedirectResponse
     */
    public function removeItem(Request $request, $componentId)
    {   
        try {
            $result = $this->cartService->removeItemByComponentId($componentId);
            
            if ($request->expectsJson()) {
                return response()->json(['success' => true, 'message' => 'Item removed from cart!']);
            }
            
            return redirect()->back()->with('message', 'Item removed from cart!');
        } catch (\Exception $e) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'error' => $e->getMessage()
                ], 422);
            }
            
            return redirect()->back()->with('error', $e->getMessage());
        }
    }
    
    /**
     * Clear all items from the cart.
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function clearCart()
    {   
        $result = $this->cartService->clearCart();
        
        if ($result) {
            return redirect()->back()->with('message', 'Cart cleared successfully!');
        }
        
        return redirect()->back()->with('error', 'Failed to clear cart.');
    }
    
    /**
     * Add a build to the cart.
     *
     * @param  int  $buildId
     * @return \Illuminate\Http\RedirectResponse
     */
    public function addBuild($buildId)
    {   
        $result = $this->cartService->addBuild($buildId);
        
        if ($result) {
            return redirect()->route('cart.index')->with('message', 'Build added to cart successfully!');
        }
        
        return redirect()->back()->with('error', 'Failed to add build to cart.');
    }
    
    /**
     * Apply a coupon to the cart.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse|\Illuminate\Http\RedirectResponse
     */
    public function applyCoupon(Request $request)
    {   
        $validated = $request->validate([
            'coupon_code' => 'required|string',
        ]);
        
        try {
            $result = $this->cartService->applyCoupon($validated['coupon_code']);
            
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'discount_amount' => $result['discount_amount'],
                    'message' => 'Coupon applied successfully'
                ]);
            }
            
            return redirect()->back()->with('message', 'Coupon applied successfully!');
        } catch (\Exception $e) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'error' => $e->getMessage()
                ], 422);
            }
            
            return redirect()->back()->with('error', $e->getMessage());
        }
    }
    
    /**
     * Proceed to checkout.
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function checkout()
    {   
        // Check if user is logged in
        if (!auth()->check()) {
            return redirect()->route('login')->with('message', 'Please login to proceed with checkout.');
        }
        
        // Check if cart has items
        $cart = $this->cartService->getCart();
        
        if (!$cart || $cart->items()->count() === 0) {
            return redirect()->route('shop.cart')->with('error', 'Your cart is empty.');
        }
        
        // Redirect to checkout page
        return redirect()->route('checkout.index');
    }
}