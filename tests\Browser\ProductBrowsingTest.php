<?php

namespace Tests\Browser;

use App\Models\Product;
use App\Models\ProductCategory;
use App\Models\User;
use Laravel\Dusk\Browser;
use Tests\Browser\Pages\ProductsPage;
use Tests\DuskTestCase;

class ProductBrowsingTest extends DuskTestCase
{
    /**
     * Test products page loads correctly.
     */
    public function test_products_page_loads(): void
    {
        // Create test data
        $category = ProductCategory::factory()->create(['name' => 'Electronics']);
        Product::factory()->count(3)->create([
            'category_id' => $category->id,
            'status' => 'active',
            'manage_stock' => false, // Don't manage stock for simplicity
        ]);

        $this->browse(function (Browser $browser) {
            $browser->visit('/products')
                    ->assertSee('Products')
                    ->assertPresent('input[name="search"]')
                    ->assertPresent('select[name="category"]')
                    ->assertPresent('.grid');
        });
    }

    /**
     * Test product search functionality.
     */
    public function test_product_search(): void
    {
        $category = ProductCategory::factory()->create(['name' => 'Electronics']);
        
        Product::factory()->create([
            'name' => 'iPhone 15 Pro',
            'category_id' => $category->id,
            'status' => 'active',
            'manage_stock' => false,
        ]);
        
        Product::factory()->create([
            'name' => 'Samsung Galaxy S24',
            'category_id' => $category->id,
            'status' => 'active',
            'manage_stock' => false,
        ]);

        $this->browse(function (Browser $browser) {
            $browser->visit('/products')
                    ->type('input[name="search"]', 'iPhone')
                    ->click('button[type="submit"]')
                    ->waitForText('iPhone 15 Pro')
                    ->assertSee('iPhone 15 Pro')
                    ->assertDontSee('Samsung Galaxy S24');
        });
    }

    /**
     * Test product category filtering.
     */
    public function test_product_category_filtering(): void
    {
        $electronics = ProductCategory::factory()->create(['name' => 'Electronics']);
        $gaming = ProductCategory::factory()->create(['name' => 'Gaming']);
        
        Product::factory()->create([
            'name' => 'Gaming Laptop',
            'category_id' => $electronics->id,
            'status' => 'active',
            'manage_stock' => false,
        ]);
        
        Product::factory()->create([
            'name' => 'Gaming Mouse',
            'category_id' => $gaming->id,
            'status' => 'active',
            'manage_stock' => false,
        ]);

        $this->browse(function (Browser $browser) {
            $browser->visit('/products')
                    ->select('select[name="category"]', 'Electronics')
                    ->click('button[type="submit"]')
                    ->waitForText('Gaming Laptop')
                    ->assertSee('Gaming Laptop')
                    ->assertDontSee('Gaming Mouse');
        });
    }

    /**
     * Test product detail view.
     */
    public function test_product_detail_view(): void
    {
        $category = ProductCategory::factory()->create(['name' => 'Electronics']);
        
        $product = Product::factory()->create([
            'name' => 'Test Product Detail',
            'description' => 'This is a detailed test product description',
            'price' => 199.99,
            'category_id' => $category->id,
            'status' => 'active',
            'manage_stock' => false,
        ]);

        $this->browse(function (Browser $browser) use ($product) {
            $browser->visit('/products')
                    ->click('a[href*="' . $product->slug . '"]')
                    ->waitForLocation('/shop/product/' . $product->slug)
                    ->assertSee('Test Product Detail')
                    ->assertSee('This is a detailed test product description')
                    ->assertSee('199.99');
        });
    }

    /**
     * Test featured products filter.
     */
    public function test_featured_products_filter(): void
    {
        $category = ProductCategory::factory()->create(['name' => 'Electronics']);
        
        Product::factory()->create([
            'name' => 'Featured Product',
            'category_id' => $category->id,
            'status' => 'active',
            'featured' => true,
            'manage_stock' => false,
        ]);
        
        Product::factory()->create([
            'name' => 'Regular Product',
            'category_id' => $category->id,
            'status' => 'active',
            'featured' => false,
            'manage_stock' => false,
        ]);

        $this->browse(function (Browser $browser) {
            $browser->visit('/products')
                    ->check('input[name="featured"]')
                    ->click('button[type="submit"]')
                    ->waitForText('Featured Product')
                    ->assertSee('Featured Product')
                    ->assertDontSee('Regular Product');
        });
    }

    /**
     * Test empty search results.
     */
    public function test_empty_search_results(): void
    {
        $this->browse(function (Browser $browser) {
            $browser->visit('/products')
                    ->type('input[name="search"]', 'nonexistent product xyz')
                    ->click('button[type="submit"]')
                    ->waitForText('No products found')
                    ->assertSee('No products found');
        });
    }

    /**
     * Test product sorting.
     */
    public function test_product_sorting(): void
    {
        $category = ProductCategory::factory()->create(['name' => 'Electronics']);
        
        Product::factory()->create([
            'name' => 'Expensive Product',
            'price' => 999.99,
            'category_id' => $category->id,
            'status' => 'active',
            'manage_stock' => false,
        ]);
        
        Product::factory()->create([
            'name' => 'Cheap Product',
            'price' => 99.99,
            'category_id' => $category->id,
            'status' => 'active',
            'manage_stock' => false,
        ]);

        $this->browse(function (Browser $browser) {
            $browser->visit('/products')
                    ->select('select[name="sort"]', 'price')
                    ->click('button[type="submit"]')
                    ->waitForText('Cheap Product')
                    ->assertSeeIn('.grid > div:first-child', 'Cheap Product');
        });
    }
}