<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Route;

class InstallationServiceProvider extends ServiceProvider
{
    public function register()
    {
        // Only handle route disabling here
        if (!File::exists(storage_path('installed'))) {
            $this->app->booted(function () {
                $this->disableDatabaseRoutes();
            });
        }
    }

    protected function disableDatabaseRoutes()
    {
        Route::middleware('web')->group(function () {
            Route::get('/install', 'App\Http\Controllers\InstallationController@showWelcome')
                ->name('installer.welcome');
        });
    }
}