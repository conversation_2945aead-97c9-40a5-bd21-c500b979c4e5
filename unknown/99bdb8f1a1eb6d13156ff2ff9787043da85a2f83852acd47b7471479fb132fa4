@extends('layouts.app')

@section('title', 'PC Builder')

@section('content')
<div class="py-12">
    <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        @if(session('message'))
            <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-4" role="alert">
                <span class="block sm:inline">{{ session('message') }}</span>
            </div>
        @endif

        @if(session('error'))
            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
                <span class="block sm:inline">{{ session('error') }}</span>
            </div>
        @endif

        @auth
            <div class="mb-8">
                @livewire('builder.saved-builds')
            </div>
        @endauth

        <div class="mb-8">
            @livewire('builder.builder-container')
        </div>

        <div class="mb-8">
            @livewire('builder.popular-builds')
        </div>
    </div>
</div>
@endsection