<?php

namespace Database\Factories;

use App\Models\Component;
use App\Models\PriceHistory;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\PriceHistory>
 */
class PriceHistoryFactory extends Factory
{
    protected $model = PriceHistory::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $price = $this->faker->randomFloat(2, 50, 1000);
        $previousPrice = $this->faker->randomFloat(2, 50, 1000);
        
        return [
            'component_id' => Component::factory(),
            'price' => $price,
            'previous_price' => $previousPrice,
            'source' => $this->faker->randomElement(['automated', 'manual', 'import']),
            'metadata' => [
                'change_amount' => $price - $previousPrice,
                'change_percentage' => (($price - $previousPrice) / $previousPrice) * 100,
                'updated_at' => $this->faker->dateTimeThisMonth()->format('c')
            ],
        ];
    }

    /**
     * Indicate that the price history is from automated tracking.
     */
    public function automated(): static
    {
        return $this->state(fn (array $attributes) => [
            'source' => 'automated',
        ]);
    }

    /**
     * Indicate that the price history is from manual entry.
     */
    public function manual(): static
    {
        return $this->state(fn (array $attributes) => [
            'source' => 'manual',
        ]);
    }

    /**
     * Create a price increase.
     */
    public function priceIncrease(): static
    {
        return $this->state(function (array $attributes) {
            $previousPrice = $this->faker->randomFloat(2, 50, 500);
            $price = $previousPrice + $this->faker->randomFloat(2, 10, 100);
            
            return [
                'price' => $price,
                'previous_price' => $previousPrice,
                'metadata' => [
                    'change_amount' => $price - $previousPrice,
                    'change_percentage' => (($price - $previousPrice) / $previousPrice) * 100,
                    'updated_at' => now()->format('c')
                ],
            ];
        });
    }

    /**
     * Create a price decrease.
     */
    public function priceDecrease(): static
    {
        return $this->state(function (array $attributes) {
            $previousPrice = $this->faker->randomFloat(2, 100, 1000);
            $price = $previousPrice - $this->faker->randomFloat(2, 10, 50);
            
            return [
                'price' => $price,
                'previous_price' => $previousPrice,
                'metadata' => [
                    'change_amount' => $price - $previousPrice,
                    'change_percentage' => (($price - $previousPrice) / $previousPrice) * 100,
                    'updated_at' => now()->format('c')
                ],
            ];
        });
    }
}