<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Exception;

class ScriptActivationService
{
    private $apiUrl;
    private $apiKey;
    private $cacheTimeout;
    private $productId;

    public function __construct()
    {
        $this->apiUrl = config('script-activation.api_url');
        $this->apiKey = config('script-activation.api_key');
        $this->cacheTimeout = config('script-activation.cache_timeout');
        $this->productId = config('script-activation.product_id');
    }

    public function validateLicense($licenseKey)
    {
        $cacheKey = 'license_status_' . md5($licenseKey);

        // Check cache first
        if (Cache::has($cacheKey)) {
            return Cache::get($cacheKey);
        }

        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->apiKey,
                'Accept' => 'application/json',
            ])->post($this->apiUrl . '/api/v1/license/verify', [
                        'license_key' => $licenseKey,
                        'product_id' => $this->productId,
                        'domain' => request()->getHost(),
                        'ip' => request()->ip(),
                    ]);

            if ($response->successful()) {
                $result = $response->json();
                Cache::put($cacheKey, $result, $this->cacheTimeout);
                return $result;
            }

            Log::error('License validation failed', [
                'status' => $response->status(),
                'response' => $response->json()
            ]);

            return [
                'status' => 'error',
                'message' => 'License validation failed'
            ];

        } catch (Exception $e) {
            Log::error('License validation error', [
                'error' => $e->getMessage()
            ]);

            return [
                'status' => 'error',
                'message' => 'Connection error'
            ];
        }
    }

    public function activateLicense(
        // $licenseKey,
        $purchaseCode
    ) {
        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->apiKey,
                'Accept' => 'application/json',
            ])->post($this->apiUrl . '/api/v1/license/activate', [
                        // 'license_key' => $licenseKey,
                        'product_id' => $this->productId,
                        'domain' => request()->getHost(),
                        'ip' => request()->ip(),
                        'purchase_code' => $purchaseCode
                    ]);

            return $response->json();

        } catch (Exception $e) {
            Log::error('License activation error', [
                'error' => $e->getMessage()
            ]);

            return [
                'status' => 'error',
                'message' => 'Activation failed'
            ];
        }
    }

    public function deactivateLicense($licenseKey)
    {
        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->apiKey,
                'Accept' => 'application/json',
            ])->post($this->apiUrl . '/api/v1/license/deactivate', [
                        'license_key' => $licenseKey,
                        'product_id' => $this->productId,
                        'domain' => request()->getHost()
                    ]);

            return $response->json();

        } catch (Exception $e) {
            Log::error('License deactivation error', [
                'error' => $e->getMessage()
            ]);

            return [
                'status' => 'error',
                'message' => 'Deactivation failed'
            ];
        }
    }
}