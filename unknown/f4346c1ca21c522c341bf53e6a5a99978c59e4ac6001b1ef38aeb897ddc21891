<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Build extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'name',
        'description',
        'is_public',
        'is_complete',
        'total_price',
        'compatibility_issues',
        'share_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_public' => 'boolean',
        'is_complete' => 'boolean',
        'total_price' => 'decimal:2',
        'compatibility_issues' => 'array',
    ];

    /**
     * Get the user that owns the build.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the components for the build.
     */
    public function components(): HasMany
    {
        return $this->hasMany(BuildComponent::class);
    }

    /**
     * Calculate the total price of the build.
     */
    public function calculateTotalPrice()
    {
        $total = $this->components->sum(function ($component) {
            return $component->price * $component->quantity;
        });
        
        $this->total_price = $total;
        $this->save();
        
        return $total;
    }

    /**
     * Check if the build is complete (has all required components).
     */
    public function checkIfComplete()
    {
        $requiredCategories = ComponentCategory::required()->get();
        $buildCategoryIds = $this->components->pluck('category_id')->unique();
        
        $isComplete = $requiredCategories->every(function ($category) use ($buildCategoryIds) {
            return $buildCategoryIds->contains($category->id);
        });
        
        $this->is_complete = $isComplete;
        $this->save();
        
        return $isComplete;
    }

    /**
     * Add a component to the build.
     */
    public function addComponent(Component $component, int $quantity = 1, ?float $price = null)
    {
        // Check if component already exists in build
        $existingComponent = $this->components()->where('component_id', $component->id)->first();
        
        if ($existingComponent) {
            $existingComponent->quantity += $quantity;
            $existingComponent->save();
            return $existingComponent;
        }
        
        // Create new build component
        return $this->components()->create([
            'component_id' => $component->id,
            'category_id' => $component->category_id,
            'quantity' => $quantity,
            'price' => $price ?? $component->price,
        ]);
    }

    /**
     * Remove a component from the build.
     */
    public function removeComponent(int $componentId)
    {
        return $this->components()->where('component_id', $componentId)->delete();
    }

    /**
     * Check compatibility of all components in the build.
     */
    public function checkCompatibility()
    {
        $issues = [];
        $components = $this->components()->with('component')->get();
        
        // Implementation of compatibility checking logic would go here
        // This is a simplified version
        
        $this->compatibility_issues = $issues;
        $this->save();
        
        return $issues;
    }
}