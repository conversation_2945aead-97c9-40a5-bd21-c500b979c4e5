@extends('admin.layouts.admin')
@php
    use App\Models\Setting;
    use Illuminate\Support\Facades\Crypt;
    $settings = Setting::all()->groupBy('group');
@endphp

@section('title', 'Website Settings')

@section('content')
    <div class="container mx-auto px-4 py-8">
        <div class="bg-white dark:bg-bg-dark rounded-lg shadow-md p-6 border border-border-light dark:border-border-dark">
            <h1 class="text-2xl font-bold mb-6 text-text-primary-light dark:text-text-primary-dark">Website Settings</h1>

            <form action="{{ route('admin.settings.update') }}" method="POST" enctype="multipart/form-data">
                @csrf
                @method('PUT')

                {{-- <div class="bg-white dark:bg-bg-dark rounded-lg shadow-md p-6 mb-6 border border-border-light dark:border-border-dark">
                    <h2 class="text-xl font-semibold mb-4 text-text-primary-light dark:text-text-primary-dark">Typography Settings</h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium mb-1 text-text-primary-light dark:text-text-primary-dark">Primary Font</label>
                            <input type="text" name="settings[typography_primary_font]" 
                                value="{{ Setting::get('typography_primary_font', 'Inter, sans-serif') }}"
                                class="w-full rounded-md border border-border-light dark:border-border-dark shadow-sm focus:border-primary-light dark:focus:border-primary-dark focus:ring focus:ring-primary-light dark:focus:ring-primary-dark focus:ring-opacity-50 bg-white dark:bg-bg-dark text-text-primary-light dark:text-text-primary-dark">
                        </div>
                        <div>
                            <label class="block text-sm font-medium mb-1 text-text-primary-light dark:text-text-primary-dark">Secondary Font</label>
                            <input type="text" name="settings[typography_secondary_font]" 
                                value="{{ Setting::get('typography_secondary_font', 'Roboto, sans-serif') }}"
                                class="w-full rounded-md border border-border-light dark:border-border-dark shadow-sm focus:border-primary-light dark:focus:border-primary-dark focus:ring focus:ring-primary-light dark:focus:ring-primary-dark focus:ring-opacity-50 bg-white dark:bg-bg-dark text-text-primary-light dark:text-text-primary-dark">
                        </div>
                        <div>
                            <label class="block text-sm font-medium mb-1 text-text-primary-light dark:text-text-primary-dark">Heading Font Size (H1)</label>
                            <input type="text" name="settings[typography_h1_size]" 
                                value="{{ Setting::get('typography_h1_size', '2.5rem') }}"
                                class="w-full rounded-md border border-border-light dark:border-border-dark shadow-sm focus:border-primary-light dark:focus:border-primary-dark focus:ring focus:ring-primary-light dark:focus:ring-primary-dark focus:ring-opacity-50 bg-white dark:bg-bg-dark text-text-primary-light dark:text-text-primary-dark">
                        </div>
                        <div>
                            <label class="block text-sm font-medium mb-1 text-text-primary-light dark:text-text-primary-dark">Heading Font Size (H2)</label>
                            <input type="text" name="settings[typography_h2_size]" 
                                value="{{ Setting::get('typography_h2_size', '2rem') }}"
                                class="w-full rounded-md border border-border-light dark:border-border-dark shadow-sm focus:border-primary-light dark:focus:border-primary-dark focus:ring focus:ring-primary-light dark:focus:ring-primary-dark focus:ring-opacity-50 bg-white dark:bg-bg-dark text-text-primary-light dark:text-text-primary-dark">
                        </div>
                    </div>
                </div> --}}

                @foreach($settings as $group => $groupSettings)
                    <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-6 mb-6 border border-gray-200 dark:border-gray-700">
                        <div class="border-b border-gray-200 dark:border-gray-600 pb-3 mb-6">
                            <h2 class="text-xl font-semibold text-text-primary-light dark:text-text-primary-dark flex items-center">
                                <span class="w-1 h-6 bg-primary-light dark:bg-primary-dark rounded-full mr-3"></span>
                                {{ ucfirst($group) }} Settings
                            </h2>
                            <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                                Configure {{ strtolower($group) }} related settings
                            </p>
                        </div>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            @foreach($groupSettings as $setting)
                                <div class="bg-white dark:bg-bg-dark rounded-md p-4 border border-border-light dark:border-border-dark">
                                    <label class="block text-sm font-medium text-text-primary-light dark:text-text-primary-dark mb-2">
                                        {{ $setting->description }}
                                    </label>
                                    
                                    @switch($setting->type)
                                        @case('text')
                                            <input type="text" name="settings[{{ $setting->key }}]" 
                                                value="{{ $setting->value }}"
                                                class="w-full rounded-md border border-border-light dark:border-border-dark shadow-sm focus:border-primary-light dark:focus:border-primary-dark focus:ring focus:ring-primary-light dark:focus:ring-primary-dark focus:ring-opacity-50 bg-white dark:bg-bg-dark text-text-primary-light dark:text-text-primary-dark">
                                            @break

                                        @case('textarea')
                                            <textarea name="settings[{{ $setting->key }}]" rows="3"
                                                class="w-full rounded-md border border-border-light dark:border-border-dark shadow-sm focus:border-primary-light dark:focus:border-primary-dark focus:ring focus:ring-primary-light dark:focus:ring-primary-dark focus:ring-opacity-50 bg-white dark:bg-bg-dark text-text-primary-light dark:text-text-primary-dark">{{ $setting->value }}</textarea>
                                            @break

                                        @case('boolean')
                                            <div class="flex items-center">
                                                <input type="hidden" name="settings[{{ $setting->key }}]" value="0">
                                                <input type="checkbox" id="{{ $setting->key }}" 
                                                    name="settings[{{ $setting->key }}]" value="1"
                                                    {{ $setting->value ? 'checked' : '' }}
                                                    class="rounded border border-border-light dark:border-border-dark text-primary-light dark:text-primary-dark focus:ring-primary-light dark:focus:ring-primary-dark bg-white dark:bg-bg-dark">
                                                <label for="{{ $setting->key }}" class="ml-2 text-sm font-medium text-text-primary-light dark:text-text-primary-dark">
                                                    Enable
                                                </label>
                                            </div>
                                            @break

                                        @case('image')
                                            <input type="file" name="settings[{{ $setting->key }}]" accept="image/*"
                                                class="w-full rounded-md border border-border-light dark:border-border-dark shadow-sm focus:border-primary-light dark:focus:border-primary-dark focus:ring focus:ring-primary-light dark:focus:ring-primary-dark focus:ring-opacity-50 bg-white dark:bg-bg-dark text-text-primary-light dark:text-text-primary-dark">
                                            @if($setting->value)
                                                <img src="{{ uploads_url($setting->value) }}" 
                                                    alt="{{ $setting->description }}"
                                                    class="mt-2 h-12 rounded border border-gray-200 dark:border-gray-600">
                                            @endif
                                            @break

                                        @case('json')
                                            <textarea name="settings[{{ $setting->key }}]" rows="3"
                                                class="w-full rounded-md border border-border-light dark:border-border-dark shadow-sm focus:border-primary-light dark:focus:ring-primary-dark focus:ring focus:ring-primary-light dark:focus:ring-primary-dark focus:ring-opacity-50 bg-white dark:bg-bg-dark text-text-primary-light dark:text-text-primary-dark font-mono text-sm">{{ is_string($setting->value) ? $setting->value : json_encode($setting->value, JSON_PRETTY_PRINT) }}</textarea>
                                            @break

                                        @default
                                            <input type="text" name="settings[{{ $setting->key }}]" 
                                                value="{{ $setting->value }}"
                                                class="w-full rounded-md border border-border-light dark:border-border-dark shadow-sm focus:border-primary-light dark:focus:border-primary-dark focus:ring focus:ring-primary-light dark:focus:ring-primary-dark focus:ring-opacity-50 bg-white dark:bg-bg-dark text-text-primary-light dark:text-text-primary-dark">
                                    @endswitch
                                </div>
                            @endforeach
                        </div>
                    </div>
                @endforeach

                <div class="flex justify-end pt-6 border-t border-gray-200 dark:border-gray-600">
                    <button type="submit"
                        class="bg-primary-light dark:bg-primary-dark text-white px-6 py-3 rounded-md hover:bg-primary-dark dark:hover:bg-primary-light focus:outline-none focus:ring-2 focus:ring-primary-light dark:focus:ring-primary-dark focus:ring-opacity-50 font-medium transition-colors duration-200">
                        Save Settings
                    </button>
                </div>
            </form>
        </div>
    </div>
@endsection