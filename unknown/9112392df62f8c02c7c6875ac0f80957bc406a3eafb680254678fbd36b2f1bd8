<?php

namespace Tests\Unit\Services;

use App\Models\Component;
use App\Models\ComponentCategory;
use App\Models\StockMovement;
use App\Models\InventoryAlert;
use App\Services\InventoryService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Log;
use Tests\TestCase;

class InventoryServiceTest extends TestCase
{
    use RefreshDatabase;

    protected InventoryService $inventoryService;
    protected Component $component;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->inventoryService = new InventoryService();
        
        $category = ComponentCategory::factory()->create();
        $this->component = Component::factory()->create([
            'category_id' => $category->id,
            'stock' => 50,
            'is_active' => true
        ]);
    }

    public function test_updates_component_stock()
    {
        Log::shouldReceive('info')->once();

        $result = $this->inventoryService->updateStock($this->component, 25, 'manual');

        $this->assertTrue($result);
        $this->assertEquals(25, $this->component->fresh()->stock);
    }

    public function test_triggers_low_stock_alert()
    {
        Log::shouldReceive('info')->once();
        Log::shouldReceive('warning')->once();

        $this->inventoryService->setLowStockThreshold(15);
        $result = $this->inventoryService->updateStock($this->component, 10, 'sale');

        $this->assertTrue($result);
        $this->assertEquals(10, $this->component->fresh()->stock);
    }

    public function test_triggers_out_of_stock_alert()
    {
        Log::shouldReceive('info')->once();
        Log::shouldReceive('warning')->once();
        Log::shouldReceive('critical')->once();

        $result = $this->inventoryService->updateStock($this->component, 0, 'sale');

        $this->assertTrue($result);
        $this->assertEquals(0, $this->component->fresh()->stock);
    }

    public function test_adjusts_stock_by_amount()
    {
        Log::shouldReceive('info')->once();

        $result = $this->inventoryService->adjustStock($this->component, -10, 'sale');

        $this->assertTrue($result);
        $this->assertEquals(40, $this->component->fresh()->stock);
    }

    public function test_prevents_negative_stock_adjustment()
    {
        Log::shouldReceive('info')->once();
        Log::shouldReceive('warning')->once();
        Log::shouldReceive('critical')->once();

        $result = $this->inventoryService->adjustStock($this->component, -100, 'sale');

        $this->assertTrue($result);
        $this->assertEquals(0, $this->component->fresh()->stock);
    }

    public function test_reserves_stock_successfully()
    {
        Log::shouldReceive('info')->once();

        $result = $this->inventoryService->reserveStock($this->component, 10);

        $this->assertTrue($result);
        $this->assertEquals(40, $this->component->fresh()->stock);
    }

    public function test_fails_to_reserve_insufficient_stock()
    {
        $result = $this->inventoryService->reserveStock($this->component, 100);

        $this->assertFalse($result);
        $this->assertEquals(50, $this->component->fresh()->stock);
    }

    public function test_releases_stock()
    {
        Log::shouldReceive('info')->once();

        $result = $this->inventoryService->releaseStock($this->component, 10);

        $this->assertTrue($result);
        $this->assertEquals(60, $this->component->fresh()->stock);
    }

    public function test_gets_low_stock_components()
    {
        $category = ComponentCategory::factory()->create();
        
        // Create components with different stock levels
        $lowStock1 = Component::factory()->create([
            'category_id' => $category->id,
            'stock' => 5,
            'is_active' => true
        ]);
        
        $lowStock2 = Component::factory()->create([
            'category_id' => $category->id,
            'stock' => 8,
            'is_active' => true
        ]);
        
        $normalStock = Component::factory()->create([
            'category_id' => $category->id,
            'stock' => 25,
            'is_active' => true
        ]);
        
        $outOfStock = Component::factory()->create([
            'category_id' => $category->id,
            'stock' => 0,
            'is_active' => true
        ]);

        $lowStockComponents = $this->inventoryService->getLowStockComponents();

        $this->assertCount(2, $lowStockComponents);
        $this->assertTrue($lowStockComponents->contains($lowStock1));
        $this->assertTrue($lowStockComponents->contains($lowStock2));
        $this->assertFalse($lowStockComponents->contains($normalStock));
        $this->assertFalse($lowStockComponents->contains($outOfStock));
    }

    public function test_gets_out_of_stock_components()
    {
        $category = ComponentCategory::factory()->create();
        
        $outOfStock = Component::factory()->create([
            'category_id' => $category->id,
            'stock' => 0,
            'is_active' => true
        ]);
        
        $inStock = Component::factory()->create([
            'category_id' => $category->id,
            'stock' => 25,
            'is_active' => true
        ]);

        $outOfStockComponents = $this->inventoryService->getOutOfStockComponents();

        $this->assertCount(1, $outOfStockComponents);
        $this->assertTrue($outOfStockComponents->contains($outOfStock));
        $this->assertFalse($outOfStockComponents->contains($inStock));
    }

    public function test_generates_inventory_report()
    {
        $category = ComponentCategory::factory()->create();
        
        // Create components with different stock levels and prices
        Component::factory()->create([
            'category_id' => $category->id,
            'stock' => 5,
            'price' => 100.00,
            'is_active' => true
        ]);
        
        Component::factory()->create([
            'category_id' => $category->id,
            'stock' => 0,
            'price' => 200.00,
            'is_active' => true
        ]);
        
        Component::factory()->create([
            'category_id' => $category->id,
            'stock' => 25,
            'price' => 50.00,
            'is_active' => true
        ]);

        $report = $this->inventoryService->getInventoryReport();

        $this->assertEquals(4, $report['total_components']); // Including the one from setUp
        $this->assertEquals(2, $report['in_stock']); // Stock > 10 (threshold)
        $this->assertEquals(1, $report['low_stock']); // Stock 1-10
        $this->assertEquals(1, $report['out_of_stock']); // Stock 0
        $this->assertIsFloat($report['total_inventory_value']);
    }

    public function test_bulk_updates_stock()
    {
        $category = ComponentCategory::factory()->create();
        
        $component2 = Component::factory()->create([
            'category_id' => $category->id,
            'stock' => 20,
            'is_active' => true
        ]);

        Log::shouldReceive('info')->twice();
        Log::shouldReceive('error')->never();

        $updates = [
            [
                'component_id' => $this->component->id,
                'stock' => 30,
                'reason' => 'bulk_update'
            ],
            [
                'component_id' => $component2->id,
                'stock' => 40,
                'reason' => 'bulk_update'
            ]
        ];

        $results = $this->inventoryService->bulkUpdateStock($updates);

        $this->assertEquals(2, $results['updated']);
        $this->assertEquals(0, $results['failed']);
        $this->assertEquals(30, $this->component->fresh()->stock);
        $this->assertEquals(40, $component2->fresh()->stock);
    }

    public function test_syncs_with_supplier_data()
    {
        Log::shouldReceive('info')->times(2);
        Log::shouldReceive('error')->never();

        $supplierData = [
            [
                'brand' => $this->component->brand,
                'model' => $this->component->model,
                'stock' => 75,
                'price' => 199.99
            ],
            [
                'brand' => 'NewBrand',
                'model' => 'NewModel',
                'stock' => 25,
                'price' => 99.99
            ]
        ];

        $results = $this->inventoryService->syncWithSupplier($supplierData);

        $this->assertEquals(1, $results['updated']);
        $this->assertEquals(1, $results['new']);
        $this->assertEquals(0, $results['failed']);
        $this->assertEquals(75, $this->component->fresh()->stock);
        $this->assertEquals(199.99, $this->component->fresh()->price);
    }

    public function test_gets_stock_movement_history()
    {
        // Create some stock movements
        StockMovement::factory()->create([
            'component_id' => $this->component->id,
            'quantity_change' => -5,
            'previous_stock' => 50,
            'new_stock' => 45,
            'type' => 'sale',
            'reason' => 'order_fulfillment'
        ]);

        $history = $this->inventoryService->getStockMovementHistory($this->component, 30);

        $this->assertNotEmpty($history);
        $this->assertInstanceOf(\Illuminate\Support\Collection::class, $history);
    }

    public function test_creates_stock_movement_on_update()
    {
        Log::shouldReceive('info')->once();

        $this->inventoryService->updateStock($this->component, 25, 'manual');

        $this->assertDatabaseHas('stock_movements', [
            'component_id' => $this->component->id,
            'quantity_change' => -25,
            'previous_stock' => 50,
            'new_stock' => 25,
            'reason' => 'manual'
        ]);
    }

    public function test_creates_inventory_alert_on_low_stock()
    {
        Log::shouldReceive('info')->once();
        Log::shouldReceive('warning')->once();

        $this->inventoryService->setLowStockThreshold(15);
        $this->inventoryService->updateStock($this->component, 10, 'sale');

        $this->assertDatabaseHas('inventory_alerts', [
            'component_id' => $this->component->id,
            'type' => 'low_stock',
            'current_stock' => 10,
            'is_resolved' => false
        ]);
    }

    public function test_creates_inventory_alert_on_out_of_stock()
    {
        Log::shouldReceive('info')->once();
        Log::shouldReceive('warning')->once();
        Log::shouldReceive('critical')->once();

        $this->inventoryService->updateStock($this->component, 0, 'sale');

        $this->assertDatabaseHas('inventory_alerts', [
            'component_id' => $this->component->id,
            'type' => 'out_of_stock',
            'current_stock' => 0,
            'is_resolved' => false
        ]);
    }

    public function test_gets_inventory_alerts()
    {
        // Create some alerts
        InventoryAlert::factory()->create([
            'component_id' => $this->component->id,
            'type' => 'low_stock',
            'is_resolved' => false
        ]);

        $alerts = $this->inventoryService->getInventoryAlerts();

        $this->assertNotEmpty($alerts);
        $this->assertInstanceOf(\Illuminate\Support\Collection::class, $alerts);
    }

    public function test_resolves_inventory_alert()
    {
        $alert = InventoryAlert::factory()->create([
            'component_id' => $this->component->id,
            'type' => 'low_stock',
            'is_resolved' => false
        ]);

        $result = $this->inventoryService->resolveAlert($alert);

        $this->assertTrue($result);
        $this->assertTrue($alert->fresh()->is_resolved);
        $this->assertNotNull($alert->fresh()->resolved_at);
    }

    public function test_gets_inventory_analytics()
    {
        // Create some test data
        StockMovement::factory()->create([
            'component_id' => $this->component->id,
            'type' => 'sale',
            'quantity_change' => -5
        ]);

        InventoryAlert::factory()->create([
            'component_id' => $this->component->id,
            'type' => 'low_stock'
        ]);

        $analytics = $this->inventoryService->getInventoryAnalytics(30);

        $this->assertIsArray($analytics);
        $this->assertArrayHasKey('total_movements', $analytics);
        $this->assertArrayHasKey('movements_by_type', $analytics);
        $this->assertArrayHasKey('total_alerts', $analytics);
        $this->assertArrayHasKey('alerts_by_type', $analytics);
        $this->assertArrayHasKey('most_active_components', $analytics);
        $this->assertArrayHasKey('stock_trends', $analytics);
        $this->assertArrayHasKey('alert_resolution_rate', $analytics);
    }

    public function test_sets_custom_thresholds()
    {
        $this->inventoryService->setLowStockThreshold(20);
        $this->inventoryService->setOutOfStockThreshold(5);

        // Use reflection to check private properties
        $reflection = new \ReflectionClass($this->inventoryService);
        
        $lowStockProperty = $reflection->getProperty('lowStockThreshold');
        $lowStockProperty->setAccessible(true);
        $this->assertEquals(20, $lowStockProperty->getValue($this->inventoryService));
        
        $outOfStockProperty = $reflection->getProperty('outOfStockThreshold');
        $outOfStockProperty->setAccessible(true);
        $this->assertEquals(5, $outOfStockProperty->getValue($this->inventoryService));
    }
}