@extends('layouts.app')

@section('content')
<div class="py-12">
    <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg">
            <div class="p-6 bg-white border-b border-gray-200">
                <div class="flex justify-between items-center mb-6">
                    <h1 class="text-2xl font-semibold text-gray-800">Order Details</h1>
                    <a href="{{ route('admin.orders.index') }}" class="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700 transition">
                        Back to Orders
                    </a>
                </div>
                
                <div class="bg-gray-50 p-6 rounded-lg mb-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <h2 class="text-lg font-medium text-gray-900 mb-2">Order Information</h2>
                            <p><span class="font-medium">Order Number:</span> {{ $order->order_number }}</p>
                            <p><span class="font-medium">Date:</span> {{ $order->created_at->format('F j, Y, g:i a') }}</p>
                            <p><span class="font-medium">Status:</span> 
                                <span class="px-2 py-1 rounded text-xs font-medium
                                    @if($order->status == 'pending') bg-yellow-100 text-yellow-800
                                    @elseif($order->status == 'processing') bg-blue-100 text-blue-800
                                    @elseif($order->status == 'completed') bg-green-100 text-green-800
                                    @elseif($order->status == 'canceled') bg-red-100 text-red-800
                                    @elseif($order->status == 'refunded') bg-purple-100 text-purple-800
                                    @endif">
                                    {{ ucfirst($order->status) }}
                                </span>
                            </p>
                            <p><span class="font-medium">Total:</span> ${{ number_format($order->total, 2) }}</p>
                        </div>
                        <div>
                            <h2 class="text-lg font-medium text-gray-900 mb-2">Customer Information</h2>
                            <p><span class="font-medium">Name:</span> {{ $order->user->name }}</p>
                            <p><span class="font-medium">Email:</span> {{ $order->user->email }}</p>
                            <p><span class="font-medium">Phone:</span> {{ $order->phone ?? 'N/A' }}</p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-gray-50 p-6 rounded-lg mb-6">
                    <h2 class="text-lg font-medium text-gray-900 mb-4">Shipping Information</h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <p><span class="font-medium">Address:</span> {{ $order->address }}</p>
                            <p><span class="font-medium">City:</span> {{ $order->city }}</p>
                            <p><span class="font-medium">State:</span> {{ $order->state }}</p>
                            <p><span class="font-medium">Zip Code:</span> {{ $order->zip_code }}</p>
                            <p><span class="font-medium">Country:</span> {{ $order->country }}</p>
                        </div>
                        <div>
                            <p><span class="font-medium">Tracking Number:</span> {{ $order->tracking_number ?? 'Not available' }}</p>
                            <p><span class="font-medium">Carrier:</span> {{ $order->tracking_carrier ?? 'Not available' }}</p>
                            @if($order->tracking_url)
                                <p><span class="font-medium">Tracking Link:</span> 
                                    <a href="{{ $order->tracking_url }}" target="_blank" class="text-blue-600 hover:underline">Track Package</a>
                                </p>
                            @endif
                        </div>
                    </div>
                </div>
                
                <div class="bg-gray-50 p-6 rounded-lg mb-6">
                    <h2 class="text-lg font-medium text-gray-900 mb-4">Order Items</h2>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-100">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Price</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quantity</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Subtotal</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                @foreach($order->orderItems as $item)
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            @if($item->component && $item->component->image)
                                                <div class="flex-shrink-0 h-10 w-10 mr-4">
                                                    <img class="h-10 w-10 rounded-full object-cover" src="{{ asset('storage/' . $item->component->image) }}" alt="{{ $item->component->name }}">
                                                </div>
                                            @endif
                                            <div>
                                                <div class="text-sm font-medium text-gray-900">
                                                    {{ $item->component ? $item->component->name : $item->name }}
                                                </div>
                                                @if($item->component)
                                                <div class="text-sm text-gray-500">
                                                    {{ $item->component->category->name }}
                                                </div>
                                                @endif
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        ${{ number_format($item->price, 2) }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        {{ $item->quantity }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        ${{ number_format($item->price * $item->quantity, 2) }}
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                            <tfoot class="bg-gray-50">
                                <tr>
                                    <td colspan="3" class="px-6 py-4 text-right text-sm font-medium text-gray-900">Subtotal:</td>
                                    <td class="px-6 py-4 text-sm text-gray-900">${{ number_format($order->subtotal, 2) }}</td>
                                </tr>
                                <tr>
                                    <td colspan="3" class="px-6 py-4 text-right text-sm font-medium text-gray-900">Tax:</td>
                                    <td class="px-6 py-4 text-sm text-gray-900">${{ number_format($order->tax, 2) }}</td>
                                </tr>
                                <tr>
                                    <td colspan="3" class="px-6 py-4 text-right text-sm font-medium text-gray-900">Shipping:</td>
                                    <td class="px-6 py-4 text-sm text-gray-900">${{ number_format($order->shipping, 2) }}</td>
                                </tr>
                                <tr>
                                    <td colspan="3" class="px-6 py-4 text-right text-sm font-bold text-gray-900">Total:</td>
                                    <td class="px-6 py-4 text-sm font-bold text-gray-900">${{ number_format($order->total, 2) }}</td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
                
                <div class="bg-gray-50 p-6 rounded-lg mb-6">
                    <h2 class="text-lg font-medium text-gray-900 mb-4">Update Order</h2>
                    <form action="{{ route('admin.orders.update-status', $order) }}" method="POST" class="space-y-4">
                        @csrf
                        @method('PUT')
                        
                        <div>
                            <label for="status" class="block text-sm font-medium text-gray-700">Order Status</label>
                            <select id="status" name="status" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md">
                                <option value="pending" {{ $order->status == 'pending' ? 'selected' : '' }}>Pending</option>
                                <option value="processing" {{ $order->status == 'processing' ? 'selected' : '' }}>Processing</option>
                                <option value="completed" {{ $order->status == 'completed' ? 'selected' : '' }}>Completed</option>
                                <option value="canceled" {{ $order->status == 'canceled' ? 'selected' : '' }}>Canceled</option>
                                <option value="refunded" {{ $order->status == 'refunded' ? 'selected' : '' }}>Refunded</option>
                            </select>
                        </div>
                        
                        <div>
                            <label for="tracking_number" class="block text-sm font-medium text-gray-700">Tracking Number</label>
                            <input type="text" name="tracking_number" id="tracking_number" value="{{ $order->tracking_number }}" class="mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
                        </div>
                        
                        <div>
                            <label for="tracking_carrier" class="block text-sm font-medium text-gray-700">Carrier</label>
                            <input type="text" name="tracking_carrier" id="tracking_carrier" value="{{ $order->tracking_carrier }}" class="mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
                        </div>
                        
                        <div>
                            <label for="tracking_url" class="block text-sm font-medium text-gray-700">Tracking URL</label>
                            <input type="url" name="tracking_url" id="tracking_url" value="{{ $order->tracking_url }}" class="mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
                        </div>
                        
                        <div>
                            <label for="notes" class="block text-sm font-medium text-gray-700">Order Notes</label>
                            <textarea id="notes" name="notes" rows="3" class="mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">{{ $order->notes }}</textarea>
                        </div>
                        
                        <div>
                            <button type="submit" class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                Update Order
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection