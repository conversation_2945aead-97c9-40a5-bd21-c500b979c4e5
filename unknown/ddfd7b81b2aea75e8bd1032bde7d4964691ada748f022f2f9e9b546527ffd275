import defaultTheme from 'tailwindcss/defaultTheme';
import forms from '@tailwindcss/forms';
import typography from '@tailwindcss/typography';

/** @type {import('tailwindcss').Config} */
export default {
    content: [
        './vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php',
        './vendor/laravel/jetstream/**/*.blade.php',
        './storage/framework/views/*.php',
        './resources/views/**/*.blade.php',
    ],

    darkMode: 'class',

    theme: {
        extend: {
            fontFamily: {
                sans: ['Figtree', ...defaultTheme.fontFamily.sans],
                tech: ['Orbitron', 'monospace'],
                content: ['Inter', 'sans-serif'],
            },
            colors: {
                // Nexus PC Brand Theme - Cyberpunk/Tech Aesthetic
                nexus: {
                    // Primary Brand Colors
                    primary: {
                        50: '#eff6ff',
                        100: '#dbeafe',
                        200: '#bfdbfe',
                        300: '#93c5fd', // Light blue accent
                        400: '#60a5fa', // Main blue accent
                        500: '#3b82f6', // Primary blue
                        600: '#2563eb', // Primary blue dark
                        700: '#1d4ed8', // Primary blue darker
                        800: '#1e40af',
                        900: '#1e3a8a', // Dark blue borders
                        950: '#172554',
                    },
                    
                    // Secondary Cyan Colors
                    secondary: {
                        50: '#ecfeff',
                        100: '#cffafe',
                        200: '#a5f3fc',
                        300: '#67e8f9',
                        400: '#22d3ee', // Cyan accent
                        500: '#06b6d4', // Main cyan
                        600: '#0891b2', // Cyan dark
                        700: '#0e7490',
                        800: '#155e75',
                        900: '#164e63', // Dark cyan borders
                        950: '#083344',
                    },
                    
                    // Accent Purple Colors
                    accent: {
                        50: '#faf5ff',
                        100: '#f3e8ff',
                        200: '#e9d5ff',
                        300: '#d8b4fe',
                        400: '#c084fc', // Purple accent
                        500: '#a855f7', // Main purple
                        600: '#9333ea', // Purple dark
                        700: '#7e22ce', // Purple darker
                        800: '#6b21a8',
                        900: '#4c1d95', // Dark purple borders
                        950: '#2e1065',
                    },
                    
                    // Success/Green Colors
                    success: {
                        50: '#f0fdf4',
                        100: '#dcfce7',
                        200: '#bbf7d0',
                        300: '#86efac',
                        400: '#4ade80', // Green accent
                        500: '#22c55e', // Main green
                        600: '#16a34a',
                        700: '#15803d',
                        800: '#166534',
                        900: '#14532d',
                        950: '#052e16',
                    },
                    
                    // Warning/Yellow Colors
                    warning: {
                        50: '#fefce8',
                        100: '#fef9c3',
                        200: '#fef08a',
                        300: '#fde047',
                        400: '#facc15', // Yellow accent
                        500: '#eab308', // Main yellow
                        600: '#ca8a04',
                        700: '#a16207',
                        800: '#854d0e',
                        900: '#713f12',
                        950: '#422006',
                    },
                    
                    // Error/Red Colors
                    error: {
                        50: '#fef2f2',
                        100: '#fee2e2',
                        200: '#fecaca',
                        300: '#fca5a5',
                        400: '#f87171',
                        500: '#ef4444', // Main red
                        600: '#dc2626',
                        700: '#b91c1c',
                        800: '#991b1b',
                        900: '#7f1d1d',
                        950: '#450a0a',
                    },
                    
                    // Dark Theme Colors
                    dark: {
                        50: '#f8fafc',
                        100: '#f1f5f9',
                        200: '#e2e8f0',
                        300: '#cbd5e1',
                        400: '#94a3b8',
                        500: '#64748b',
                        600: '#475569',
                        700: '#334155',
                        800: '#1e293b', // Card backgrounds
                        900: '#0f172a', // Main dark background
                        950: '#0a0f1c', // Darkest background
                    },
                    
                    // Gray Scale for Text
                    gray: {
                        50: '#f9fafb',
                        100: '#f3f4f6',
                        200: '#e5e7eb',
                        300: '#d1d5db', // Light text
                        400: '#9ca3af', // Medium text
                        500: '#6b7280',
                        600: '#4b5563',
                        700: '#374151',
                        800: '#1f2937',
                        900: '#111827',
                        950: '#030712',
                    },
                    
                    // Special Effect Colors with Opacity
                    glow: {
                        blue: 'rgba(59, 130, 246, 0.3)',
                        cyan: 'rgba(6, 182, 212, 0.3)',
                        purple: 'rgba(168, 85, 247, 0.3)',
                        green: 'rgba(34, 197, 94, 0.3)',
                    },
                    
                    // Border Colors with Opacity
                    border: {
                        blue: {
                            light: 'rgba(59, 130, 246, 0.2)',
                            medium: 'rgba(59, 130, 246, 0.3)',
                            strong: 'rgba(59, 130, 246, 0.5)',
                        },
                        cyan: {
                            light: 'rgba(6, 182, 212, 0.2)',
                            medium: 'rgba(6, 182, 212, 0.3)',
                            strong: 'rgba(6, 182, 212, 0.5)',
                        },
                        purple: {
                            light: 'rgba(168, 85, 247, 0.2)',
                            medium: 'rgba(168, 85, 247, 0.3)',
                            strong: 'rgba(168, 85, 247, 0.5)',
                        },
                        green: {
                            light: 'rgba(34, 197, 94, 0.2)',
                            medium: 'rgba(34, 197, 94, 0.3)',
                            strong: 'rgba(34, 197, 94, 0.5)',
                        },
                    },
                    
                    // Background Colors with Opacity
                    bg: {
                        blue: {
                            light: 'rgba(59, 130, 246, 0.1)',
                            medium: 'rgba(59, 130, 246, 0.2)',
                        },
                        cyan: {
                            light: 'rgba(6, 182, 212, 0.1)',
                            medium: 'rgba(6, 182, 212, 0.2)',
                        },
                        purple: {
                            light: 'rgba(168, 85, 247, 0.1)',
                            medium: 'rgba(168, 85, 247, 0.2)',
                        },
                        green: {
                            light: 'rgba(34, 197, 94, 0.1)',
                            medium: 'rgba(34, 197, 94, 0.2)',
                        },
                    },
                },
            },
            
            // Custom Box Shadows for Glow Effects
            boxShadow: {
                'glow-blue': '0 0 20px rgba(59, 130, 246, 0.3)',
                'glow-cyan': '0 0 20px rgba(6, 182, 212, 0.3)',
                'glow-purple': '0 0 20px rgba(168, 85, 247, 0.3)',
                'glow-green': '0 0 20px rgba(34, 197, 94, 0.3)',
                'glow-blue-lg': '0 0 30px rgba(59, 130, 246, 0.4)',
                'glow-cyan-lg': '0 0 30px rgba(6, 182, 212, 0.4)',
                'glow-purple-lg': '0 0 30px rgba(168, 85, 247, 0.4)',
            },
            
            // Custom Animations
            animation: {
                'float': 'float 6s ease-in-out infinite',
                'pulse-ring': 'pulse-ring 2s infinite',
                'grid-move': 'grid-move 20s linear infinite',
                'glow-pulse': 'glow-pulse 2s ease-in-out infinite alternate',
            },
            
            // Custom Keyframes
            keyframes: {
                float: {
                    '0%, 100%': { transform: 'translateY(0px)' },
                    '50%': { transform: 'translateY(-20px)' },
                },
                'pulse-ring': {
                    '0%': { transform: 'scale(0.8)', opacity: '1' },
                    '100%': { transform: 'scale(1.2)', opacity: '0' },
                },
                'grid-move': {
                    '0%': { backgroundPosition: '0 0' },
                    '100%': { backgroundPosition: '20px 20px' },
                },
                'glow-pulse': {
                    '0%': { boxShadow: '0 0 20px rgba(59, 130, 246, 0.3)' },
                    '100%': { boxShadow: '0 0 30px rgba(59, 130, 246, 0.6)' },
                },
            },
            
            // Custom Gradients
            backgroundImage: {
                'gradient-radial': 'radial-gradient(var(--tw-gradient-stops))',
                'gradient-conic': 'conic-gradient(from 180deg at 50% 50%, var(--tw-gradient-stops))',
                'cyber-grid': 'linear-gradient(rgba(59, 130, 246, 0.1) 1px, transparent 1px), linear-gradient(90deg, rgba(59, 130, 246, 0.1) 1px, transparent 1px)',
            },
        },
    },

    plugins: [forms, typography],
};
