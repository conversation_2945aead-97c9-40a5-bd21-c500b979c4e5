<?php

namespace Tests\Performance;

use App\Models\Cart;
use App\Models\CartItem;
use App\Models\Component;
use App\Models\ComponentCategory;
use App\Models\Order;
use App\Models\User;
use App\Services\CartService;
use App\Services\CheckoutService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\DB;
use Tests\TestCase;

class EcommercePerformanceTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;
    protected array $components;
    protected CartService $cartService;
    protected CheckoutService $checkoutService;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->user = User::factory()->create();
        $this->cartService = app(CartService::class);
        $this->checkoutService = app(CheckoutService::class);
        
        // Create large dataset for performance testing
        $this->createLargeEcommerceDataset();
    }

    /** @test */
    public function cart_operations_perform_efficiently_with_many_items()
    {
        $startTime = microtime(true);
        $startMemory = memory_get_usage();
        
        // Add 100 different components to cart
        for ($i = 0; $i < 100; $i++) {
            $component = $this->components[array_rand($this->components)];
            $quantity = rand(1, 5);
            
            $itemStartTime = microtime(true);
            
            $this->cartService->addToCart($component, $quantity, $this->user);
            
            $itemEndTime = microtime(true);
            $itemTime = ($itemEndTime - $itemStartTime) * 1000;
            
            // Each cart addition should complete under 50ms
            $this->assertLessThan(50, $itemTime, 
                "Adding item {$i} to cart took {$itemTime}ms, which exceeds 50ms limit");
        }
        
        $endTime = microtime(true);
        $endMemory = memory_get_usage();
        
        $totalTime = ($endTime - $startTime) * 1000;
        $memoryUsage = ($endMemory - $startMemory) / 1024 / 1024;
        
        // Total time for 100 cart additions should be under 3 seconds
        $this->assertLessThan(3000, $totalTime, 
            "Adding 100 items to cart took {$totalTime}ms, which exceeds 3000ms limit");
        
        // Memory usage should be reasonable
        $this->assertLessThan(50, $memoryUsage, 
            "Memory usage was {$memoryUsage}MB, which exceeds 50MB limit");
    }

    /** @test */
    public function cart_total_calculation_performs_efficiently_with_large_cart()
    {
        // Create a cart with 200 items
        $cart = Cart::factory()->create(['user_id' => $this->user->id]);
        
        for ($i = 0; $i < 200; $i++) {
            CartItem::factory()->create([
                'cart_id' => $cart->id,
                'component_id' => $this->components[array_rand($this->components)]->id,
                'quantity' => rand(1, 10),
                'price' => rand(50, 1000) + (rand(0, 99) / 100)
            ]);
        }
        
        $startTime = microtime(true);
        
        // Calculate total 50 times to test performance
        for ($i = 0; $i < 50; $i++) {
            $total = $this->cartService->getCartTotal($this->user);
            $this->assertGreaterThan(0, $total);
        }
        
        $endTime = microtime(true);
        $totalTime = ($endTime - $startTime) * 1000;
        $averageTime = $totalTime / 50;
        
        // Average calculation time should be under 20ms
        $this->assertLessThan(20, $averageTime, 
            "Average cart total calculation took {$averageTime}ms, which exceeds 20ms limit");
    }

    /** @test */
    public function checkout_process_handles_concurrent_orders_efficiently()
    {
        $users = User::factory()->count(20)->create();
        $startTime = microtime(true);
        
        // Simulate 20 concurrent checkout processes
        $checkoutTimes = [];
        
        foreach ($users as $user) {
            $cart = Cart::factory()->create(['user_id' => $user->id]);
            
            // Add 5 random items to each cart
            for ($i = 0; $i < 5; $i++) {
                CartItem::factory()->create([
                    'cart_id' => $cart->id,
                    'component_id' => $this->components[array_rand($this->components)]->id,
                    'quantity' => rand(1, 3),
                    'price' => rand(50, 500)
                ]);
            }
            
            $checkoutStartTime = microtime(true);
            
            // Process checkout
            $order = $this->checkoutService->processCheckout($cart, [
                'name' => $user->name,
                'email' => $user->email,
                'phone' => '555-1234',
                'address' => '123 Main St',
                'city' => 'Anytown',
                'state' => 'CA',
                'zipcode' => '12345',
                'country' => 'US'
            ], [
                'payment_method' => 'credit_card',
                'card_number' => '****************',
                'card_expiry' => '12/25',
                'card_cvv' => '123',
                'card_name' => $user->name
            ]);
            
            $checkoutEndTime = microtime(true);
            $checkoutTime = ($checkoutEndTime - $checkoutStartTime) * 1000;
            $checkoutTimes[] = $checkoutTime;
            
            $this->assertNotNull($order);
            
            // Individual checkout should complete under 1 second
            $this->assertLessThan(1000, $checkoutTime, 
                "Checkout for user {$user->id} took {$checkoutTime}ms, which exceeds 1000ms limit");
        }
        
        $endTime = microtime(true);
        $totalTime = ($endTime - $startTime) * 1000;
        $averageCheckoutTime = array_sum($checkoutTimes) / count($checkoutTimes);
        
        // All 20 checkouts should complete under 10 seconds
        $this->assertLessThan(10000, $totalTime, 
            "20 concurrent checkouts took {$totalTime}ms, which exceeds 10000ms limit");
        
        // Average checkout time should be under 500ms
        $this->assertLessThan(500, $averageCheckoutTime, 
            "Average checkout time was {$averageCheckoutTime}ms, which exceeds 500ms limit");
    }

    /** @test */
    public function product_search_and_filtering_scales_with_large_catalog()
    {
        $searchQueries = [
            ['q' => 'Intel'],
            ['q' => 'gaming'],
            ['category' => 'cpu'],
            ['brand' => 'AMD'],
            ['price_min' => 100, 'price_max' => 500],
            ['q' => 'Intel', 'category' => 'cpu'],
            ['brand' => 'NVIDIA', 'price_min' => 300]
        ];
        
        foreach ($searchQueries as $query) {
            $startTime = microtime(true);
            
            $response = $this->actingAs($this->user)
                          ->get(route('shop.search', $query));
            
            $endTime = microtime(true);
            $searchTime = ($endTime - $startTime) * 1000;
            
            $response->assertStatus(200);
            
            // Search should complete under 300ms even with large catalog
            $this->assertLessThan(300, $searchTime, 
                "Search with query " . json_encode($query) . " took {$searchTime}ms");
        }
    }

    /** @test */
    public function order_history_loading_performs_efficiently_with_many_orders()
    {
        // Create 500 orders for the user
        $orders = Order::factory()->count(500)->create([
            'user_id' => $this->user->id,
            'created_at' => now()->subDays(rand(1, 365))
        ]);
        
        $startTime = microtime(true);
        
        $response = $this->actingAs($this->user)
                      ->get(route('account.orders'));
        
        $endTime = microtime(true);
        $loadTime = ($endTime - $startTime) * 1000;
        
        $response->assertStatus(200);
        
        // Loading order history should complete under 500ms
        $this->assertLessThan(500, $loadTime, 
            "Loading order history took {$loadTime}ms, which exceeds 500ms limit");
        
        // Test pagination performance
        $startTime = microtime(true);
        
        $response = $this->actingAs($this->user)
                      ->get(route('account.orders', ['page' => 10]));
        
        $endTime = microtime(true);
        $paginationTime = ($endTime - $startTime) * 1000;
        
        $response->assertStatus(200);
        
        // Pagination should also be fast
        $this->assertLessThan(200, $paginationTime, 
            "Order history pagination took {$paginationTime}ms, which exceeds 200ms limit");
    }

    /** @test */
    public function inventory_updates_handle_concurrent_stock_changes()
    {
        $component = $this->components[0];
        $component->update(['stock' => 100]);
        
        $startTime = microtime(true);
        
        // Simulate 50 concurrent stock updates
        $processes = [];
        for ($i = 0; $i < 50; $i++) {
            $processes[] = $this->simulateStockUpdate($component, 1);
        }
        
        // Wait for all processes
        foreach ($processes as $process) {
            $process->wait();
        }
        
        $endTime = microtime(true);
        $totalTime = ($endTime - $startTime) * 1000;
        
        // Concurrent stock updates should complete under 2 seconds
        $this->assertLessThan(2000, $totalTime, 
            "50 concurrent stock updates took {$totalTime}ms, which exceeds 2000ms limit");
        
        // Verify final stock is correct (100 - 50 = 50)
        $this->assertEquals(50, $component->fresh()->stock);
    }

    /** @test */
    public function database_queries_are_optimized_for_shop_operations()
    {
        DB::enableQueryLog();
        
        // Load shop page with products
        $response = $this->actingAs($this->user)
                      ->get(route('shop.index'));
        
        $queries = DB::getQueryLog();
        $queryCount = count($queries);
        
        $response->assertStatus(200);
        
        // Shop page should not exceed 10 queries
        $this->assertLessThan(10, $queryCount, 
            "Shop page executed {$queryCount} queries, which exceeds 10 query limit");
        
        DB::flushQueryLog();
        
        // Load product details page
        $component = $this->components[0];
        $response = $this->actingAs($this->user)
                      ->get(route('shop.component', $component->id));
        
        $queries = DB::getQueryLog();
        $queryCount = count($queries);
        
        $response->assertStatus(200);
        
        // Product details should not exceed 5 queries
        $this->assertLessThan(5, $queryCount, 
            "Product details page executed {$queryCount} queries, which exceeds 5 query limit");
    }

    /** @test */
    public function cart_session_handling_performs_efficiently()
    {
        // Test guest cart performance
        $startTime = microtime(true);
        
        for ($i = 0; $i < 50; $i++) {
            $component = $this->components[array_rand($this->components)];
            
            $response = $this->post(route('cart.add'), [
                'component_id' => $component->id,
                'quantity' => 1
            ]);
            
            $response->assertStatus(200);
        }
        
        $endTime = microtime(true);
        $totalTime = ($endTime - $startTime) * 1000;
        
        // Adding 50 items to guest cart should complete under 2 seconds
        $this->assertLessThan(2000, $totalTime, 
            "Adding 50 items to guest cart took {$totalTime}ms, which exceeds 2000ms limit");
        
        // Test cart merging performance when user logs in
        $startTime = microtime(true);
        
        $response = $this->post(route('login'), [
            'email' => $this->user->email,
            'password' => 'password'
        ]);
        
        $endTime = microtime(true);
        $mergeTime = ($endTime - $startTime) * 1000;
        
        // Cart merging should complete under 500ms
        $this->assertLessThan(500, $mergeTime, 
            "Cart merging took {$mergeTime}ms, which exceeds 500ms limit");
    }

    /** @test */
    public function payment_processing_handles_load_efficiently()
    {
        $users = User::factory()->count(10)->create();
        $paymentTimes = [];
        
        foreach ($users as $user) {
            $cart = Cart::factory()->create(['user_id' => $user->id]);
            
            CartItem::factory()->create([
                'cart_id' => $cart->id,
                'component_id' => $this->components[array_rand($this->components)]->id,
                'quantity' => 1,
                'price' => 99.99
            ]);
            
            $startTime = microtime(true);
            
            // Simulate payment processing
            $response = $this->actingAs($user)
                          ->post(route('checkout.payment'), [
                              'payment_method' => 'credit_card',
                              'card_number' => '****************',
                              'card_expiry' => '12/25',
                              'card_cvv' => '123',
                              'card_name' => $user->name
                          ]);
            
            $endTime = microtime(true);
            $paymentTime = ($endTime - $startTime) * 1000;
            $paymentTimes[] = $paymentTime;
            
            // Individual payment should complete under 2 seconds
            $this->assertLessThan(2000, $paymentTime, 
                "Payment processing took {$paymentTime}ms, which exceeds 2000ms limit");
        }
        
        $averagePaymentTime = array_sum($paymentTimes) / count($paymentTimes);
        
        // Average payment time should be under 1 second
        $this->assertLessThan(1000, $averagePaymentTime, 
            "Average payment time was {$averagePaymentTime}ms, which exceeds 1000ms limit");
    }

    /** @test */
    public function memory_usage_remains_stable_during_extended_shopping_session()
    {
        $initialMemory = memory_get_usage();
        $peakMemory = $initialMemory;
        
        // Simulate extended shopping session
        for ($i = 0; $i < 1000; $i++) {
            // Browse products
            $this->get(route('shop.index', ['page' => rand(1, 10)]));
            
            // View product details
            $component = $this->components[array_rand($this->components)];
            $this->get(route('shop.component', $component->id));
            
            // Add to cart occasionally
            if ($i % 10 === 0) {
                $this->post(route('cart.add'), [
                    'component_id' => $component->id,
                    'quantity' => 1
                ]);
            }
            
            $currentMemory = memory_get_usage();
            $peakMemory = max($peakMemory, $currentMemory);
            
            // Force garbage collection every 100 iterations
            if ($i % 100 === 0) {
                gc_collect_cycles();
            }
        }
        
        $finalMemory = memory_get_usage();
        $memoryIncrease = ($finalMemory - $initialMemory) / 1024 / 1024;
        $peakIncrease = ($peakMemory - $initialMemory) / 1024 / 1024;
        
        // Memory increase should be minimal
        $this->assertLessThan(100, $memoryIncrease, 
            "Final memory increase was {$memoryIncrease}MB, which exceeds 100MB limit");
        
        // Peak memory should be reasonable
        $this->assertLessThan(300, $peakIncrease, 
            "Peak memory increase was {$peakIncrease}MB, which exceeds 300MB limit");
    }

    protected function createLargeEcommerceDataset(): void
    {
        // Create categories
        $categories = ComponentCategory::factory()->count(10)->create();
        
        // Create 5000 components across all categories
        $this->components = Component::factory()->count(5000)->create([
            'category_id' => function() use ($categories) {
                return $categories->random()->id;
            },
            'is_active' => true,
            'stock' => rand(0, 100),
            'price' => rand(50, 2000) + (rand(0, 99) / 100),
            'is_featured' => rand(0, 1) === 1,
        ]);
    }

    protected function simulateStockUpdate($component, $quantity)
    {
        return new class($component, $quantity) {
            private $component;
            private $quantity;
            
            public function __construct($component, $quantity) {
                $this->component = $component;
                $this->quantity = $quantity;
            }
            
            public function wait() {
                // Simulate stock update
                $this->component->decrement('stock', $this->quantity);
                usleep(rand(10000, 50000)); // 10-50ms
                return true;
            }
        };
    }
}