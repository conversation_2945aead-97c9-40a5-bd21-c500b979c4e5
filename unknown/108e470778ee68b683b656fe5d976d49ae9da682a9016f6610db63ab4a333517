<?php

namespace Tests\Unit\Jobs;

use App\Jobs\SendOrderStatusUpdate;
use App\Mail\OrderStatusUpdate;
use App\Models\EmailLog;
use App\Models\Order;
use App\Models\User;
use App\Services\EmailTrackingService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Queue;
use Tests\TestCase;

class SendOrderStatusUpdateTest extends TestCase
{
    use RefreshDatabase;

    public function test_job_sends_order_status_update_email()
    {
        Mail::fake();
        
        $user = User::factory()->create([
            'notification_settings' => ['order_status_updates' => true],
        ]);
        $order = Order::factory()->create([
            'user_id' => $user->id,
            'status' => Order::STATUS_PROCESSING,
        ]);

        $job = new SendOrderStatusUpdate($order, Order::STATUS_PENDING, 'Your order is being processed');
        $job->handle(new EmailTrackingService());

        Mail::assertQueued(OrderStatusUpdate::class, function ($mail) use ($order) {
            return $mail->order->id === $order->id &&
                   $mail->previousStatus === Order::STATUS_PENDING &&
                   $mail->updateMessage === 'Your order is being processed';
        });
    }

    public function test_job_skips_email_when_user_disabled_notifications()
    {
        Mail::fake();
        
        $user = User::factory()->create([
            'notification_settings' => ['order_status_updates' => false],
        ]);
        $order = Order::factory()->create([
            'user_id' => $user->id,
            'status' => Order::STATUS_PROCESSING,
        ]);

        $job = new SendOrderStatusUpdate($order, Order::STATUS_PENDING);
        $job->handle(new EmailTrackingService());

        Mail::assertNotQueued(OrderStatusUpdate::class);
    }

    public function test_job_skips_irrelevant_status_transitions()
    {
        Mail::fake();
        
        $user = User::factory()->create([
            'notification_settings' => ['order_status_updates' => true],
        ]);
        $order = Order::factory()->create([
            'user_id' => $user->id,
            'status' => Order::STATUS_PENDING,
        ]);

        $job = new SendOrderStatusUpdate($order, Order::STATUS_PENDING);
        $job->handle(new EmailTrackingService());

        Mail::assertNotQueued(OrderStatusUpdate::class);
    }

    public function test_job_creates_email_log_entry()
    {
        Mail::fake();
        
        $user = User::factory()->create([
            'notification_settings' => ['order_status_updates' => true],
        ]);
        $order = Order::factory()->create([
            'user_id' => $user->id,
            'order_number' => 'PCB123456789',
            'status' => Order::STATUS_PROCESSING,
        ]);

        $job = new SendOrderStatusUpdate($order, Order::STATUS_PENDING);
        $job->handle(new EmailTrackingService());

        $this->assertDatabaseHas('email_logs', [
            'type' => 'order_status_update',
            'recipient' => $user->email,
            'subject' => 'Order Processing - PCB123456789',
            'status' => 'sent',
            'related_id' => $order->id,
            'related_type' => Order::class,
        ]);
    }

    public function test_job_handles_email_failure()
    {
        Mail::shouldReceive('to')->andThrow(new \Exception('SMTP Error'));
        
        $user = User::factory()->create([
            'notification_settings' => ['order_status_updates' => true],
        ]);
        $order = Order::factory()->create([
            'user_id' => $user->id,
            'status' => Order::STATUS_PROCESSING,
        ]);

        $job = new SendOrderStatusUpdate($order, Order::STATUS_PENDING);
        
        $this->expectException(\Exception::class);
        $job->handle(new EmailTrackingService());

        $this->assertDatabaseHas('email_logs', [
            'type' => 'order_status_update',
            'status' => 'failed',
            'error_message' => 'SMTP Error',
        ]);
    }

    public function test_job_is_queueable()
    {
        Queue::fake();
        
        $user = User::factory()->create();
        $order = Order::factory()->create([
            'user_id' => $user->id,
            'status' => Order::STATUS_PROCESSING,
        ]);

        SendOrderStatusUpdate::dispatch($order, Order::STATUS_PENDING, 'Test message');

        Queue::assertPushed(SendOrderStatusUpdate::class, function ($job) use ($order) {
            return $job->order->id === $order->id &&
                   $job->previousStatus === Order::STATUS_PENDING &&
                   $job->message === 'Test message';
        });
    }

    public function test_job_has_correct_retry_configuration()
    {
        $user = User::factory()->create();
        $order = Order::factory()->create(['user_id' => $user->id]);

        $job = new SendOrderStatusUpdate($order, Order::STATUS_PENDING);

        $this->assertEquals(3, $job->tries);
        $this->assertEquals(60, $job->timeout);
        $this->assertEquals([30, 120, 300], $job->backoff());
    }

    public function test_job_handles_null_message()
    {
        Mail::fake();
        
        $user = User::factory()->create([
            'notification_settings' => ['order_status_updates' => true],
        ]);
        $order = Order::factory()->create([
            'user_id' => $user->id,
            'status' => Order::STATUS_PROCESSING,
        ]);

        $job = new SendOrderStatusUpdate($order, Order::STATUS_PENDING);
        $job->handle(new EmailTrackingService());

        Mail::assertQueued(OrderStatusUpdate::class, function ($mail) {
            return $mail->updateMessage === null;
        });
    }

    public function test_job_includes_tracking_info_when_available()
    {
        Mail::fake();
        
        $user = User::factory()->create([
            'notification_settings' => ['order_status_updates' => true],
        ]);
        $order = Order::factory()->create([
            'user_id' => $user->id,
            'status' => Order::STATUS_COMPLETED,
            'tracking_number' => 'TRK123456789',
            'tracking_carrier' => 'UPS',
            'tracking_url' => 'https://ups.com/track/TRK123456789',
        ]);

        $job = new SendOrderStatusUpdate($order, Order::STATUS_PROCESSING);
        $job->handle(new EmailTrackingService());

        Mail::assertQueued(OrderStatusUpdate::class, function ($mail) use ($order) {
            return $mail->order->tracking_number === 'TRK123456789';
        });
    }
}