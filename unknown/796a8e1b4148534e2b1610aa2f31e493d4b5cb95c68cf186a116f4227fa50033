<div>
    <h2 class="text-xl font-bold mb-4">Saved Builds</h2>
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
            <h3 class="font-semibold mb-2">Your Builds</h3>
            <ul>
                @foreach ($builds as $build)
                    <li class="mb-2 border-b pb-2 flex justify-between items-center">
                        <div>
                            <span class="font-semibold">{{ $build->name }}</span> <br>
                            <span class="text-xs text-gray-600">{{ $build->created_at->format('Y-m-d') }}</span>
                            <span class="ml-2 text-xs px-2 py-1 rounded {{ $build->is_public ? 'bg-green-200 text-green-800' : 'bg-gray-200' }}">
                                {{ $build->is_public ? 'Public' : 'Private' }}
                            </span>
                        </div>
                        <div>
                            <button wire:click="togglePrivacy({{ $build->id }})" class="ml-2 text-indigo-600 underline">Make {{ $build->is_public ? 'Private' : 'Public' }}</button>
                            <button wire:click="selectBuild({{ $build->id }})" class="ml-2 text-blue-600 underline">View</button>
                            <button wire:click="generateShareToken({{ $build->id }})" class="ml-2 text-green-600 underline">Share</button>
                            @if ($build->share_token)
                                <button wire:click="revokeShare({{ $build->id }})" class="ml-2 text-red-600 underline">Revoke Link</button>
                            @endif
                        </div>
                    </li>
                @endforeach
            </ul>
        </div>
        <div>
            @if ($selectedBuild)
                <div class="border rounded p-4 bg-gray-50">
                    <h4 class="font-semibold mb-2">{{ $selectedBuild->name }}</h4>
                    <div>Description: {{ $selectedBuild->description }}</div>
                    <div>Total Price: ${{ number_format($selectedBuild->total_price, 2) }}</div>
                    <div class="mt-2">
                        <h5 class="font-semibold">Components:</h5>
                        <ul class="list-disc ml-6">
                            @foreach ($selectedBuild->components as $component)
                                <li>{{ $component->name }} ({{ $component->pivot->quantity }})</li>
                            @endforeach
                        </ul>
                    </div>
                    <div class="mt-2">
                        <span class="text-xs px-2 py-1 rounded bg-gray-200">{{ ucfirst($selectedBuild->compatibility_status) }}</span>
                    </div>
                    <button wire:click="clearSelectedBuild" class="mt-4 text-blue-600 underline">Back to Builds</button>
                </div>
            @endif
        </div>
    </div>
    @if ($showShareModal && $shareToken)
        <div class="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50">
            <div class="bg-white p-6 rounded shadow-lg">
                <h4 class="font-semibold mb-2">Shareable Link</h4>
                <input type="text" readonly class="w-full border p-2 mb-2" value="{{ url('/build/share/' . $shareToken) }}" />
                <form wire:submit.prevent="generateShareToken({{ $selectedBuild->id }})" class="mb-2">
                    <label class="block mb-1">Send to email:</label>
                    <input type="email" wire:model.defer="recipientEmail" class="w-full border p-2 mb-2" placeholder="<EMAIL>" />
                    <button type="submit" class="bg-green-600 text-white px-4 py-2 rounded">Send Link</button>
                </form>
                <button wire:click="closeShareModal" class="bg-blue-600 text-white px-4 py-2 rounded">Close</button>
            </div>
        </div>
    @endif
</div> 