<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('component_compatibilities', function (Blueprint $table) {
            $table->id();
            $table->foreignId('component_id')->nullable()->constrained()->onDelete('cascade');
            $table->foreignId('compatible_component_id')->nullable()->constrained('components')->onDelete('cascade');
            $table->foreignId('category_id')->nullable()->constrained('component_categories')->onDelete('cascade');
            $table->foreignId('compatible_category_id')->nullable()->constrained('component_categories')->onDelete('cascade');
            $table->string('rule_type');
            $table->json('rule_value')->nullable();
            $table->string('error_message')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('component_compatibilities');
    }
};