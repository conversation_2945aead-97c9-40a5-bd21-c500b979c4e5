# Color Extraction from welcome.blade.php

## Hardcoded Colors Found

### Blue Family
- `bg-blue-500`
- `bg-blue-600`
- `text-blue-400`
- `from-blue-400`
- `from-blue-500`
- `from-blue-600`
- `to-blue-500`
- `to-blue-600`
- `border-blue-400`
- `border-blue-500`
- `border-blue-900/30`
- `border-blue-900/50`
- `hover:border-blue-500/30`
- `hover:border-blue-500/50`
- `hover:shadow-blue-500/20`
- `bg-blue-500/10`
- `bg-blue-500/20`
- `group-hover:bg-blue-500/30`
- `rgba(59, 130, 246, 0.1)` (grid lines)
- `rgba(59, 130, 246, 0.2)` (stat card border)
- `rgba(59, 130, 246, 0.3)` (tooltip border, box shadow)
- `rgba(59, 130, 246, 0.5)` (text shadow)
- `rgba(59, 130, 246, 0.6)` (icon shadow)

### Cyan Family
- `to-cyan-500`
- `to-cyan-600`
- `from-cyan-400`
- `from-cyan-500`
- `via-cyan-400`
- `text-cyan-400`
- `border-cyan-900/30`
- `border-cyan-900/50`
- `hover:border-cyan-500/30`
- `hover:border-cyan-500/50`
- `hover:shadow-cyan-500/20`
- `bg-cyan-500/20`
- `group-hover:bg-cyan-500/30`

### Purple Family
- `bg-purple-500`
- `bg-purple-600`
- `from-purple-500`
- `to-purple-500`
- `text-purple-400`
- `border-purple-900/30`
- `border-purple-900/50`
- `hover:border-purple-500/30`
- `hover:border-purple-500/50`
- `hover:shadow-purple-500/20`
- `bg-purple-500/20`
- `group-hover:bg-purple-500/30`

### Green Family
- `from-green-500`
- `to-green-500`
- `bg-green-600`
- `text-green-400`
- `border-green-900/30`
- `hover:border-green-500/50`
- `hover:shadow-green-500/20`
- `bg-green-500/20`
- `group-hover:bg-green-500/30`

### Pink Family
- `to-pink-500`

### Orange Family
- `from-orange-500`

### Red Family
- `to-red-500`

### Teal Family
- `to-teal-500`

## Gradients
- `bg-gradient-to-br from-blue-400 to-cyan-600`
- `bg-gradient-to-br from-green-500 to-teal-500`
- `bg-gradient-to-br from-purple-500 to-pink-500`
- `bg-gradient-to-br from-orange-500 to-red-500`
- `from-blue-500 to-cyan-500`
- `from-cyan-500 to-blue-500`
- `from-purple-500 to-blue-500`
- `from-blue-600 to-cyan-600`
- `from-blue-400 via-cyan-400 to-blue-600`