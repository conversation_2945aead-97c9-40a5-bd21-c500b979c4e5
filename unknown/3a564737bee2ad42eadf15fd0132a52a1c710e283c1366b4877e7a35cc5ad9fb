@props(['comment'])

<div class="mb-6 bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden transition-all duration-300 hover:shadow-xl"
    id="comment-{{ $comment->id }}">
    <div class="p-6">
        <div class="flex justify-between items-start">
            <div class="flex items-center gap-3">
                <div class="relative">
                    <div
                        class="bg-gradient-to-br from-primary-500 to-primary-600 text-white rounded-full w-10 h-10 flex items-center justify-center shadow-md">
                        <span class="text-sm font-medium">{{ substr($comment->author->name, 0, 2) }}</span>
                    </div>
                </div>
                <div>
                    <h4 class="font-semibold text-gray-900 dark:text-white">{{ $comment->author->name }}</h4>
                    <span class="text-sm text-gray-500 dark:text-gray-400">
                        {{ $comment->created_at->diffForHumans() }}
                    </span>
                </div>
            </div>

            @auth
                @if(Auth::id() === $comment->user_id)
                    <div class="relative group">
                        <button
                            class="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                                class="w-5 h-5 stroke-current text-gray-500 dark:text-gray-400">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M5 12h.01M12 12h.01M19 12h.01M6 12a1 1 0 11-2 0 1 1 0 012 0zm7 0a1 1 0 11-2 0 1 1 0 012 0zm7 0a1 1 0 11-2 0 1 1 0 012 0z">
                                </path>
                            </svg>
                        </button>
                        <ul
                            class="hidden group-hover:block absolute right-0 mt-1 py-2 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-xl z-10 border border-gray-200 dark:border-gray-700">
                            <li>
                                <button onclick="toggleEditForm({{ $comment->id }})"
                                    class="w-full text-left px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200">
                                    Edit
                                </button>
                            </li>
                            <li>
                                <form action="{{ route('comments.delete', $comment) }}" method="POST" class="w-full">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit"
                                        class="w-full text-left px-4 py-2 text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors duration-200">
                                        Delete
                                    </button>
                                </form>
                            </li>
                        </ul>
                    </div>
                @endif
            @endauth
        </div>

        <div class="mt-4">
            <p class="text-gray-700 dark:text-gray-300 leading-relaxed">{{ strip_tags($comment->content) }}</p>
        </div>

        {{-- Edit Form (hidden by default) --}}
        <div id="edit-form-{{ $comment->id }}" class="hidden mt-4">
            <form action="{{ route('comments.update', $comment) }}" method="POST" class="space-y-4">
                @csrf
                @method('PATCH')
                <textarea name="content"
                    class="w-full px-4 py-3 border border-gray-300 dark:border-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-gray-700 dark:text-white transition-colors duration-200"
                    required maxlength="1000" rows="3">{{ strip_tags($comment->content) }}</textarea>
                <div class="flex justify-between items-center">
                    <div class="text-sm text-gray-500 dark:text-gray-400">
                        <span id="char-count-{{ $comment->id }}">0</span>/1000 characters
                    </div>
                    <div class="space-x-2">
                        <button type="button" onclick="toggleEditForm({{ $comment->id }})"
                            class="px-4 py-2 text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors duration-200">
                            Cancel
                        </button>
                        <button type="submit"
                            class="px-4 py-2 bg-primary-600 hover:bg-primary-700 text-white rounded-lg transition-colors duration-200">
                            Update
                        </button>
                    </div>
                </div>
            </form>
        </div>

        {{-- Reply Form --}}
        @auth
            <div class="mt-4">
                <button onclick="toggleReplyForm({{ $comment->id }})"
                    class="px-4 py-2 text-primary-600 dark:text-primary-400 hover:bg-primary-50 dark:hover:bg-primary-900/20 rounded-lg transition-colors duration-200">
                    Reply
                </button>
                <div id="reply-form-{{ $comment->id }}" class="hidden mt-4">
                    <form action="{{ route('comments.store', $comment->post) }}" method="POST" class="space-y-4">
                        @csrf
                        <input type="hidden" name="parent_id" value="{{ $comment->id }}">
                        <textarea name="content"
                            class="w-full px-4 py-3 border border-gray-300 dark:border-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-gray-700 dark:text-white transition-colors duration-200"
                            placeholder="Write your reply..." required rows="3"></textarea>
                        <div class="flex justify-end space-x-2">
                            <button type="button" onclick="toggleReplyForm({{ $comment->id }})"
                                class="px-4 py-2 text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors duration-200">
                                Cancel
                            </button>
                            <button type="submit"
                                class="px-4 py-2 bg-primary-600 hover:bg-primary-700 text-white rounded-lg transition-colors duration-200">
                                Submit Reply
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        @endauth

        {{-- Nested Replies --}}
        @if($comment->replies->count() > 0)
            <div class="mt-6 border-t border-gray-200 dark:border-gray-700"></div>
            <div class="ml-8 space-y-4">
                @foreach($comment->replies as $reply)
                    <x-comment :comment="$reply" />
                @endforeach
            </div>
        @endif
    </div>
</div>