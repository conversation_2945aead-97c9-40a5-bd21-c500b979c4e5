<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Component;
use Illuminate\Http\Request;

class AuditController extends Controller
{
    /**
     * Display audit logs.
     */
    public function index()
    {
        // For now, return a simple view with mock audit data
        // In a real implementation, you'd have an audit log table
        $auditLogs = collect([
            [
                'id' => 1,
                'action' => 'Component Updated',
                'user' => auth()->user()->name,
                'description' => 'Updated component details',
                'created_at' => now()->subHours(2)
            ],
            [
                'id' => 2,
                'action' => 'Component Created',
                'user' => auth()->user()->name,
                'description' => 'Created new component',
                'created_at' => now()->subHours(5)
            ]
        ]);

        return view('admin.audit.index', compact('auditLogs'));
    }

    /**
     * Get audit logs for a specific component.
     */
    public function component(Component $component)
    {
        // Mock audit data for component changes
        $changes = [
            [
                'field' => 'name',
                'old_value' => 'Original Name',
                'new_value' => 'Updated Name',
                'changed_at' => now()->subHours(1),
                'changed_by' => auth()->user()->name
            ],
            [
                'field' => 'price',
                'old_value' => '100.00',
                'new_value' => '150.00',
                'changed_at' => now()->subHours(1),
                'changed_by' => auth()->user()->name
            ]
        ];

        return response()->json(['changes' => $changes]);
    }

    /**
     * Export audit logs.
     */
    public function export(Request $request)
    {
        $dateFrom = $request->get('date_from', now()->subDays(7)->format('Y-m-d'));
        $dateTo = $request->get('date_to', now()->format('Y-m-d'));

        // Mock CSV data
        $csvData = "Date,Action,User,Description\n";
        $csvData .= now()->format('Y-m-d H:i:s') . ",Component Updated," . auth()->user()->name . ",Updated component details\n";
        $csvData .= now()->subHours(3)->format('Y-m-d H:i:s') . ",Component Created," . auth()->user()->name . ",Created new component\n";

        $response = response($csvData, 200, [
            'Content-Disposition' => 'attachment; filename="audit_logs.csv"'
        ]);
        $response->header('Content-Type', 'text/csv');
        return $response;
    }
}