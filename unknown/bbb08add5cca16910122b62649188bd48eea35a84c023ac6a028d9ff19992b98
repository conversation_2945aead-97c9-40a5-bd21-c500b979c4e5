@extends('layouts.admin')

@section('title', 'Payment Dashboard')

@section('content')
<div class="min-h-screen bg-gray-50 py-12">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">Payment Dashboard</h1>
                    <p class="text-gray-600">Monitor payment performance and gateway statistics</p>
                </div>
                
                <div class="flex items-center space-x-4">
                    <div class="flex items-center space-x-2">
                        <span class="text-sm text-gray-500">Last updated:</span>
                        <span class="text-sm font-medium text-gray-900" id="lastUpdated">
                            {{ now()->format('M d, Y h:i A') }}
                        </span>
                    </div>
                    
                    <button onclick="refreshDashboard()" 
                            class="bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700 transition duration-200 flex items-center">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                        </svg>
                        Refresh
                    </button>
                </div>
            </div>
        </div>

        <!-- Key Metrics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center">
                    <div class="p-2 bg-green-100 rounded-lg">
                        <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Total Revenue</p>
                        <p class="text-2xl font-semibold text-gray-900" id="dashboardTotalRevenue">
                            {{ $paymentStats['currency'] ?? 'INR' }} {{ number_format($paymentStats['total_revenue'] ?? 0, 2) }}
                        </p>
                        <p class="text-xs text-green-600 mt-1">
                            +{{ number_format($paymentStats['revenue_change'] ?? 0, 1) }}% from last month
                        </p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center">
                    <div class="p-2 bg-blue-100 rounded-lg">
                        <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Total Transactions</p>
                        <p class="text-2xl font-semibold text-gray-900" id="dashboardTotalTransactions">
                            {{ number_format($paymentStats['total_transactions'] ?? 0) }}
                        </p>
                        <p class="text-xs text-blue-600 mt-1">
                            +{{ number_format($paymentStats['transaction_change'] ?? 0, 1) }}% from last month
                        </p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center">
                    <div class="p-2 bg-green-100 rounded-lg">
                        <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Success Rate</p>
                        <p class="text-2xl font-semibold text-gray-900" id="dashboardSuccessRate">
                            {{ number_format($paymentStats['success_rate'] ?? 0, 1) }}%
                        </p>
                        <p class="text-xs text-green-600 mt-1">
                            +{{ number_format($paymentStats['success_rate_change'] ?? 0, 1) }}% from last month
                        </p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center">
                    <div class="p-2 bg-yellow-100 rounded-lg">
                        <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Avg Processing Time</p>
                        <p class="text-2xl font-semibold text-gray-900" id="dashboardAvgTime">
                            {{ number_format($paymentStats['avg_processing_time'] ?? 0, 1) }}s
                        </p>
                        <p class="text-xs text-yellow-600 mt-1">
                            {{ number_format($paymentStats['processing_time_change'] ?? 0, 1) }}s from last month
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Payment Widgets -->
        @include('admin.dashboard.payment-widgets')

        <!-- Gateway Status Overview -->
        <div class="bg-white rounded-lg shadow-md mb-8">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">Gateway Status Overview</h3>
            </div>
            
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    @foreach(['razorpay', 'payumoney', 'cashfree'] as $gateway)
                        @php
                            $setting = $gatewaySettings->firstWhere('gateway_name', $gateway);
                            $isEnabled = $setting?->is_enabled ?? false;
                            $isConfigured = $setting && !empty($setting->settings);
                            $stats = $gatewayStats[$gateway] ?? null;
                        @endphp
                        
                        <div class="border border-gray-200 rounded-lg p-4">
                            <div class="flex items-center justify-between mb-4">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center mr-3">
                                        @if($gateway === 'razorpay')
                                            <img src="{{ asset('images/gateways/razorpay-logo.svg') }}" alt="Razorpay" class="w-6 h-6">
                                        @elseif($gateway === 'payumoney')
                                            <img src="{{ asset('images/gateways/payumoney-logo.svg') }}" alt="PayUmoney" class="w-6 h-6">
                                        @elseif($gateway === 'cashfree')
                                            <img src="{{ asset('images/gateways/cashfree-logo.svg') }}" alt="Cashfree" class="w-6 h-6">
                                        @endif
                                    </div>
                                    <div>
                                        <h4 class="font-medium text-gray-900">{{ ucfirst($gateway) }}</h4>
                                        <p class="text-sm text-gray-500">Payment Gateway</p>
                                    </div>
                                </div>
                                
                                <div class="flex items-center space-x-2">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $isEnabled ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                        {{ $isEnabled ? 'Active' : 'Inactive' }}
                                    </span>
                                </div>
                            </div>
                            
                            <div class="space-y-2">
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-600">Configuration:</span>
                                    <span class="font-medium {{ $isConfigured ? 'text-green-600' : 'text-red-600' }}">
                                        {{ $isConfigured ? 'Configured' : 'Not Configured' }}
                                    </span>
                                </div>
                                
                                @if($stats)
                                    <div class="flex justify-between text-sm">
                                        <span class="text-gray-600">Success Rate:</span>
                                        <span class="font-medium text-gray-900">{{ number_format($stats['success_rate'], 1) }}%</span>
                                    </div>
                                    
                                    <div class="flex justify-between text-sm">
                                        <span class="text-gray-600">Revenue:</span>
                                        <span class="font-medium text-gray-900">{{ $stats['currency'] }} {{ number_format($stats['revenue'], 2) }}</span>
                                    </div>
                                    
                                    <div class="flex justify-between text-sm">
                                        <span class="text-gray-600">Transactions:</span>
                                        <span class="font-medium text-gray-900">{{ number_format($stats['transactions']) }}</span>
                                    </div>
                                @else
                                    <div class="text-sm text-gray-500">No transaction data available</div>
                                @endif
                            </div>
                            
                            <div class="mt-4 pt-4 border-t border-gray-200">
                                <div class="flex space-x-2">
                                    <a href="{{ route('admin.gateways.show', $gateway) }}" 
                                       class="flex-1 text-center bg-indigo-600 text-white py-2 px-3 rounded-md hover:bg-indigo-700 transition duration-200 text-sm">
                                        Configure
                                    </a>
                                    
                                    @if($isConfigured)
                                        <button onclick="testGateway('{{ $gateway }}')" 
                                                class="px-3 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition duration-200 text-sm">
                                            Test
                                        </button>
                                    @endif
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        </div>

        <!-- Real-time Monitoring -->
        <div class="bg-white rounded-lg shadow-md">
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-900">Real-time Monitoring</h3>
                    <div class="flex items-center space-x-2">
                        <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                        <span class="text-sm text-gray-500">Live</span>
                    </div>
                </div>
            </div>
            
            <div class="p-6">
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <!-- Live Transaction Feed -->
                    <div>
                        <h4 class="text-sm font-medium text-gray-900 mb-4">Live Transaction Feed</h4>
                        <div class="space-y-3 max-h-64 overflow-y-auto" id="liveTransactionFeed">
                            <div class="text-center py-8 text-gray-500">
                                <svg class="w-8 h-8 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                </svg>
                                <p class="text-sm">Waiting for live transactions...</p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- System Health -->
                    <div>
                        <h4 class="text-sm font-medium text-gray-900 mb-4">System Health</h4>
                        <div class="space-y-3">
                            <div class="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                                <div class="flex items-center">
                                    <div class="w-2 h-2 bg-green-500 rounded-full mr-3"></div>
                                    <span class="text-sm font-medium text-gray-900">Database Connection</span>
                                </div>
                                <span class="text-sm text-green-600">Healthy</span>
                            </div>
                            
                            <div class="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                                <div class="flex items-center">
                                    <div class="w-2 h-2 bg-green-500 rounded-full mr-3"></div>
                                    <span class="text-sm font-medium text-gray-900">Payment Processing</span>
                                </div>
                                <span class="text-sm text-green-600">Operational</span>
                            </div>
                            
                            <div class="flex items-center justify-between p-3 bg-yellow-50 rounded-lg">
                                <div class="flex items-center">
                                    <div class="w-2 h-2 bg-yellow-500 rounded-full mr-3"></div>
                                    <span class="text-sm font-medium text-gray-900">Webhook Processing</span>
                                </div>
                                <span class="text-sm text-yellow-600">Delayed</span>
                            </div>
                            
                            <div class="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                                <div class="flex items-center">
                                    <div class="w-2 h-2 bg-green-500 rounded-full mr-3"></div>
                                    <span class="text-sm font-medium text-gray-900">Gateway Connectivity</span>
                                </div>
                                <span class="text-sm text-green-600">All Online</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Real-time updates
let liveUpdateInterval;

document.addEventListener('DOMContentLoaded', function() {
    startLiveUpdates();
});

function startLiveUpdates() {
    // Update every 5 seconds for real-time feel
    liveUpdateInterval = setInterval(function() {
        updateLiveTransactionFeed();
        updateDashboardMetrics();
    }, 5000);
}

function stopLiveUpdates() {
    if (liveUpdateInterval) {
        clearInterval(liveUpdateInterval);
    }
}

async function updateLiveTransactionFeed() {
    try {
        const response = await fetch('/admin/api/live-transactions', {
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            }
        });
        
        if (response.ok) {
            const transactions = await response.json();
            const feed = document.getElementById('liveTransactionFeed');
            
            if (transactions.length > 0) {
                feed.innerHTML = transactions.map(transaction => `
                    <div class="flex items-center justify-between p-2 bg-gray-50 rounded">
                        <div class="flex items-center">
                            <div class="w-2 h-2 bg-blue-500 rounded-full mr-2"></div>
                            <span class="text-sm font-medium">${transaction.user_name}</span>
                            <span class="text-xs text-gray-500 ml-2">${transaction.gateway_name}</span>
                        </div>
                        <div class="text-right">
                            <div class="text-sm font-medium">${transaction.currency} ${transaction.amount}</div>
                            <div class="text-xs text-gray-500">${transaction.time_ago}</div>
                        </div>
                    </div>
                `).join('');
            }
        }
    } catch (error) {
        console.error('Failed to update live transaction feed:', error);
    }
}

async function updateDashboardMetrics() {
    try {
        const response = await fetch('/admin/api/dashboard-metrics', {
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            }
        });
        
        if (response.ok) {
            const data = await response.json();
            
            document.getElementById('dashboardTotalRevenue').textContent = `${data.currency} ${data.total_revenue.toLocaleString()}`;
            document.getElementById('dashboardTotalTransactions').textContent = data.total_transactions.toLocaleString();
            document.getElementById('dashboardSuccessRate').textContent = `${data.success_rate.toFixed(1)}%`;
            document.getElementById('dashboardAvgTime').textContent = `${data.avg_processing_time.toFixed(1)}s`;
            document.getElementById('lastUpdated').textContent = new Date().toLocaleString();
        }
    } catch (error) {
        console.error('Failed to update dashboard metrics:', error);
    }
}

async function refreshDashboard() {
    await Promise.all([
        updateDashboardMetrics(),
        updateLiveTransactionFeed(),
        updatePaymentStats(),
        updateRecentTransactions()
    ]);
}

async function testGateway(gateway) {
    try {
        const response = await fetch(`/admin/gateways/${gateway}/test`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            }
        });
        
        const data = await response.json();
        
        if (data.success) {
            alert(`✅ ${gateway} gateway test successful!`);
        } else {
            alert(`❌ ${gateway} gateway test failed: ${data.message}`);
        }
    } catch (error) {
        alert(`❌ Error testing ${gateway}: ${error.message}`);
    }
}

// Cleanup on page unload
window.addEventListener('beforeunload', function() {
    stopLiveUpdates();
});
</script>
@endsection