<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\File;

class AdminController extends Controller
{
    /**
     * Update API settings.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function updateApiSettings(Request $request)
    {
        $validated = $request->validate([
            'api_enabled' => 'boolean',
            'api_rate_limit' => 'integer|min:1|max:1000',
            'api_key_required' => 'boolean',
        ]);

        // Update settings in database or config
        foreach ($validated as $key => $value) {
            setting([$key => $value]);
        }

        return redirect()->back()->with('success', 'API settings updated successfully.');
    }

    /**
     * Update email settings.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function updateEmailSettings(Request $request)
    {
        $validated = $request->validate([
            'mail_driver' => 'required|string',
            'mail_host' => 'nullable|string',
            'mail_port' => 'nullable|integer',
            'mail_username' => 'nullable|string',
            'mail_password' => 'nullable|string',
            'mail_encryption' => 'nullable|string',
            'mail_from_address' => 'required|email',
            'mail_from_name' => 'required|string',
        ]);

        // Update mail configuration
        foreach ($validated as $key => $value) {
            setting([$key => $value]);
        }

        return redirect()->back()->with('success', 'Email settings updated successfully.');
    }

    /**
     * Toggle maintenance mode.
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function toggleMaintenance()
    {
        if (app()->isDownForMaintenance()) {
            Artisan::call('up');
            $message = 'Maintenance mode disabled.';
        } else {
            Artisan::call('down', ['--secret' => 'admin-secret']);
            $message = 'Maintenance mode enabled.';
        }

        return redirect()->back()->with('success', $message);
    }

    /**
     * Clear application cache.
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function clearCache()
    {
        try {
            // Clear various caches
            Artisan::call('cache:clear');
            Artisan::call('config:clear');
            Artisan::call('route:clear');
            Artisan::call('view:clear');
            
            // Clear compiled views
            if (File::exists(storage_path('framework/views'))) {
                File::cleanDirectory(storage_path('framework/views'));
            }

            return redirect()->back()->with('success', 'Cache cleared successfully.');
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Failed to clear cache: ' . $e->getMessage());
        }
    }

    /**
     * Show the admin profile page.
     *
     * @return \Illuminate\View\View
     */
    public function profile(): View
    {
        $user = auth()->user();
        return view('admin.profile', compact('user'));
    }

    /**
     * Update the admin profile.
     *
     * @param  \App\Http\Requests\Admin\UpdateProfileRequest  $request
     * @return \Illuminate\Http\JsonResponse|\Illuminate\Http\RedirectResponse
     */
    public function updateProfile(UpdateProfileRequest $request)
    {
        $user = Auth::user();
        
        // Ensure the user is an admin
        if (!$user->isAdmin()) {
            return back()->withErrors([
                'error' => 'Unauthorized access.'
            ]);
        }
        
        // Update the profile
        $user->update($request->validated());
        
        // Return success response
        if ($request->expectsJson()) {
            return response()->json([
                'message' => 'Profile updated successfully!',
                'user' => $user->fresh()
            ]);
        }

        return back()->with('success', 'Profile updated successfully!');
    }

    /**
     * Update admin password
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse|\Illuminate\Http\RedirectResponse
     */
    public function updatePassword(Request $request)
    {
        // Validate the request
        $request->validate([
            'current_password' => ['required', 'string'],
            'new_password' => ['required', 'string', 'confirmed', Password::min(8)],
        ]);

        $user = Auth::user();
        
        // Ensure the user is an admin
        if (!$user->isAdmin()) {
            return back()->withErrors([
                'error' => 'Unauthorized access.'
            ]);
        }
        
        // Check if current password is correct
        if (!Hash::check($request->current_password, $user->password)) {
            return back()->withErrors([
                'current_password' => 'The current password is incorrect.'
            ]);
        }

        // Update the password
        $user->update([
            'password' => Hash::make($request->new_password)
        ]);

        // Return success response
        if ($request->expectsJson()) {
            return response()->json([
                'message' => 'Password updated successfully!'
            ]);
        }

        return back()->with('success', 'Password updated successfully!');
    }
}