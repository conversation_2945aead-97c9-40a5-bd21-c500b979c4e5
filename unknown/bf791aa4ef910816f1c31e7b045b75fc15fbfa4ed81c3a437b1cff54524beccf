<?php

namespace Tests\Browser;

use App\Models\Component;
use App\Models\ComponentCategory;
use App\Models\User;
use Illuminate\Foundation\Testing\DatabaseMigrations;
use Lara<PERSON>\Dusk\Browser;
use Tests\DuskTestCase;

class PcBuilderBrowserTest extends DuskTestCase
{
    use DatabaseMigrations;

    protected User $user;
    protected ComponentCategory $motherboardCategory;
    protected ComponentCategory $cpuCategory;
    protected ComponentCategory $ramCategory;
    protected Component $motherboard;
    protected Component $cpu;
    protected Component $ram;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->user = User::factory()->create();
        
        // Create categories
        $this->motherboardCategory = ComponentCategory::factory()->create([
            'name' => 'Motherboard',
            'slug' => 'motherboard',
            'is_required' => true,
            'display_order' => 1,
        ]);
        
        $this->cpuCategory = ComponentCategory::factory()->create([
            'name' => 'CPU',
            'slug' => 'cpu',
            'is_required' => true,
            'display_order' => 2,
        ]);
        
        $this->ramCategory = ComponentCategory::factory()->create([
            'name' => 'RAM',
            'slug' => 'ram',
            'is_required' => true,
            'display_order' => 3,
        ]);
        
        // Create components
        $this->motherboard = Component::factory()->create([
            'category_id' => $this->motherboardCategory->id,
            'name' => 'ASUS ROG Strix B550-F',
            'brand' => 'ASUS',
            'price' => 189.99,
            'stock' => 10,
            'is_active' => true,
        ]);
        
        $this->cpu = Component::factory()->create([
            'category_id' => $this->cpuCategory->id,
            'name' => 'AMD Ryzen 7 5800X',
            'brand' => 'AMD',
            'price' => 299.99,
            'stock' => 5,
            'is_active' => true,
        ]);
        
        $this->ram = Component::factory()->create([
            'category_id' => $this->ramCategory->id,
            'name' => 'Corsair Vengeance 32GB',
            'brand' => 'Corsair',
            'price' => 129.99,
            'stock' => 15,
            'is_active' => true,
        ]);
    }

    /** @test */
    public function user_can_build_pc_through_browser_interface()
    {
        $this->browse(function (Browser $browser) {
            $browser->loginAs($this->user)
                    ->visit('/builder')
                    ->assertSee('PC Builder')
                    ->assertSee('Select components to build your PC');

            // Step 1: Select motherboard category
            $browser->click('[data-category="motherboard"]')
                    ->waitForText('Select Motherboard')
                    ->assertSee($this->motherboard->name)
                    ->assertSee('$189.99');

            // Step 2: Add motherboard to build
            $browser->click('[data-component-id="' . $this->motherboard->id . '"] .add-component-btn')
                    ->waitForText('Added to build')
                    ->assertSee('Your Build')
                    ->assertSee($this->motherboard->name)
                    ->assertSee('$189.99');

            // Step 3: Select CPU category
            $browser->click('[data-category="cpu"]')
                    ->waitForText('Select CPU')
                    ->assertSee($this->cpu->name)
                    ->assertSee('Compatible'); // Should show compatibility indicator

            // Step 4: Add CPU to build
            $browser->click('[data-component-id="' . $this->cpu->id . '"] .add-component-btn')
                    ->waitForText('Added to build')
                    ->assertSee($this->cpu->name)
                    ->assertSee('$489.98'); // Updated total

            // Step 5: Select RAM category
            $browser->click('[data-category="ram"]')
                    ->waitForText('Select RAM')
                    ->assertSee($this->ram->name);

            // Step 6: Add RAM to build
            $browser->click('[data-component-id="' . $this->ram->id . '"] .add-component-btn')
                    ->waitForText('Added to build')
                    ->assertSee($this->ram->name)
                    ->assertSee('$619.97'); // Updated total

            // Step 7: Verify build summary
            $browser->assertSee('Build Summary')
                    ->assertSee('3 components selected')
                    ->assertSee('Total: $619.97');

            // Step 8: Save build
            $browser->click('#save-build-btn')
                    ->waitFor('#save-build-modal')
                    ->type('#build-name', 'My Gaming PC')
                    ->type('#build-description', 'High-performance gaming build')
                    ->click('#save-build-submit')
                    ->waitForText('Build saved successfully')
                    ->assertSee('Build saved successfully');

            // Step 9: Add build to cart
            $browser->click('#add-to-cart-btn')
                    ->waitForText('Added to cart')
                    ->assertSee('3 items added to cart');

            // Step 10: Verify cart
            $browser->click('#cart-icon')
                    ->waitFor('#cart-dropdown')
                    ->assertSee($this->motherboard->name)
                    ->assertSee($this->cpu->name)
                    ->assertSee($this->ram->name)
                    ->assertSee('$619.97');
        });
    }

    /** @test */
    public function user_can_handle_compatibility_issues_in_browser()
    {
        // Create incompatible CPU
        $incompatibleCpu = Component::factory()->create([
            'category_id' => $this->cpuCategory->id,
            'name' => 'Intel Core i9-13900K',
            'brand' => 'Intel',
            'price' => 589.99,
            'stock' => 10,
            'is_active' => true,
            'specifications' => [
                'socket' => 'LGA1700' // Different from motherboard
            ]
        ]);

        $this->browse(function (Browser $browser) use ($incompatibleCpu) {
            $browser->loginAs($this->user)
                    ->visit('/builder');

            // Add motherboard first
            $browser->click('[data-category="motherboard"]')
                    ->click('[data-component-id="' . $this->motherboard->id . '"] .add-component-btn')
                    ->waitForText('Added to build');

            // Try to add incompatible CPU
            $browser->click('[data-category="cpu"]')
                    ->waitForText('Select CPU')
                    ->assertSee($incompatibleCpu->name)
                    ->assertSee('Incompatible'); // Should show incompatibility warning

            $browser->click('[data-component-id="' . $incompatibleCpu->id . '"] .add-component-btn')
                    ->waitFor('.compatibility-warning')
                    ->assertSee('Compatibility Issue')
                    ->assertSee('socket mismatch')
                    ->click('.compatibility-warning .close-btn');

            // Verify component was not added
            $browser->assertDontSee($incompatibleCpu->name, '#build-summary');

            // Add compatible CPU instead
            $browser->assertSee($this->cpu->name)
                    ->assertSee('Compatible')
                    ->click('[data-component-id="' . $this->cpu->id . '"] .add-component-btn')
                    ->waitForText('Added to build')
                    ->assertSee($this->cpu->name, '#build-summary');
        });
    }

    /** @test */
    public function user_can_search_and_filter_components_in_browser()
    {
        // Create additional components for testing
        $intelCpu = Component::factory()->create([
            'category_id' => $this->cpuCategory->id,
            'name' => 'Intel Core i7-12700K',
            'brand' => 'Intel',
            'price' => 399.99,
            'stock' => 8,
            'is_active' => true,
        ]);

        $this->browse(function (Browser $browser) use ($intelCpu) {
            $browser->loginAs($this->user)
                    ->visit('/builder')
                    ->click('[data-category="cpu"]')
                    ->waitForText('Select CPU');

            // Test search functionality
            $browser->type('#component-search', 'Intel')
                    ->waitFor('.search-results')
                    ->assertSee($intelCpu->name)
                    ->assertDontSee($this->cpu->name); // AMD CPU should be hidden

            // Clear search
            $browser->clear('#component-search')
                    ->keys('#component-search', '{enter}')
                    ->waitFor('.component-list')
                    ->assertSee($intelCpu->name)
                    ->assertSee($this->cpu->name); // Both should be visible

            // Test brand filter
            $browser->select('#brand-filter', 'AMD')
                    ->waitFor('.filtered-results')
                    ->assertSee($this->cpu->name)
                    ->assertDontSee($intelCpu->name);

            // Test price range filter
            $browser->select('#brand-filter', '') // Clear brand filter
                    ->type('#min-price', '200')
                    ->type('#max-price', '400')
                    ->click('#apply-filters')
                    ->waitFor('.filtered-results')
                    ->assertSee($this->cpu->name) // $299.99
                    ->assertSee($intelCpu->name); // $399.99

            // Test sort functionality
            $browser->select('#sort-by', 'price-asc')
                    ->waitFor('.sorted-results')
                    ->assertSeeIn('.component-list .component-card:first-child', $this->cpu->name);

            $browser->select('#sort-by', 'price-desc')
                    ->waitFor('.sorted-results')
                    ->assertSeeIn('.component-list .component-card:first-child', $intelCpu->name);
        });
    }

    /** @test */
    public function user_can_manage_saved_builds_in_browser()
    {
        $this->browse(function (Browser $browser) {
            $browser->loginAs($this->user)
                    ->visit('/builder');

            // Build and save a PC
            $browser->click('[data-category="motherboard"]')
                    ->click('[data-component-id="' . $this->motherboard->id . '"] .add-component-btn')
                    ->waitForText('Added to build')
                    ->click('[data-category="cpu"]')
                    ->click('[data-component-id="' . $this->cpu->id . '"] .add-component-btn')
                    ->waitForText('Added to build')
                    ->click('#save-build-btn')
                    ->waitFor('#save-build-modal')
                    ->type('#build-name', 'Gaming Build')
                    ->check('#is-public')
                    ->click('#save-build-submit')
                    ->waitForText('Build saved successfully');

            // Navigate to saved builds
            $browser->visit('/account/builds')
                    ->assertSee('My Saved Builds')
                    ->assertSee('Gaming Build')
                    ->assertSee('Public')
                    ->assertSee('$489.98');

            // Edit build
            $browser->click('.build-card:first-child .edit-btn')
                    ->waitFor('#edit-build-modal')
                    ->clear('#build-name')
                    ->type('#build-name', 'Updated Gaming Build')
                    ->uncheck('#is-public')
                    ->click('#update-build-submit')
                    ->waitForText('Build updated successfully')
                    ->assertSee('Updated Gaming Build')
                    ->assertSee('Private');

            // Share build
            $browser->click('.build-card:first-child .share-btn')
                    ->waitFor('#share-build-modal')
                    ->assertSee('Share Build')
                    ->click('#copy-link-btn')
                    ->waitForText('Link copied to clipboard');

            // Load build in builder
            $browser->click('.build-card:first-child .load-btn')
                    ->waitForLocation('/builder')
                    ->assertSee('Updated Gaming Build')
                    ->assertSee($this->motherboard->name)
                    ->assertSee($this->cpu->name);

            // Delete build
            $browser->visit('/account/builds')
                    ->click('.build-card:first-child .delete-btn')
                    ->waitFor('#confirm-delete-modal')
                    ->click('#confirm-delete-btn')
                    ->waitForText('Build deleted successfully')
                    ->assertDontSee('Updated Gaming Build');
        });
    }

    /** @test */
    public function user_can_view_build_compatibility_details_in_browser()
    {
        $this->browse(function (Browser $browser) {
            $browser->loginAs($this->user)
                    ->visit('/builder');

            // Add components to build
            $browser->click('[data-category="motherboard"]')
                    ->click('[data-component-id="' . $this->motherboard->id . '"] .add-component-btn')
                    ->waitForText('Added to build')
                    ->click('[data-category="cpu"]')
                    ->click('[data-component-id="' . $this->cpu->id . '"] .add-component-btn')
                    ->waitForText('Added to build');

            // View compatibility details
            $browser->click('#compatibility-details-btn')
                    ->waitFor('#compatibility-modal')
                    ->assertSee('Compatibility Analysis')
                    ->assertSee('Socket Compatibility')
                    ->assertSee('Compatible')
                    ->assertSee('Power Requirements')
                    ->assertSee('Memory Compatibility');

            // Check detailed compatibility info
            $browser->assertSee('Motherboard Socket: AM4')
                    ->assertSee('CPU Socket: AM4')
                    ->assertSee('✓ Compatible')
                    ->click('#compatibility-modal .close-btn');

            // Add RAM and check updated compatibility
            $browser->click('[data-category="ram"]')
                    ->click('[data-component-id="' . $this->ram->id . '"] .add-component-btn')
                    ->waitForText('Added to build')
                    ->click('#compatibility-details-btn')
                    ->waitFor('#compatibility-modal')
                    ->assertSee('Memory Type: DDR4')
                    ->assertSee('Motherboard Memory Support: DDR4')
                    ->assertSee('✓ Compatible');
        });
    }

    /** @test */
    public function guest_user_can_use_builder_with_limited_features()
    {
        $this->browse(function (Browser $browser) {
            $browser->visit('/builder')
                    ->assertSee('PC Builder')
                    ->assertSee('Sign in to save builds');

            // Can add components to build
            $browser->click('[data-category="motherboard"]')
                    ->click('[data-component-id="' . $this->motherboard->id . '"] .add-component-btn')
                    ->waitForText('Added to build')
                    ->assertSee($this->motherboard->name);

            // Cannot save build (should prompt to login)
            $browser->click('#save-build-btn')
                    ->waitFor('#login-prompt-modal')
                    ->assertSee('Sign in to save your build')
                    ->assertSee('Create Account')
                    ->assertSee('Sign In');

            // Can add to cart as guest
            $browser->click('#login-prompt-modal .close-btn')
                    ->click('#add-to-cart-btn')
                    ->waitForText('Added to cart')
                    ->assertSee('1 item added to cart');

            // Can proceed to checkout as guest
            $browser->click('#cart-icon')
                    ->click('#checkout-btn')
                    ->waitForLocation('/checkout')
                    ->assertSee('Checkout')
                    ->assertSee('Continue as Guest')
                    ->assertSee('Sign In');
        });
    }

    /** @test */
    public function builder_interface_is_responsive_on_mobile()
    {
        $this->browse(function (Browser $browser) {
            $browser->resize(375, 667) // iPhone SE dimensions
                    ->loginAs($this->user)
                    ->visit('/builder')
                    ->assertSee('PC Builder');

            // Mobile navigation should work
            $browser->click('#mobile-menu-btn')
                    ->waitFor('#mobile-category-menu')
                    ->assertSee('Motherboard')
                    ->assertSee('CPU')
                    ->assertSee('RAM');

            // Select category on mobile
            $browser->click('[data-mobile-category="motherboard"]')
                    ->waitForText('Select Motherboard')
                    ->assertSee($this->motherboard->name);

            // Component cards should be mobile-friendly
            $browser->assertPresent('.component-card.mobile-layout')
                    ->click('[data-component-id="' . $this->motherboard->id . '"] .add-component-btn')
                    ->waitForText('Added to build');

            // Build summary should be collapsible on mobile
            $browser->click('#mobile-build-summary-toggle')
                    ->waitFor('#mobile-build-summary')
                    ->assertSee($this->motherboard->name)
                    ->assertSee('$189.99');

            // Mobile cart should work
            $browser->click('#mobile-cart-btn')
                    ->waitFor('#mobile-cart-drawer')
                    ->assertSee('Your Cart')
                    ->click('#add-to-cart-btn')
                    ->waitForText('Added to cart');
        });
    }

    /** @test */
    public function builder_handles_real_time_stock_updates()
    {
        $this->browse(function (Browser $browser) {
            $browser->loginAs($this->user)
                    ->visit('/builder')
                    ->click('[data-category="cpu"]')
                    ->waitForText('Select CPU')
                    ->assertSee('In Stock (5)'); // Initial stock

            // Simulate stock update (this would normally come from WebSocket or polling)
            $this->cpu->update(['stock' => 2]);

            // Refresh component list to see updated stock
            $browser->click('#refresh-components-btn')
                    ->waitFor('.stock-updated')
                    ->assertSee('In Stock (2)')
                    ->assertSee('Low Stock', '.stock-warning');

            // Set to out of stock
            $this->cpu->update(['stock' => 0]);

            $browser->click('#refresh-components-btn')
                    ->waitFor('.stock-updated')
                    ->assertSee('Out of Stock')
                    ->assertPresent('[data-component-id="' . $this->cpu->id . '"] .add-component-btn:disabled');
        });
    }
}