<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class AdminMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Check if user is not authenticated (guest)
        if (!auth()->check()) {
            // For API requests, return a 401 response
            if ($request->expectsJson() || $request->is('api/*')) {
                return response()->json(['error' => 'Unauthenticated'], 401);
            }

            // For web requests (including tests), redirect to admin login
            return redirect()->route('admin.login');
        }

        // Check if authenticated user is not admin or not active
        if (auth()->user()->role !== 'admin' || auth()->user()->status !== 'active') {
            // For API requests, return a 403 response
            if ($request->expectsJson() || $request->is('api/*')) {
                return response()->json(['error' => 'Unauthorized access'], 403);
            }

            // For test environments, return a 403 response without HTML
            if (app()->environment('testing')) {
                return response()->json(['error' => 'Unauthorized access'], 403);
            }

            // For regular web requests, redirect to login with error
            return redirect()->route('admin.login')->with('error', 'Unauthorized access');
        }

        return $next($request);
    }
}