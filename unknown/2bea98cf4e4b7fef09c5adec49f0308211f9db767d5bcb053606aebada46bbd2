<?php

namespace Tests\Browser;

use App\Models\Component;
use App\Models\ComponentCategory;
use App\Models\Order;
use App\Models\User;
use Illuminate\Foundation\Testing\DatabaseMigrations;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Lara<PERSON>\Dusk\Browser;
use Tests\DuskTestCase;

class AdminBrowserTest extends DuskTestCase
{
    use DatabaseMigrations;

    protected User $admin;
    protected User $regularUser;
    protected ComponentCategory $category;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->admin = User::factory()->create([
            'role' => 'admin',
            'name' => 'Admin User',
            'email' => '<EMAIL>'
        ]);
        
        $this->regularUser = User::factory()->create([
            'role' => 'user',
            'name' => 'Regular User',
            'email' => '<EMAIL>'
        ]);
        
        $this->category = ComponentCategory::factory()->create([
            'name' => 'CPU',
            'slug' => 'cpu'
        ]);
        
        Storage::fake('public');
    }

    /** @test */
    public function admin_can_manage_components_through_browser()
    {
        $this->browse(function (Browser $browser) {
            $browser->loginAs($this->admin)
                    ->visit('/admin/dashboard')
                    ->assertSee('Admin Dashboard')
                    ->assertSee('Component Management')
                    ->assertSee('Order Management');

            // Navigate to component management
            $browser->click('#components-menu')
                    ->waitForLocation('/admin/components')
                    ->assertSee('Component Management')
                    ->assertSee('Add New Component');

            // Create new component
            $browser->click('#add-component-btn')
                    ->waitFor('#component-form-modal')
                    ->assertSee('Add New Component')
                    ->type('#component-name', 'Intel Core i9-13900K')
                    ->type('#component-description', 'High-performance gaming CPU')
                    ->select('#component-category', $this->category->id)
                    ->type('#component-brand', 'Intel')
                    ->type('#component-model', 'i9-13900K')
                    ->type('#component-price', '589.99')
                    ->type('#component-stock', '25')
                    ->check('#component-active')
                    ->check('#component-featured');

            // Add specifications
            $browser->click('#add-spec-btn')
                    ->type('#specs-0-key', 'Cores')
                    ->type('#specs-0-value', '24')
                    ->click('#add-spec-btn')
                    ->type('#specs-1-key', 'Threads')
                    ->type('#specs-1-value', '32')
                    ->click('#add-spec-btn')
                    ->type('#specs-2-key', 'Base Clock')
                    ->type('#specs-2-value', '3.0 GHz');

            // Save component
            $browser->click('#save-component-btn')
                    ->waitForText('Component created successfully')
                    ->assertSee('Intel Core i9-13900K')
                    ->assertSee('$589.99')
                    ->assertSee('25')
                    ->assertSee('Active')
                    ->assertSee('Featured');

            // Edit component
            $browser->click('.component-row:first-child .edit-btn')
                    ->waitFor('#component-form-modal')
                    ->assertInputValue('#component-name', 'Intel Core i9-13900K')
                    ->clear('#component-price')
                    ->type('#component-price', '649.99')
                    ->click('#save-component-btn')
                    ->waitForText('Component updated successfully')
                    ->assertSee('$649.99');

            // Upload component image
            $browser->click('.component-row:first-child .upload-image-btn')
                    ->waitFor('#image-upload-modal')
                    ->attach('#component-image', __DIR__ . '/fixtures/cpu-image.jpg')
                    ->click('#upload-image-btn')
                    ->waitForText('Image uploaded successfully')
                    ->assertPresent('.component-row:first-child .component-image');

            // Test bulk operations
            $browser->check('.component-row:first-child .select-checkbox')
                    ->click('#bulk-actions-dropdown')
                    ->click('#bulk-toggle-status')
                    ->waitFor('#confirm-bulk-action-modal')
                    ->click('#confirm-bulk-action')
                    ->waitForText('Bulk action completed')
                    ->assertSee('Inactive', '.component-row:first-child');

            // Test search and filtering
            $browser->type('#search-input', 'Intel')
                    ->click('#search-btn')
                    ->waitFor('.search-results')
                    ->assertSee('Intel Core i9-13900K');

            $browser->select('#category-filter', $this->category->id)
                    ->waitFor('.filtered-results')
                    ->assertSee('Intel Core i9-13900K');

            $browser->select('#status-filter', 'inactive')
                    ->waitFor('.filtered-results')
                    ->assertSee('Intel Core i9-13900K');

            // Test sorting
            $browser->click('#sort-by-price')
                    ->waitFor('.sorted-results')
                    ->assertSeeIn('.component-row:first-child', 'Intel Core i9-13900K');

            // Delete component
            $browser->click('.component-row:first-child .delete-btn')
                    ->waitFor('#confirm-delete-modal')
                    ->assertSee('Are you sure you want to delete this component?')
                    ->click('#confirm-delete-btn')
                    ->waitForText('Component deleted successfully')
                    ->assertDontSee('Intel Core i9-13900K');
        });
    }

    /** @test */
    public function admin_can_manage_orders_through_browser()
    {
        // Create test orders
        $order1 = Order::factory()->create([
            'user_id' => $this->regularUser->id,
            'status' => 'pending',
            'payment_status' => 'pending',
            'total_amount' => 599.99,
            'order_number' => 'ORD-001'
        ]);

        $order2 = Order::factory()->create([
            'user_id' => $this->regularUser->id,
            'status' => 'processing',
            'payment_status' => 'paid',
            'total_amount' => 1299.99,
            'order_number' => 'ORD-002'
        ]);

        $this->browse(function (Browser $browser) use ($order1, $order2) {
            $browser->loginAs($this->admin)
                    ->visit('/admin/orders')
                    ->assertSee('Order Management')
                    ->assertSee('ORD-001')
                    ->assertSee('ORD-002')
                    ->assertSee('Pending')
                    ->assertSee('Processing');

            // Filter orders by status
            $browser->select('#status-filter', 'pending')
                    ->waitFor('.filtered-results')
                    ->assertSee('ORD-001')
                    ->assertDontSee('ORD-002');

            // View order details
            $browser->select('#status-filter', '') // Clear filter
                    ->waitFor('.all-orders')
                    ->click('[data-order-id="' . $order1->id . '"] .view-btn')
                    ->waitFor('#order-details-modal')
                    ->assertSee('Order Details')
                    ->assertSee('ORD-001')
                    ->assertSee($this->regularUser->name)
                    ->assertSee('$599.99')
                    ->assertSee('Pending');

            // Update order status
            $browser->select('#order-status-select', 'processing')
                    ->type('#status-notes', 'Payment confirmed, preparing for shipment')
                    ->click('#update-status-btn')
                    ->waitForText('Order status updated')
                    ->assertSee('Processing');

            $browser->click('#close-modal-btn')
                    ->waitUntilMissing('#order-details-modal');

            // Add tracking information
            $browser->click('[data-order-id="' . $order2->id . '"] .view-btn')
                    ->waitFor('#order-details-modal')
                    ->click('#add-tracking-tab')
                    ->type('#tracking-number', 'TRK123456789')
                    ->select('#carrier', 'UPS')
                    ->select('#tracking-status', 'shipped')
                    ->click('#add-tracking-btn')
                    ->waitForText('Tracking information added')
                    ->assertSee('TRK123456789')
                    ->assertSee('UPS');

            $browser->click('#close-modal-btn');

            // Test bulk operations
            $browser->check('[data-order-id="' . $order1->id . '"] .select-checkbox')
                    ->check('[data-order-id="' . $order2->id . '"] .select-checkbox')
                    ->click('#bulk-actions-dropdown')
                    ->click('#bulk-export')
                    ->waitFor('#export-options-modal')
                    ->select('#export-format', 'csv')
                    ->click('#export-btn')
                    ->waitForText('Export started');

            // Search orders
            $browser->type('#order-search', 'ORD-001')
                    ->click('#search-orders-btn')
                    ->waitFor('.search-results')
                    ->assertSee('ORD-001')
                    ->assertDontSee('ORD-002');

            // Date range filter
            $browser->clear('#order-search')
                    ->click('#search-orders-btn')
                    ->waitFor('.all-orders')
                    ->type('#date-from', now()->subDays(7)->format('Y-m-d'))
                    ->type('#date-to', now()->format('Y-m-d'))
                    ->click('#apply-date-filter')
                    ->waitFor('.date-filtered-results')
                    ->assertSee('ORD-001')
                    ->assertSee('ORD-002');
        });
    }

    /** @test */
    public function admin_can_view_analytics_dashboard()
    {
        // Create test data
        $components = Component::factory()->count(10)->create([
            'category_id' => $this->category->id,
            'is_active' => true
        ]);

        $orders = Order::factory()->count(5)->create([
            'status' => 'completed',
            'payment_status' => 'paid',
            'created_at' => now()->subDays(rand(1, 30))
        ]);

        $this->browse(function (Browser $browser) {
            $browser->loginAs($this->admin)
                    ->visit('/admin/dashboard')
                    ->assertSee('Admin Dashboard');

            // Check dashboard widgets
            $browser->assertSee('Total Components')
                    ->assertSee('Total Orders')
                    ->assertSee('Total Revenue')
                    ->assertSee('Active Users');

            // View sales analytics
            $browser->click('#sales-analytics-tab')
                    ->waitFor('#sales-chart')
                    ->assertSee('Sales Overview')
                    ->assertSee('Revenue Trend')
                    ->assertPresent('#sales-chart canvas');

            // Change date range
            $browser->select('#analytics-period', 'last_7_days')
                    ->waitFor('.chart-updated')
                    ->assertPresent('#sales-chart canvas');

            // View inventory analytics
            $browser->click('#inventory-analytics-tab')
                    ->waitFor('#inventory-chart')
                    ->assertSee('Inventory Overview')
                    ->assertSee('Stock Levels')
                    ->assertSee('Low Stock Alerts')
                    ->assertPresent('#inventory-chart canvas');

            // View customer analytics
            $browser->click('#customer-analytics-tab')
                    ->waitFor('#customer-chart')
                    ->assertSee('Customer Analytics')
                    ->assertSee('New Customers')
                    ->assertSee('Customer Lifetime Value')
                    ->assertPresent('#customer-chart canvas');

            // Generate custom report
            $browser->click('#generate-report-btn')
                    ->waitFor('#report-modal')
                    ->select('#report-type', 'sales_by_category')
                    ->type('#report-date-from', now()->subDays(30)->format('Y-m-d'))
                    ->type('#report-date-to', now()->format('Y-m-d'))
                    ->click('#generate-report-submit')
                    ->waitForText('Report generated successfully')
                    ->assertSee('Download Report');
        });
    }

    /** @test */
    public function admin_can_manage_users()
    {
        $this->browse(function (Browser $browser) {
            $browser->loginAs($this->admin)
                    ->visit('/admin/users')
                    ->assertSee('User Management')
                    ->assertSee($this->regularUser->name)
                    ->assertSee($this->regularUser->email)
                    ->assertSee('user'); // Role

            // View user details
            $browser->click('[data-user-id="' . $this->regularUser->id . '"] .view-btn')
                    ->waitFor('#user-details-modal')
                    ->assertSee('User Details')
                    ->assertSee($this->regularUser->name)
                    ->assertSee($this->regularUser->email)
                    ->assertSee('Registration Date');

            // Update user role
            $browser->select('#user-role-select', 'moderator')
                    ->click('#update-role-btn')
                    ->waitForText('User role updated')
                    ->assertSee('moderator');

            // Suspend user
            $browser->click('#suspend-user-btn')
                    ->waitFor('#suspend-user-modal')
                    ->type('#suspension-reason', 'Violation of terms of service')
                    ->type('#suspension-duration', '30')
                    ->click('#confirm-suspension')
                    ->waitForText('User suspended successfully')
                    ->assertSee('Suspended');

            $browser->click('#close-modal-btn');

            // Search users
            $browser->type('#user-search', $this->regularUser->name)
                    ->click('#search-users-btn')
                    ->waitFor('.search-results')
                    ->assertSee($this->regularUser->name);

            // Filter by role
            $browser->clear('#user-search')
                    ->click('#search-users-btn')
                    ->select('#role-filter', 'moderator')
                    ->waitFor('.filtered-results')
                    ->assertSee($this->regularUser->name);

            // View user activity
            $browser->click('[data-user-id="' . $this->regularUser->id . '"] .activity-btn')
                    ->waitFor('#user-activity-modal')
                    ->assertSee('User Activity')
                    ->assertSee('Recent Actions');
        });
    }

    /** @test */
    public function admin_interface_handles_real_time_updates()
    {
        $this->browse(function (Browser $browser) {
            $browser->loginAs($this->admin)
                    ->visit('/admin/dashboard')
                    ->assertSee('Admin Dashboard');

            // Should see real-time notifications
            $browser->assertPresent('#notification-bell')
                    ->click('#notification-bell')
                    ->waitFor('#notifications-dropdown')
                    ->assertSee('Notifications');

            // Create a new order (simulate real-time update)
            $newOrder = Order::factory()->create([
                'user_id' => $this->regularUser->id,
                'status' => 'pending',
                'payment_status' => 'paid',
                'total_amount' => 299.99
            ]);

            // Should receive notification
            $browser->waitFor('.new-notification')
                    ->assertSee('New order received')
                    ->assertSee($newOrder->order_number);

            // Navigate to orders and see the new order
            $browser->click('#orders-menu')
                    ->waitForLocation('/admin/orders')
                    ->assertSee($newOrder->order_number);

            // Test live stock updates
            $component = Component::factory()->create([
                'category_id' => $this->category->id,
                'stock' => 5
            ]);

            $browser->visit('/admin/components')
                    ->assertSee($component->name)
                    ->assertSee('5', '[data-component-id="' . $component->id . '"] .stock-cell');

            // Simulate stock change
            $component->update(['stock' => 2]);

            // Should see updated stock with warning
            $browser->waitFor('.stock-updated')
                    ->assertSee('2', '[data-component-id="' . $component->id . '"] .stock-cell')
                    ->assertPresent('[data-component-id="' . $component->id . '"] .low-stock-warning');
        });
    }

    /** @test */
    public function regular_user_cannot_access_admin_interface()
    {
        $this->browse(function (Browser $browser) {
            $browser->loginAs($this->regularUser)
                    ->visit('/admin/dashboard')
                    ->assertSee('403')
                    ->assertSee('Forbidden');

            // Try direct access to admin routes
            $browser->visit('/admin/components')
                    ->assertSee('403')
                    ->visit('/admin/orders')
                    ->assertSee('403')
                    ->visit('/admin/users')
                    ->assertSee('403');

            // Should not see admin menu items
            $browser->visit('/')
                    ->assertDontSee('Admin Dashboard')
                    ->assertDontSee('Component Management')
                    ->assertDontSee('Order Management');
        });
    }

    /** @test */
    public function admin_can_handle_bulk_operations_efficiently()
    {
        // Create multiple components for bulk operations
        $components = Component::factory()->count(10)->create([
            'category_id' => $this->category->id,
            'is_active' => true,
            'is_featured' => false
        ]);

        $this->browse(function (Browser $browser) use ($components) {
            $browser->loginAs($this->admin)
                    ->visit('/admin/components')
                    ->assertSee('Component Management');

            // Select all components
            $browser->check('#select-all-checkbox')
                    ->waitFor('.all-selected')
                    ->assertSee('10 items selected');

            // Bulk toggle featured status
            $browser->click('#bulk-actions-dropdown')
                    ->click('#bulk-toggle-featured')
                    ->waitFor('#bulk-action-modal')
                    ->assertSee('Toggle Featured Status')
                    ->assertSee('10 components')
                    ->click('#confirm-bulk-action')
                    ->waitForText('Bulk action completed successfully');

            // Verify all components are now featured
            foreach ($components as $component) {
                $browser->assertSee('Featured', '[data-component-id="' . $component->id . '"]');
            }

            // Bulk price update
            $browser->check('#select-all-checkbox')
                    ->click('#bulk-actions-dropdown')
                    ->click('#bulk-price-update')
                    ->waitFor('#bulk-price-modal')
                    ->select('#price-adjustment-type', 'percentage_increase')
                    ->type('#price-adjustment-value', '10')
                    ->click('#apply-price-update')
                    ->waitForText('Prices updated successfully');

            // Bulk export
            $browser->check('#select-all-checkbox')
                    ->click('#bulk-actions-dropdown')
                    ->click('#bulk-export')
                    ->waitFor('#export-modal')
                    ->select('#export-format', 'csv')
                    ->check('#include-specifications')
                    ->click('#start-export')
                    ->waitForText('Export completed')
                    ->assertSee('Download File');

            // Bulk delete with confirmation
            $browser->check('#select-all-checkbox')
                    ->click('#bulk-actions-dropdown')
                    ->click('#bulk-delete')
                    ->waitFor('#bulk-delete-modal')
                    ->assertSee('Delete 10 Components')
                    ->assertSee('This action cannot be undone')
                    ->type('#confirmation-text', 'DELETE')
                    ->click('#confirm-bulk-delete')
                    ->waitForText('Components deleted successfully')
                    ->assertSee('No components found');
        });
    }

    /** @test */
    public function admin_interface_is_responsive_on_tablet()
    {
        $this->browse(function (Browser $browser) {
            $browser->resize(768, 1024) // iPad dimensions
                    ->loginAs($this->admin)
                    ->visit('/admin/dashboard')
                    ->assertSee('Admin Dashboard');

            // Mobile admin menu should work
            $browser->click('#admin-mobile-menu')
                    ->waitFor('#admin-sidebar-mobile')
                    ->assertSee('Dashboard')
                    ->assertSee('Components')
                    ->assertSee('Orders')
                    ->assertSee('Users');

            // Navigate to components
            $browser->click('#components-mobile-link')
                    ->waitForLocation('/admin/components')
                    ->assertPresent('.admin-table.tablet-layout');

            // Mobile-friendly component form
            $browser->click('#add-component-btn')
                    ->waitFor('#component-form-modal')
                    ->assertPresent('.form-layout.mobile-friendly')
                    ->type('#component-name', 'Test Component')
                    ->select('#component-category', $this->category->id)
                    ->type('#component-price', '99.99')
                    ->click('#save-component-btn')
                    ->waitForText('Component created successfully');

            // Mobile-friendly data tables
            $browser->assertPresent('.data-table.responsive')
                    ->assertPresent('.mobile-card-view');
        });
    }
}