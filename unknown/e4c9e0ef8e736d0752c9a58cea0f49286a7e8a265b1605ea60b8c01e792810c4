<?php

namespace App\Livewire\Admin;

use App\Models\Component;
use App\Models\ComponentCategory;
use Livewire\Component as LivewireComponent;
use Livewire\WithFileUploads;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Storage;

class ComponentForm extends LivewireComponent
{
    use WithFileUploads;

    public ?Component $component = null;
    public $componentId = null;
    public $name = '';
    public $description = '';
    public $category_id = '';
    public $brand = '';
    public $model = '';
    public $price = '';
    public $stock = '';
    public $image;
    public $existing_image = '';
    public $specs = [];
    public $is_featured = false;
    public $is_active = true;
    
    public $categories = [];
    public $isEditing = false;

    protected $rules = [
        'name' => 'required|string|max:255',
        'description' => 'nullable|string',
        'category_id' => 'required|exists:component_categories,id',
        'brand' => 'required|string|max:100',
        'model' => 'required|string|max:100',
        'price' => 'required|numeric|min:0',
        'stock' => 'required|integer|min:0',
        'image' => 'nullable|image|max:2048',
        'specs' => 'array',
        'is_featured' => 'boolean',
        'is_active' => 'boolean',
    ];

    public function mount($componentId = null)
    {
        $this->categories = ComponentCategory::orderBy('name')->get();
        
        if ($componentId) {
            $this->componentId = $componentId;
            $this->component = Component::findOrFail($componentId);
            $this->isEditing = true;
            $this->loadComponent();
        }
    }

    public function loadComponent()
    {
        if ($this->component) {
            $this->name = $this->component->name;
            $this->description = $this->component->description;
            $this->category_id = $this->component->category_id;
            $this->brand = $this->component->brand;
            $this->model = $this->component->model;
            $this->price = $this->component->price;
            $this->stock = $this->component->stock;
            $this->existing_image = $this->component->image;
            $this->specs = $this->component->specs ?? [];
            $this->is_featured = $this->component->is_featured;
            $this->is_active = $this->component->is_active;
        }
    }

    public function addSpec()
    {
        $this->specs[] = ['key' => '', 'value' => ''];
    }

    public function removeSpec($index)
    {
        unset($this->specs[$index]);
        $this->specs = array_values($this->specs);
    }

    public function save()
    {
        $this->validate();

        $data = [
            'name' => $this->name,
            'slug' => Str::slug($this->name),
            'description' => $this->description,
            'category_id' => $this->category_id,
            'brand' => $this->brand,
            'model' => $this->model,
            'price' => $this->price,
            'stock' => $this->stock,
            'specs' => $this->formatSpecs(),
            'is_featured' => $this->is_featured,
            'is_active' => $this->is_active,
        ];

        // Handle image upload
        if ($this->image) {
            if ($this->existing_image) {
                Storage::disk('public')->delete($this->existing_image);
            }
            $data['image'] = $this->image->store('components', 'public');
        }

        if ($this->isEditing) {
            $this->component->update($data);
            session()->flash('message', 'Component updated successfully.');
        } else {
            Component::create($data);
            session()->flash('message', 'Component created successfully.');
            $this->reset();
        }

        $this->dispatch('component-saved');
    }

    private function formatSpecs()
    {
        $formatted = [];
        foreach ($this->specs as $spec) {
            if (!empty($spec['key']) && !empty($spec['value'])) {
                $formatted[$spec['key']] = $spec['value'];
            }
        }
        return $formatted;
    }

    public function cancel()
    {
        if ($this->isEditing) {
            $this->loadComponent();
        } else {
            $this->reset();
        }
        $this->dispatch('form-cancelled');
    }

    public function render()
    {
        return view('livewire.admin.component-form');
    }
}