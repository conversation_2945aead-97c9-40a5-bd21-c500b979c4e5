<?php

namespace Tests\Feature\Integration;

use Tests\TestCase;
use App\Models\User;
use App\Models\Transaction;
use App\Models\GatewaySetting;
use App\Services\Payment\Gateways\RazorpayService;
use App\Services\Payment\Gateways\PayUmoneyService;
use App\Services\Payment\Gateways\CashfreeService;
use App\Services\Payment\Exceptions\WebhookVerificationException;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Support\Facades\Log;

class WebhookProcessingIntegrationTest extends TestCase
{
    use DatabaseTransactions;

    protected User $user;
    protected array $gatewaySettings;
    protected array $transactions;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test user
        $this->user = User::factory()->create([
            'email' => '<EMAIL>'
        ]);

        // Create gateway settings for all three gateways
        $this->gatewaySettings = [
            GatewaySetting::GATEWAY_RAZORPAY => GatewaySetting::create([
                'gateway_name' => GatewaySetting::GATEWAY_RAZORPAY,
                'is_enabled' => true,
                'is_test_mode' => true,
                'settings' => [
                    'key_id' => 'rzp_test_1234567890',
                    'key_secret' => 'test_secret_key',
                    'webhook_secret' => 'test_webhook_secret'
                ]
            ]),
            GatewaySetting::GATEWAY_PAYUMONEY => GatewaySetting::create([
                'gateway_name' => GatewaySetting::GATEWAY_PAYUMONEY,
                'is_enabled' => true,
                'is_test_mode' => true,
                'settings' => [
                    'merchant_key' => 'test_merchant_key',
                    'salt' => 'test_salt_key',
                    'auth_header' => 'test_auth_header'
                ]
            ]),
            GatewaySetting::GATEWAY_CASHFREE => GatewaySetting::create([
                'gateway_name' => GatewaySetting::GATEWAY_CASHFREE,
                'is_enabled' => true,
                'is_test_mode' => true,
                'settings' => [
                    'app_id' => 'test_app_id',
                    'secret_key' => 'test_secret_key',
                    'client_id' => 'test_client_id',
                    'client_secret' => 'test_client_secret'
                ]
            ])
        ];

        // Create test transactions for each gateway
        $this->transactions = [
            GatewaySetting::GATEWAY_RAZORPAY => Transaction::create([
                'user_id' => $this->user->id,
                'gateway_name' => GatewaySetting::GATEWAY_RAZORPAY,
                'amount' => 100.00,
                'currency' => 'INR',
                'status' => Transaction::STATUS_PROCESSING,
                'transaction_id' => 'TXN_RZP_' . uniqid(),
                'gateway_transaction_id' => 'pay_test123',
                'payment_details' => ['razorpay_order_id' => 'order_test123']
            ]),
            GatewaySetting::GATEWAY_PAYUMONEY => Transaction::create([
                'user_id' => $this->user->id,
                'gateway_name' => GatewaySetting::GATEWAY_PAYUMONEY,
                'amount' => 150.00,
                'currency' => 'INR',
                'status' => Transaction::STATUS_PROCESSING,
                'transaction_id' => 'TXN_PAYU_' . uniqid(),
            ]),
            GatewaySetting::GATEWAY_CASHFREE => Transaction::create([
                'user_id' => $this->user->id,
                'gateway_name' => GatewaySetting::GATEWAY_CASHFREE,
                'amount' => 200.00,
                'currency' => 'INR',
                'status' => Transaction::STATUS_PROCESSING,
                'transaction_id' => 'TXN_CF_' . uniqid(),
            ])
        ];
    }

    /**
     * Test Razorpay webhook processing for all event types
     */
    public function test_razorpay_webhook_processing_all_events()
    {
        $service = new RazorpayService();
        $transaction = $this->transactions[GatewaySetting::GATEWAY_RAZORPAY];

        // Test payment.captured event
        $capturedPayload = [
            'event' => 'payment.captured',
            'payload' => [
                'payment' => [
                    'entity' => [
                        'id' => 'pay_test123',
                        'order_id' => 'order_test123',
                        'status' => 'captured',
                        'method' => 'card',
                        'amount' => 10000
                    ]
                ]
            ]
        ];

        $_SERVER['HTTP_X_RAZORPAY_SIGNATURE'] = hash_hmac('sha256', json_encode($capturedPayload), 'test_webhook_secret');

        $result = $service->handleWebhook($capturedPayload);
        
        $this->assertTrue($result['success']);
        $this->assertEquals('Payment captured', $result['message']);

        $transaction->refresh();
        $this->assertEquals(Transaction::STATUS_COMPLETED, $transaction->status);
        $this->assertTrue($transaction->webhook_verified);

        // Reset transaction status for next test
        $transaction->update(['status' => Transaction::STATUS_PROCESSING, 'webhook_verified' => false]);

        // Test payment.failed event
        $failedPayload = [
            'event' => 'payment.failed',
            'payload' => [
                'payment' => [
                    'entity' => [
                        'id' => 'pay_test123',
                        'order_id' => 'order_test123',
                        'status' => 'failed',
                        'error_code' => 'BAD_REQUEST_ERROR',
                        'error_description' => 'Payment failed due to insufficient funds'
                    ]
                ]
            ]
        ];

        $_SERVER['HTTP_X_RAZORPAY_SIGNATURE'] = hash_hmac('sha256', json_encode($failedPayload), 'test_webhook_secret');

        $result = $service->handleWebhook($failedPayload);
        
        $this->assertTrue($result['success']);
        $this->assertEquals('Payment failure processed', $result['message']);

        $transaction->refresh();
        $this->assertEquals(Transaction::STATUS_FAILED, $transaction->status);
        $this->assertEquals('Payment failed due to insufficient funds', $transaction->failure_reason);
        $this->assertTrue($transaction->webhook_verified);

        // Reset transaction status for next test
        $transaction->update(['status' => Transaction::STATUS_PROCESSING, 'webhook_verified' => false, 'failure_reason' => null]);

        // Test payment.authorized event
        $authorizedPayload = [
            'event' => 'payment.authorized',
            'payload' => [
                'payment' => [
                    'entity' => [
                        'id' => 'pay_test123',
                        'order_id' => 'order_test123',
                        'status' => 'authorized',
                        'method' => 'card'
                    ]
                ]
            ]
        ];

        $_SERVER['HTTP_X_RAZORPAY_SIGNATURE'] = hash_hmac('sha256', json_encode($authorizedPayload), 'test_webhook_secret');

        $result = $service->handleWebhook($authorizedPayload);
        
        $this->assertTrue($result['success']);
        $this->assertEquals('Payment authorization processed', $result['message']);

        $transaction->refresh();
        $this->assertEquals(Transaction::STATUS_PROCESSING, $transaction->status);
        $this->assertTrue($transaction->webhook_verified);

        // Test unhandled event
        $unhandledPayload = [
            'event' => 'order.paid',
            'payload' => []
        ];

        $_SERVER['HTTP_X_RAZORPAY_SIGNATURE'] = hash_hmac('sha256', json_encode($unhandledPayload), 'test_webhook_secret');

        $result = $service->handleWebhook($unhandledPayload);
        
        $this->assertTrue($result['success']);
        $this->assertEquals('Event not handled', $result['message']);
        $this->assertEquals('order.paid', $result['event']);
    }

    /**
     * Test PayUmoney callback processing for all status types
     */
    public function test_payumoney_callback_processing_all_statuses()
    {
        $service = new PayUmoneyService();
        $transaction = $this->transactions[GatewaySetting::GATEWAY_PAYUMONEY];

        // Test success callback
        $successPayload = [
            'status' => 'success',
            'txnid' => $transaction->transaction_id,
            'amount' => '150.00',
            'productinfo' => 'Payment',
            'firstname' => 'Customer',
            'email' => '<EMAIL>',
            'payuMoneyId' => 'payu_123456',
            'mode' => 'CC',
            'bank_ref_num' => 'bank_ref_123',
            'key' => 'test_merchant_key',
            'udf1' => '',
            'udf2' => '',
            'udf3' => '',
            'udf4' => '',
            'udf5' => '',
        ];

        // Generate valid hash for success - using the exact format from PayUmoneyService->verifyHash
        $hashString = 'test_salt_key|success|||||' . 
                     ($successPayload['udf5'] ?? '') . '|' . 
                     ($successPayload['udf4'] ?? '') . '|' . 
                     ($successPayload['udf3'] ?? '') . '|' . 
                     ($successPayload['udf2'] ?? '') . '|' . 
                     ($successPayload['udf1'] ?? '') . '|' . 
                     $successPayload['email'] . '|' . 
                     $successPayload['firstname'] . '|' . 
                     $successPayload['productinfo'] . '|' . 
                     $successPayload['amount'] . '|' . 
                     $successPayload['txnid'] . '|' . 
                     $successPayload['key'];
        $successPayload['hash'] = strtolower(hash('sha512', $hashString));

        $result = $service->handleWebhook($successPayload);
        
        $this->assertTrue($result['success']);
        $this->assertEquals('Callback processed successfully', $result['message']);
        $this->assertEquals(Transaction::STATUS_COMPLETED, $result['transaction_status']);

        $transaction->refresh();
        $this->assertEquals(Transaction::STATUS_COMPLETED, $transaction->status);
        $this->assertTrue($transaction->webhook_verified);
        $this->assertEquals('payu_123456', $transaction->gateway_transaction_id);

        // Reset transaction status for next test
        $transaction->update(['status' => Transaction::STATUS_PROCESSING, 'webhook_verified' => false, 'gateway_transaction_id' => null]);

        // Test failure callback
        $failurePayload = [
            'status' => 'failure',
            'txnid' => $transaction->transaction_id,
            'amount' => '150.00',
            'productinfo' => 'Payment',
            'firstname' => 'Customer',
            'email' => '<EMAIL>',
            'error' => 'Payment failed due to insufficient funds',
            'key' => 'test_merchant_key',
            'udf1' => '',
            'udf2' => '',
            'udf3' => '',
            'udf4' => '',
            'udf5' => '',
        ];

        // Generate valid hash for failure - using the exact format from PayUmoneyService->verifyHash
        $hashString = 'test_salt_key|failure|||||' . 
                     ($failurePayload['udf5'] ?? '') . '|' . 
                     ($failurePayload['udf4'] ?? '') . '|' . 
                     ($failurePayload['udf3'] ?? '') . '|' . 
                     ($failurePayload['udf2'] ?? '') . '|' . 
                     ($failurePayload['udf1'] ?? '') . '|' . 
                     $failurePayload['email'] . '|' . 
                     $failurePayload['firstname'] . '|' . 
                     $failurePayload['productinfo'] . '|' . 
                     $failurePayload['amount'] . '|' . 
                     $failurePayload['txnid'] . '|' . 
                     $failurePayload['key'];
        $failurePayload['hash'] = strtolower(hash('sha512', $hashString));

        $result = $service->handleWebhook($failurePayload);
        
        $this->assertTrue($result['success']);
        $this->assertEquals('Callback processed successfully', $result['message']);
        $this->assertEquals(Transaction::STATUS_FAILED, $result['transaction_status']);

        $transaction->refresh();
        $this->assertEquals(Transaction::STATUS_FAILED, $transaction->status);
        $this->assertEquals('Payment failed due to insufficient funds', $transaction->failure_reason);
        $this->assertTrue($transaction->webhook_verified);

        // Reset transaction status for next test
        $transaction->update(['status' => Transaction::STATUS_PROCESSING, 'webhook_verified' => false, 'failure_reason' => null]);

        // Test pending callback
        $pendingPayload = [
            'status' => 'pending',
            'txnid' => $transaction->transaction_id,
            'amount' => '150.00',
            'productinfo' => 'Payment',
            'firstname' => 'Customer',
            'email' => '<EMAIL>',
            'key' => 'test_merchant_key',
            'udf1' => '',
            'udf2' => '',
            'udf3' => '',
            'udf4' => '',
            'udf5' => '',
        ];

        // Generate valid hash for pending - using the exact format from PayUmoneyService->verifyHash
        $hashString = 'test_salt_key|pending|||||' . 
                     ($pendingPayload['udf5'] ?? '') . '|' . 
                     ($pendingPayload['udf4'] ?? '') . '|' . 
                     ($pendingPayload['udf3'] ?? '') . '|' . 
                     ($pendingPayload['udf2'] ?? '') . '|' . 
                     ($pendingPayload['udf1'] ?? '') . '|' . 
                     $pendingPayload['email'] . '|' . 
                     $pendingPayload['firstname'] . '|' . 
                     $pendingPayload['productinfo'] . '|' . 
                     $pendingPayload['amount'] . '|' . 
                     $pendingPayload['txnid'] . '|' . 
                     $pendingPayload['key'];
        $pendingPayload['hash'] = strtolower(hash('sha512', $hashString));

        $result = $service->handleWebhook($pendingPayload);
        
        $this->assertTrue($result['success']);
        $this->assertEquals('Callback processed successfully', $result['message']);
        $this->assertEquals(Transaction::STATUS_PROCESSING, $result['transaction_status']);

        $transaction->refresh();
        $this->assertEquals(Transaction::STATUS_PROCESSING, $transaction->status);
        $this->assertTrue($transaction->webhook_verified);
    }

    /**
     * Test Cashfree webhook processing for all event types
     */
    public function test_cashfree_webhook_processing_all_events()
    {
        $service = new CashfreeService();
        $transaction = $this->transactions[GatewaySetting::GATEWAY_CASHFREE];

        // Test payment success webhook
        $successPayload = [
            'type' => 'PAYMENT_SUCCESS_WEBHOOK',
            'data' => [
                'order' => [
                    'order_id' => $transaction->transaction_id,
                    'cf_order_id' => 'order_test123',
                    'order_status' => 'PAID'
                ],
                'payment' => [
                    'cf_payment_id' => 'payment_test123',
                    'payment_status' => 'SUCCESS',
                    'payment_amount' => 200.00,
                    'payment_method' => 'card',
                    'bank_reference' => 'bank_ref_123',
                    'auth_id' => 'auth_123'
                ]
            ]
        ];

        // Mock webhook signature
        $timestamp = time();
        $signatureData = $timestamp . json_encode($successPayload);
        $expectedSignature = base64_encode(hash_hmac('sha256', $signatureData, 'test_secret_key', true));
        
        $_SERVER['HTTP_X_WEBHOOK_SIGNATURE'] = $expectedSignature;
        $_SERVER['HTTP_X_WEBHOOK_TIMESTAMP'] = $timestamp;

        $result = $service->handleWebhook($successPayload);
        
        $this->assertTrue($result['success']);
        $this->assertEquals('Payment success processed', $result['message']);

        $transaction->refresh();
        $this->assertEquals(Transaction::STATUS_COMPLETED, $transaction->status);
        $this->assertEquals('order_test123', $transaction->gateway_transaction_id);
        $this->assertTrue($transaction->webhook_verified);

        // Reset transaction status for next test
        $transaction->update(['status' => Transaction::STATUS_PROCESSING, 'webhook_verified' => false, 'gateway_transaction_id' => null]);

        // Test payment failed webhook
        $failedPayload = [
            'type' => 'PAYMENT_FAILED_WEBHOOK',
            'data' => [
                'order' => [
                    'order_id' => $transaction->transaction_id,
                    'cf_order_id' => 'order_test123',
                    'order_status' => 'FAILED'
                ],
                'payment' => [
                    'cf_payment_id' => 'payment_test123',
                    'payment_status' => 'FAILED',
                    'payment_message' => 'Payment failed due to insufficient funds',
                    'error_details' => 'Insufficient balance'
                ]
            ]
        ];

        $timestamp = time();
        $signatureData = $timestamp . json_encode($failedPayload);
        $expectedSignature = base64_encode(hash_hmac('sha256', $signatureData, 'test_secret_key', true));
        
        $_SERVER['HTTP_X_WEBHOOK_SIGNATURE'] = $expectedSignature;
        $_SERVER['HTTP_X_WEBHOOK_TIMESTAMP'] = $timestamp;

        $result = $service->handleWebhook($failedPayload);
        
        $this->assertTrue($result['success']);
        $this->assertEquals('Payment failure processed', $result['message']);

        $transaction->refresh();
        $this->assertEquals(Transaction::STATUS_FAILED, $transaction->status);
        $this->assertEquals('Payment failed due to insufficient funds', $transaction->failure_reason);
        $this->assertTrue($transaction->webhook_verified);

        // Reset transaction status for next test
        $transaction->update(['status' => Transaction::STATUS_PROCESSING, 'webhook_verified' => false, 'failure_reason' => null]);

        // Test payment dropped webhook
        $droppedPayload = [
            'type' => 'PAYMENT_USER_DROPPED_WEBHOOK',
            'data' => [
                'order' => [
                    'order_id' => $transaction->transaction_id,
                    'cf_order_id' => 'order_test123',
                    'order_status' => 'TERMINATED'
                ]
            ]
        ];

        $timestamp = time();
        $signatureData = $timestamp . json_encode($droppedPayload);
        $expectedSignature = base64_encode(hash_hmac('sha256', $signatureData, 'test_secret_key', true));
        
        $_SERVER['HTTP_X_WEBHOOK_SIGNATURE'] = $expectedSignature;
        $_SERVER['HTTP_X_WEBHOOK_TIMESTAMP'] = $timestamp;

        $result = $service->handleWebhook($droppedPayload);
        
        $this->assertTrue($result['success']);
        $this->assertEquals('Payment cancellation processed', $result['message']);

        $transaction->refresh();
        $this->assertEquals(Transaction::STATUS_CANCELLED, $transaction->status);
        $this->assertEquals('Payment cancelled by user', $transaction->failure_reason);
        $this->assertTrue($transaction->webhook_verified);

        // Test unhandled webhook type
        $unhandledPayload = [
            'type' => 'UNKNOWN_WEBHOOK_TYPE',
            'data' => []
        ];

        $timestamp = time();
        $signatureData = $timestamp . json_encode($unhandledPayload);
        $expectedSignature = base64_encode(hash_hmac('sha256', $signatureData, 'test_secret_key', true));
        
        $_SERVER['HTTP_X_WEBHOOK_SIGNATURE'] = $expectedSignature;
        $_SERVER['HTTP_X_WEBHOOK_TIMESTAMP'] = $timestamp;

        $result = $service->handleWebhook($unhandledPayload);
        
        $this->assertTrue($result['success']);
        $this->assertEquals('Webhook type not handled', $result['message']);
        $this->assertEquals('UNKNOWN_WEBHOOK_TYPE', $result['type']);
    }

    /**
     * Test webhook signature verification failures
     */
    public function test_webhook_signature_verification_failures()
    {
        // Test Razorpay signature verification failure
        $razorpayService = new RazorpayService();
        $webhookPayload = ['event' => 'payment.captured', 'payload' => []];
        
        $_SERVER['HTTP_X_RAZORPAY_SIGNATURE'] = 'invalid_signature';
        
        $this->expectException(WebhookVerificationException::class);
        $this->expectExceptionMessage('Webhook signature verification failed');
        
        $razorpayService->handleWebhook($webhookPayload);
    }

    public function test_payumoney_hash_verification_failure()
    {
        $payumoneyService = new PayUmoneyService();
        
        $webhookPayload = [
            'status' => 'success',
            'txnid' => 'test_txn_123',
            'amount' => '100.00',
            'productinfo' => 'Payment',
            'firstname' => 'Customer',
            'email' => '<EMAIL>',
            'key' => 'test_merchant_key',
            'hash' => 'invalid_hash_value',
            'udf1' => '',
            'udf2' => '',
            'udf3' => '',
            'udf4' => '',
            'udf5' => '',
        ];

        $this->expectException(WebhookVerificationException::class);
        $this->expectExceptionMessage('PayUmoney callback hash verification failed');
        
        $payumoneyService->handleWebhook($webhookPayload);
    }

    public function test_cashfree_signature_verification_failure()
    {
        $cashfreeService = new CashfreeService();
        
        $webhookPayload = [
            'type' => 'PAYMENT_SUCCESS_WEBHOOK',
            'data' => []
        ];

        $_SERVER['HTTP_X_WEBHOOK_SIGNATURE'] = 'invalid_signature';
        $_SERVER['HTTP_X_WEBHOOK_TIMESTAMP'] = time();

        $this->expectException(WebhookVerificationException::class);
        $this->expectExceptionMessage('Webhook signature verification failed');
        
        $cashfreeService->handleWebhook($webhookPayload);
    }

    /**
     * Test webhook processing with missing transaction
     */
    public function test_webhook_processing_missing_transaction()
    {
        // Test Razorpay with missing transaction
        $razorpayService = new RazorpayService();
        
        $webhookPayload = [
            'event' => 'payment.captured',
            'payload' => [
                'payment' => [
                    'entity' => [
                        'id' => 'pay_nonexistent',
                        'order_id' => 'order_nonexistent',
                        'status' => 'captured'
                    ]
                ]
            ]
        ];

        $_SERVER['HTTP_X_RAZORPAY_SIGNATURE'] = hash_hmac('sha256', json_encode($webhookPayload), 'test_webhook_secret');

        $result = $razorpayService->handleWebhook($webhookPayload);
        
        $this->assertFalse($result['success']);
        $this->assertEquals('Transaction not found', $result['message']);

        // Test PayUmoney with missing transaction
        $payumoneyService = new PayUmoneyService();
        
        $webhookPayload = [
            'status' => 'success',
            'txnid' => 'nonexistent_transaction',
            'amount' => '100.00',
            'productinfo' => 'Payment',
            'firstname' => 'Customer',
            'email' => '<EMAIL>',
            'key' => 'test_merchant_key',
            'udf1' => '',
            'udf2' => '',
            'udf3' => '',
            'udf4' => '',
            'udf5' => '',
        ];

        // Generate valid hash - using the exact format from PayUmoneyService->verifyHash
        $hashString = 'test_salt_key|success|||||' . 
                     ($webhookPayload['udf5'] ?? '') . '|' . 
                     ($webhookPayload['udf4'] ?? '') . '|' . 
                     ($webhookPayload['udf3'] ?? '') . '|' . 
                     ($webhookPayload['udf2'] ?? '') . '|' . 
                     ($webhookPayload['udf1'] ?? '') . '|' . 
                     $webhookPayload['email'] . '|' . 
                     $webhookPayload['firstname'] . '|' . 
                     $webhookPayload['productinfo'] . '|' . 
                     $webhookPayload['amount'] . '|' . 
                     $webhookPayload['txnid'] . '|' . 
                     $webhookPayload['key'];
        $webhookPayload['hash'] = strtolower(hash('sha512', $hashString));

        $result = $payumoneyService->handleWebhook($webhookPayload);
        
        $this->assertFalse($result['success']);
        $this->assertEquals('Transaction not found', $result['message']);

        // Test Cashfree with missing transaction
        $cashfreeService = new CashfreeService();
        
        $webhookPayload = [
            'type' => 'PAYMENT_SUCCESS_WEBHOOK',
            'data' => [
                'order' => [
                    'order_id' => 'nonexistent_transaction',
                    'cf_order_id' => 'order_test123'
                ],
                'payment' => []
            ]
        ];

        $timestamp = time();
        $signatureData = $timestamp . json_encode($webhookPayload);
        $expectedSignature = base64_encode(hash_hmac('sha256', $signatureData, 'test_secret_key', true));
        
        $_SERVER['HTTP_X_WEBHOOK_SIGNATURE'] = $expectedSignature;
        $_SERVER['HTTP_X_WEBHOOK_TIMESTAMP'] = $timestamp;

        $result = $cashfreeService->handleWebhook($webhookPayload);
        
        $this->assertFalse($result['success']);
        $this->assertEquals('Transaction not found', $result['message']);
    }

    /**
     * Test webhook processing with malformed payloads
     */
    public function test_webhook_processing_malformed_payloads()
    {
        // Test PayUmoney with missing transaction ID
        $payumoneyService = new PayUmoneyService();
        
        $webhookPayload = [
            'status' => 'success',
            'amount' => '100.00',
            'productinfo' => 'Payment',
            'firstname' => 'Customer',
            'email' => '<EMAIL>',
            'key' => 'test_merchant_key',
            'udf1' => '',
            'udf2' => '',
            'udf3' => '',
            'udf4' => '',
            'udf5' => '',
            'txnid' => '', // Empty transaction ID
        ];
        
        // Generate valid hash with empty txnid - using the exact format from PayUmoneyService->verifyHash
        $hashString = 'test_salt_key|success|||||' . 
                     ($webhookPayload['udf5'] ?? '') . '|' . 
                     ($webhookPayload['udf4'] ?? '') . '|' . 
                     ($webhookPayload['udf3'] ?? '') . '|' . 
                     ($webhookPayload['udf2'] ?? '') . '|' . 
                     ($webhookPayload['udf1'] ?? '') . '|' . 
                     $webhookPayload['email'] . '|' . 
                     $webhookPayload['firstname'] . '|' . 
                     $webhookPayload['productinfo'] . '|' . 
                     $webhookPayload['amount'] . '|' . 
                     $webhookPayload['txnid'] . '|' . 
                     $webhookPayload['key'];
        $webhookPayload['hash'] = strtolower(hash('sha512', $hashString));

        $this->expectException(\App\Services\Payment\Exceptions\PaymentGatewayException::class);
        $this->expectExceptionMessage('Callback processing failed: Transaction ID missing in callback');
        
        $payumoneyService->handleWebhook($webhookPayload);
    }

    /**
     * Test webhook processing with missing headers
     */
    public function test_webhook_processing_missing_headers()
    {
        // Test Razorpay with missing signature header
        $razorpayService = new RazorpayService();
        
        if (isset($_SERVER['HTTP_X_RAZORPAY_SIGNATURE'])) {
            unset($_SERVER['HTTP_X_RAZORPAY_SIGNATURE']);
        }

        $webhookPayload = ['event' => 'payment.captured', 'payload' => []];

        $this->expectException(WebhookVerificationException::class);
        $this->expectExceptionMessage('Webhook signature missing');
        
        $razorpayService->handleWebhook($webhookPayload);
    }

    public function test_cashfree_webhook_missing_timestamp()
    {
        $cashfreeService = new CashfreeService();
        
        $_SERVER['HTTP_X_WEBHOOK_SIGNATURE'] = 'test_signature';
        
        if (isset($_SERVER['HTTP_X_WEBHOOK_TIMESTAMP'])) {
            unset($_SERVER['HTTP_X_WEBHOOK_TIMESTAMP']);
        }

        $webhookPayload = ['type' => 'PAYMENT_SUCCESS_WEBHOOK', 'data' => []];

        $this->expectException(WebhookVerificationException::class);
        $this->expectExceptionMessage('Webhook timestamp missing');
        
        $cashfreeService->handleWebhook($webhookPayload);
    }

    /**
     * Test concurrent webhook processing
     */
    public function test_concurrent_webhook_processing()
    {
        $razorpayService = new RazorpayService();
        
        // Create multiple transactions
        $transactions = [];
        for ($i = 0; $i < 3; $i++) {
            $transactions[] = Transaction::create([
                'user_id' => $this->user->id,
                'gateway_name' => GatewaySetting::GATEWAY_RAZORPAY,
                'amount' => 100.00 + $i,
                'currency' => 'INR',
                'status' => Transaction::STATUS_PROCESSING,
                'transaction_id' => 'TXN_CONCURRENT_' . $i . '_' . uniqid(),
                'gateway_transaction_id' => 'pay_test' . $i,
                'payment_details' => ['razorpay_order_id' => 'order_test' . $i]
            ]);
        }

        // Process webhooks for all transactions
        foreach ($transactions as $i => $transaction) {
            $webhookPayload = [
                'event' => 'payment.captured',
                'payload' => [
                    'payment' => [
                        'entity' => [
                            'id' => 'pay_test' . $i,
                            'order_id' => 'order_test' . $i,
                            'status' => 'captured',
                            'method' => 'card'
                        ]
                    ]
                ]
            ];

            $_SERVER['HTTP_X_RAZORPAY_SIGNATURE'] = hash_hmac('sha256', json_encode($webhookPayload), 'test_webhook_secret');

            $result = $razorpayService->handleWebhook($webhookPayload);
            
            $this->assertTrue($result['success']);
            $this->assertEquals('Payment captured', $result['message']);
        }

        // Verify all transactions were updated
        foreach ($transactions as $transaction) {
            $transaction->refresh();
            $this->assertEquals(Transaction::STATUS_COMPLETED, $transaction->status);
            $this->assertTrue($transaction->webhook_verified);
        }
    }

    /**
     * Test webhook processing with database transaction rollback scenarios
     */
    public function test_webhook_processing_database_integrity()
    {
        $razorpayService = new RazorpayService();
        $transaction = $this->transactions[GatewaySetting::GATEWAY_RAZORPAY];

        // Verify initial state
        $this->assertEquals(Transaction::STATUS_PROCESSING, $transaction->status);
        $this->assertFalse((bool)$transaction->webhook_verified);

        $webhookPayload = [
            'event' => 'payment.captured',
            'payload' => [
                'payment' => [
                    'entity' => [
                        'id' => 'pay_test123',
                        'order_id' => 'order_test123',
                        'status' => 'captured',
                        'method' => 'card'
                    ]
                ]
            ]
        ];

        $_SERVER['HTTP_X_RAZORPAY_SIGNATURE'] = hash_hmac('sha256', json_encode($webhookPayload), 'test_webhook_secret');

        $result = $razorpayService->handleWebhook($webhookPayload);
        
        $this->assertTrue($result['success']);

        // Verify transaction was updated atomically
        $transaction->refresh();
        $this->assertEquals(Transaction::STATUS_COMPLETED, $transaction->status);
        $this->assertTrue($transaction->webhook_verified);
        $this->assertArrayHasKey('webhook_captured_at', $transaction->payment_details);
    }
}