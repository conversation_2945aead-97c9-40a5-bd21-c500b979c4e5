<?php

namespace Database\Factories;

use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Order>
 */
class OrderFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $subtotal = $this->faker->randomFloat(2, 100, 5000);
        $tax = $subtotal * 0.1; // 10% tax
        $shipping = $this->faker->randomFloat(2, 10, 50);
        $discount = $this->faker->randomFloat(2, 0, $subtotal * 0.2); // Up to 20% discount
        $total = $subtotal + $tax + $shipping - $discount;
        
        return [
            'user_id' => User::factory(),
            'order_number' => 'ORD-' . $this->faker->unique()->numerify('######'),
            'status' => $this->faker->randomElement(['pending', 'processing', 'completed', 'canceled', 'refunded']),
            'total' => $total,
            'subtotal' => $subtotal,
            'tax' => $tax,
            'shipping' => $shipping,
            'discount' => $discount,
            'billing_name' => $this->faker->name(),
            'billing_email' => $this->faker->email(),
            'billing_phone' => $this->faker->phoneNumber(),
            'billing_address' => $this->faker->streetAddress(),
            'billing_city' => $this->faker->city(),
            'billing_state' => $this->faker->state(),
            'billing_zipcode' => $this->faker->postcode(),
            'billing_country' => $this->faker->country(),
            'shipping_name' => $this->faker->name(),
            'shipping_email' => $this->faker->email(),
            'shipping_phone' => $this->faker->phoneNumber(),
            'shipping_address' => $this->faker->streetAddress(),
            'shipping_city' => $this->faker->city(),
            'shipping_state' => $this->faker->state(),
            'shipping_zipcode' => $this->faker->postcode(),
            'shipping_country' => $this->faker->country(),
            'notes' => $this->faker->optional(0.3)->paragraph(),
            'payment_status' => $this->faker->randomElement(['pending', 'paid', 'failed', 'refunded']),
        ];
    }

    /**
     * Indicate that the order is pending.
     *
     * @return static
     */
    public function pending()
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => 'pending',
                'payment_status' => 'pending',
            ];
        });
    }

    /**
     * Indicate that the order is completed.
     *
     * @return static
     */
    public function completed()
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => 'completed',
                'payment_status' => 'paid',
            ];
        });
    }

    /**
     * Indicate that the order is cancelled.
     *
     * @return static
     */
    public function cancelled()
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => 'canceled',
                'payment_status' => 'refunded',
            ];
        });
    }


}