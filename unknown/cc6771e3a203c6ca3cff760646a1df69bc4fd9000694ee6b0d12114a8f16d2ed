<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Component;
use App\Models\ComponentCategory;

class ComponentSeeder extends Seeder
{
    public function run()
    {
        $cpuCategory = ComponentCategory::where('slug', 'cpu')->first();
        $mbCategory = ComponentCategory::where('slug', 'motherboard')->first();

        Component::create([
            'name' => 'Intel Core i7',
            'slug' => 'intel-core-i7',
            'description' => '8-core CPU',
            'category_id' => $cpuCategory->id,
            'brand' => 'Intel',
            'model' => 'i7-11700K',
            'price' => 320.00,
            'stock' => 10,
            'image' => 'intel-i7.jpg',
            'specs' => [
                'socket' => 'LGA1200',
                'power_consumption' => '125W',
            ],
            'is_featured' => true,
            'is_active' => true,
        ]);

        Component::create([
            'name' => 'ASUS Prime Z590',
            'slug' => 'asus-prime-z590',
            'description' => 'Motherboard for Intel CPUs',
            'category_id' => $mbCategory->id,
            'brand' => 'ASUS',
            'model' => 'Prime Z590',
            'price' => 200.00,
            'stock' => 5,
            'image' => 'asus-z590.jpg',
            'specs' => [
                'socket' => 'LGA1200',
                'ram_slots' => 4,
            ],
            'is_featured' => false,
            'is_active' => true,
        ]);
    }
}