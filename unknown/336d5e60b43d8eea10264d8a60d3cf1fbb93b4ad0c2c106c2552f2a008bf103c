<?php

use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

afterEach(function () {
    // Clean up session and auth state after each test to prevent leakage.
    if (auth()->check()) {
        Auth::logout();
    }
    session()->flush();
});

test('admin login screen can be rendered', function () {
    $response = $this->get(route('admin.login'));

    $response->assertStatus(200);
    $response->assertViewIs('admin.auth.login');
});

test('admin can authenticate using the login screen', function () {
    // Create an admin user
    $admin = User::factory()->create([
        'role' => User::ROLE_ADMIN,
        'status' => User::STATUS_ACTIVE,
    ]);

    $response = $this->post(route('admin.login.post'), [
        'email' => $admin->email,
        'password' => 'password',
    ]);

    $this->assertAuthenticated();
    $response->assertRedirect(route('admin.dashboard', absolute: false));
});

test('non-admin users cannot authenticate to admin panel', function () {
    // Create a regular user
    $user = User::factory()->create([
        'role' => User::ROLE_USER,
        'status' => User::STATUS_ACTIVE,
    ]);

    $response = $this->post(route('admin.login.post'), [
        'email' => $user->email,
        'password' => 'password',
    ]);

    // User should be logged out after attempting to access admin
    $this->assertGuest();
    $response->assertRedirect(route('admin.login'));
    $response->assertSessionHas('error', 'You do not have admin privileges.');
});

test('inactive admin users cannot authenticate', function () {
    // Create an inactive admin user
    $inactiveAdmin = User::factory()->create([
        'role' => User::ROLE_ADMIN,
        'status' => User::STATUS_INACTIVE,
    ]);

    $response = $this->post(route('admin.login.post'), [
        'email' => $inactiveAdmin->email,
        'password' => 'password',
    ]);

    // User should be logged out after attempting to access admin
    $this->assertGuest();
    $response->assertRedirect(route('admin.login'));
    $response->assertSessionHas('error', 'Your account is inactive. Please contact the administrator.');
});

test('admin can logout', function () {
    $admin = User::factory()->create([
        'role' => User::ROLE_ADMIN,
        'status' => User::STATUS_ACTIVE,
    ]);

    $response = $this->actingAs($admin)->post(route('admin.logout'));

    $this->assertGuest();
    $response->assertRedirect(route('admin.login'));
});

test('admin login validates required fields', function () {
    // Submit the form with empty fields
    $response = $this->post(route('admin.login.post'), [
        'email' => '',
        'password' => '',
    ]);

    $response->assertSessionHasErrors(['email', 'password']);
});

test('admin login fails with invalid credentials', function () {
    $admin = User::factory()->create([
        'role' => User::ROLE_ADMIN,
        'status' => User::STATUS_ACTIVE,
    ]);

    $response = $this->post(route('admin.login.post'), [
        'email' => $admin->email,
        'password' => 'wrong-password',
    ]);

    $this->assertGuest();
    $response->assertSessionHasErrors('email');
});