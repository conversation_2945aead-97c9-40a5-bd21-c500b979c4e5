@props([
    'transaction' => null,
    'status' => 'pending',
    'showDetails' => true,
    'showActions' => true,
    'compact' => false
])

@php
$statusConfig = [
    'completed' => [
        'color' => 'green',
        'icon' => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>',
        'title' => 'Payment Successful!',
        'message' => 'Your payment has been processed successfully.',
        'bgClass' => 'bg-green-50 border-green-200',
        'iconClass' => 'bg-green-100 text-green-600',
        'titleClass' => 'text-green-900',
        'messageClass' => 'text-green-700'
    ],
    'failed' => [
        'color' => 'red',
        'icon' => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>',
        'title' => 'Payment Failed',
        'message' => 'Unfortunately, your payment could not be processed.',
        'bgClass' => 'bg-red-50 border-red-200',
        'iconClass' => 'bg-red-100 text-red-600',
        'titleClass' => 'text-red-900',
        'messageClass' => 'text-red-700'
    ],
    'pending' => [
        'color' => 'yellow',
        'icon' => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>',
        'title' => 'Payment Pending',
        'message' => 'Your payment is being processed. Please wait.',
        'bgClass' => 'bg-yellow-50 border-yellow-200',
        'iconClass' => 'bg-yellow-100 text-yellow-600',
        'titleClass' => 'text-yellow-900',
        'messageClass' => 'text-yellow-700'
    ],
    'processing' => [
        'color' => 'blue',
        'icon' => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>',
        'title' => 'Processing Payment',
        'message' => 'Your payment is currently being processed.',
        'bgClass' => 'bg-blue-50 border-blue-200',
        'iconClass' => 'bg-blue-100 text-blue-600',
        'titleClass' => 'text-blue-900',
        'messageClass' => 'text-blue-700'
    ],
    'cancelled' => [
        'color' => 'gray',
        'icon' => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728"></path>',
        'title' => 'Payment Cancelled',
        'message' => 'The payment was cancelled.',
        'bgClass' => 'bg-gray-50 border-gray-200',
        'iconClass' => 'bg-gray-100 text-gray-600',
        'titleClass' => 'text-gray-900',
        'messageClass' => 'text-gray-700'
    ]
];

$currentStatus = $transaction ? $transaction->status : $status;
$config = $statusConfig[$currentStatus] ?? $statusConfig['pending'];
@endphp

<div class="transaction-status {{ $compact ? 'compact' : '' }}" {{ $attributes }}>
    <div class="status-card {{ $config['bgClass'] }} border rounded-lg p-{{ $compact ? '4' : '6' }}">
        <div class="flex items-start {{ $compact ? 'space-x-3' : 'space-x-4' }}">
            <!-- Status Icon -->
            <div class="flex-shrink-0">
                <div class="w-{{ $compact ? '10' : '12' }} h-{{ $compact ? '10' : '12' }} {{ $config['iconClass'] }} rounded-full flex items-center justify-center">
                    @if($currentStatus === 'processing')
                        <svg class="w-{{ $compact ? '5' : '6' }} h-{{ $compact ? '5' : '6' }} animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            {!! $config['icon'] !!}
                        </svg>
                    @else
                        <svg class="w-{{ $compact ? '5' : '6' }} h-{{ $compact ? '5' : '6' }}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            {!! $config['icon'] !!}
                        </svg>
                    @endif
                </div>
            </div>
            
            <!-- Status Content -->
            <div class="flex-1 min-w-0">
                <h3 class="text-{{ $compact ? 'lg' : 'xl' }} font-semibold {{ $config['titleClass'] }} mb-1">
                    {{ $config['title'] }}
                </h3>
                <p class="text-{{ $compact ? 'sm' : 'base' }} {{ $config['messageClass'] }} mb-{{ $compact ? '2' : '4' }}">
                    {{ $config['message'] }}
                </p>
                
                @if($transaction && $transaction->failure_reason && $currentStatus === 'failed')
                    <div class="mt-2 p-3 bg-red-100 border border-red-200 rounded-md">
                        <p class="text-sm text-red-800 font-medium">Error Details:</p>
                        <p class="text-sm text-red-700 mt-1">{{ $transaction->failure_reason }}</p>
                    </div>
                @endif
                
                @if($showDetails && $transaction && !$compact)
                    <div class="transaction-details mt-4 bg-white bg-opacity-50 rounded-lg p-4">
                        <h4 class="text-sm font-medium text-gray-900 mb-3">Transaction Details</h4>
                        <div class="grid grid-cols-1 sm:grid-cols-2 gap-3 text-sm">
                            <div class="detail-item">
                                <span class="text-gray-600">Transaction ID:</span>
                                <span class="font-mono font-medium text-gray-900 ml-2">{{ $transaction->transaction_id }}</span>
                            </div>
                            
                            <div class="detail-item">
                                <span class="text-gray-600">Amount:</span>
                                <span class="font-semibold text-gray-900 ml-2">
                                    {{ $transaction->currency }} {{ number_format($transaction->amount, 2) }}
                                </span>
                            </div>
                            
                            <div class="detail-item">
                                <span class="text-gray-600">Payment Gateway:</span>
                                <span class="font-medium text-gray-900 ml-2">{{ ucfirst($transaction->gateway_name) }}</span>
                            </div>
                            
                            <div class="detail-item">
                                <span class="text-gray-600">Date & Time:</span>
                                <span class="font-medium text-gray-900 ml-2">{{ $transaction->created_at->format('M d, Y H:i') }}</span>
                            </div>
                            
                            @if($transaction->gateway_transaction_id)
                                <div class="detail-item sm:col-span-2">
                                    <span class="text-gray-600">Gateway Reference:</span>
                                    <span class="font-mono text-sm text-gray-900 ml-2">{{ $transaction->gateway_transaction_id }}</span>
                                </div>
                            @endif
                        </div>
                    </div>
                @endif
            </div>
        </div>
        
        @if($showActions && !$compact)
            <div class="status-actions mt-6 flex flex-col sm:flex-row gap-3">
                @if($currentStatus === 'completed')
                    <a href="{{ route('payment.create') }}" 
                       class="inline-flex items-center justify-center px-4 py-2 bg-indigo-600 text-white text-sm font-medium rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition-colors">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        Make Another Payment
                    </a>
                    
                    @if($transaction)
                        <button type="button" onclick="downloadReceipt('{{ $transaction->id }}')"
                                class="inline-flex items-center justify-center px-4 py-2 bg-gray-100 text-gray-700 text-sm font-medium rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                            Download Receipt
                        </button>
                    @endif
                    
                @elseif($currentStatus === 'failed')
                    <a href="{{ route('payment.create') }}" 
                       class="inline-flex items-center justify-center px-4 py-2 bg-indigo-600 text-white text-sm font-medium rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition-colors">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                        </svg>
                        Try Again
                    </a>
                    
                @elseif($currentStatus === 'pending' || $currentStatus === 'processing')
                    @if($transaction)
                        <button type="button" onclick="checkPaymentStatus('{{ $transaction->id }}')"
                                class="inline-flex items-center justify-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                            </svg>
                            Check Status
                        </button>
                    @endif
                @endif
                
                <a href="/" 
                   class="inline-flex items-center justify-center px-4 py-2 bg-gray-100 text-gray-700 text-sm font-medium rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
                    </svg>
                    Back to Home
                </a>
            </div>
        @endif
    </div>
    
    @if($currentStatus === 'pending' || $currentStatus === 'processing')
        <!-- Auto-refresh for pending/processing status -->
        <div class="mt-4 text-center">
            <div class="inline-flex items-center text-sm text-gray-600">
                <svg class="animate-spin -ml-1 mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <span>Auto-refreshing status...</span>
            </div>
        </div>
    @endif
</div>

<script>
// Auto-refresh for pending/processing transactions
@if(($transaction && in_array($transaction->status, ['pending', 'processing'])) || in_array($status, ['pending', 'processing']))
let statusCheckInterval;

function startStatusCheck() {
    @if($transaction)
    statusCheckInterval = setInterval(function() {
        checkPaymentStatus('{{ $transaction->id }}', true);
    }, 5000); // Check every 5 seconds
    @endif
}

function stopStatusCheck() {
    if (statusCheckInterval) {
        clearInterval(statusCheckInterval);
    }
}

// Start auto-refresh when page loads
document.addEventListener('DOMContentLoaded', function() {
    startStatusCheck();
});

// Stop auto-refresh when page is hidden
document.addEventListener('visibilitychange', function() {
    if (document.hidden) {
        stopStatusCheck();
    } else {
        startStatusCheck();
    }
});
@endif

// Payment status checking function
async function checkPaymentStatus(transactionId, isAutoRefresh = false) {
    try {
        if (!isAutoRefresh) {
            // Show loading state for manual checks
            const button = event.target;
            const originalText = button.innerHTML;
            button.disabled = true;
            button.innerHTML = '<svg class="animate-spin -ml-1 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>Checking...';
        }
        
        const response = await fetch(`/payment/status/${transactionId}`, {
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            }
        });
        
        const result = await response.json();
        
        // If status changed, reload the page to show updated status
        if (result.status === 'completed' || result.status === 'failed') {
            if (isAutoRefresh) {
                location.reload();
            } else {
                window.location.href = result.status === 'completed' 
                    ? `/payment/success/${transactionId}` 
                    : `/payment/failed/${transactionId}`;
            }
        }
        
        if (!isAutoRefresh) {
            // Reset button state
            setTimeout(() => {
                button.disabled = false;
                button.innerHTML = originalText;
            }, 1000);
        }
        
    } catch (error) {
        console.error('Status check failed:', error);
        if (!isAutoRefresh) {
            alert('Failed to check payment status. Please try again.');
        }
    }
}

// Download receipt function
function downloadReceipt(transactionId) {
    window.open(`/payment/receipt/${transactionId}`, '_blank');
}

// Copy transaction ID to clipboard
function copyTransactionId(transactionId) {
    navigator.clipboard.writeText(transactionId).then(function() {
        // Show success message
        const message = document.createElement('div');
        message.className = 'fixed top-4 right-4 bg-green-50 border border-green-200 rounded-lg p-3 shadow-lg z-50';
        message.innerHTML = '<div class="flex items-center text-sm text-green-800"><svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>Transaction ID copied to clipboard</div>';
        document.body.appendChild(message);
        
        setTimeout(() => {
            message.remove();
        }, 3000);
    });
}
</script>

<style>
.transaction-status.compact .status-card {
    @apply p-3;
}

.transaction-status .detail-item {
    @apply flex flex-wrap items-center;
}

@media (max-width: 640px) {
    .transaction-status .status-actions {
        @apply flex-col space-y-2;
    }
    
    .transaction-status .transaction-details .grid {
        @apply grid-cols-1;
    }
}

.transaction-status .animate-spin {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}
</style>