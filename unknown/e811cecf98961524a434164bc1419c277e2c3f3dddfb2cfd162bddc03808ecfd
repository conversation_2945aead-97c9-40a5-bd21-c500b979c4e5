<?php

use App\Models\User;
use App\Models\BlogPost;
use App\Models\Comment;
use App\Models\BlogPostCategory;
use App\Models\BlogPostTag;
use Illuminate\Foundation\Testing\RefreshDatabase;
// use Tests\TestCase; // Add this line

uses(RefreshDatabase::class); // Modify this line

test('it can create a blog post', function () {
    $blogPost = BlogPost::factory()->create([
        'title' => 'Test Blog Post',
        'content' => 'Test Content',
        'is_published' => true
    ]);

    expect($blogPost)->toBeInstanceOf(BlogPost::class)
        ->and($blogPost->title)->toBe('Test Blog Post')
        ->and($blogPost->content)->toBe('Test Content')
        ->and($blogPost->is_published)->toBeTrue();
});

test('it automatically generates slug from title', function () {
    $blogPost = BlogPost::factory()->create([
        'title' => 'Test Blog Post'
    ]);

    expect($blogPost->slug)->toBe('test-blog-post');
});

test('it generates unique slug when duplicate exists', function () {
    BlogPost::factory()->create([
        'title' => 'Test Blog Post',
        'slug' => 'test-blog-post'
    ]);

    $blogPost = BlogPost::factory()->create([
        'title' => 'Test Blog Post'
    ]);

    expect($blogPost->slug)->toBe('test-blog-post-1');
});

test('it belongs to a user', function () {
    $user = User::factory()->create();
    $category = BlogPostCategory::factory()->create(); // Create a category
    $blogPost = BlogPost::factory()->create([
        'user_id' => $user->id,
        'blog_post_category_id' => $category->id, // Pass the category id
    ]);

    expect($blogPost->user)->toBeInstanceOf(User::class);
    expect($blogPost->user->id)->toBe($user->id);
});


test('it has comments relationship', function () {
    $user = User::factory()->create();
    $category = BlogPostCategory::factory()->create();
    $blogPost = BlogPost::factory()->create([
        'user_id' => $user->id,
        'blog_post_category_id' => $category->id,
    ]);
    Comment::factory()->count(3)->create(['blog_post_id' => $blogPost->id, 'user_id' => $user->id]);

    expect($blogPost->comments)->toHaveCount(3);
    expect($blogPost->comments->first())->toBeInstanceOf(Comment::class);
});

test('it belongs to a blog post category', function () {
    $user = User::factory()->create(); // Create a user
    $category = BlogPostCategory::factory()->create();
    $blogPost = BlogPost::factory()->create([
        'user_id' => $user->id, // Pass the user id
        'blog_post_category_id' => $category->id,
    ]);

    expect($blogPost->category)->toBeInstanceOf(BlogPostCategory::class);
    expect($blogPost->category->id)->toBe($category->id);
});

test('it has many tags', function () {
    $user = User::factory()->create();
    $category = BlogPostCategory::factory()->create();
    $blogPost = BlogPost::factory()->create([
        'user_id' => $user->id,
        'blog_post_category_id' => $category->id,
    ]);
    $tags = BlogPostTag::factory()->count(3)->create();
    $blogPost->tags()->attach($tags->pluck('id')->toArray());

    expect($blogPost->tags)->toHaveCount(3);
    expect($blogPost->tags->first())->toBeInstanceOf(BlogPostTag::class);
});

test('it can scope published posts', function () {
    BlogPost::factory()->create(['is_published' => true]);
    BlogPost::factory()->create(['is_published' => false]);

    $publishedPosts = BlogPost::published()->get();
    
    expect($publishedPosts)->toHaveCount(1)
        ->and($publishedPosts->first()->is_published)->toBeTrue();
});

test('it can scope featured posts', function () {
    BlogPost::factory()->create(['featured' => true]);
    BlogPost::factory()->create(['featured' => false]);

    $featuredPosts = BlogPost::featured()->get();
    
    expect($featuredPosts)->toHaveCount(1)
        ->and($featuredPosts->first()->featured)->toBeTrue();
});

test('it can search posts', function () {
    $blogPost = BlogPost::factory()->create([
        'title' => 'Unique Title',
        'content' => 'Unique Content'
    ]);

    $searchResults = BlogPost::search('Unique')->get();
    
    expect($searchResults)->toHaveCount(1)
        ->and($searchResults->first()->id)->toBe($blogPost->id);
});

test('it can search posts by tag', function () {
    $blogPost = BlogPost::factory()->create();
    $tag = BlogPostTag::factory()->create(['slug' => 'test-tag']);
    $blogPost->tags()->attach($tag);

    $searchResults = BlogPost::byTag('test-tag')->get();
    
    expect($searchResults)->toHaveCount(1)
        ->and($searchResults->first()->id)->toBe($blogPost->id);
});

test('it returns correct publish status', function () {
    $publishedPost = BlogPost::factory()->create(['is_published' => true]);
    $draftPost = BlogPost::factory()->create(['is_published' => false]);

    expect($publishedPost->publish_status)->toBe('Published')
        ->and($draftPost->publish_status)->toBe('Draft');
});