<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Build>
 */
class BuildFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $buildNames = [
            'Gaming Beast', 'Budget Builder', 'Workstation Pro', 'Silent Runner',
            'RGB Master', 'Compact Power', 'Ultimate Gaming', 'Content Creator',
            'Office Warrior', 'Streaming Setup'
        ];
        
        return [
            'user_id' => \App\Models\User::factory(),
            'name' => $this->faker->randomElement($buildNames) . ' ' . $this->faker->year(),
            'description' => $this->faker->paragraph(),
            'is_public' => $this->faker->boolean(30),
            'is_complete' => $this->faker->boolean(70),
            'total_price' => $this->faker->randomFloat(2, 500, 5000),
            'compatibility_issues' => $this->faker->boolean(10) ? ['Some compatibility warning'] : null,
        ];
    }
}
