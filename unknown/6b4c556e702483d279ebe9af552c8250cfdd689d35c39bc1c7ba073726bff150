@extends('layouts.app')

@section('title', $component->name)

@section('content')
    <div class="bg-gray-100 min-h-screen">
        <div class="py-8">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="bg-white rounded-lg shadow-md p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                        <!-- Product Image -->
                        <div>
                            @if($component->image)
                                <img src="{{ $component->image }}" alt="{{ $component->name }}" class="w-full h-96 object-cover rounded-lg">
                            @else
                                <div class="w-full h-96 bg-gray-200 rounded-lg flex items-center justify-center">
                                    <span class="text-gray-500">No Image Available</span>
                                </div>
                            @endif
                        </div>

                        <!-- Product Details -->
                        <div>
                            <h1 class="text-3xl font-bold text-gray-900 mb-4">{{ $component->name }}</h1>
                            
                            <div class="mb-4">
                                <span class="text-sm text-gray-500">Brand:</span>
                                <span class="text-lg font-medium text-gray-900">{{ $component->brand }}</span>
                            </div>

                            <div class="mb-4">
                                <span class="text-sm text-gray-500">Category:</span>
                                <span class="text-lg font-medium text-gray-900">{{ $component->category->name ?? 'N/A' }}</span>
                            </div>

                            <div class="mb-6">
                                <span class="text-3xl font-bold text-blue-600">${{ number_format($component->price, 2) }}</span>
                            </div>

                            <div class="mb-6">
                                @if($component->stock > 0)
                                    <span class="text-green-600 font-medium">In Stock ({{ $component->stock }} available)</span>
                                @else
                                    <span class="text-red-600 font-medium">Out of Stock</span>
                                @endif
                            </div>

                            @if($component->description)
                                <div class="mb-6">
                                    <h3 class="text-lg font-semibold mb-2">Description</h3>
                                    <p class="text-gray-700">{{ $component->description }}</p>
                                </div>
                            @endif

                            @if($component->specifications)
                                <div class="mb-6">
                                    <h3 class="text-lg font-semibold mb-2">Specifications</h3>
                                    <div class="space-y-2">
                                        @foreach($component->specifications as $key => $value)
                                            <div class="flex justify-between">
                                                <span class="text-gray-600">{{ ucfirst(str_replace('_', ' ', $key)) }}:</span>
                                                <span class="text-gray-900">{{ $value }}</span>
                                            </div>
                                        @endforeach
                                    </div>
                                </div>
                            @endif

                            <!-- Add to Cart Button -->
                            @if($component->stock > 0)
                                <form action="{{ route('cart.add') }}" method="POST" class="mb-4">
                                    @csrf
                                    <input type="hidden" name="component_id" value="{{ $component->id }}">
                                    <div class="flex items-center space-x-4 mb-4">
                                        <label for="quantity" class="text-sm font-medium text-gray-700">Quantity:</label>
                                        <input type="number" id="quantity" name="quantity" value="1" min="1" max="{{ $component->stock }}" 
                                               class="w-20 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    </div>
                                    <button type="submit" 
                                            class="w-full bg-blue-600 text-white py-3 px-6 rounded-lg font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                                        Add to Cart
                                    </button>
                                </form>
                            @endif

                            <!-- Back to Shop -->
                            <a href="{{ route('shop.index') }}" 
                               class="inline-flex items-center text-blue-600 hover:text-blue-800">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                                </svg>
                                Back to Shop
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection