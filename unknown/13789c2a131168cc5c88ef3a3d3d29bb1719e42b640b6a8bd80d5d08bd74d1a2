<?php

namespace Tests\Unit\Models;

use App\Models\BlogPost;
use App\Models\BlogPostTag;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Str;
use Tests\TestCase;

uses(RefreshDatabase::class);

it('automatically generates a slug when the name is set', function () {
    $name = 'A Test Tag';
    $expectedSlug = Str::slug($name);

    // Test using the mutator directly
    $tag = new BlogPostTag();
    $tag->name = $name;

    expect($tag->slug)->toBe($expectedSlug);

    // Test via mass assignment/creation
    $tagFromCreate = BlogPostTag::factory()->create(['name' => 'Another Test Tag']);
    expect($tagFromCreate->slug)->toBe('another-test-tag');
});


it('has a posts relationship', function () {
    $tag = BlogPostTag::factory()->create();
    $posts = BlogPost::factory()->count(3)->create();

    $tag->posts()->attach($posts->pluck('id'));

    // Assert the relationship type
    expect($tag->posts())->toBeInstanceOf(BelongsToMany::class);

    // Assert the posts are correctly associated
    expect($tag->posts)->toHaveCount(3);
    expect($tag->posts->first())->toBeInstanceOf(BlogPost::class);
});

it('can be created with a factory', function () {
    $tag = BlogPostTag::factory()->create();
    expect($tag)->toBeInstanceOf(BlogPostTag::class);
    $this->assertDatabaseHas('blog_post_tags', ['id' => $tag->id]);
}); 