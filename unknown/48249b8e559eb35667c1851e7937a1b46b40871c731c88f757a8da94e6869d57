<?php

namespace App\Jobs;

use App\Mail\BuildShared;
use App\Models\Build;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;

class SendBuildShared implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $build;
    public $recipientEmail;
    public $tries = 3;
    public $backoff = [60, 300, 600];

    public function __construct(Build $build, $recipientEmail)
    {
        $this->build = $build;
        $this->recipientEmail = $recipientEmail;
    }

    public function handle()
    {
        try {
            $sharedBy = $this->build->user;
            Mail::to($this->recipientEmail)->send(new BuildShared($this->build, $sharedBy, $this->recipientEmail));
            Log::info('Build shared email sent', ['build_id' => $this->build->id, 'recipient' => $this->recipientEmail]);
        } catch (\Exception $e) {
            Log::error('Failed to send build shared email', ['build_id' => $this->build->id, 'recipient' => $this->recipientEmail, 'error' => $e->getMessage()]);
            throw $e;
        }
    }

    public function failed($exception)
    {
        Log::critical('Build shared email job failed', ['build_id' => $this->build->id, 'recipient' => $this->recipientEmail, 'error' => $exception->getMessage()]);
    }
} 