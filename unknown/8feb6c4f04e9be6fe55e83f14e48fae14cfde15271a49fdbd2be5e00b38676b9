@extends('layouts.app')
@section('content')
    <div class="bg-nexus-dark-900 dark:bg-nexus-dark-950 text-white transition-colors duration-300">
        @include('sections.hero')
        @include('sections.features')
        @include('sections.testimonials')
        @include('sections.comparison')
        @include('sections.technology')
        @include('sections.faq')
        @include('sections.builds')
        @include('sections.cta')
    </div>
@endsection

@push('styles')
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Inter:wght@300;400;500;600&display=swap');

        .tech-font {
            font-family: 'Orbitron', monospace;
        }

        .content-font {
            font-family: 'Inter', sans-serif;
        }

        .cyber-grid {
            background-image:
                linear-gradient(rgba(59, 130, 246, 0.1) 1px, transparent 1px),
                linear-gradient(90deg, rgba(59, 130, 246, 0.1) 1px, transparent 1px);
            background-size: 20px 20px;
            animation: grid-move 20s linear infinite;
        }

        @keyframes grid-move {
            0% {
                background-position: 0 0;
            }

            100% {
                background-position: 20px 20px;
            }
        }

        .glow {
            box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
        }

        .neon-text {
            text-shadow: 0 0 10px rgba(59, 130, 246, 0.5);
        }

        .component-icon {
            transition: all 0.3s ease;
            position: relative;
        }

        .component-icon:hover {
            transform: scale(1.1);
            filter: drop-shadow(0 0 15px rgba(59, 130, 246, 0.6));
        }

        .tooltip {
            position: absolute;
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 12px 16px;
            border-radius: 8px;
            font-size: 12px;
            border: 1px solid rgba(59, 130, 246, 0.3);
            opacity: 0;
            pointer-events: none;
            transform: translateY(10px);
            transition: all 0.3s ease;
            z-index: 1000;
            min-width: 200px;
        }

        .tooltip.show {
            opacity: 1;
            transform: translateY(0);
        }

        .stat-card {
            background: linear-gradient(135deg, rgba(15, 23, 42, 0.8) 0%, rgba(30, 41, 59, 0.8) 100%);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(59, 130, 246, 0.2);
        }

        .pulse-ring {
            animation: pulse-ring 2s infinite;
        }

        @keyframes pulse-ring {
            0% {
                transform: scale(0.8);
                opacity: 1;
            }

            100% {
                transform: scale(1.2);
                opacity: 0;
            }
        }

        .floating {
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {

            0%,
            100% {
                transform: translateY(0px);
            }

            50% {
                transform: translateY(-20px);
            }
        }
    </style>
@endpush

@push('scripts')
    <script>
        // Initialize GSAP animations
        gsap.registerPlugin();

        // Animated counter for performance stats
        function animateCounter(id, target, duration = 2000, suffix = '') {
            const element = document.getElementById(id);
            const increment = target / (duration / 16);
            let current = 0;

            const timer = setInterval(() => {
                current += increment;
                if (current >= target) {
                    current = target;
                    clearInterval(timer);
                }
                element.textContent = Math.floor(current) + suffix;
            }, 16);
        }

        // Rotating taglines
        const taglines = [
            "Silent. Powerful. Smart.",
            "Performance Redefined.",
            "Future-Ready Computing.",
            "Precision Engineering.",
            "Next-Gen Performance."
        ];

        let currentTagline = 0;

        function rotateTagline() {
            const taglineEl = document.getElementById('tagline');

            gsap.to(taglineEl, {
                opacity: 0,
                y: -20,
                duration: 0.5,
                ease: "power2.out",
                onComplete: () => {
                    currentTagline = (currentTagline + 1) % taglines.length;
                    taglineEl.textContent = taglines[currentTagline];
                    gsap.to(taglineEl, {
                        opacity: 1,
                        y: 0,
                        duration: 0.5,
                        ease: "power2.out"
                    });
                }
            });
        }

        // Component tooltip data
        const tooltipData = {
            cpu: {
                title: "Intel Core i9-13900KS",
                specs: [
                    "24 Cores, 32 Threads",
                    "Base: 3.2GHz, Boost: 6.0GHz",
                    "Performance: 98,500 PassMark",
                    "TDP: 150W"
                ]
            },
            gpu: {
                title: "NVIDIA RTX 4090",
                specs: [
                    "16,384 CUDA Cores",
                    "24GB GDDR6X Memory",
                    "4K Gaming: 120+ FPS",
                    "Ray Tracing: Ultimate"
                ]
            },
            ram: {
                title: "DDR5-6000 32GB",
                specs: [
                    "32GB (2x16GB) Kit",
                    "Speed: 6000 MT/s",
                    "Latency: CL30",
                    "RGB Lighting"
                ]
            },
            storage: {
                title: "Samsung 990 PRO 2TB",
                specs: [
                    "PCIe 4.0 NVMe SSD",
                    "Read: 7,450 MB/s",
                    "Write: 6,900 MB/s",
                    "Endurance: 1,200 TBW"
                ]
            }
        };

        // Tooltip functionality
        const tooltip = document.getElementById('tooltip');
        const componentIcons = document.querySelectorAll('.component-icon');

        componentIcons.forEach(icon => {
            icon.addEventListener('mouseenter', (e) => {
                const componentType = e.currentTarget.dataset.tooltip;
                const data = tooltipData[componentType];

                if (data) {
                    const specsHTML = data.specs.map(spec => `<div class="mb-1">${spec}</div>`).join('');
                    tooltip.innerHTML = `
                <div class="font-semibold text-nexus-primary-400 mb-2">${data.title}</div>
                <div class="text-nexus-gray-300">${specsHTML}</div>
            `;

                    const rect = e.currentTarget.getBoundingClientRect();
                    tooltip.style.left = rect.left + rect.width / 2 - tooltip.offsetWidth / 2 + 'px';
                    tooltip.style.top = rect.top - tooltip.offsetHeight - 10 + 'px';
                    tooltip.classList.add('show');
                }
            });

            icon.addEventListener('mouseleave', () => {
                tooltip.classList.remove('show');
            });
        });

        // Real-time performance simulation
        function simulatePerformance() {
            const fpsBase = 165;
            const tempBase = 68;
            const wattsBase = 125;

            // Simulate fluctuations
            const fpsVariation = Math.random() * 20 - 10;
            const tempVariation = Math.random() * 10 - 5;
            const wattsVariation = Math.random() * 30 - 15;

            const targetFPS = Math.max(30, fpsBase + fpsVariation);
            const targetTemp = Math.max(35, tempBase + tempVariation);
            const targetWatts = Math.max(50, wattsBase + wattsVariation);

            // Animate to new values
            gsap.to({
                fps: parseInt(document.getElementById('fps-counter').textContent)
            }, {
                fps: targetFPS,
                duration: 2,
                ease: "power2.out",
                onUpdate: function() {
                    document.getElementById('fps-counter').textContent = Math.floor(this.targets()[0].fps);
                }
            });

            gsap.to({
                temp: parseInt(document.getElementById('temp-counter').textContent)
            }, {
                temp: targetTemp,
                duration: 2,
                ease: "power2.out",
                onUpdate: function() {
                    document.getElementById('temp-counter').textContent = Math.floor(this.targets()[0].temp);
                }
            });

            gsap.to({
                watts: parseInt(document.getElementById('watts-counter').textContent)
            }, {
                watts: targetWatts,
                duration: 2,
                ease: "power2.out",
                onUpdate: function() {
                    document.getElementById('watts-counter').textContent = Math.floor(this.targets()[0].watts);
                }
            });
        }

        // Initialize animations on page load
        document.addEventListener('DOMContentLoaded', () => {
            // Animate headline entrance
            gsap.from('#headline', {
                opacity: 0,
                y: 50,
                duration: 1.5,
                ease: "power3.out"
            });

            // Animate tagline entrance
            gsap.from('#tagline', {
                opacity: 0,
                y: 30,
                duration: 1,
                delay: 0.5,
                ease: "power2.out"
            });

            // Animate stat cards
            gsap.from('.stat-card', {
                opacity: 0,
                y: 30,
                duration: 0.8,
                stagger: 0.2,
                delay: 1,
                ease: "power2.out"
            });

            // Animate component icons
            gsap.from('.component-icon', {
                opacity: 0,
                scale: 0.8,
                duration: 0.6,
                stagger: 0.1,
                delay: 1.5,
                ease: "back.out(1.7)"
            });

            // Start initial counter animations
            setTimeout(() => {
                animateCounter('fps-counter', 165);
                animateCounter('temp-counter', 68);
                animateCounter('watts-counter', 125);
            }, 2000);

            // Start periodic updates
            setInterval(simulatePerformance, 5000);
            setInterval(rotateTagline, 4000);
        });
    </script>
@endpush