APP_NAME=Laravel
APP_ENV=dusk.local
APP_KEY=base64:nyoDy0oYq3VyFIg39KoEAkD7deycr5js/8v/rNzpY/Y=
APP_DEBUG=true
APP_TIMEZONE=UTC
APP_URL=http://127.0.0.1:8000
APP_DEMO=true

APP_LOCALE=en
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=en_US

APP_MAINTENANCE_DRIVER=file

PHP_CLI_SERVER_WORKERS=4

BCRYPT_ROUNDS=4

LOG_CHANNEL=stack
LOG_STACK=single
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

# Use SQLite for faster testing
DB_CONNECTION=sqlite
DB_DATABASE=:memory:

SESSION_DRIVER=array
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=null

BROADCAST_CONNECTION=log
FILESYSTEM_DISK=local
QUEUE_CONNECTION=sync

CACHE_STORE=array
CACHE_PREFIX=

MAIL_MAILER=array

# Dusk specific settings
DUSK_DRIVER_URL=http://localhost:9515
DUSK_HEADLESS=true

# Payment Gateway Configuration (Test Mode)
PAYMENT_DEFAULT_GATEWAY=razorpay
PAYMENT_DEFAULT_CURRENCY=INR
PAYMENT_WEBHOOK_TIMEOUT=30
PAYMENT_MAX_RETRY_ATTEMPTS=3
PAYMENT_LOG_LEVEL=info
PAYMENT_LOG_CHANNEL=single
PAYMENT_ENCRYPT_DETAILS=false
PAYMENT_WEBHOOK_RATE_LIMIT=60
PAYMENT_REQUIRE_CSRF=false

# Test Payment Gateway Credentials
RAZORPAY_ENABLED=true
RAZORPAY_TEST_MODE=true
RAZORPAY_KEY_ID=test_key
RAZORPAY_KEY_SECRET=test_secret
RAZORPAY_WEBHOOK_SECRET=test_webhook_secret

PAYUMONEY_ENABLED=true
PAYUMONEY_TEST_MODE=true
PAYUMONEY_MERCHANT_KEY=test_merchant_key
PAYUMONEY_SALT=test_salt
PAYUMONEY_AUTH_HEADER=test_auth

CASHFREE_ENABLED=true
CASHFREE_TEST_MODE=true
CASHFREE_APP_ID=test_app_id
CASHFREE_SECRET_KEY=test_secret_key
CASHFREE_CLIENT_ID=test_client_id
CASHFREE_CLIENT_SECRET=test_client_secret