<div>
    <div class="bg-white rounded-lg shadow p-6 mb-6">
        <h2 class="text-xl font-semibold mb-4">Component Categories</h2>
        
        <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
            @foreach($categories as $category)
                <div 
                    wire:click="selectCategory('{{ $category->slug }}')" 
                    class="cursor-pointer p-4 rounded-lg border transition-all {{ $selectedCategorySlug === $category->slug ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-blue-300 hover:bg-blue-50' }}"
                >
                    <div class="flex flex-col items-center text-center">
                        @if($category->icon)
                            <div class="text-gray-500 mb-2">
                                <i class="{{ $category->icon }} text-2xl"></i>
                            </div>
                        @else
                            <div class="w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center mb-2">
                                <span class="text-gray-500 text-lg font-bold">{{ substr($category->name, 0, 1) }}</span>
                            </div>
                        @endif
                        
                        <span class="text-sm font-medium">{{ $category->name }}</span>
                        
                        @if($category->is_required)
                            <span class="text-xs text-red-500 mt-1">Required</span>
                        @endif
                        
                        @if(isset($category->has_component) && $category->has_component)
                            <span class="inline-flex items-center px-2 py-0.5 mt-2 rounded text-xs font-medium bg-green-100 text-green-800">
                                <svg class="-ml-0.5 mr-1.5 h-2 w-2 text-green-400" fill="currentColor" viewBox="0 0 8 8">
                                    <circle cx="4" cy="4" r="3" />
                                </svg>
                                Selected
                            </span>
                        @endif
                    </div>
                </div>
            @endforeach
        </div>
    </div>
</div>