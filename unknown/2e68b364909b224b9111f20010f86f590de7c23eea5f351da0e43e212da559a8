# Design Document

## Overview

The PC Builder Web Application with E-commerce is built on Laravel 11 with Jetstream for authentication, Livewire for reactive components, and Tailwind CSS for styling. The system follows a modular architecture separating the PC builder functionality from the e-commerce components while sharing common services and models.

The application uses a service-oriented architecture with dedicated services for compatibility checking, cart management, and payment processing. Real-time updates are handled through Livewire components, and background tasks are managed through Laravel's queue system.

## Architecture

### Technology Stack
- **Backend**: Laravel 11 with PHP 8.2+
- **Authentication**: Laravel Jetstream with Sanctum
- **Frontend**: Livewire 3.0 with Alpine.js and Tailwind CSS
- **Database**: SQLite (development) / MySQL (production)
- **Queue System**: Laravel Queues for background processing
- **Email**: Laravel Mail with queue support
- **Asset Building**: Vite with Laravel plugin

### System Architecture

```mermaid
graph TB
    subgraph "Frontend Layer"
        A[Blade Templates] --> B[Livewire Components]
        B --> C[Alpine.js Interactions]
    end
    
    subgraph "Application Layer"
        D[Controllers] --> E[Services]
        F[Livewire Components] --> E
        E --> G[Models]
    end
    
    subgraph "Data Layer"
        G --> H[Database]
        I[Queue Jobs] --> H
        J[Mail System] --> K[Email Templates]
    end
    
    subgraph "External Services"
        L[Payment Gateway]
        M[Price Tracking APIs]
    end
    
    E --> L
    E --> M
    I --> J
```
## 
Components and Interfaces

### Core Models

#### Component Model
```php
class Component extends Model
{
    protected $fillable = [
        'name', 'category_id', 'brand', 'model', 'price', 
        'specifications', 'image_url', 'stock_quantity', 
        'is_active', 'compatibility_data'
    ];
    
    protected $casts = [
        'specifications' => 'array',
        'compatibility_data' => 'array',
        'price' => 'decimal:2'
    ];
}
```

#### Build Model
```php
class Build extends Model
{
    protected $fillable = [
        'user_id', 'name', 'description', 'total_price', 
        'is_public', 'compatibility_status'
    ];
    
    public function components()
    {
        return $this->belongsToMany(Component::class, 'build_components')
                    ->withPivot('quantity', 'price_at_time');
    }
}
```

#### Cart and Order Models
```php
class Cart extends Model
{
    protected $fillable = ['user_id', 'session_id'];
    
    public function items()
    {
        return $this->hasMany(CartItem::class);
    }
}

class Order extends Model
{
    protected $fillable = [
        'user_id', 'order_number', 'status', 'total_amount',
        'shipping_address', 'billing_address', 'payment_status'
    ];
    
    protected $casts = [
        'shipping_address' => 'array',
        'billing_address' => 'array'
    ];
}
```### Se
rvice Layer

#### CompatibilityService
Handles component compatibility checking using predefined rules and component specifications.

```php
class CompatibilityService
{
    public function checkCompatibility(array $components): CompatibilityResult
    public function getCompatibleComponents(Component $baseComponent, string $targetCategory): Collection
    public function validateBuild(Build $build): ValidationResult
}
```

#### BuilderService
Manages PC build creation, modification, and validation.

```php
class BuilderService
{
    public function createBuild(User $user, array $components): Build
    public function addComponent(Build $build, Component $component): bool
    public function removeComponent(Build $build, Component $component): bool
    public function calculateTotalPrice(Build $build): float
}
```

#### CartService
Handles shopping cart operations and session management.

```php
class CartService
{
    public function addToCart(Component $component, int $quantity, ?User $user = null): CartItem
    public function removeFromCart(CartItem $item): bool
    public function getCartTotal(?User $user = null): float
    public function mergeGuestCart(User $user, string $sessionId): void
}
```

#### CheckoutService
Manages the checkout process and order creation.

```php
class CheckoutService
{
    public function processCheckout(Cart $cart, array $shippingData, array $paymentData): Order
    public function calculateShipping(Cart $cart, array $address): float
    public function processPayment(Order $order, array $paymentData): PaymentResult
}
```### Liv
ewire Components

#### Builder Components
- **BuilderContainer**: Main PC builder interface
- **ComponentSelector**: Component selection with filtering and compatibility
- **CompatibilityChecker**: Real-time compatibility validation
- **BuildSummary**: Build overview with pricing and compatibility status
- **SaveBuildForm**: Build saving and sharing functionality

#### Shop Components
- **ProductList**: Component browsing with filtering and search
- **ProductCard**: Individual component display
- **Cart**: Shopping cart management
- **Checkout**: Checkout process handling

#### Admin Components
- **ComponentForm**: Component creation and editing
- **ComponentsList**: Component inventory management
- **OrderManagement**: Order processing and status updates
- **Dashboard**: Admin analytics and overview

## Data Models

### Database Schema

#### Components Table
```sql
CREATE TABLE components (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(255) NOT NULL,
    category_id BIGINT NOT NULL,
    brand VARCHAR(100),
    model VARCHAR(100),
    price DECIMAL(10,2) NOT NULL,
    specifications JSON,
    compatibility_data JSON,
    image_url VARCHAR(500),
    stock_quantity INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    INDEX idx_category (category_id),
    INDEX idx_active_stock (is_active, stock_quantity)
);
```

#### Builds Table
```sql
CREATE TABLE builds (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    total_price DECIMAL(10,2),
    is_public BOOLEAN DEFAULT FALSE,
    compatibility_status ENUM('compatible', 'warning', 'incompatible'),
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    INDEX idx_user_public (user_id, is_public)
);
```#### 
Build Components Pivot Table
```sql
CREATE TABLE build_components (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    build_id BIGINT NOT NULL,
    component_id BIGINT NOT NULL,
    quantity INT DEFAULT 1,
    price_at_time DECIMAL(10,2),
    UNIQUE KEY unique_build_component (build_id, component_id)
);
```

#### Orders and Cart Tables
```sql
CREATE TABLE carts (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NULL,
    session_id VARCHAR(255) NULL,
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    INDEX idx_user (user_id),
    INDEX idx_session (session_id)
);

CREATE TABLE orders (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    order_number VARCHAR(50) UNIQUE NOT NULL,
    status ENUM('pending', 'processing', 'shipped', 'delivered', 'cancelled'),
    total_amount DECIMAL(10,2) NOT NULL,
    shipping_address JSON,
    billing_address JSON,
    payment_status ENUM('pending', 'paid', 'failed', 'refunded'),
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    INDEX idx_user_status (user_id, status),
    INDEX idx_order_number (order_number)
);
```

### Compatibility Rules System

The compatibility system uses a rule-based approach stored in JSON format:

```json
{
  "motherboard_cpu": {
    "socket_match": "required",
    "chipset_compatibility": "required"
  },
  "motherboard_ram": {
    "memory_type": "required",
    "max_capacity": "warning",
    "speed_support": "warning"
  },
  "psu_components": {
    "wattage_calculation": "required",
    "connector_availability": "required"
  }
}
```## Er
ror Handling

### Exception Hierarchy
- **BuilderException**: Base exception for PC builder operations
- **CompatibilityException**: Component compatibility errors
- **CartException**: Shopping cart operation errors
- **CheckoutException**: Checkout process errors
- **PaymentException**: Payment processing errors

### Error Response Format
```php
class ErrorResponse
{
    public string $message;
    public string $code;
    public array $details;
    public ?array $suggestions;
}
```

### Validation Rules
- Component specifications validation
- Build compatibility validation
- Cart quantity and availability validation
- Checkout data validation
- Payment information validation

## Testing Strategy

### Unit Tests
- Model relationships and methods
- Service class functionality
- Compatibility rule validation
- Price calculation logic
- Cart operations

### Feature Tests
- PC builder workflow
- E-commerce checkout process
- User authentication and authorization
- Admin component management
- Order processing

### Integration Tests
- Payment gateway integration
- Email notification system
- Price tracking service
- Database transactions
- Queue job processing

### Browser Tests (Laravel Dusk)
- Complete PC building workflow
- Shopping cart and checkout process
- Admin panel functionality
- Responsive design validation
- JavaScript interactions

### Test Data Management
- Component factory with realistic specifications
- Build factory with valid component combinations
- User factory with different roles
- Order factory with various statuses
- Seeded compatibility rules for testing

### Performance Testing
- Database query optimization
- Livewire component rendering
- Large component catalog handling
- Concurrent user sessions
- Cart and checkout performance