<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class BlogPost extends Model
{
    use HasFactory;

    protected $table = 'blog_posts';

    protected $fillable = [
        'title',
        'content',
        'slug',
        'excerpt',
        'featured_image',
        'user_id',
        'is_published',
        'published_at',
        'meta_title',
        'meta_description',
        'meta_keywords',
        'blog_post_category_id',
        'featured'
    ];


    protected $casts = [
        'is_published' => 'boolean',
        'featured' => 'boolean',
        'published_at' => 'datetime',
        'meta_keywords' => 'json'
    ];

    protected $dates = ['published_at'];

    // Automatically generate a slug when setting the title
    public function setTitleAttribute($value)
    {
        $this->attributes['title'] = $value;

        // Always generate slug if not set or is null/empty
        if (!isset($this->attributes['slug']) || empty($this->attributes['slug'])) {
            $baseSlug = generateSlug($value);
            $slug = $baseSlug;
            $counter = 1;

            // Check if slug exists and append counter until unique
            while (static::where('slug', $slug)->exists()) {
                $slug = $baseSlug . '-' . $counter;
                $counter++;
            }

            $this->attributes['slug'] = $slug;
        }
    }

    // Ensure slug is always set, even if null is passed
    public function setSlugAttribute($value)
    {
        if (empty($value)) {
            // Fallback to title-based slug
            $title = $this->attributes['title'] ?? '';
            $baseSlug = generateSlug($title);
            $slug = $baseSlug;
            $counter = 1;
            while (static::where('slug', $slug)->exists()) {
                $slug = $baseSlug . '-' . $counter;
                $counter++;
            }
            $this->attributes['slug'] = $slug;
        } else {
            $this->attributes['slug'] = $value;
        }
    }

    public function getPublishStatusAttribute()
    {
        return $this->is_published ? 'Published' : 'Draft';
    }

    // Relationship with User (Author)
    public function author()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    // Alias for test compatibility
    public function user()
    {
        return $this->author();
    }

    // Relationship with Comments
    public function comments()
    {
        return $this->hasMany(Comment::class, 'blog_post_id');
    }

    // Scope for published posts
    public function scopePublished($query)
    {
        return $query->where('is_published', true);
    }

    public function scopeFeatured($query)
    {
        return $query->where('featured', true);
    }

    /**
     * Get the category that owns the blog post
     */
    public function category()
    {
        return $this->belongsTo(BlogPostCategory::class, 'blog_post_category_id');
    }

    /**
     * Get the tags for the blog post
     */
    public function tags()
    {
        return $this->belongsToMany(BlogPostTag::class, 'blog_post_tag', 'blog_post_id', 'blog_post_tag_id');
    }

    // Add new scopes
    public function scopeWithBasicRelations($query)
    {
        return $query->with(['author', 'category', 'tags']);
    }

    public function scopeSearch($query, $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('title', 'like', "%{$search}%")
                ->orWhere('content', 'like', "%{$search}%")
                ->orWhereHas('tags', function ($q) use ($search) {
                    $q->where('name', 'like', "%{$search}%");
                });
        });
    }

    public function scopeByTag($query, $tag)
    {
        return $query->whereHas('tags', function ($q) use ($tag) {
            $q->where('slug', $tag);
        });
    }

    /**
     * Calculate reading time in minutes
     * Average reading speed is 200-250 words per minute
     */
    public function getReadingTimeAttribute()
    {
        $wordCount = str_word_count(strip_tags($this->content));
        $readingTime = ceil($wordCount / 200); // 200 words per minute
        return max(1, $readingTime); // Minimum 1 minute
    }

    protected static function boot()
    {
        parent::boot();
    }
}