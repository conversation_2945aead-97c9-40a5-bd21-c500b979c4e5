@extends('layouts.app')

@section('content')
<div class="min-h-screen flex items-center justify-center relative overflow-hidden bg-light text-primary-light dark:bg-dark dark:text-primary-dark">
    <!-- Animated particles -->
    <div class="particle" style="left: 10%; animation-delay: 0s;"></div>
    <div class="particle" style="left: 20%; animation-delay: 1s;"></div>
    <div class="particle" style="left: 30%; animation-delay: 2s;"></div>
    <div class="particle" style="left: 40%; animation-delay: 3s;"></div>
    <div class="particle" style="left: 50%; animation-delay: 4s;"></div>
    <div class="particle" style="left: 60%; animation-delay: 5s;"></div>
    <div class="particle" style="left: 70%; animation-delay: 6s;"></div>
    <div class="particle" style="left: 80%; animation-delay: 7s;"></div>
    <div class="particle" style="left: 90%; animation-delay: 8s;"></div>

    <div class="text-center z-10 px-4 max-w-4xl mx-auto">
        <!-- Maintenance icon and title -->
        <div class="fade-in-up" style="animation-delay: 0.2s; opacity: 0;">
            <div class="float mb-8">
                <div class="inline-block p-6 bg-gradient-to-r from-primary-light to-primary-dark dark:from-primary-dark dark:to-primary-light rounded-full glow mb-6 mt-12">
                    <svg class="w-16 h-16 text-white spin-slow" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z">
                        </path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    </svg>
                </div>
                <h1 class="text-4xl md:text-6xl font-black text-gradient mb-4">
                    Under Maintenance
                </h1>
            </div>
        </div>

        <!-- Maintenance message -->
        <div class="fade-in-up" style="animation-delay: 0.4s; opacity: 0;">
            <h2 class="text-2xl md:text-3xl font-bold text-primary-light dark:text-primary-dark mb-4">
                We're Making Things Better
            </h2>
            <p class="text-lg md:text-xl text-secondary-light dark:text-secondary-dark mb-8 max-w-2xl mx-auto leading-relaxed">
                Our site is currently undergoing scheduled maintenance to improve your experience.
                We'll be back online shortly with enhanced features and better performance.
            </p>
        </div>

        <!-- Progress indicator -->
        <div class="fade-in-up mb-8" style="animation-delay: 0.6s; opacity: 0;">
            <div class="bg-border-light dark:bg-border-dark rounded-full h-3 mb-4 overflow-hidden">
                <div class="bg-gradient-to-r from-primary-light to-primary-dark dark:from-primary-dark dark:to-primary-light h-3 rounded-full progress-bar glow"></div>
            </div>
            <p class="text-secondary-light dark:text-secondary-dark text-sm">Estimated completion: 75%</p>
        </div>

        <!-- Status updates -->
        <div class="fade-in-up mb-8" style="animation-delay: 0.8s; opacity: 0;">
            <div class="grid md:grid-cols-3 gap-4">
                <div class="bg-black bg-opacity-30 dark:bg-white dark:bg-opacity-10 backdrop-blur-sm rounded-lg p-4 border border-green-600">
                    <div class="flex items-center justify-center mb-2">
                        <div class="w-3 h-3 bg-green-500 rounded-full pulse mr-2"></div>
                        <span class="text-green-400 font-medium">Database</span>
                    </div>
                    <p class="text-secondary-light dark:text-secondary-dark text-sm">Optimization Complete</p>
                </div>

                <div class="bg-black bg-opacity-30 dark:bg-white dark:bg-opacity-10 backdrop-blur-sm rounded-lg p-4 border border-yellow-600">
                    <div class="flex items-center justify-center mb-2">
                        <div class="w-3 h-3 bg-yellow-500 rounded-full pulse mr-2"></div>
                        <span class="text-yellow-400 font-medium">Server</span>
                    </div>
                    <p class="text-secondary-light dark:text-secondary-dark text-sm">Updates in Progress</p>
                </div>

                <div class="bg-black bg-opacity-30 dark:bg-white dark:bg-opacity-10 backdrop-blur-sm rounded-lg p-4 border border-border-light dark:border-border-dark">
                    <div class="flex items-center justify-center mb-2">
                        <div class="w-3 h-3 bg-border-light dark:bg-border-dark rounded-full mr-2"></div>
                        <span class="text-secondary-light dark:text-secondary-dark font-medium">Frontend</span>
                    </div>
                    <p class="text-secondary-light dark:text-secondary-dark text-sm">Pending</p>
                </div>
            </div>
        </div>

        <!-- Contact and updates section -->
        <div class="fade-in-up" style="animation-delay: 1.0s; opacity: 0;">
            <div class="bg-black bg-opacity-30 dark:bg-white dark:bg-opacity-10 backdrop-blur-sm rounded-2xl border border-border-light dark:border-border-dark p-8 card-hover">
                <h3 class="text-xl font-semibold text-primary-light dark:text-primary-dark mb-6">Stay Connected</h3>

                <div class="grid md:grid-cols-2 gap-6">
                    <div class="text-left">
                        <h4 class="text-primary-light dark:text-primary-dark font-medium mb-3 flex items-center">
                            <svg class="w-5 h-5 mr-2 text-primary-dark dark:text-primary-light" fill="none" stroke="currentColor"
                                viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            Estimated Downtime
                        </h4>
                        <p class="text-secondary-light dark:text-secondary-dark mb-4">Approximately 2-3 hours</p>

                        <h4 class="text-primary-light dark:text-primary-dark font-medium mb-3 flex items-center">
                            <svg class="w-5 h-5 mr-2 text-primary-dark dark:text-primary-light" fill="none" stroke="currentColor"
                                viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z">
                                </path>
                            </svg>
                            Need Help?
                        </h4>
                        <p class="text-secondary-light dark:text-secondary-dark text-sm">Contact our support <NAME_EMAIL></p>
                    </div>

                    <div class="text-left">
                        <h4 class="text-primary-light dark:text-primary-dark font-medium mb-3 flex items-center">
                            <svg class="w-5 h-5 mr-2 text-accent-light dark:text-accent-dark" fill="none" stroke="currentColor"
                                viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M15 17h5l-5 5v-5zM4 19h5v-5H4v5zM13 5h5l-5-5v5zM4 1h5V1H4v5z"></path>
                            </svg>
                            What's New
                        </h4>
                        <ul class="text-secondary-light dark:text-secondary-dark text-sm space-y-1">
                            <li>• Enhanced security features</li>
                            <li>• Improved page load speeds</li>
                            <li>• New user interface elements</li>
                            <li>• Bug fixes and optimizations</li>
                        </ul>
                    </div>
                </div>

                <!-- Action buttons -->
                <div class="flex flex-col sm:flex-row gap-4 justify-center items-center mt-8">
                    <button onclick="window.location.reload()"
                        class="card-hover glow bg-gradient-to-r from-primary-light to-primary-dark dark:from-primary-dark dark:to-primary-light hover:from-primary-dark hover:to-primary-light dark:hover:from-primary-light dark:hover:to-primary-dark text-white font-semibold py-3 px-6 rounded-full shadow-lg transition-all duration-300">
                        <span class="flex items-center gap-2">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15">
                                </path>
                            </svg>
                            Refresh Page
                        </span>
                    </button>

                    <button onclick="window.history.back()"
                        class="card-hover bg-border-light dark:bg-border-dark hover:bg-border-dark dark:hover:bg-border-light text-primary-light dark:text-primary-dark font-semibold py-3 px-6 rounded-full border-2 border-border-light dark:border-border-dark hover:border-primary-light dark:hover:border-primary-dark transition-all duration-300">
                        <span class="flex items-center gap-2">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                            </svg>
                            Go Back
                        </span>
                    </button>
                </div>
            </div>
        </div>

        <!-- Footer message -->
        <div class="fade-in-up mt-8" style="animation-delay: 1.2s; opacity: 0;">
            <p class="text-secondary-light dark:text-secondary-dark text-sm">
                Thank you for your patience. We appreciate your understanding.
            </p>
        </div>
    </div>
    <script>
        // Add interactivity and real-time updates
        document.addEventListener('DOMContentLoaded', function() {
            // Trigger animations on load
            const elements = document.querySelectorAll('.fade-in-up');
            elements.forEach((el, index) => {
                setTimeout(() => {
                    el.style.opacity = '1';
                }, index * 200);
            });

            // Add mouse move parallax effect
            document.addEventListener('mousemove', function(e) {
                const particles = document.querySelectorAll('.particle');
                const x = e.clientX / window.innerWidth;
                const y = e.clientY / window.innerHeight;

                particles.forEach((particle, index) => {
                    const speed = (index + 1) * 0.5;
                    particle.style.transform =
                        `translateX(${x * speed}px) translateY(${y * speed}px)`;
                });
            });

            // Auto-refresh functionality (optional)
            let refreshTimer = 300; // 5 minutes in seconds
            const refreshInterval = setInterval(() => {
                refreshTimer--;
                if (refreshTimer <= 0) {
                    window.location.reload();
                }
            }, 1000);

            // Update progress bar periodically
            const progressBar = document.querySelector('.progress-bar');
            let progress = 75;
            setInterval(() => {
                progress = Math.min(progress + Math.random() * 2, 95);
                progressBar.style.width = progress + '%';
            }, 5000);
        });
    </script>
</div>
@endsection

@push('styles')
    <style>
        @keyframes float {

            0%,
            100% {
                transform: translateY(0px) rotate(0deg);
            }

            50% {
                transform: translateY(-20px) rotate(3deg);
            }
        }

        @keyframes glow {

            0%,
            100% {
                box-shadow: 0 0 20px rgba(139, 92, 246, 0.3);
            }

            50% {
                box-shadow: 0 0 40px rgba(139, 92, 246, 0.6);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes spin {
            from {
                transform: rotate(0deg);
            }

            to {
                transform: rotate(360deg);
            }
        }

        @keyframes pulse {

            0%,
            100% {
                opacity: 1;
            }

            50% {
                opacity: 0.5;
            }
        }

        @keyframes progress {
            0% {
                width: 0%;
            }

            100% {
                width: 75%;
            }
        }

        .float {
            animation: float 4s ease-in-out infinite;
        }

        .glow {
            animation: glow 2s ease-in-out infinite alternate;
        }

        .fade-in-up {
            animation: fadeInUp 0.8s ease-out forwards;
        }

        .spin-slow {
            animation: spin 3s linear infinite;
        }

        .pulse {
            animation: pulse 2s ease-in-out infinite;
        }

        .gradient-bg {
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 25%, #16213e 50%, #0f3460 75%, #533483 100%);
        }

        .text-gradient {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .card-hover {
            transition: all 0.3s ease;
        }

        .card-hover:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
        }

        .particle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: rgba(139, 92, 246, 0.6);
            border-radius: 50%;
            animation: particle 8s linear infinite;
        }

        @keyframes particle {
            0% {
                transform: translateY(100vh) translateX(0);
                opacity: 0;
            }

            10% {
                opacity: 1;
            }

            90% {
                opacity: 1;
            }

            100% {
                transform: translateY(-100px) translateX(100px);
                opacity: 0;
            }
        }

        .progress-bar {
            animation: progress 3s ease-out forwards;
        }
    </style>
@endpush
