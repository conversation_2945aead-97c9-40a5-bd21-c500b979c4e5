<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== DEBUGGING CATEGORIES ===\n\n";

// Check ComponentCategory
echo "1. ComponentCategory:\n";
$componentCategoryCount = \App\Models\ComponentCategory::count();
echo "   Total: $componentCategoryCount\n";

if ($componentCategoryCount > 0) {
    $categories = \App\Models\ComponentCategory::orderBy('name')->get();
    foreach ($categories as $category) {
        echo "   - {$category->name} (slug: {$category->slug})\n";
    }
} else {
    echo "   No ComponentCategory records found!\n";
}

// Check Product categories
echo "\n2. Product Categories:\n";
$productCategories = \App\Models\Product::active()
    ->whereNotNull('category')
    ->distinct()
    ->pluck('category');

echo "   Unique product categories: " . $productCategories->count() . "\n";
foreach ($productCategories->take(10) as $category) {
    echo "   - $category\n";
}

// Check SearchService
echo "\n3. SearchService:\n";
try {
    $searchService = app(\App\Services\SearchService::class);
    $filterOptions = $searchService->getFilterOptions([]);
    echo "   Brands from SearchService: " . $filterOptions['brands']->count() . "\n";
    echo "   Specs from SearchService: " . count($filterOptions['specifications']) . "\n";
    echo "   Price range: {$filterOptions['price_range']['min']} - {$filterOptions['price_range']['max']}\n";
} catch (Exception $e) {
    echo "   ERROR: " . $e->getMessage() . "\n";
}

echo "\n=== DEBUG COMPLETE ===\n";