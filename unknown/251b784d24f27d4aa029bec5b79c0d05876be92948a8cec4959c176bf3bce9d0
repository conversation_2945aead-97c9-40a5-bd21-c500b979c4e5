<?php

namespace App\Services\Gateways;

use App\Contracts\PaymentGatewayInterface;
use App\Models\GatewaySetting;
use App\Exceptions\PaymentGatewayException;
use App\Exceptions\GatewayConfigurationException;
use Razorpay\Api\Api;

class RazorpayService implements PaymentGatewayInterface
{
    private Api $razorpay;
    private array $config;

    public function __construct()
    {
        $this->loadConfiguration();
        $this->razorpay = new Api($this->config['key_id'], $this->config['key_secret']);
    }

    public function createPayment(array $data): array
    {
        try {
            $orderData = [
                'receipt' => $data['transaction_id'],
                'amount' => $data['amount'] * 100, // Convert to paise
                'currency' => $data['currency'],
                'notes' => [
                    'user_id' => $data['user_id'],
                    'description' => $data['description']
                ]
            ];

            $order = $this->razorpay->order->create($orderData);

            return [
                'order_id' => $order['id'],
                'amount' => $order['amount'],
                'currency' => $order['currency'],
                'key' => $this->config['key_id'],
                'name' => config('app.name'),
                'description' => $data['description'],
                'prefill' => [
                    'name' => auth()->user()->name ?? '',
                    'email' => auth()->user()->email ?? '',
                ],
                'theme' => [
                    'color' => $this->config['theme_color'] ?? '#3399cc'
                ]
            ];

        } catch (\Exception $e) {
            throw new PaymentGatewayException(
                'Razorpay order creation failed',
                'RAZORPAY_ORDER_FAILED',
                $e->getMessage()
            );
        }
    }

    public function verifyPayment(string $transactionId, array $data): bool
    {
        try {
            $attributes = [
                'razorpay_order_id' => $data['razorpay_order_id'],
                'razorpay_payment_id' => $data['razorpay_payment_id'],
                'razorpay_signature' => $data['razorpay_signature']
            ];

            $this->razorpay->utility->verifyPaymentSignature($attributes);
            return true;

        } catch (\Exception $e) {
            return false;
        }
    }

    public function handleWebhook(array $payload): array
    {
        // Implement webhook handling
        $event = $payload['event'];
        $payment = $payload['payload']['payment']['entity'];

        return [
            'event' => $event,
            'payment_id' => $payment['id'],
            'order_id' => $payment['order_id'],
            'status' => $payment['status']
        ];
    }

    public function getPaymentStatus(string $transactionId): string
    {
        // Implementation for getting payment status
        return 'pending';
    }

    private function loadConfiguration(): void
    {
        $setting = GatewaySetting::byGateway('razorpay')->enabled()->first();
        
        if (!$setting) {
            throw new GatewayConfigurationException('Razorpay gateway not configured');
        }

        $this->config = $setting->settings;
    }
}
