<?php

namespace Tests\Feature\Api;

use App\Models\Component;
use App\Models\ComponentCategory;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Lara<PERSON>\Sanctum\Sanctum;
use Tests\TestCase;

class ComponentApiTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test categories
        $this->cpuCategory = ComponentCategory::factory()->create([
            'name' => 'CPU',
            'slug' => 'cpu'
        ]);
        
        $this->gpuCategory = ComponentCategory::factory()->create([
            'name' => 'GPU',
            'slug' => 'gpu'
        ]);
    }

    public function test_can_list_components()
    {
        // Create test components
        Component::factory()->count(5)->create([
            'category_id' => $this->cpuCategory->id,
            'is_active' => true
        ]);

        $response = $this->getJson('/api/v1/components');

        // Debug the response
        echo "Response status: " . $response->getStatusCode() . "\n";
        echo "Response content: " . $response->getContent() . "\n";

        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'name',
                        'brand',
                        'model',
                        'price',
                        'category',
                        'is_active'
                    ]
                ],
                'meta' => [
                    'current_page',
                    'last_page',
                    'per_page',
                    'total'
                ],
                'links'
            ]);
    }

    public function test_can_filter_components_by_category()
    {
        Component::factory()->create([
            'category_id' => $this->cpuCategory->id,
            'is_active' => true
        ]);
        
        Component::factory()->create([
            'category_id' => $this->gpuCategory->id,
            'is_active' => true
        ]);

        $response = $this->getJson('/api/v1/components?category=cpu');

        $response->assertStatus(200);
        $data = $response->json('data');
        
        foreach ($data as $component) {
            $this->assertEquals('cpu', $component['category']['slug']);
        }
    }

    public function test_can_search_components()
    {
        Component::query()->delete();
        Component::factory()->create([
            'name' => 'Intel Core i7',
            'category_id' => $this->cpuCategory->id,
            'is_active' => true
        ]);
        
        Component::factory()->create([
            'name' => 'AMD Ryzen 5',
            'category_id' => $this->cpuCategory->id,
            'is_active' => true
        ]);

        $response = $this->getJson('/api/v1/components?search=Intel');

        $response->assertStatus(200);
        $data = $response->json('data');
        
        $this->assertCount(1, $data);
        $this->assertStringContainsString('Intel', $data[0]['name']);
    }

    public function test_can_filter_components_by_price_range()
    {
        Component::factory()->create([
            'price' => 100.00,
            'category_id' => $this->cpuCategory->id,
            'is_active' => true
        ]);
        
        Component::factory()->create([
            'price' => 500.00,
            'category_id' => $this->cpuCategory->id,
            'is_active' => true
        ]);

        $response = $this->getJson('/api/v1/components?min_price=200&max_price=600');

        $response->assertStatus(200);
        $data = $response->json('data');
        
        foreach ($data as $component) {
            $this->assertGreaterThanOrEqual(200, $component['price']);
            $this->assertLessThanOrEqual(600, $component['price']);
        }
    }

    public function test_can_show_single_component()
    {
        $component = Component::factory()->create([
            'category_id' => $this->cpuCategory->id,
            'is_active' => true
        ]);

        $response = $this->getJson("/api/v1/components/{$component->id}");

        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    'id',
                    'name',
                    'brand',
                    'model',
                    'price',
                    'specs',
                    'category',
                    'average_rating',
                    'review_count'
                ]
            ]);
    }

    public function test_can_get_compatible_components()
    {
        $baseComponent = Component::factory()->create([
            'category_id' => $this->cpuCategory->id,
            'is_active' => true
        ]);

        $response = $this->getJson("/api/v1/components/{$baseComponent->id}/compatible?category=gpu");

        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => []
            ]);
    }

    public function test_can_get_component_categories()
    {
        $response = $this->getJson('/api/v1/categories');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'name',
                        'slug',
                        'components'
                    ]
                ]
            ]);
    }

    public function test_can_get_component_brands()
    {
        Component::factory()->create([
            'brand' => 'Intel',
            'category_id' => $this->cpuCategory->id,
            'is_active' => true
        ]);
        
        Component::factory()->create([
            'brand' => 'AMD',
            'category_id' => $this->cpuCategory->id,
            'is_active' => true
        ]);

        $response = $this->getJson('/api/v1/brands?category=cpu');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => []
            ]);
        
        $brands = $response->json('data');
        $this->assertContains('Intel', $brands);
        $this->assertContains('AMD', $brands);
    }

    public function test_only_shows_active_components()
    {
        Component::factory()->create([
            'category_id' => $this->cpuCategory->id,
            'is_active' => true
        ]);
        
        Component::factory()->create([
            'category_id' => $this->cpuCategory->id,
            'is_active' => false
        ]);

        $response = $this->getJson('/api/v1/components');

        $response->assertStatus(200);
        $data = $response->json('data');
        
        foreach ($data as $component) {
            $this->assertTrue($component['is_active']);
        }
    }

    public function test_can_sort_components()
    {
        Component::factory()->create([
            'name' => 'Z Component',
            'price' => 100,
            'category_id' => $this->cpuCategory->id,
            'is_active' => true
        ]);
        
        Component::factory()->create([
            'name' => 'A Component',
            'price' => 200,
            'category_id' => $this->cpuCategory->id,
            'is_active' => true
        ]);

        // Test sorting by name ascending
        $response = $this->getJson('/api/v1/components?sort=name&direction=asc');
        $response->assertStatus(200);
        $data = $response->json('data');
        $this->assertEquals('A Component', $data[0]['name']);

        // Test sorting by price descending
        $response = $this->getJson('/api/v1/components?sort=price&direction=desc');
        $response->assertStatus(200);
        $data = $response->json('data');
        $this->assertEquals(200, $data[0]['price']);
    }

    public function test_validates_component_api_parameters()
    {
        $response = $this->getJson('/api/v1/components?category=invalid-category');
        $response->assertStatus(422);

        $response = $this->getJson('/api/v1/components?min_price=-10');
        $response->assertStatus(422);

        $response = $this->getJson('/api/v1/components?per_page=200');
        $response->assertStatus(422);
    }
}