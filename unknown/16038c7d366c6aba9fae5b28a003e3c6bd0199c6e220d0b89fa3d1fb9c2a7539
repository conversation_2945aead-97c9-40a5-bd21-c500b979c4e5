<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Product;
use App\Models\ProductCategory;

class ProductSeeder extends Seeder
{
    public function run(): void
    {
        // Get some categories to assign to products
        $categories = ProductCategory::where('parent_id', '!=', null)->get();
        
        if ($categories->isEmpty()) {
            $this->command->warn('No product categories found. Creating basic categories first.');
            // Create a basic category if none exist
            $parentCategory = ProductCategory::create([
                'name' => 'General',
                'description' => 'General products',
                'sort_order' => 1,
                'is_active' => true,
            ]);
            
            $category = ProductCategory::create([
                'name' => 'Electronics',
                'description' => 'Electronic products',
                'parent_id' => $parentCategory->id,
                'sort_order' => 1,
                'is_active' => true,
            ]);
            
            $categories = collect([$category]);
        }

        // Create 20 sample products
        for ($i = 1; $i <= 20; $i++) {
            $category = $categories->random();
            
            Product::create([
                'name' => "Sample Product {$i}",
                'slug' => "sample-product-{$i}",
                'description' => "This is a detailed description for Sample Product {$i}. It includes all the features and specifications that make this product unique and valuable for customers.",
                'short_description' => "Short description for Sample Product {$i}",
                'sku' => "SKU-" . str_pad($i, 4, '0', STR_PAD_LEFT),
                'price' => rand(500, 50000),
                'sale_price' => rand(1, 10) <= 3 ? rand(400, 45000) : null, // 30% chance of sale price
                'stock_quantity' => rand(5, 100),
                'manage_stock' => true,
                'in_stock' => true,
                'status' => 'active',
                'type' => 'simple',
                'category' => $category->name,
                'category_id' => $category->id,
                'images' => [
                    '/images/products/sample-' . $i . '-1.jpg',
                    '/images/products/sample-' . $i . '-2.jpg'
                ],
                'attributes' => [
                    'color' => ['Red', 'Blue', 'Green', 'Black', 'White'][rand(0, 4)],
                    'material' => ['Plastic', 'Metal', 'Glass'][rand(0, 2)],
                    'warranty' => ['1 Year', '2 Years', '3 Years'][rand(0, 2)]
                ],
                'weight' => rand(100, 5000) / 100, // 1.00 to 50.00 kg
                'dimensions' => [
                    'length' => rand(10, 50),
                    'width' => rand(10, 50),
                    'height' => rand(5, 30)
                ],
                'brand' => ['Samsung', 'Apple', 'Intel', 'AMD', 'NVIDIA', 'ASUS', 'MSI'][rand(0, 6)],
                'model' => 'MODEL-' . $i,
                'sort_order' => $i,
                'featured' => rand(1, 10) <= 2, // 20% chance of being featured
                'meta_data' => [
                    'seo_title' => "Sample Product {$i} - Best Price Online",
                    'seo_description' => "Buy Sample Product {$i} at best price with fast delivery",
                    'keywords' => "sample, product, electronics, {$i}"
                ]
            ]);
        }

        $this->command->info('Created 20 sample products successfully.');
    }
}