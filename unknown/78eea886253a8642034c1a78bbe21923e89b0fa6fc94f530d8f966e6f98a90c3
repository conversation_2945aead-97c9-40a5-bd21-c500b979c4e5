<x-admin-layout>
    <div class="container mx-auto px-4 py-6">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-2xl font-semibold text-text-primary-light dark:text-text-primary-dark">Landing Page Settings</h1>
        </div>

        <div class="bg-white dark:bg-gray-800 shadow-sm rounded-lg overflow-hidden p-6">
            <form action="{{ route('admin.landing-page.update') }}" method="POST" enctype="multipart/form-data">
                @csrf
                @method('PUT')

                <!-- Hero Section -->
                <div class="mb-6">
                    <h2 class="text-xl font-semibold mb-4">Hero Section</h2>
                    
                    <div class="grid grid-cols-1 gap-4">
                        <div>
                            <label for="hero_title" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Hero Title</label>
                            <input type="text" name="hero_title" id="hero_title" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-light focus:ring focus:ring-primary-light focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white" value="{{ old('hero_title', get_setting('hero_title')) }}">
                        </div>

                        <div>
                            <label for="hero_subtitle" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Hero Subtitle</label>
                            <textarea name="hero_subtitle" id="hero_subtitle" rows="3" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-light focus:ring focus:ring-primary-light focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white">{{ old('hero_subtitle', get_setting('hero_subtitle')) }}</textarea>
                        </div>

                        <div>
                            <label for="hero_cta_text" class="block text-sm font-medium text-gray-700 dark:text-gray-300">CTA Button Text</label>
                            <input type="text" name="hero_cta_text" id="hero_cta_text" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-light focus:ring focus:ring-primary-light focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white" value="{{ old('hero_cta_text', get_setting('hero_cta_text')) }}">
                        </div>

                        <div>
                            <label for="hero_cta_link" class="block text-sm font-medium text-gray-700 dark:text-gray-300">CTA Button Link</label>
                            <input type="text" name="hero_cta_link" id="hero_cta_link" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-light focus:ring focus:ring-primary-light focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white" value="{{ old('hero_cta_link', get_setting('hero_cta_link')) }}">
                        </div>
                    </div>
                </div>

                <!-- Features Section -->
                <div class="mb-6">
                    <h2 class="text-xl font-semibold mb-4">Features Section</h2>
                    
                    <div class="grid grid-cols-1 gap-4">
                        <div>
                            <label for="features_title" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Section Title</label>
                            <input type="text" name="features_title" id="features_title" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-light focus:ring focus:ring-primary-light focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white" value="{{ old('features_title', get_setting('features_title')) }}">
                        </div>

                        <div>
                            <label for="features_subtitle" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Section Subtitle</label>
                            <textarea name="features_subtitle" id="features_subtitle" rows="3" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-light focus:ring focus:ring-primary-light focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white">{{ old('features_subtitle', get_setting('features_subtitle')) }}</textarea>
                        </div>
                    </div>
                </div>

                <div class="flex justify-end">
                    <button type="submit" class="px-4 py-2 bg-primary-light dark:bg-primary-dark text-white rounded-lg hover:bg-primary-light/90 dark:hover:bg-primary-dark/90 transition-colors duration-200">
                        Save Changes
                    </button>
                </div>
            </form>
        </div>
    </div>
</x-admin-layout>