<?php

namespace Tests\Unit\Services;

use Tests\TestCase;
use App\Services\Payment\Gateways\RazorpayService;
use App\Models\GatewaySetting;
use App\Models\Transaction;
use App\Models\User;
use App\Services\Payment\Exceptions\GatewayConfigurationException;
use App\Services\Payment\Exceptions\InvalidPaymentDataException;
use App\Services\Payment\Exceptions\PaymentGatewayException;
use App\Services\Payment\Exceptions\WebhookVerificationException;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Support\Facades\Log;
use Mockery;
use Razorpay\Api\Api;
use Razorpay\Api\Errors\BadRequestError;
use Razorpay\Api\Errors\ServerError;

class RazorpayServiceTest extends TestCase
{
    use DatabaseTransactions;

    protected RazorpayService $razorpayService;
    protected GatewaySetting $gatewaySetting;
    protected User $user;
    protected Transaction $transaction;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test user
        $this->user = User::factory()->create();

        // Create gateway setting for testing
        $this->gatewaySetting = new GatewaySetting();
        $this->gatewaySetting->gateway_name = GatewaySetting::GATEWAY_RAZORPAY;
        $this->gatewaySetting->is_enabled = true;
        $this->gatewaySetting->is_test_mode = true;
        $this->gatewaySetting->settings = [
            'key_id' => 'rzp_test_1234567890',
            'key_secret' => 'test_secret_key',
            'webhook_secret' => 'test_webhook_secret'
        ];
        $this->gatewaySetting->save();

        // Create test transaction
        $this->transaction = Transaction::create([
            'user_id' => $this->user->id,
            'gateway_name' => GatewaySetting::GATEWAY_RAZORPAY,
            'amount' => 100.00,
            'currency' => 'INR',
            'status' => Transaction::STATUS_PENDING,
            'transaction_id' => 'TXN_' . uniqid(),
        ]);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_constructor_loads_configuration_successfully()
    {
        $service = new RazorpayService();
        
        $this->assertEquals(GatewaySetting::GATEWAY_RAZORPAY, $service->getGatewayName());
        $this->assertTrue($service->isEnabled());
    }

    public function test_constructor_throws_exception_when_configuration_not_found()
    {
        // Delete the gateway setting
        $this->gatewaySetting->delete();

        $this->expectException(GatewayConfigurationException::class);
        $this->expectExceptionMessage('Razorpay gateway configuration not found');

        new RazorpayService();
    }

    public function test_constructor_throws_exception_when_credentials_missing()
    {
        // Update settings to remove credentials
        $this->gatewaySetting->forceFill([
            'settings' => json_encode([
                'key_id' => '',
                'key_secret' => '',
                'webhook_secret' => 'test_webhook_secret'
            ])
        ]);
        $this->gatewaySetting->save();

        $this->expectException(GatewayConfigurationException::class);
        $this->expectExceptionMessage('Razorpay API credentials not configured');

        new RazorpayService();
    }

    public function test_create_payment_success()
    {
        // Mock Razorpay API
        $mockApi = Mockery::mock(Api::class);
        $mockOrder = Mockery::mock();
        
        $mockOrder->shouldReceive('create')
            ->once()
            ->with([
                'amount' => 10000, // 100.00 * 100
                'currency' => 'INR',
                'receipt' => $this->transaction->transaction_id,
                'notes' => [
                    'user_id' => $this->user->id,
                    'transaction_id' => $this->transaction->transaction_id,
                ]
            ])
            ->andReturn([
                'id' => 'order_test123',
                'amount' => 10000,
                'currency' => 'INR',
                'status' => 'created'
            ]);

        $mockApi->order = $mockOrder;

        // Create service and inject mock
        $service = new RazorpayService();
        $reflection = new \ReflectionClass($service);
        $razorpayProperty = $reflection->getProperty('razorpay');
        $razorpayProperty->setAccessible(true);
        $razorpayProperty->setValue($service, $mockApi);

        $paymentData = [
            'amount' => 100.00,
            'currency' => 'INR',
            'user_id' => $this->user->id,
            'transaction_id' => $this->transaction->transaction_id,
        ];

        $result = $service->createPayment($paymentData);

        $this->assertTrue($result['success']);
        $this->assertEquals('order_test123', $result['gateway_order_id']);
        $this->assertEquals(10000, $result['amount']);
        $this->assertEquals('INR', $result['currency']);
        $this->assertEquals('rzp_test_1234567890', $result['key_id']);
        $this->assertTrue($result['test_mode']);
    }

    public function test_create_payment_validates_required_fields()
    {
        $service = new RazorpayService();

        $this->expectException(InvalidPaymentDataException::class);
        $this->expectExceptionMessage('Missing required field: amount');

        $service->createPayment([
            'currency' => 'INR',
            'user_id' => $this->user->id,
            'transaction_id' => $this->transaction->transaction_id,
        ]);
    }

    public function test_create_payment_validates_amount()
    {
        $service = new RazorpayService();

        $this->expectException(InvalidPaymentDataException::class);
        $this->expectExceptionMessage('Amount must be a positive number');

        $service->createPayment([
            'amount' => -100,
            'currency' => 'INR',
            'user_id' => $this->user->id,
            'transaction_id' => $this->transaction->transaction_id,
        ]);
    }

    public function test_create_payment_validates_currency()
    {
        $service = new RazorpayService();

        $this->expectException(InvalidPaymentDataException::class);
        $this->expectExceptionMessage('Currency must be a 3-character code');

        $service->createPayment([
            'amount' => 100.00,
            'currency' => 'INVALID',
            'user_id' => $this->user->id,
            'transaction_id' => $this->transaction->transaction_id,
        ]);
    }

    public function test_create_payment_handles_bad_request_error()
    {
        $mockApi = Mockery::mock(Api::class);
        $mockOrder = Mockery::mock();
        
        $mockOrder->shouldReceive('create')
            ->once()
            ->andThrow(new BadRequestError('Invalid amount', 'BAD_REQUEST_ERROR', 400));

        $mockApi->order = $mockOrder;

        $service = new RazorpayService();
        $reflection = new \ReflectionClass($service);
        $razorpayProperty = $reflection->getProperty('razorpay');
        $razorpayProperty->setAccessible(true);
        $razorpayProperty->setValue($service, $mockApi);

        $this->expectException(InvalidPaymentDataException::class);
        $this->expectExceptionMessage('Invalid payment data: Invalid amount');

        $service->createPayment([
            'amount' => 100.00,
            'currency' => 'INR',
            'user_id' => $this->user->id,
            'transaction_id' => $this->transaction->transaction_id,
        ]);
    }

    public function test_create_payment_handles_server_error()
    {
        $mockApi = Mockery::mock(Api::class);
        $mockOrder = Mockery::mock();
        
        $mockOrder->shouldReceive('create')
            ->once()
            ->andThrow(new ServerError('Server error', 'SERVER_ERROR', 500));

        $mockApi->order = $mockOrder;

        $service = new RazorpayService();
        $reflection = new \ReflectionClass($service);
        $razorpayProperty = $reflection->getProperty('razorpay');
        $razorpayProperty->setAccessible(true);
        $razorpayProperty->setValue($service, $mockApi);

        $this->expectException(PaymentGatewayException::class);
        $this->expectExceptionMessage('Razorpay server error: Server error');

        $service->createPayment([
            'amount' => 100.00,
            'currency' => 'INR',
            'user_id' => $this->user->id,
            'transaction_id' => $this->transaction->transaction_id,
        ]);
    }

    public function test_verify_payment_success()
    {
        // Mock Razorpay API
        $mockApi = Mockery::mock(Api::class);
        $mockUtility = Mockery::mock();
        $mockPayment = Mockery::mock();

        $mockUtility->shouldReceive('verifyPaymentSignature')
            ->once()
            ->with([
                'razorpay_order_id' => 'order_test123',
                'razorpay_payment_id' => 'pay_test123',
                'razorpay_signature' => 'test_signature'
            ])
            ->andReturn(true);

        $mockPayment->shouldReceive('fetch')
            ->once()
            ->with('pay_test123')
            ->andReturn([
                'id' => 'pay_test123',
                'status' => 'captured',
                'method' => 'card',
                'bank' => 'HDFC',
                'amount' => 10000
            ]);

        $mockApi->utility = $mockUtility;
        $mockApi->payment = $mockPayment;

        $service = new RazorpayService();
        $reflection = new \ReflectionClass($service);
        $razorpayProperty = $reflection->getProperty('razorpay');
        $razorpayProperty->setAccessible(true);
        $razorpayProperty->setValue($service, $mockApi);

        $verificationData = [
            'razorpay_order_id' => 'order_test123',
            'razorpay_payment_id' => 'pay_test123',
            'razorpay_signature' => 'test_signature'
        ];

        $result = $service->verifyPayment($this->transaction->transaction_id, $verificationData);

        $this->assertTrue($result);

        // Check transaction was updated
        $this->transaction->refresh();
        $this->assertEquals(Transaction::STATUS_COMPLETED, $this->transaction->status);
        $this->assertEquals('pay_test123', $this->transaction->gateway_transaction_id);
        $this->assertArrayHasKey('razorpay_payment_id', $this->transaction->payment_details);
    }

    public function test_verify_payment_fails_with_missing_fields()
    {
        $service = new RazorpayService();

        $result = $service->verifyPayment($this->transaction->transaction_id, [
            'razorpay_order_id' => 'order_test123',
            // Missing razorpay_payment_id and razorpay_signature
        ]);

        $this->assertFalse($result);
    }

    public function test_verify_payment_fails_with_invalid_transaction()
    {
        $service = new RazorpayService();

        $result = $service->verifyPayment('invalid_transaction_id', [
            'razorpay_order_id' => 'order_test123',
            'razorpay_payment_id' => 'pay_test123',
            'razorpay_signature' => 'test_signature'
        ]);

        $this->assertFalse($result);
    }

    public function test_handle_webhook_payment_captured()
    {
        // Update transaction with gateway details
        $this->transaction->update([
            'gateway_transaction_id' => 'pay_test123',
            'payment_details' => ['razorpay_order_id' => 'order_test123']
        ]);

        $service = new RazorpayService();

        // Create webhook payload
        $webhookPayload = [
            'event' => 'payment.captured',
            'payload' => [
                'payment' => [
                    'entity' => [
                        'id' => 'pay_test123',
                        'order_id' => 'order_test123',
                        'status' => 'captured',
                        'method' => 'card',
                        'amount' => 10000
                    ]
                ]
            ]
        ];
        
        // Mock webhook signature verification with the actual payload
        $_SERVER['HTTP_X_RAZORPAY_SIGNATURE'] = hash_hmac('sha256', json_encode($webhookPayload), 'test_webhook_secret');

        $result = $service->handleWebhook($webhookPayload);

        $this->assertTrue($result['success']);
        $this->assertEquals('Payment captured', $result['message']);

        // Check transaction was updated
        $this->transaction->refresh();
        $this->assertEquals(Transaction::STATUS_COMPLETED, $this->transaction->status);
        $this->assertTrue($this->transaction->webhook_verified);
    }

    public function test_handle_webhook_payment_failed()
    {
        // Update transaction with gateway details
        $this->transaction->update([
            'gateway_transaction_id' => 'pay_test123',
            'payment_details' => ['razorpay_order_id' => 'order_test123']
        ]);

        $service = new RazorpayService();

        // Create webhook payload
        $webhookPayload = [
            'event' => 'payment.failed',
            'payload' => [
                'payment' => [
                    'entity' => [
                        'id' => 'pay_test123',
                        'order_id' => 'order_test123',
                        'status' => 'failed',
                        'error_code' => 'BAD_REQUEST_ERROR',
                        'error_description' => 'Payment failed due to insufficient funds'
                    ]
                ]
            ]
        ];
        
        // Mock webhook signature verification with the actual payload
        $_SERVER['HTTP_X_RAZORPAY_SIGNATURE'] = hash_hmac('sha256', json_encode($webhookPayload), 'test_webhook_secret');

        $result = $service->handleWebhook($webhookPayload);

        $this->assertTrue($result['success']);
        $this->assertEquals('Payment failure processed', $result['message']);

        // Check transaction was updated
        $this->transaction->refresh();
        $this->assertEquals(Transaction::STATUS_FAILED, $this->transaction->status);
        $this->assertEquals('Payment failed due to insufficient funds', $this->transaction->failure_reason);
        $this->assertTrue($this->transaction->webhook_verified);
    }

    public function test_handle_webhook_payment_authorized()
    {
        // Update transaction with gateway details
        $this->transaction->update([
            'gateway_transaction_id' => 'pay_test123',
            'payment_details' => ['razorpay_order_id' => 'order_test123']
        ]);

        $service = new RazorpayService();

        // Create webhook payload
        $webhookPayload = [
            'event' => 'payment.authorized',
            'payload' => [
                'payment' => [
                    'entity' => [
                        'id' => 'pay_test123',
                        'order_id' => 'order_test123',
                        'status' => 'authorized',
                        'method' => 'card'
                    ]
                ]
            ]
        ];
        
        // Mock webhook signature verification with the actual payload
        $_SERVER['HTTP_X_RAZORPAY_SIGNATURE'] = hash_hmac('sha256', json_encode($webhookPayload), 'test_webhook_secret');

        $result = $service->handleWebhook($webhookPayload);

        $this->assertTrue($result['success']);
        $this->assertEquals('Payment authorization processed', $result['message']);

        // Check transaction was updated
        $this->transaction->refresh();
        $this->assertEquals(Transaction::STATUS_PROCESSING, $this->transaction->status);
        $this->assertTrue($this->transaction->webhook_verified);
    }

    public function test_handle_webhook_unhandled_event()
    {
        $service = new RazorpayService();

        // Create webhook payload
        $webhookPayload = [
            'event' => 'order.paid',
            'payload' => []
        ];
        
        // Mock webhook signature verification with the actual payload
        $_SERVER['HTTP_X_RAZORPAY_SIGNATURE'] = hash_hmac('sha256', json_encode($webhookPayload), 'test_webhook_secret');

        $result = $service->handleWebhook($webhookPayload);

        $this->assertTrue($result['success']);
        $this->assertEquals('Event not handled', $result['message']);
        $this->assertEquals('order.paid', $result['event']);
    }

    public function test_handle_webhook_fails_invalid_signature()
    {
        $this->expectException(WebhookVerificationException::class);
        $this->expectExceptionMessage('Webhook signature verification failed');

        $service = new RazorpayService();

        // Create webhook payload
        $webhookPayload = [
            'event' => 'payment.captured',
            'payload' => []
        ];
        
        // Set an invalid signature
        $_SERVER['HTTP_X_RAZORPAY_SIGNATURE'] = 'invalid_signature';

        $service->handleWebhook($webhookPayload);
    }

    public function test_handle_webhook_fails_missing_signature()
    {
        $this->expectException(WebhookVerificationException::class);
        $this->expectExceptionMessage('Webhook signature missing');

        $service = new RazorpayService();

        // Remove any existing signature
        if (isset($_SERVER['HTTP_X_RAZORPAY_SIGNATURE'])) {
            unset($_SERVER['HTTP_X_RAZORPAY_SIGNATURE']);
        }

        // Create webhook payload
        $webhookPayload = [
            'event' => 'payment.captured',
            'payload' => []
        ];

        $service->handleWebhook($webhookPayload);
    }

    public function test_handle_webhook_fails_missing_webhook_secret()
    {
        $this->expectException(WebhookVerificationException::class);
        $this->expectExceptionMessage('Webhook secret not configured');

        // Update settings to remove webhook secret
        $this->gatewaySetting->settings = [
            'key_id' => 'rzp_test_1234567890',
            'key_secret' => 'test_secret_key',
            // webhook_secret is missing
        ];
        $this->gatewaySetting->save();

        $service = new RazorpayService();

        // Create webhook payload
        $webhookPayload = [
            'event' => 'payment.captured',
            'payload' => []
        ];

        // Set a valid signature to ensure we reach the webhook secret check
        $_SERVER['HTTP_X_RAZORPAY_SIGNATURE'] = 'test_signature';

        $service->handleWebhook($webhookPayload);
    }

    public function test_get_payment_status_success()
    {
        // Update transaction with gateway transaction ID
        $this->transaction->update([
            'gateway_transaction_id' => 'pay_test123'
        ]);

        // Mock Razorpay API
        $mockApi = Mockery::mock(Api::class);
        $mockPayment = Mockery::mock();

        $mockPayment->shouldReceive('fetch')
            ->once()
            ->with('pay_test123')
            ->andReturn([
                'id' => 'pay_test123',
                'status' => 'captured'
            ]);

        $mockApi->payment = $mockPayment;

        $service = new RazorpayService();
        $reflection = new \ReflectionClass($service);
        $razorpayProperty = $reflection->getProperty('razorpay');
        $razorpayProperty->setAccessible(true);
        $razorpayProperty->setValue($service, $mockApi);

        $status = $service->getPaymentStatus($this->transaction->transaction_id);

        $this->assertEquals(Transaction::STATUS_COMPLETED, $status);
    }

    public function test_get_payment_status_returns_pending_for_missing_transaction()
    {
        $service = new RazorpayService();

        $status = $service->getPaymentStatus('invalid_transaction_id');

        $this->assertEquals(Transaction::STATUS_PENDING, $status);
    }

    public function test_get_payment_status_returns_pending_for_missing_gateway_transaction_id()
    {
        $service = new RazorpayService();

        $status = $service->getPaymentStatus($this->transaction->transaction_id);

        $this->assertEquals(Transaction::STATUS_PENDING, $status);
    }

    public function test_get_payment_status_handles_api_error()
    {
        // Update transaction with gateway transaction ID
        $this->transaction->update([
            'gateway_transaction_id' => 'pay_test123'
        ]);

        // Mock Razorpay API to throw exception
        $mockApi = Mockery::mock(Api::class);
        $mockPayment = Mockery::mock();

        $mockPayment->shouldReceive('fetch')
            ->once()
            ->with('pay_test123')
            ->andThrow(new \Exception('API Error'));

        $mockApi->payment = $mockPayment;

        $service = new RazorpayService();
        $reflection = new \ReflectionClass($service);
        $razorpayProperty = $reflection->getProperty('razorpay');
        $razorpayProperty->setAccessible(true);
        $razorpayProperty->setValue($service, $mockApi);

        $status = $service->getPaymentStatus($this->transaction->transaction_id);

        $this->assertEquals(Transaction::STATUS_PENDING, $status);
    }

    public function test_gateway_name_and_enabled_status()
    {
        $service = new RazorpayService();

        $this->assertEquals(GatewaySetting::GATEWAY_RAZORPAY, $service->getGatewayName());
        $this->assertTrue($service->isEnabled());

        // Disable gateway
        $this->gatewaySetting->update(['is_enabled' => false]);
        
        $service = new RazorpayService();
        $this->assertFalse($service->isEnabled());
    }

    public function test_payment_status_mapping()
    {
        // Update transaction with gateway transaction ID
        $this->transaction->update([
            'gateway_transaction_id' => 'pay_test123'
        ]);

        $statusMappings = [
            'captured' => Transaction::STATUS_COMPLETED,
            'failed' => Transaction::STATUS_FAILED,
            'authorized' => Transaction::STATUS_PROCESSING,
            'created' => Transaction::STATUS_PENDING,
            'unknown_status' => Transaction::STATUS_PENDING,
        ];

        foreach ($statusMappings as $razorpayStatus => $expectedStatus) {
            // Mock Razorpay API
            $mockApi = Mockery::mock(Api::class);
            $mockPayment = Mockery::mock();

            $mockPayment->shouldReceive('fetch')
                ->once()
                ->with('pay_test123')
                ->andReturn([
                    'id' => 'pay_test123',
                    'status' => $razorpayStatus
                ]);

            $mockApi->payment = $mockPayment;

            $service = new RazorpayService();
            $reflection = new \ReflectionClass($service);
            $razorpayProperty = $reflection->getProperty('razorpay');
            $razorpayProperty->setAccessible(true);
            $razorpayProperty->setValue($service, $mockApi);

            $status = $service->getPaymentStatus($this->transaction->transaction_id);

            $this->assertEquals($expectedStatus, $status, "Failed for Razorpay status: {$razorpayStatus}");
        }
    }
}