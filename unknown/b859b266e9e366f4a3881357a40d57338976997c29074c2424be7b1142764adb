<?php

namespace App\Livewire\Builder;

use App\Models\Build;
use App\Services\BuilderService;
use Livewire\Component;

class PopularBuilds extends Component
{
    public $builds = [];
    public $limit = 6;
    public $showCompleteOnly = true;
    
    public function mount($limit = 6, $showCompleteOnly = true)
    {
        $this->limit = $limit;
        $this->showCompleteOnly = $showCompleteOnly;
        $this->loadBuilds();
    }
    
    public function loadBuilds()
    {
        $builderService = app(BuilderService::class);
        
        $filters = [
            'complete_only' => $this->showCompleteOnly,
            'sort_by' => 'created_at',
            'sort_dir' => 'desc',
        ];
        
        $this->builds = $builderService->getPublicBuilds($this->limit, $filters);
    }
    
    public function cloneBuild($buildId)
    {
        if (!auth()->check()) {
            session()->flash('error', 'You must be logged in to clone builds.');
            return;
        }
        
        $originalBuild = Build::find($buildId);
        
        if (!$originalBuild || !$originalBuild->is_public) {
            session()->flash('error', 'Build not found or not accessible.');
            return;
        }
        
        $builderService = app(BuilderService::class);
        
        try {
            $clonedBuild = $builderService->cloneBuild($originalBuild, auth()->user(), [
                'name' => 'Clone of ' . $originalBuild->name,
                'is_public' => false,
            ]);
            
            session()->flash('message', 'Build cloned successfully!');
            $this->dispatch('buildCloned', $clonedBuild->id);
            
            // Redirect to builder with cloned build
            return redirect()->route('builder.index', ['build' => $clonedBuild->id]);
            
        } catch (\Exception $e) {
            session()->flash('error', 'Failed to clone build: ' . $e->getMessage());
        }
    }
    
    public function viewBuild($buildId)
    {
        return redirect()->route('builder.show', ['id' => $buildId]);
    }
    
    public function refresh()
    {
        $this->loadBuilds();
    }
    
    public function render()
    {
        return view('livewire.builder.popular-builds');
    }
}