<?php

namespace App\Livewire\User;

use Livewire\Component;
use Illuminate\Support\Facades\Auth;
use App\Models\Order;
use App\Models\Build;

class Notifications extends Component
{
    public $notifications = [];

    public function mount()
    {
        $user = Auth::user();
        // Example: gather order status updates and build sharing notifications
        $orders = Order::where('user_id', $user->id)->where('status', '!=', 'delivered')->get();
        foreach ($orders as $order) {
            $this->notifications[] = [
                'type' => 'order',
                'message' => "Order #{$order->order_number} is currently {$order->status}.",
                'date' => $order->updated_at,
            ];
        }
        $builds = Build::where('user_id', $user->id)->whereNotNull('share_token')->get();
        foreach ($builds as $build) {
            $this->notifications[] = [
                'type' => 'build',
                'message' => "Your build '{$build->name}' has a shareable link.",
                'date' => $build->updated_at,
            ];
        }
        usort($this->notifications, function($a, $b) {
            return strtotime($b['date']) <=> strtotime($a['date']);
        });
    }

    public function render()
    {
        return view('livewire.user.notifications');
    }
} 