<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "Testing compatibility service...\n";

// Create component categories
$cpuCategory = App\Models\ComponentCategory::firstOrCreate(
    ['slug' => 'cpu'],
    [
        'name' => 'CPU',
        'description' => 'Central Processing Unit',
        'icon' => 'cpu',
        'display_order' => 1,
        'is_required' => true,
    ]
);

$motherboardCategory = App\Models\ComponentCategory::firstOrCreate(
    ['slug' => 'motherboard'],
    [
        'name' => 'Motherboard',
        'description' => 'Motherboard',
        'icon' => 'motherboard',
        'display_order' => 2,
        'is_required' => true,
    ]
);

$ramCategory = App\Models\ComponentCategory::firstOrCreate(
    ['slug' => 'memory-ram'],
    [
        'name' => 'Memory (RAM)',
        'description' => 'System Memory',
        'icon' => 'memory',
        'display_order' => 3,
        'is_required' => true,
    ]
);

$psuCategory = App\Models\ComponentCategory::firstOrCreate(
    ['slug' => 'power-supply'],
    [
        'name' => 'Power Supply',
        'description' => 'Power Supply Unit',
        'icon' => 'psu',
        'display_order' => 4,
        'is_required' => true,
    ]
);

echo "Created component categories\n";

// Create compatible components
$cpu = App\Models\Component::factory()->create([
    'name' => 'Intel Core i7-13700K',
    'category_id' => $cpuCategory->id,
    'specs' => [
        'socket' => 'LGA1700',
        'power_consumption' => '125W',
    ],
]);

$motherboard = App\Models\Component::factory()->create([
    'name' => 'ASUS Z790 Motherboard',
    'category_id' => $motherboardCategory->id,
    'specs' => [
        'socket' => 'LGA1700',
        'memory_type' => 'DDR5',
        'max_memory_speed' => '5600',
    ],
]);

$ram = App\Models\Component::factory()->create([
    'name' => 'Corsair DDR5-5600 32GB',
    'category_id' => $ramCategory->id,
    'specs' => [
        'memory_type' => 'DDR5',
        'speed' => '5600',
        'power_consumption' => '10W',
    ],
]);

$psu = App\Models\Component::factory()->create([
    'name' => 'Corsair RM850x 850W',
    'category_id' => $psuCategory->id,
    'specs' => [
        'wattage' => '850W',
    ],
]);

echo "Created compatible components\n";

// Test compatibility service
$compatibilityService = new App\Services\CompatibilityService();

// Test basic compatibility
$result = $compatibilityService->checkCompatibility([$cpu, $motherboard, $ram, $psu]);
echo "Compatibility check result: " . ($result->isCompatible() ? 'COMPATIBLE' : 'INCOMPATIBLE') . "\n";
echo "Issues: " . count($result->getIssues()) . "\n";
echo "Warnings: " . count($result->getWarnings()) . "\n";

// Test component model methods
echo "\nTesting Component model methods:\n";
echo "CPU is compatible with motherboard: " . ($cpu->isCompatibleWith($motherboard) ? 'YES' : 'NO') . "\n";
echo "CPU power consumption: " . $cpu->getPowerConsumption() . "W\n";
echo "CPU has socket spec: " . ($cpu->hasSpec('socket') ? 'YES' : 'NO') . "\n";
echo "CPU socket: " . $cpu->getSpec('socket', 'Unknown') . "\n";

// Test getting compatible components
$compatibleMotherboards = $cpu->getCompatibleComponents('motherboard');
echo "Compatible motherboards for CPU: " . $compatibleMotherboards->count() . "\n";

// Create incompatible component to test
$incompatibleMotherboard = App\Models\Component::factory()->create([
    'name' => 'AMD B550 Motherboard',
    'category_id' => $motherboardCategory->id,
    'specs' => [
        'socket' => 'AM4',
        'memory_type' => 'DDR4',
    ],
]);

echo "\nTesting incompatible components:\n";
$incompatibleResult = $compatibilityService->checkCompatibility([$cpu, $incompatibleMotherboard]);
echo "CPU + Incompatible MB: " . ($incompatibleResult->isCompatible() ? 'COMPATIBLE' : 'INCOMPATIBLE') . "\n";

if (!$incompatibleResult->isCompatible()) {
    foreach ($incompatibleResult->getIssues() as $issue) {
        echo "Issue: " . $issue['message'] . "\n";
    }
}

// Test build validation
$user = App\Models\User::factory()->create();
$build = App\Models\Build::factory()->create(['user_id' => $user->id]);

// Add components to build
$build->components()->create([
    'component_id' => $cpu->id,
    'category_id' => $cpu->category_id,
    'quantity' => 1,
    'price' => $cpu->price,
]);

$build->components()->create([
    'component_id' => $motherboard->id,
    'category_id' => $motherboard->category_id,
    'quantity' => 1,
    'price' => $motherboard->price,
]);

$build->components()->create([
    'component_id' => $ram->id,
    'category_id' => $ram->category_id,
    'quantity' => 1,
    'price' => $ram->price,
]);

$build->components()->create([
    'component_id' => $psu->id,
    'category_id' => $psu->category_id,
    'quantity' => 1,
    'price' => $psu->price,
]);

echo "\nTesting build validation:\n";
$buildValidation = $compatibilityService->validateBuild($build);
echo "Build is valid: " . ($buildValidation->isValid() ? 'YES' : 'NO') . "\n";
echo "Total power consumption: " . $buildValidation->getPowerConsumption() . "W\n";
echo "PSU capacity: " . $buildValidation->getPsuCapacity() . "W\n";

echo "Compatibility service test completed!\n";