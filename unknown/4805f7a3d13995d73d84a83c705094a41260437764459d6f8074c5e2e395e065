<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON> Installer - Admin Account Creation</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <style>
        /* Base Styles */
        .gradient-background {
            background: linear-gradient(135deg, #EBF4FF 0%, #E6FFFA 100%);
            min-height: 100vh;
        }

        /* Animation Keyframes */
        @keyframes slideIn {
            from { opacity: 0; transform: translateY(-20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes scaleIn {
            from { transform: scale(0.95); opacity: 0; }
            to { transform: scale(1); opacity: 1; }
        }

        /* Component Styles */
        .installer-card {
            animation: scaleIn 0.5s ease-out;
            background: white;
            border-radius: 1rem;
            box-shadow: 0 10px 25px -5px rgba(59, 130, 246, 0.1), 0 8px 10px -6px rgba(59, 130, 246, 0.1);
        }

        .installer-header {
            background: linear-gradient(135deg, #4F46E5 0%, #3B82F6 100%);
            border-radius: 1rem 1rem 0 0;
            padding: 2rem;
        }

        .form-group {
            animation: fadeIn 0.5s ease-out forwards;
            opacity: 0;
        }

        .form-group:nth-child(1) { animation-delay: 0.1s; }
        .form-group:nth-child(2) { animation-delay: 0.2s; }
        .form-group:nth-child(3) { animation-delay: 0.3s; }
        .form-group:nth-child(4) { animation-delay: 0.4s; }

        .hover-scale {
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }

        .hover-scale:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 6px -1px rgba(59, 130, 246, 0.2), 0 2px 4px -1px rgba(59, 130, 246, 0.1);
        }

        .progress-bar {
            display: flex;
            margin: 2rem 0;
            justify-content: space-between;
            position: relative;
        }

        .progress-bar::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 2px;
            background: #E5E7EB;
            transform: translateY(-50%);
            z-index: 0;
        }

        .progress-step {
            width: 2.5rem;
            height: 2.5rem;
            border-radius: 50%;
            background: white;
            border: 2px solid #E5E7EB;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            z-index: 1;
            transition: all 0.3s ease;
        }

        .progress-step.active {
            border-color: #3B82F6;
            background: #3B82F6;
            color: white;
        }

        .progress-step.completed {
            border-color: #10B981;
            background: #10B981;
            color: white;
        }

        .floating-label {
            position: absolute;
            left: 1rem;
            top: 0.75rem;
            transition: all 0.2s ease;
            pointer-events: none;
            opacity: 0.6;
            background-color: white;
            padding: 0 0.25rem;
        }

        .form-input:focus ~ .floating-label,
        .form-input:not(:placeholder-shown) ~ .floating-label {
            transform: translateY(-1.25rem) scale(0.85);
            opacity: 1;
            color: #3B82F6;
        }
    </style>
</head>

<body class="gradient-background">
    <div class="min-h-screen flex items-center justify-center p-4">
        <div class="max-w-4xl w-full">
            <!-- Progress Bar -->
            <div class="progress-bar mb-8">
                <div class="progress-step completed">1</div>
                <div class="progress-step completed">2</div>
                <div class="progress-step completed">3</div>
                <div class="progress-step completed">4</div>
                <div class="progress-step completed">5</div>
                <div class="progress-step active">6</div>
            </div>

            <div class="installer-card">
                <!-- Header -->
                <div class="installer-header text-center">
                    <div class="flex justify-center mb-6">
                        <i data-lucide="user-plus" class="h-16 w-16 text-white opacity-90"></i>
                    </div>
                    <h1 class="text-4xl font-bold text-white mb-2">Create Admin Account</h1>
                    <p class="text-blue-100 text-lg">Set up your administrator account</p>
                </div>

                <!-- Content -->
                <div class="p-8">
                    <div class="space-y-8">
                        <!-- Introduction -->
                        <div class="text-center max-w-2xl mx-auto">
                            <p class="text-gray-600 text-lg leading-relaxed">
                                Create your administrator account to manage your application.
                                This account will have full access to all features.
                            </p>
                        </div>

                        <!-- Admin Creation Form -->
                        <form action="{{ route('installer.admin.save') }}" method="POST" class="space-y-6">
                            @csrf
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <!-- First Name -->
                                <div class="form-group relative">
                                    <input type="text" id="name" name="name" required placeholder=" "
                                        class="form-input block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200">
                                    <label for="name" class="floating-label flex items-center text-sm font-medium text-gray-700">
                                        <i data-lucide="user" class="h-4 w-4 mr-2"></i>
                                        Full Name
                                    </label>
                                </div>

                                <!-- Last Name -->
                                {{-- <div class="form-group relative">
                                    <input type="text" id="last_name" name="last_name" required placeholder=" "
                                        class="form-input block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200">
                                    <label for="last_name" class="floating-label flex items-center text-sm font-medium text-gray-700">
                                        <i data-lucide="user" class="h-4 w-4 mr-2"></i>
                                        Last Name
                                    </label>
                                </div> --}}

                                <!-- Email -->
                                <div class="form-group relative">
                                    <input type="email" id="email" name="email" required placeholder=" "
                                        class="form-input block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200">
                                    <label for="email" class="floating-label flex items-center text-sm font-medium text-gray-700">
                                        <i data-lucide="mail" class="h-4 w-4 mr-2"></i>
                                        Email Address
                                    </label>
                                </div>

                                <!-- Password -->
                                <div class="form-group relative">
                                    <input type="password" id="password" name="password" required placeholder=" "
                                        class="form-input block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200">
                                    <label for="password" class="floating-label flex items-center text-sm font-medium text-gray-700">
                                        <i data-lucide="key" class="h-4 w-4 mr-2"></i>
                                        Password
                                    </label>
                                </div>

                                <!-- Password Confirmation -->
                                <div class="form-group relative">
                                    <input type="password" id="password_confirmation" name="password_confirmation" required placeholder=" "
                                        class="form-input block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200">
                                    <label for="password_confirmation" class="floating-label flex items-center text-sm font-medium text-gray-700">
                                        <i data-lucide="key" class="h-4 w-4 mr-2"></i>
                                        Confirm Password
                                    </label>
                                </div>
                            </div>

                            @if ($errors->any())
                                <div class="bg-red-50 border border-red-200 rounded-xl p-4">
                                    <div class="flex items-start">
                                        <i data-lucide="alert-triangle" class="h-5 w-5 text-red-500 mt-0.5 mr-3"></i>
                                        <div>
                                            <h3 class="text-red-800 font-medium">Validation Error</h3>
                                            <ul class="mt-2 text-sm text-red-700 space-y-1">
                                                @foreach ($errors->all() as $error)
                                                    <li>• {{ $error }}</li>
                                                @endforeach
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            @endif

                            <!-- Action Buttons -->
                            <div class="flex space-x-4">
                                <a href="{{ route('installer.database') }}"
                                    class="flex-1 flex justify-center items-center py-3 px-4 rounded-lg text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-all duration-200">
                                    <i data-lucide="arrow-left" class="h-5 w-5 mr-2"></i>
                                    Back
                                </a>

                                <button type="submit"
                                    class="hover-scale flex-1 flex justify-center items-center py-3 px-4 rounded-lg text-sm font-medium text-white bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200 shadow-lg">
                                    <span>Create Account & Complete Installation</span>
                                    <i data-lucide="arrow-right" class="h-5 w-5 ml-2"></i>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Initialize Lucide icons
        lucide.createIcons();
    </script>
</body>

</html>
