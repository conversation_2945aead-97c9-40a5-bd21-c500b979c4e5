<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "Testing model relationships...\n";

// Test Build with components
$build = App\Models\Build::with('components.component')->first();
if ($build) {
    echo "Build: {$build->name} has {$build->components->count()} components\n";
    
    foreach ($build->components as $buildComponent) {
        if ($buildComponent->component) {
            echo "- {$buildComponent->component->name} (Qty: {$buildComponent->quantity})\n";
        }
    }
} else {
    echo "No builds found\n";
}

// Test Component with category
$component = App\Models\Component::with('category')->first();
if ($component) {
    echo "\nComponent: {$component->name} in category: {$component->category->name}\n";
} else {
    echo "No components found\n";
}

// Test Cart with items
$cart = App\Models\Cart::with('items.component')->first();
if ($cart) {
    echo "\nCart has {$cart->items->count()} items, total: \${$cart->total}\n";
    
    foreach ($cart->items as $item) {
        if ($item->component) {
            echo "- {$item->component->name} (Qty: {$item->quantity}, Price: \${$item->price})\n";
        }
    }
} else {
    echo "No carts found\n";
}

// Test Order with items and payment
$order = App\Models\Order::with(['items.component', 'payment'])->first();
if ($order) {
    echo "\nOrder {$order->order_number} has {$order->items->count()} items, total: \${$order->total}\n";
    echo "Status: {$order->status}, Payment Status: {$order->payment_status}\n";
    
    if ($order->payment) {
        echo "Payment: {$order->payment->payment_method} - {$order->payment->status}\n";
    }
} else {
    echo "No orders found\n";
}

echo "Relationships test completed!\n";