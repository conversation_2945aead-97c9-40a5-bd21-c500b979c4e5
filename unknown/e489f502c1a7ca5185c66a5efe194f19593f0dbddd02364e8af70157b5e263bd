<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class CommentRequest extends FormRequest
{
    public function rules()
    {
        return [
            'content' => [
                'required',
                'string',
                'max:300',
                'not_regex:/<script\b[^>]*>(.*?)<\/script>/is' // Prevent script tags
            ],
            'parent_id' => 'nullable|exists:comments,id'
        ];
    }

    public function messages()
    {
        return [
            'content.not_regex' => 'HTML scripts are not allowed in comments.'
        ];
    }
}