<?php

namespace App\Services;

use App\Models\Order;
use App\Models\Payment;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;

class PaymentGatewayService
{
    /**
     * Process a payment for an order.
     *
     * @param Order $order
     * @param array $paymentData
     * @return Payment
     * @throws ValidationException
     */
    public function processPayment(Order $order, array $paymentData): Payment
    {
        // Check if order can be paid
        if ($order->payment_status === 'paid') {
            throw new \InvalidArgumentException('Order has already been paid');
        }

        if ($order->status === 'cancelled' || $order->status === 'canceled') {
            throw new \InvalidArgumentException('Cannot process payment for cancelled order');
        }

        // Check for supported payment method first
        $paymentMethod = $paymentData['payment_method'] ?? null;
        $supportedMethods = ['credit_card', 'paypal', 'bank_transfer'];
        
        if (!in_array($paymentMethod, $supportedMethods)) {
            throw new \InvalidArgumentException("Unsupported payment method: {$paymentMethod}");
        }

        // Validate payment data
        $this->validatePaymentData($paymentData);
        
        return match ($paymentMethod) {
            'credit_card' => $this->processCreditCardPayment($order, $paymentData),
            'paypal' => $this->processPayPalPayment($order, $paymentData),
            'bank_transfer' => $this->processBankTransferPayment($order, $paymentData),
        };
    }

    /**
     * Process credit card payment.
     *
     * @param Order $order
     * @param array $paymentData
     * @return Payment
     */
    protected function processCreditCardPayment(Order $order, array $paymentData): Payment
    {
        // Simulate credit card processing
        // In a real application, this would integrate with Stripe, Square, etc.
        
        $cardNumber = $paymentData['card_number'];
        $lastFour = substr($cardNumber, -4);
        
        // Simulate payment processing delay and potential failure
        $isSuccessful = $this->simulatePaymentProcessing($cardNumber);
        
        $transactionId = 'CC_' . time() . '_' . mt_rand(1000, 9999);
        
        $payment = Payment::create([
            'order_id' => $order->id,
            'payment_method' => Payment::METHOD_CREDIT_CARD,
            'transaction_id' => $transactionId,
            'amount' => $order->total,
            'status' => $isSuccessful ? Payment::STATUS_COMPLETED : Payment::STATUS_FAILED,
            'payment_data' => [
                'card_last_four' => $lastFour,
                'card_type' => $this->detectCardType($cardNumber),
                'card_name' => $paymentData['card_name'],
                'processed_at' => now()->toISOString(),
            ],
        ]);

        // Update order status
        if ($isSuccessful) {
            $order->update([
                'payment_status' => 'paid',
                'status' => 'processing',
            ]);
        } else {
            $order->update(['payment_status' => 'failed']);
        }

        return $payment;
    }

    /**
     * Process PayPal payment.
     *
     * @param Order $order
     * @param array $paymentData
     * @return Payment
     */
    protected function processPayPalPayment(Order $order, array $paymentData): Payment
    {
        // Simulate PayPal processing
        // In a real application, this would integrate with PayPal API
        
        $transactionId = 'PP_' . time() . '_' . mt_rand(1000, 9999);
        
        // PayPal payments are typically successful in simulation
        $isSuccessful = true;
        
        $payment = Payment::create([
            'order_id' => $order->id,
            'payment_method' => Payment::METHOD_PAYPAL,
            'transaction_id' => $transactionId,
            'amount' => $order->total,
            'status' => $isSuccessful ? Payment::STATUS_COMPLETED : Payment::STATUS_FAILED,
            'payment_data' => [
                'paypal_email' => $paymentData['paypal_email'] ?? null,
                'processed_at' => now()->toISOString(),
            ],
        ]);

        // Update order status
        if ($isSuccessful) {
            $order->update([
                'payment_status' => 'paid',
                'status' => 'processing',
            ]);
        } else {
            $order->update(['payment_status' => 'failed']);
        }

        return $payment;
    }

    /**
     * Process bank transfer payment.
     *
     * @param Order $order
     * @param array $paymentData
     * @return Payment
     */
    protected function processBankTransferPayment(Order $order, array $paymentData): Payment
    {
        // Bank transfers are typically pending until confirmed
        $transactionId = 'BT_' . time() . '_' . mt_rand(1000, 9999);
        
        $payment = Payment::create([
            'order_id' => $order->id,
            'payment_method' => Payment::METHOD_BANK_TRANSFER,
            'transaction_id' => $transactionId,
            'amount' => $order->total,
            'status' => Payment::STATUS_PENDING,
            'payment_data' => [
                'bank_name' => $paymentData['bank_name'] ?? null,
                'account_number' => $paymentData['account_number'] ?? null,
                'routing_number' => $paymentData['routing_number'] ?? null,
                'processed_at' => now()->toISOString(),
            ],
        ]);

        // Bank transfers remain pending until manually confirmed
        $order->update(['payment_status' => 'pending']);

        return $payment;
    }

    /**
     * Refund a payment.
     *
     * @param Payment $payment
     * @param float|null $amount
     * @param string|null $reason
     * @return Payment
     */
    public function refundPayment(Payment $payment, ?float $amount = null, ?string $reason = null): Payment
    {
        if ($payment->status !== Payment::STATUS_COMPLETED) {
            throw new \InvalidArgumentException('Can only refund completed payments');
        }

        $refundAmount = $amount ?? $payment->amount;
        
        if ($refundAmount > $payment->amount) {
            throw new \InvalidArgumentException('Refund amount cannot exceed original payment amount');
        }

        // Simulate refund processing
        $refundTransactionId = 'REF_' . $payment->transaction_id . '_' . time();
        
        // Update payment status
        $payment->update([
            'status' => Payment::STATUS_REFUNDED,
            'payment_data' => array_merge($payment->payment_data ?? [], [
                'refund_transaction_id' => $refundTransactionId,
                'refund_amount' => $refundAmount,
                'refund_reason' => $reason,
                'refunded_at' => now()->toISOString(),
            ]),
        ]);

        // Update order status
        $payment->order->update([
            'payment_status' => 'refunded',
            'status' => 'canceled', // Use US spelling to match DB
        ]);

        return $payment;
    }

    /**
     * Validate payment data based on payment method.
     *
     * @param array $paymentData
     * @return void
     * @throws ValidationException
     */
    protected function validatePaymentData(array $paymentData): void
    {
        $rules = [
            'payment_method' => 'required|string|in:credit_card,paypal,bank_transfer',
        ];

        // Add method-specific validation rules
        switch ($paymentData['payment_method'] ?? null) {
            case 'credit_card':
                $rules = array_merge($rules, [
                    'card_number' => 'required|string|regex:/^\d{13,19}$/',
                    'card_expiry' => 'required|string|regex:/^\d{2}\/\d{2}$/',
                    'card_cvv' => 'required|string|regex:/^\d{3,4}$/',
                    'card_name' => 'required|string|max:255',
                ]);
                break;
                
            case 'paypal':
                $rules = array_merge($rules, [
                    'paypal_email' => 'sometimes|email|max:255',
                ]);
                break;
                
            case 'bank_transfer':
                $rules = array_merge($rules, [
                    'bank_name' => 'required|string|max:255',
                    'account_number' => 'required|string|max:50',
                    'routing_number' => 'required|string|max:20',
                ]);
                break;
        }

        $validator = Validator::make($paymentData, $rules);

        if ($validator->fails()) {
            throw new ValidationException($validator);
        }

        // Additional credit card validation
        if ($paymentData['payment_method'] === 'credit_card') {
            $this->validateCreditCard($paymentData);
        }
    }

    /**
     * Validate credit card data.
     *
     * @param array $paymentData
     * @return void
     * @throws ValidationException
     */
    protected function validateCreditCard(array $paymentData): void
    {
        $cardNumber = $paymentData['card_number'];
        $expiry = $paymentData['card_expiry'];

        // Validate card number using Luhn algorithm (only for non-test cards)
        // Allow test cards ending in 0 or 5 for simulation purposes
        $lastDigit = (int) substr($cardNumber, -1);
        if (!in_array($lastDigit, [0, 5]) && !$this->validateLuhn($cardNumber)) {
            throw ValidationException::withMessages([
                'card_number' => ['Invalid credit card number'],
            ]);
        }

        // Validate expiry date
        [$month, $year] = explode('/', $expiry);
        $expiryDate = \Carbon\Carbon::createFromFormat('m/y', $month . '/' . $year)->endOfMonth();
        
        if ($expiryDate->isPast()) {
            throw ValidationException::withMessages([
                'card_expiry' => ['Credit card has expired'],
            ]);
        }
    }

    /**
     * Validate credit card number using Luhn algorithm.
     *
     * @param string $cardNumber
     * @return bool
     */
    protected function validateLuhn(string $cardNumber): bool
    {
        $cardNumber = preg_replace('/\D/', '', $cardNumber);
        $length = strlen($cardNumber);
        
        if ($length < 13 || $length > 19) {
            return false;
        }

        $sum = 0;
        $alternate = false;

        for ($i = $length - 1; $i >= 0; $i--) {
            $digit = (int) $cardNumber[$i];
            
            if ($alternate) {
                $digit *= 2;
                if ($digit > 9) {
                    $digit = ($digit % 10) + 1;
                }
            }
            
            $sum += $digit;
            $alternate = !$alternate;
        }

        return ($sum % 10) === 0;
    }

    /**
     * Detect credit card type from card number.
     *
     * @param string $cardNumber
     * @return string
     */
    protected function detectCardType(string $cardNumber): string
    {
        $cardNumber = preg_replace('/\D/', '', $cardNumber);
        
        // Visa
        if (preg_match('/^4/', $cardNumber)) {
            return 'visa';
        }
        
        // Mastercard
        if (preg_match('/^5[1-5]/', $cardNumber) || preg_match('/^2[2-7]/', $cardNumber)) {
            return 'mastercard';
        }
        
        // American Express
        if (preg_match('/^3[47]/', $cardNumber)) {
            return 'amex';
        }
        
        // Discover
        if (preg_match('/^6(?:011|5)/', $cardNumber)) {
            return 'discover';
        }
        
        return 'unknown';
    }

    /**
     * Simulate payment processing with potential failures.
     *
     * @param string $cardNumber
     * @return bool
     */
    protected function simulatePaymentProcessing(string $cardNumber): bool
    {
        // Simulate different scenarios based on card number
        $lastDigit = (int) substr($cardNumber, -1);
        
        // Cards ending in 0 or 5 fail (20% failure rate)
        if (in_array($lastDigit, [0, 5])) {
            return false;
        }
        
        return true;
    }

    /**
     * Get payment status for an order.
     *
     * @param Order $order
     * @return array
     */
    public function getPaymentStatus(Order $order): array
    {
        $payment = $order->payment;
        
        if (!$payment) {
            return [
                'status' => 'no_payment',
                'message' => 'No payment found for this order',
            ];
        }

        return [
            'status' => $payment->status,
            'payment_method' => $payment->payment_method,
            'transaction_id' => $payment->transaction_id,
            'amount' => $payment->amount,
            'processed_at' => $payment->created_at,
        ];
    }

    /**
     * Confirm a pending bank transfer payment.
     *
     * @param Payment $payment
     * @return Payment
     */
    public function confirmBankTransfer(Payment $payment): Payment
    {
        if ($payment->payment_method !== Payment::METHOD_BANK_TRANSFER) {
            throw new \InvalidArgumentException('Can only confirm bank transfer payments');
        }

        if ($payment->status !== Payment::STATUS_PENDING) {
            throw new \InvalidArgumentException('Payment is not pending confirmation');
        }

        $payment->update([
            'status' => Payment::STATUS_COMPLETED,
            'payment_data' => array_merge($payment->payment_data ?? [], [
                'confirmed_at' => now()->toISOString(),
            ]),
        ]);

        // Update order status
        $payment->order->update([
            'payment_status' => 'paid',
            'status' => 'processing',
        ]);

        return $payment;
    }

    /**
     * Get supported payment methods.
     *
     * @return array
     */
    public function getSupportedPaymentMethods(): array
    {
        return [
            [
                'id' => 'credit_card',
                'name' => 'Credit Card',
                'description' => 'Pay with Visa, Mastercard, American Express, or Discover',
                'processing_time' => 'Instant',
            ],
            [
                'id' => 'paypal',
                'name' => 'PayPal',
                'description' => 'Pay securely with your PayPal account',
                'processing_time' => 'Instant',
            ],
            [
                'id' => 'bank_transfer',
                'name' => 'Bank Transfer',
                'description' => 'Direct bank transfer (requires manual confirmation)',
                'processing_time' => '1-3 business days',
            ],
        ];
    }

    /**
     * Calculate processing fee for a payment method.
     *
     * @param string $paymentMethod
     * @param float $amount
     * @return float
     */
    public function calculateProcessingFee(string $paymentMethod, float $amount): float
    {
        return match ($paymentMethod) {
            'credit_card' => round($amount * 0.029 + 0.30, 2), // 2.9% + $0.30
            'paypal' => round($amount * 0.034 + 0.30, 2), // 3.4% + $0.30
            'bank_transfer' => 0, // No fee for bank transfers
            default => 0,
        };
    }
}