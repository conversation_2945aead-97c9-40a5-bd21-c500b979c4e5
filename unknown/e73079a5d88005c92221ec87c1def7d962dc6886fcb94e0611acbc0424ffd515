<?php

namespace App\Jobs;

use App\Mail\OrderStatusUpdate;
use App\Models\Order;
use App\Services\EmailTrackingService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class SendOrderStatusUpdate implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public $tries = 3;

    /**
     * The maximum number of seconds the job can run.
     *
     * @var int
     */
    public $timeout = 60;

    /**
     * Create a new job instance.
     */
    public function __construct(
        public Order $order,
        public string $previousStatus,
        public ?string $message = null
    ) {
        // Set the queue for email jobs
        $this->onQueue('emails');
    }

    /**
     * Execute the job.
     */
    public function handle(EmailTrackingService $emailTrackingService): void
    {
        $emailLog = null;
        
        try {
            // Check if user has order status update notifications enabled
            if (!$this->order->user->getNotificationSetting('order_status_updates', true)) {
                Log::info('Order status update email skipped - user disabled notifications', [
                    'order_id' => $this->order->id,
                    'user_id' => $this->order->user_id,
                    'previous_status' => $this->previousStatus,
                    'current_status' => $this->order->status,
                ]);
                return;
            }

            // Skip sending email for certain status transitions that might not be relevant
            if ($this->shouldSkipStatusUpdate()) {
                Log::info('Order status update email skipped - status transition not relevant', [
                    'order_id' => $this->order->id,
                    'previous_status' => $this->previousStatus,
                    'current_status' => $this->order->status,
                ]);
                return;
            }

            // Log the email attempt
            $statusText = ucfirst($this->order->status);
            $emailLog = $emailTrackingService->logEmailAttempt(
                'order_status_update',
                $this->order->user->email,
                "Order {$statusText} - {$this->order->order_number}",
                $this->order->id,
                Order::class
            );

            // Update attempt count if this is a retry
            if ($this->attempts() > 1) {
                $emailTrackingService->incrementAttempt($emailLog);
            }

            // Send the email
            Mail::to($this->order->user->email)
                ->send(new OrderStatusUpdate($this->order, $this->previousStatus, $this->message));

            // Mark as sent
            $emailTrackingService->markEmailSent($emailLog);

            Log::info('Order status update email sent successfully', [
                'order_id' => $this->order->id,
                'order_number' => $this->order->order_number,
                'user_email' => $this->order->user->email,
                'previous_status' => $this->previousStatus,
                'current_status' => $this->order->status,
                'email_log_id' => $emailLog->id,
            ]);

        } catch (\Exception $e) {
            // Mark as failed if we have an email log
            if ($emailLog) {
                $emailTrackingService->markEmailFailed($emailLog, $e->getMessage(), $this->attempts());
            }

            Log::error('Failed to send order status update email', [
                'order_id' => $this->order->id,
                'order_number' => $this->order->order_number,
                'previous_status' => $this->previousStatus,
                'current_status' => $this->order->status,
                'error' => $e->getMessage(),
                'attempt' => $this->attempts(),
                'email_log_id' => $emailLog?->id,
            ]);

            // Re-throw the exception to trigger retry mechanism
            throw $e;
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('Order status update email job failed permanently', [
            'order_id' => $this->order->id,
            'order_number' => $this->order->order_number,
            'previous_status' => $this->previousStatus,
            'current_status' => $this->order->status,
            'error' => $exception->getMessage(),
            'attempts' => $this->attempts(),
        ]);
    }

    /**
     * Calculate the number of seconds to wait before retrying the job.
     */
    public function backoff(): array
    {
        return [30, 120, 300]; // 30 seconds, 2 minutes, 5 minutes
    }

    /**
     * Determine if we should skip sending email for this status update.
     */
    private function shouldSkipStatusUpdate(): bool
    {
        // Don't send emails for certain transitions
        $skipTransitions = [
            // Don't send email when going from pending to pending (no real change)
            'pending' => ['pending'],
            // Could add more skip rules here
        ];

        return isset($skipTransitions[$this->previousStatus]) &&
               in_array($this->order->status, $skipTransitions[$this->previousStatus]);
    }
}