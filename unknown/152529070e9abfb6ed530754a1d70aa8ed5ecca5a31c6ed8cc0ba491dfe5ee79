<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Default Payment Gateway
    |--------------------------------------------------------------------------
    |
    | This option controls the default payment gateway that will be used
    | when no specific gateway is requested.
    |
    */
    'default_gateway' => env('PAYMENT_DEFAULT_GATEWAY', 'razorpay'),

    /*
    |--------------------------------------------------------------------------
    | Default Currency
    |--------------------------------------------------------------------------
    |
    | The default currency for payment transactions.
    |
    */
    'default_currency' => env('PAYMENT_DEFAULT_CURRENCY', 'INR'),

    /*
    |--------------------------------------------------------------------------
    | Webhook Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for webhook handling including timeout and retry settings.
    |
    */
    'webhook' => [
        'timeout' => env('PAYMENT_WEBHOOK_TIMEOUT', 30),
        'max_retry_attempts' => env('PAYMENT_MAX_RETRY_ATTEMPTS', 3),
    ],

    /*
    |--------------------------------------------------------------------------
    | Logging Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for payment-related logging.
    |
    */
    'logging' => [
        'level' => env('PAYMENT_LOG_LEVEL', 'info'),
        'channel' => env('PAYMENT_LOG_CHANNEL', 'single'),
    ],

    /*
    |--------------------------------------------------------------------------
    | Gateway Configurations
    |--------------------------------------------------------------------------
    |
    | Configuration for each payment gateway. These settings can be overridden
    | by database settings in the gateway_settings table.
    |
    */
    'gateways' => [
        'razorpay' => [
            'name' => 'Razorpay',
            'enabled' => env('RAZORPAY_ENABLED', true),
            'test_mode' => env('RAZORPAY_TEST_MODE', true),
            'key_id' => env('RAZORPAY_KEY_ID'),
            'key_secret' => env('RAZORPAY_KEY_SECRET'),
            'webhook_secret' => env('RAZORPAY_WEBHOOK_SECRET'),
            'supported_currencies' => ['INR'],
            'min_amount' => 100, // in paise
            'max_amount' => 1500000, // in paise
        ],

        'payumoney' => [
            'name' => 'PayUmoney',
            'enabled' => env('PAYUMONEY_ENABLED', true),
            'test_mode' => env('PAYUMONEY_TEST_MODE', true),
            'merchant_key' => env('PAYUMONEY_MERCHANT_KEY'),
            'salt' => env('PAYUMONEY_SALT'),
            'auth_header' => env('PAYUMONEY_AUTH_HEADER'),
            'supported_currencies' => ['INR'],
            'min_amount' => 1, // in rupees
            'max_amount' => 100000, // in rupees
            'endpoints' => [
                'test' => 'https://test.payu.in/_payment',
                'live' => 'https://secure.payu.in/_payment',
            ],
        ],

        'cashfree' => [
            'name' => 'Cashfree',
            'enabled' => env('CASHFREE_ENABLED', true),
            'test_mode' => env('CASHFREE_TEST_MODE', true),
            'app_id' => env('CASHFREE_APP_ID'),
            'secret_key' => env('CASHFREE_SECRET_KEY'),
            'client_id' => env('CASHFREE_CLIENT_ID'),
            'client_secret' => env('CASHFREE_CLIENT_SECRET'),
            'supported_currencies' => ['INR'],
            'min_amount' => 1, // in rupees
            'max_amount' => 100000, // in rupees
            'endpoints' => [
                'test' => 'https://sandbox.cashfree.com/pg',
                'live' => 'https://api.cashfree.com/pg',
            ],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Transaction Status Constants
    |--------------------------------------------------------------------------
    |
    | Available transaction statuses.
    |
    */
    'transaction_statuses' => [
        'pending',
        'processing',
        'completed',
        'failed',
        'cancelled',
    ],

    /*
    |--------------------------------------------------------------------------
    | Security Settings
    |--------------------------------------------------------------------------
    |
    | Security-related configuration for payment processing.
    |
    */
    'security' => [
        'encrypt_payment_details' => env('PAYMENT_ENCRYPT_DETAILS', true),
        'webhook_rate_limit' => env('PAYMENT_WEBHOOK_RATE_LIMIT', 60), // requests per minute
        'require_csrf' => env('PAYMENT_REQUIRE_CSRF', true),
    ],
];