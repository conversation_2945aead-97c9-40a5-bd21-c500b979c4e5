<?php

namespace Tests\Browser\Pages;

use <PERSON><PERSON>\Dusk\Browser;

class LoginPage extends Page
{
    /**
     * Get the URL for the page.
     */
    public function url(): string
    {
        return '/login';
    }

    /**
     * Assert that the browser is on the page.
     */
    public function assert(Browser $browser): void
    {
        $browser->assertPathIs($this->url());
    }

    /**
     * Get the element shortcuts for the page.
     */
    public function elements(): array
    {
        return [
            '@email' => 'input[name="email"]',
            '@password' => 'input[name="password"]',
            '@loginButton' => 'button[type="submit"]',
            '@rememberMe' => 'input[name="remember"]',
            '@forgotPassword' => 'a[href*="password/reset"]',
        ];
    }

    /**
     * Login with the given credentials.
     */
    public function loginWith(Browser $browser, string $email, string $password): void
    {
        $browser->type('@email', $email)
                ->type('@password', $password)
                ->click('@loginButton');
    }
}