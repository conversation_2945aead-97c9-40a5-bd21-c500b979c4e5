<?php

namespace App\Services;

use App\Models\Component;
use App\Models\ComponentCategory;
use App\Models\ComponentCompatibility;
use App\Models\Build;
use Illuminate\Support\Collection;

class CompatibilityService
{
    /**
     * Check compatibility between components in a build.
     */
    public function checkCompatibility(array $components): CompatibilityResult
    {
        $issues = [];
        $warnings = [];
        
        // Convert component IDs to Component models if needed
        $componentModels = $this->resolveComponents($components);
        
        // Check each component against all others (avoid duplicate checks)
        for ($i = 0; $i < count($componentModels); $i++) {
            for ($j = $i + 1; $j < count($componentModels); $j++) {
                $component = $componentModels[$i];
                $otherComponent = $componentModels[$j];
                
                $result = $this->checkComponentPair($component, $otherComponent);
                
                if (!$result['compatible']) {
                    $issues[] = [
                        'component1' => $component,
                        'component2' => $otherComponent,
                        'message' => $result['message'],
                        'severity' => $result['severity'],
                    ];
                }
                
                if (!empty($result['warnings'])) {
                    $warnings = array_merge($warnings, $result['warnings']);
                }
            }
        }
        
        // Check for missing required components
        $missingRequired = $this->checkRequiredComponents($componentModels);
        if (!empty($missingRequired)) {
            $issues = array_merge($issues, $missingRequired);
        }
        
        return new CompatibilityResult([
            'compatible' => empty($issues),
            'issues' => $issues,
            'warnings' => $warnings,
            'components' => $componentModels,
        ]);
    }
    
    /**
     * Get compatible components for a given component and target category.
     */
    public function getCompatibleComponents(Component $baseComponent, string $targetCategory): Collection
    {
        $targetCategoryModel = ComponentCategory::where('slug', $targetCategory)->first();
        
        if (!$targetCategoryModel) {
            return collect();
        }
        
        // Get all components in the target category
        $candidates = Component::where('category_id', $targetCategoryModel->id)
            ->where('is_active', true)
            ->where('stock', '>', 0)
            ->get();
        
        // Filter by compatibility
        return $candidates->filter(function ($candidate) use ($baseComponent) {
            $result = $this->checkComponentPair($baseComponent, $candidate);
            return $result['compatible'];
        });
    }
    
    /**
     * Validate a complete build for compatibility.
     */
    public function validateBuild(Build $build): ValidationResult
    {
        $components = $build->components()->with('component')->get()
            ->pluck('component')
            ->filter();
        
        $compatibilityResult = $this->checkCompatibility($components->toArray());
        
        // Calculate power consumption
        $totalPower = $this->calculatePowerConsumption($components);
        $psuPower = $this->getPsuPower($components);
        
        $powerIssues = [];
        if ($psuPower && $totalPower > $psuPower * 0.8) {
            $powerIssues[] = [
                'type' => 'power_insufficient',
                'message' => "Total power consumption ({$totalPower}W) exceeds 80% of PSU capacity ({$psuPower}W)",
                'severity' => 'error',
            ];
        }
        
        return new ValidationResult([
            'valid' => $compatibilityResult->isCompatible() && empty($powerIssues),
            'compatibility_result' => $compatibilityResult,
            'power_consumption' => $totalPower,
            'psu_capacity' => $psuPower,
            'power_issues' => $powerIssues,
        ]);
    }
    
    /**
     * Check compatibility between two specific components.
     */
    protected function checkComponentPair(Component $component1, Component $component2): array
    {
        $result = [
            'compatible' => true,
            'message' => '',
            'severity' => 'info',
            'warnings' => [],
        ];
        
        // Check explicit compatibility rules
        $rules = ComponentCompatibility::where(function ($query) use ($component1, $component2) {
            $query->where('component_id', $component1->id)
                  ->where('compatible_component_id', $component2->id);
        })->orWhere(function ($query) use ($component1, $component2) {
            $query->where('category_id', $component1->category_id)
                  ->where('compatible_category_id', $component2->category_id);
        })->get();
        
        foreach ($rules as $rule) {
            if (!$rule->checkCompatibility($component1, $component2)) {
                $result['compatible'] = false;
                $result['message'] = $rule->error_message ?: 'Components are not compatible';
                $result['severity'] = 'error';
                break;
            }
        }
        
        // Check common compatibility scenarios
        if ($result['compatible']) {
            $result = $this->checkCommonCompatibilityRules($component1, $component2, $result);
        }
        
        return $result;
    }
    
    /**
     * Check common compatibility rules based on component categories and specs.
     */
    protected function checkCommonCompatibilityRules(Component $component1, Component $component2, array $result): array
    {
        $cat1 = $component1->category->slug ?? '';
        $cat2 = $component2->category->slug ?? '';
        
        // CPU and Motherboard socket compatibility
        if (($cat1 === 'cpu' && $cat2 === 'motherboard') || ($cat1 === 'motherboard' && $cat2 === 'cpu')) {
            $cpu = $cat1 === 'cpu' ? $component1 : $component2;
            $motherboard = $cat1 === 'motherboard' ? $component1 : $component2;
            
            $cpuSocket = $cpu->specs['socket'] ?? null;
            $mbSocket = $motherboard->specs['socket'] ?? null;
            
            if ($cpuSocket && $mbSocket && $cpuSocket !== $mbSocket) {
                $result['compatible'] = false;
                $result['message'] = "CPU socket ({$cpuSocket}) is not compatible with motherboard socket ({$mbSocket})";
                $result['severity'] = 'error';
            }
        }
        
        // RAM and Motherboard compatibility
        if (($cat1 === 'memory-ram' && $cat2 === 'motherboard') || ($cat1 === 'motherboard' && $cat2 === 'memory-ram')) {
            $ram = $cat1 === 'memory-ram' ? $component1 : $component2;
            $motherboard = $cat1 === 'motherboard' ? $component1 : $component2;
            
            $ramType = $ram->specs['memory_type'] ?? null;
            $mbMemoryType = $motherboard->specs['memory_type'] ?? null;
            
            if ($ramType && $mbMemoryType && $ramType !== $mbMemoryType) {
                $result['compatible'] = false;
                $result['message'] = "RAM type ({$ramType}) is not supported by motherboard ({$mbMemoryType})";
                $result['severity'] = 'error';
            }
            
            // Check memory speed
            $ramSpeed = $ram->specs['speed'] ?? null;
            $mbMaxSpeed = $motherboard->specs['max_memory_speed'] ?? null;
            
            if ($ramSpeed && $mbMaxSpeed && $ramSpeed > $mbMaxSpeed) {
                $result['warnings'][] = [
                    'message' => "RAM speed ({$ramSpeed}MHz) exceeds motherboard maximum ({$mbMaxSpeed}MHz)",
                    'severity' => 'warning',
                ];
            }
        }
        
        // Graphics Card and Power Supply compatibility
        if (($cat1 === 'graphics-card' && $cat2 === 'power-supply') || ($cat1 === 'power-supply' && $cat2 === 'graphics-card')) {
            $gpu = $cat1 === 'graphics-card' ? $component1 : $component2;
            $psu = $cat1 === 'power-supply' ? $component1 : $component2;
            
            $gpuPower = $gpu->specs['power_consumption'] ?? null;
            $psuPower = $psu->specs['wattage'] ?? null;
            
            if ($gpuPower && $psuPower) {
                $gpuPowerValue = (int) filter_var($gpuPower, FILTER_SANITIZE_NUMBER_INT);
                $psuPowerValue = (int) filter_var($psuPower, FILTER_SANITIZE_NUMBER_INT);
                
                if ($gpuPowerValue > $psuPowerValue * 0.6) {
                    $result['warnings'][] = [
                        'message' => "Graphics card power consumption ({$gpuPowerValue}W) is high relative to PSU capacity ({$psuPowerValue}W)",
                        'severity' => 'warning',
                    ];
                }
            }
        }
        
        return $result;
    }
    
    /**
     * Check for missing required components.
     */
    protected function checkRequiredComponents(Collection $components): array
    {
        $issues = [];
        $requiredCategories = ComponentCategory::where('is_required', true)->get();
        $presentCategories = $components->pluck('category_id')->unique();
        
        foreach ($requiredCategories as $category) {
            if (!$presentCategories->contains($category->id)) {
                $issues[] = [
                    'type' => 'missing_required',
                    'message' => "Required component category '{$category->name}' is missing",
                    'severity' => 'error',
                    'category' => $category,
                ];
            }
        }
        
        return $issues;
    }
    
    /**
     * Calculate total power consumption of components.
     */
    protected function calculatePowerConsumption(Collection $components): int
    {
        $totalPower = 0;
        
        foreach ($components as $component) {
            $power = $component->specs['power_consumption'] ?? null;
            if ($power) {
                $powerValue = (int) filter_var($power, FILTER_SANITIZE_NUMBER_INT);
                $totalPower += $powerValue;
            }
        }
        
        return $totalPower;
    }
    
    /**
     * Get PSU power capacity from components.
     */
    protected function getPsuPower(Collection $components): ?int
    {
        $psu = $components->first(function ($component) {
            return $component->category->slug === 'power-supply';
        });
        
        if (!$psu) {
            return null;
        }
        
        $wattage = $psu->specs['wattage'] ?? null;
        return $wattage ? (int) filter_var($wattage, FILTER_SANITIZE_NUMBER_INT) : null;
    }
    
    /**
     * Resolve components from mixed array of IDs and models.
     */
    protected function resolveComponents(array $components): Collection
    {
        return collect($components)->map(function ($component) {
            if ($component instanceof Component) {
                return $component;
            }
            
            if (is_numeric($component)) {
                return Component::with('category')->find($component);
            }
            
            return null;
        })->filter();
    }
}

