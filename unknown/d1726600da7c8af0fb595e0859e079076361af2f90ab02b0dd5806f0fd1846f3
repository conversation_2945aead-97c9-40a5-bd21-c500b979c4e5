<?php

require_once 'vendor/autoload.php';

use App\Models\Coupon;
use App\Models\Product;
use App\Models\ProductCategory;
use App\Services\CouponService;
use Illuminate\Foundation\Application;
use Illuminate\Support\Facades\Auth;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "Testing coupon category hierarchy...\n";

try {
    // Create test categories
    $electronics = ProductCategory::create([
        'name' => 'Electronics Test',
        'slug' => 'electronics-test',
        'is_active' => true,
    ]);
    
    $smartphones = ProductCategory::create([
        'name' => 'Smartphones Test',
        'slug' => 'smartphones-test',
        'parent_id' => $electronics->id,
        'is_active' => true,
    ]);
    
    echo "Created categories: Electronics (ID: {$electronics->id}), Smartphones (ID: {$smartphones->id})\n";
    
    // Test getAllChildren
    $children = $electronics->getAllChildren();
    echo "Electronics children count: " . $children->count() . "\n";
    
    if ($children->count() > 0) {
        echo "First child: " . $children->first()->name . " (ID: " . $children->first()->id . ")\n";
    }
    
    // Create a test product
    $phone = Product::factory()->create([
        'category_id' => $smartphones->id,
        'price' => 50000,
    ]);
    
    echo "Created phone product (ID: {$phone->id}) in smartphones category\n";
    
    // Create a coupon
    $coupon = Coupon::create([
        'code' => 'TESTCOUPON123',
        'name' => 'Test Coupon',
        'type' => 'percentage',
        'value' => 15,
        'applicable_categories' => [$electronics->id],
        'is_active' => true,
    ]);
    
    echo "Created coupon (ID: {$coupon->id}) applicable to electronics category\n";
    
    // Test if coupon is applicable to phone
    $isApplicable = $coupon->isApplicableToProducts([$phone->id]);
    echo "Is coupon applicable to phone? " . ($isApplicable ? 'YES' : 'NO') . "\n";
    
    // Clean up
    $coupon->delete();
    $phone->delete();
    $smartphones->delete();
    $electronics->delete();
    
    echo "Test completed successfully!\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
