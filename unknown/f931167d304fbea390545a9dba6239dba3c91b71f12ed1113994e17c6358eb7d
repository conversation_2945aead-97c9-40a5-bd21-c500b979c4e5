<?php

namespace Tests\Feature;

use App\Livewire\Reviews\CommentSection;
use App\Models\Component;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Review;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Livewire\Livewire;
use Tests\TestCase;

class ReviewSystemTest extends TestCase
{
    use RefreshDatabase;

    public function test_guest_user_cannot_see_review_form()
    {
        $component = Component::factory()->create();

        Livewire::test(CommentSection::class, ['component' => $component])
            ->assertSee('Login to Review')
            ->assertDontSee('Write a Review');
    }

    public function test_authenticated_user_without_purchase_cannot_review()
    {
        $user = User::factory()->create();
        $component = Component::factory()->create();

        $this->actingAs($user);

        Livewire::test(CommentSection::class, ['component' => $component])
            ->assertSee('Purchase required to review')
            ->assertDontSee('Write a Review');
    }

    public function test_user_with_purchase_can_see_review_form()
    {
        $user = User::factory()->create();
        $component = Component::factory()->create();
        
        // Create completed order
        $order = Order::factory()->completed()->create(['user_id' => $user->id]);
        OrderItem::factory()->create([
            'order_id' => $order->id,
            'component_id' => $component->id,
        ]);

        $this->actingAs($user);

        Livewire::test(CommentSection::class, ['component' => $component])
            ->assertSee('Write a Review')
            ->assertDontSee('Purchase required to review');
    }

    public function test_user_who_already_reviewed_cannot_review_again()
    {
        $user = User::factory()->create();
        $component = Component::factory()->create();
        
        // Create completed order
        $order = Order::factory()->completed()->create(['user_id' => $user->id]);
        OrderItem::factory()->create([
            'order_id' => $order->id,
            'component_id' => $component->id,
        ]);

        // User already reviewed
        Review::factory()->create([
            'user_id' => $user->id,
            'component_id' => $component->id,
        ]);

        $this->actingAs($user);

        Livewire::test(CommentSection::class, ['component' => $component])
            ->assertSee('You have already reviewed this product')
            ->assertDontSee('Write a Review');
    }

    public function test_user_can_toggle_review_form()
    {
        $user = User::factory()->create();
        $component = Component::factory()->create();
        
        // Create completed order
        $order = Order::factory()->completed()->create(['user_id' => $user->id]);
        OrderItem::factory()->create([
            'order_id' => $order->id,
            'component_id' => $component->id,
        ]);

        $this->actingAs($user);

        Livewire::test(CommentSection::class, ['component' => $component])
            ->assertSet('showReviewForm', false)
            ->call('toggleReviewForm')
            ->assertSet('showReviewForm', true)
            ->assertSee('Write Your Review')
            ->call('toggleReviewForm')
            ->assertSet('showReviewForm', false);
    }

    public function test_user_can_submit_review()
    {
        $user = User::factory()->create();
        $component = Component::factory()->create();
        
        // Create completed order
        $order = Order::factory()->completed()->create(['user_id' => $user->id]);
        OrderItem::factory()->create([
            'order_id' => $order->id,
            'component_id' => $component->id,
        ]);

        $this->actingAs($user);

        Livewire::test(CommentSection::class, ['component' => $component])
            ->call('toggleReviewForm')
            ->set('rating', 4)
            ->set('title', 'Great product!')
            ->set('comment', 'This component works perfectly in my build. Highly recommended.')
            ->call('submitReview')
            ->assertHasNoErrors()
            ->assertSet('showReviewForm', false)
            ->assertSee('You have already reviewed this product'); // User now shows as having reviewed

        $this->assertDatabaseHas('reviews', [
            'user_id' => $user->id,
            'component_id' => $component->id,
            'rating' => 4,
            'title' => 'Great product!',
            'comment' => 'This component works perfectly in my build. Highly recommended.',
        ]);
    }

    public function test_review_validation_works()
    {
        $user = User::factory()->create();
        $component = Component::factory()->create();
        
        // Create completed order
        $order = Order::factory()->completed()->create(['user_id' => $user->id]);
        OrderItem::factory()->create([
            'order_id' => $order->id,
            'component_id' => $component->id,
        ]);

        $this->actingAs($user);

        Livewire::test(CommentSection::class, ['component' => $component])
            ->call('toggleReviewForm')
            ->set('rating', 0) // Invalid rating
            ->set('title', str_repeat('a', 256)) // Too long title
            ->set('comment', str_repeat('a', 2001)) // Too long comment
            ->call('submitReview')
            ->assertHasErrors(['rating', 'title', 'comment']);
    }

    public function test_reviews_display_correctly()
    {
        $component = Component::factory()->create();
        
        Review::factory()->approved()->create([
            'component_id' => $component->id,
            'rating' => 5,
            'title' => 'Excellent!',
            'comment' => 'Perfect component.',
        ]);
        
        Review::factory()->approved()->create([
            'component_id' => $component->id,
            'rating' => 4,
            'title' => 'Good quality',
            'comment' => 'Works as expected.',
        ]);

        Livewire::test(CommentSection::class, ['component' => $component])
            ->assertSee('Excellent!')
            ->assertSee('Perfect component.')
            ->assertSee('Good quality')
            ->assertSee('Works as expected.')
            ->assertSee('4.5') // Average rating
            ->assertSee('2 reviews');
    }

    public function test_pending_reviews_are_not_displayed()
    {
        $component = Component::factory()->create();
        
        Review::factory()->approved()->create([
            'component_id' => $component->id,
            'title' => 'Approved Review',
        ]);
        
        Review::factory()->pending()->create([
            'component_id' => $component->id,
            'title' => 'Pending Review',
        ]);

        Livewire::test(CommentSection::class, ['component' => $component])
            ->assertSee('Approved Review')
            ->assertDontSee('Pending Review');
    }

    public function test_rating_filter_works()
    {
        $component = Component::factory()->create();
        
        Review::factory()->approved()->rating(5)->create([
            'component_id' => $component->id,
            'title' => 'Five Star Review',
        ]);
        
        Review::factory()->approved()->rating(3)->create([
            'component_id' => $component->id,
            'title' => 'Three Star Review',
        ]);

        Livewire::test(CommentSection::class, ['component' => $component])
            ->assertSee('Five Star Review')
            ->assertSee('Three Star Review')
            ->set('filterByRating', '5')
            ->assertSee('Five Star Review')
            ->assertDontSee('Three Star Review')
            ->set('filterByRating', '3')
            ->assertDontSee('Five Star Review')
            ->assertSee('Three Star Review');
    }

    public function test_sorting_works()
    {
        $component = Component::factory()->create();
        
        $oldReview = Review::factory()->approved()->create([
            'component_id' => $component->id,
            'title' => 'Old Review',
            'created_at' => now()->subDays(5),
        ]);
        
        $newReview = Review::factory()->approved()->create([
            'component_id' => $component->id,
            'title' => 'New Review',
            'created_at' => now()->subDay(),
        ]);

        // Test newest first (default)
        $component = Livewire::test(CommentSection::class, ['component' => $component])
            ->assertSeeInOrder(['New Review', 'Old Review']);

        // Test oldest first
        $component->set('sortBy', 'oldest')
            ->assertSeeInOrder(['Old Review', 'New Review']);
    }

    public function test_verified_purchase_badge_shows_correctly()
    {
        $user = User::factory()->create();
        $component = Component::factory()->create();
        
        // Create completed order for verified purchase
        $order = Order::factory()->completed()->create(['user_id' => $user->id]);
        OrderItem::factory()->create([
            'order_id' => $order->id,
            'component_id' => $component->id,
        ]);

        $review = Review::factory()->approved()->create([
            'user_id' => $user->id,
            'component_id' => $component->id,
        ]);

        Livewire::test(CommentSection::class, ['component' => $component])
            ->assertSee('Verified Purchase');
    }

    public function test_review_stats_display_correctly()
    {
        $component = Component::factory()->create();
        
        // Create reviews with different ratings
        Review::factory()->approved()->rating(5)->count(3)->create(['component_id' => $component->id]);
        Review::factory()->approved()->rating(4)->count(2)->create(['component_id' => $component->id]);
        Review::factory()->approved()->rating(3)->count(1)->create(['component_id' => $component->id]);

        Livewire::test(CommentSection::class, ['component' => $component])
            ->assertSee('4.3') // Average rating
            ->assertSee('6 reviews'); // Total count
    }

    public function test_no_reviews_message_displays_when_no_reviews()
    {
        $component = Component::factory()->create();

        Livewire::test(CommentSection::class, ['component' => $component])
            ->assertSee('No reviews yet. Be the first to review this product!');
    }

    public function test_auto_approval_works_for_good_reviews()
    {
        $user = User::factory()->create();
        $component = Component::factory()->create();
        
        // Create completed order
        $order = Order::factory()->completed()->create(['user_id' => $user->id]);
        OrderItem::factory()->create([
            'order_id' => $order->id,
            'component_id' => $component->id,
        ]);

        $this->actingAs($user);

        Livewire::test(CommentSection::class, ['component' => $component])
            ->call('toggleReviewForm')
            ->set('rating', 4)
            ->set('title', 'Great product with excellent performance')
            ->set('comment', 'This component exceeded my expectations. Great build quality and performance. Would definitely recommend to others.')
            ->call('submitReview');

        $review = Review::where('user_id', $user->id)->first();
        $this->assertTrue($review->is_approved);
    }

    public function test_flagged_reviews_require_moderation()
    {
        $user = User::factory()->create();
        $component = Component::factory()->create();
        
        // Create completed order
        $order = Order::factory()->completed()->create(['user_id' => $user->id]);
        OrderItem::factory()->create([
            'order_id' => $order->id,
            'component_id' => $component->id,
        ]);

        $this->actingAs($user);

        Livewire::test(CommentSection::class, ['component' => $component])
            ->call('toggleReviewForm')
            ->set('rating', 1)
            ->set('title', 'Terrible spam product')
            ->set('comment', 'This is fake and terrible quality.')
            ->call('submitReview');

        $review = Review::where('user_id', $user->id)->first();
        $this->assertFalse($review->is_approved);
    }
}