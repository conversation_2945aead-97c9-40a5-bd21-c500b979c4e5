@extends('admin.layouts.admin')

@section('title', 'Mail Configuration')
@section('page-title', 'Mail Configuration')

@section('content')
<div class="container mx-auto px-4 py-8">
    <div class="bg-white dark:bg-bg-dark rounded-lg shadow-md p-6 border border-border-light dark:border-border-dark">
        <h1 class="text-2xl font-bold mb-6 text-text-primary-light dark:text-text-primary-dark">Mail Configuration</h1>

        @if(session('success'))
            <div class="bg-green-100 dark:bg-green-900/20 border-l-4 border-green-500 dark:border-green-700 text-green-700 dark:text-green-200 p-4 mb-6" role="alert">
                <p>{{ session('success') }}</p>
            </div>
        @endif

        @if(session('error'))
            <div class="bg-red-100 dark:bg-red-900/20 border-l-4 border-red-500 dark:border-red-700 text-red-700 dark:text-red-200 p-4 mb-6" role="alert">
                <p>{{ session('error') }}</p>
            </div>
        @endif

        <form action="{{ route('admin.mail-config.update') }}" method="POST" class="mb-8">
            @csrf
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="mail_driver" class="block text-sm font-medium text-text-primary-light dark:text-text-primary-dark mb-2">
                        Mail Driver <span class="text-red-500">*</span>
                    </label>
                    <select id="mail_driver" name="mail_driver" required
                        class="w-full rounded-md border border-border-light dark:border-border-dark shadow-sm focus:border-primary-light dark:focus:border-primary-dark focus:ring focus:ring-primary-light dark:focus:ring-primary-dark focus:ring-opacity-50 bg-white dark:bg-bg-dark text-text-primary-light dark:text-text-primary-dark">
                        <option value="smtp" {{ $mailConfig['mail_driver'] == 'smtp' ? 'selected' : '' }}>SMTP</option>
                        <option value="sendmail" {{ $mailConfig['mail_driver'] == 'sendmail' ? 'selected' : '' }}>Sendmail</option>
                        <option value="mailgun" {{ $mailConfig['mail_driver'] == 'mailgun' ? 'selected' : '' }}>Mailgun</option>
                        <option value="ses" {{ $mailConfig['mail_driver'] == 'ses' ? 'selected' : '' }}>Amazon SES</option>
                        <option value="postmark" {{ $mailConfig['mail_driver'] == 'postmark' ? 'selected' : '' }}>Postmark</option>
                    </select>
                    @error('mail_driver')
                        <p class="text-red-500 dark:text-red-400 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="mail_host" class="block text-sm font-medium text-text-primary-light dark:text-text-primary-dark mb-2">
                        Mail Host <span class="text-red-500">*</span>
                    </label>
                    <input type="text" id="mail_host" name="mail_host" value="{{ $mailConfig['mail_host'] }}" required
                        class="w-full rounded-md border border-border-light dark:border-border-dark shadow-sm focus:border-primary-light dark:focus:border-primary-dark focus:ring focus:ring-primary-light dark:focus:ring-primary-dark focus:ring-opacity-50 bg-white dark:bg-bg-dark text-text-primary-light dark:text-text-primary-dark">
                    @error('mail_host')
                        <p class="text-red-500 dark:text-red-400 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="mail_port" class="block text-sm font-medium text-text-primary-light dark:text-text-primary-dark mb-2">
                        Mail Port <span class="text-red-500">*</span>
                    </label>
                    <input type="number" id="mail_port" name="mail_port" value="{{ $mailConfig['mail_port'] }}" required
                        class="w-full rounded-md border border-border-light dark:border-border-dark shadow-sm focus:border-primary-light dark:focus:border-primary-dark focus:ring focus:ring-primary-light dark:focus:ring-primary-dark focus:ring-opacity-50 bg-white dark:bg-bg-dark text-text-primary-light dark:text-text-primary-dark">
                    @error('mail_port')
                        <p class="text-red-500 dark:text-red-400 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="mail_encryption" class="block text-sm font-medium text-text-primary-light dark:text-text-primary-dark mb-2">
                        Encryption
                    </label>
                    <select id="mail_encryption" name="mail_encryption"
                        class="w-full rounded-md border border-border-light dark:border-border-dark shadow-sm focus:border-primary-light dark:focus:border-primary-dark focus:ring focus:ring-primary-light dark:focus:ring-primary-dark focus:ring-opacity-50 bg-white dark:bg-bg-dark text-text-primary-light dark:text-text-primary-dark">
                        <option value="" {{ $mailConfig['mail_encryption'] == '' ? 'selected' : '' }}>None</option>
                        <option value="tls" {{ $mailConfig['mail_encryption'] == 'tls' ? 'selected' : '' }}>TLS</option>
                        <option value="ssl" {{ $mailConfig['mail_encryption'] == 'ssl' ? 'selected' : '' }}>SSL</option>
                    </select>
                    @error('mail_encryption')
                        <p class="text-red-500 dark:text-red-400 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="mail_username" class="block text-sm font-medium text-text-primary-light dark:text-text-primary-dark mb-2">
                        Username
                    </label>
                    <input type="text" id="mail_username" name="mail_username" value="{{ $mailConfig['mail_username'] }}"
                        class="w-full rounded-md border border-border-light dark:border-border-dark shadow-sm focus:border-primary-light dark:focus:border-primary-dark focus:ring focus:ring-primary-light dark:focus:ring-primary-dark focus:ring-opacity-50 bg-white dark:bg-bg-dark text-text-primary-light dark:text-text-primary-dark">
                    @error('mail_username')
                        <p class="text-red-500 dark:text-red-400 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="mail_password" class="block text-sm font-medium text-text-primary-light dark:text-text-primary-dark mb-2">
                        Password
                    </label>
                    <input type="password" id="mail_password" name="mail_password" value="{{ $mailConfig['mail_password'] }}"
                        class="w-full rounded-md border border-border-light dark:border-border-dark shadow-sm focus:border-primary-light dark:focus:border-primary-dark focus:ring focus:ring-primary-light dark:focus:ring-primary-dark focus:ring-opacity-50 bg-white dark:bg-bg-dark text-text-primary-light dark:text-text-primary-dark">
                    @error('mail_password')
                        <p class="text-red-500 dark:text-red-400 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="mail_from_address" class="block text-sm font-medium text-text-primary-light dark:text-text-primary-dark mb-2">
                        From Address <span class="text-red-500">*</span>
                    </label>
                    <input type="email" id="mail_from_address" name="mail_from_address" value="{{ $mailConfig['mail_from_address'] }}" required
                        class="w-full rounded-md border border-border-light dark:border-border-dark shadow-sm focus:border-primary-light dark:focus:border-primary-dark focus:ring focus:ring-primary-light dark:focus:ring-primary-dark focus:ring-opacity-50 bg-white dark:bg-bg-dark text-text-primary-light dark:text-text-primary-dark">
                    @error('mail_from_address')
                        <p class="text-red-500 dark:text-red-400 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="mail_from_name" class="block text-sm font-medium text-text-primary-light dark:text-text-primary-dark mb-2">
                        From Name <span class="text-red-500">*</span>
                    </label>
                    <input type="text" id="mail_from_name" name="mail_from_name" value="{{ $mailConfig['mail_from_name'] }}" required
                        class="w-full rounded-md border border-border-light dark:border-border-dark shadow-sm focus:border-primary-light dark:focus:border-primary-dark focus:ring focus:ring-primary-light dark:focus:ring-primary-dark focus:ring-opacity-50 bg-white dark:bg-bg-dark text-text-primary-light dark:text-text-primary-dark">
                    @error('mail_from_name')
                        <p class="text-red-500 dark:text-red-400 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>
            </div>

            <div class="flex justify-end mt-6">
                <button type="submit"
                    class="bg-primary-light dark:bg-primary-dark text-white px-4 py-2 rounded-md hover:bg-primary-dark dark:hover:bg-primary-light focus:outline-none focus:ring-2 focus:ring-primary-light dark:focus:ring-primary-dark focus:ring-opacity-50">
                    Save Configuration
                </button>
            </div>
        </form>

        <div class="border-t border-border-light dark:border-border-dark pt-6">
            <h2 class="text-xl font-semibold mb-4 text-text-primary-light dark:text-text-primary-dark">Test Email Configuration</h2>
            <p class="text-text-secondary-light dark:text-text-secondary-dark mb-4">Send a test email to verify your configuration is working correctly.</p>
            
            <form id="test-email-form" class="flex flex-col md:flex-row gap-4 items-end">
                <div class="flex-grow">
                    <label for="test_email" class="block text-sm font-medium text-text-primary-light dark:text-text-primary-dark mb-2">
                        Email Address <span class="text-red-500">*</span>
                    </label>
                    <input type="email" id="test_email" name="test_email" required
                        class="w-full rounded-md border border-border-light dark:border-border-dark shadow-sm focus:border-primary-light dark:focus:border-primary-dark focus:ring focus:ring-primary-light dark:focus:ring-primary-dark focus:ring-opacity-50 bg-white dark:bg-bg-dark text-text-primary-light dark:text-text-primary-dark">
                </div>
                <div>
                    <button type="submit" id="send-test-email"
                        class="bg-green-600 dark:bg-green-700 text-white px-4 py-2 rounded-md hover:bg-green-700 dark:hover:bg-green-500 focus:outline-none focus:ring-2 focus:ring-green-500 dark:focus:ring-green-400 focus:ring-opacity-50">
                        Send Test Email
                    </button>
                </div>
            </form>
            
            <div id="test-email-result" class="mt-4 hidden"></div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const testEmailForm = document.getElementById('test-email-form');
        const testEmailResult = document.getElementById('test-email-result');
        const sendTestEmailButton = document.getElementById('send-test-email');
        
        testEmailForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Show loading state
            sendTestEmailButton.disabled = true;
            sendTestEmailButton.innerHTML = 'Sending...';
            testEmailResult.innerHTML = '';
            testEmailResult.className = 'mt-4 p-4';
            testEmailResult.classList.remove('hidden');
            testEmailResult.classList.add('bg-gray-100', 'text-gray-700', 'dark:bg-bg-dark', 'dark:text-text-primary-dark', 'border', 'border-border-light', 'dark:border-border-dark');
            testEmailResult.innerHTML = 'Sending test email, please wait...';
            
            // Get the CSRF token
            const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
            
            // Send the request
            fetch('{{ route("admin.mail-config.test") }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': csrfToken
                },
                body: JSON.stringify({
                    test_email: document.getElementById('test_email').value
                })
            })
            .then(response => response.json())
            .then(data => {
                // Reset button state
                sendTestEmailButton.disabled = false;
                sendTestEmailButton.innerHTML = 'Send Test Email';
                
                // Show result
                testEmailResult.innerHTML = data.message;
                testEmailResult.className = 'mt-4 p-4';
                
                if (data.success) {
                    testEmailResult.classList.add('bg-green-100', 'dark:bg-green-900/20', 'text-green-700', 'dark:text-green-200', 'border-l-4', 'border-green-500', 'dark:border-green-700');
                } else {
                    testEmailResult.classList.add('bg-red-100', 'dark:bg-red-900/20', 'text-red-700', 'dark:text-red-200', 'border-l-4', 'border-red-500', 'dark:border-red-700');
                }
            })
            .catch(error => {
                // Reset button state
                sendTestEmailButton.disabled = false;
                sendTestEmailButton.innerHTML = 'Send Test Email';
                
                // Show error
                testEmailResult.className = 'mt-4 p-4 bg-red-100 dark:bg-red-900/20 text-red-700 dark:text-red-200 border-l-4 border-red-500 dark:border-red-700';
                testEmailResult.innerHTML = 'An error occurred while sending the test email. Please try again.';
                console.error('Error:', error);
            });
        });
    });
</script>
@endpush