<div class="space-y-6">
    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div class="flex">
            <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-blue-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                </svg>
            </div>
            <div class="ml-3">
                <h3 class="text-sm font-medium text-blue-800">Razorpay Configuration</h3>
                <div class="mt-2 text-sm text-blue-700">
                    <p>Get your API credentials from the <a href="https://dashboard.razorpay.com/app/keys" target="_blank" class="underline">Razorpay Dashboard</a>.</p>
                    <p class="mt-1">For webhooks, use: <code class="bg-blue-100 px-1 rounded">{{ url('/webhooks/razorpay') }}</code></p>
                </div>
            </div>
        </div>
    </div>

    <!-- Key ID -->
    <div>
        <label for="key_id" class="block text-sm font-medium text-gray-700 mb-2">
            Key ID <span class="text-red-500">*</span>
        </label>
        <input type="text" 
               id="key_id" 
               name="settings[key_id]" 
               value="{{ old('settings.key_id', $settings['key_id'] ?? '') }}"
               placeholder="rzp_test_xxxxxxxxxx or rzp_live_xxxxxxxxxx"
               class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 @error('settings.key_id') border-red-500 @enderror"
               required>
        @error('settings.key_id')
            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
        @enderror
        <p class="mt-1 text-sm text-gray-500">Your Razorpay Key ID from the dashboard</p>
    </div>

    <!-- Key Secret -->
    <div>
        <label for="key_secret" class="block text-sm font-medium text-gray-700 mb-2">
            Key Secret <span class="text-red-500">*</span>
        </label>
        <div class="relative">
            <input type="password" 
                   id="key_secret" 
                   name="settings[key_secret]" 
                   value="{{ old('settings.key_secret', $settings['key_secret'] ?? '') }}"
                   placeholder="Enter your Razorpay Key Secret"
                   class="w-full px-3 py-2 pr-10 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 @error('settings.key_secret') border-red-500 @enderror"
                   required>
            <button type="button" 
                    onclick="togglePasswordVisibility('key_secret')"
                    class="absolute inset-y-0 right-0 pr-3 flex items-center">
                <svg class="h-5 w-5 text-gray-400" id="key_secret_eye_open" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                </svg>
                <svg class="h-5 w-5 text-gray-400 hidden" id="key_secret_eye_closed" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                </svg>
            </button>
        </div>
        @error('settings.key_secret')
            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
        @enderror
        <p class="mt-1 text-sm text-gray-500">Your Razorpay Key Secret (keep this secure)</p>
    </div>

    <!-- Webhook Secret -->
    <div>
        <label for="webhook_secret" class="block text-sm font-medium text-gray-700 mb-2">
            Webhook Secret
        </label>
        <div class="relative">
            <input type="password" 
                   id="webhook_secret" 
                   name="settings[webhook_secret]" 
                   value="{{ old('settings.webhook_secret', $settings['webhook_secret'] ?? '') }}"
                   placeholder="Enter your Razorpay Webhook Secret (optional)"
                   class="w-full px-3 py-2 pr-10 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 @error('settings.webhook_secret') border-red-500 @enderror">
            <button type="button" 
                    onclick="togglePasswordVisibility('webhook_secret')"
                    class="absolute inset-y-0 right-0 pr-3 flex items-center">
                <svg class="h-5 w-5 text-gray-400" id="webhook_secret_eye_open" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                </svg>
                <svg class="h-5 w-5 text-gray-400 hidden" id="webhook_secret_eye_closed" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                </svg>
            </button>
        </div>
        @error('settings.webhook_secret')
            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
        @enderror
        <p class="mt-1 text-sm text-gray-500">Optional: Used for webhook signature verification (recommended for security)</p>
    </div>
</div>

<script>
function togglePasswordVisibility(fieldId) {
    const field = document.getElementById(fieldId);
    const eyeOpen = document.getElementById(fieldId + '_eye_open');
    const eyeClosed = document.getElementById(fieldId + '_eye_closed');
    
    if (field.type === 'password') {
        field.type = 'text';
        eyeOpen.classList.add('hidden');
        eyeClosed.classList.remove('hidden');
    } else {
        field.type = 'password';
        eyeOpen.classList.remove('hidden');
        eyeClosed.classList.add('hidden');
    }
}
</script>