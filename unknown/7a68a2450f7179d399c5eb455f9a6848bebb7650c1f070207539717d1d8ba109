<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON>vel Installer - Database Configuration</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <style>
        /* Base Styles */
        .gradient-background {
            background: linear-gradient(135deg, #EBF4FF 0%, #E6FFFA 100%);
            min-height: 100vh;
        }

        /* Animation Keyframes */
        @keyframes slideIn {
            from { opacity: 0; transform: translateY(-20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes scaleIn {
            from { transform: scale(0.95); opacity: 0; }
            to { transform: scale(1); opacity: 1; }
        }

        /* Component Styles */
        .installer-card {
            animation: scaleIn 0.5s ease-out;
            background: white;
            border-radius: 1rem;
            box-shadow: 0 10px 25px -5px rgba(59, 130, 246, 0.1), 0 8px 10px -6px rgba(59, 130, 246, 0.1);
        }

        .installer-header {
            background: linear-gradient(135deg, #4F46E5 0%, #3B82F6 100%);
            border-radius: 1rem 1rem 0 0;
            padding: 2rem;
        }

        .form-group {
            animation: fadeIn 0.5s ease-out forwards;
            opacity: 0;
        }

        .form-group:nth-child(1) { animation-delay: 0.1s; }
        .form-group:nth-child(2) { animation-delay: 0.2s; }
        .form-group:nth-child(3) { animation-delay: 0.3s; }
        .form-group:nth-child(4) { animation-delay: 0.4s; }
        .form-group:nth-child(5) { animation-delay: 0.5s; }

        .hover-scale {
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }

        .hover-scale:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 6px -1px rgba(59, 130, 246, 0.2), 0 2px 4px -1px rgba(59, 130, 246, 0.1);
        }

        .progress-bar {
            display: flex;
            margin: 2rem 0;
            justify-content: space-between;
            position: relative;
        }

        .progress-bar::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 2px;
            background: #E5E7EB;
            transform: translateY(-50%);
            z-index: 0;
        }

        .progress-step {
            width: 2.5rem;
            height: 2.5rem;
            border-radius: 50%;
            background: white;
            border: 2px solid #E5E7EB;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            z-index: 1;
            transition: all 0.3s ease;
        }

        .progress-step.active {
            border-color: #3B82F6;
            background: #3B82F6;
            color: white;
        }

        .progress-step.completed {
            border-color: #10B981;
            background: #10B981;
            color: white;
        }

        .floating-label {
            position: absolute;
            left: 1rem;
            top: 0.75rem;
            transition: all 0.2s ease;
            pointer-events: none;
            opacity: 0.6;
            background-color: white;
            padding: 0 0.25rem;
        }

        .form-input:focus ~ .floating-label,
        .form-input:not(:placeholder-shown) ~ .floating-label {
            transform: translateY(-1.25rem) scale(0.85);
            opacity: 1;
            color: #3B82F6;
        }

        .test-connection-status {
            transition: all 0.3s ease;
        }
    </style>
</head>

<body class="gradient-background">
    <div class="min-h-screen flex items-center justify-center p-4">
        <div class="max-w-4xl w-full">
            <!-- Progress Bar -->
            <div class="progress-bar mb-8">
                <div class="progress-step completed">1</div>
                <div class="progress-step completed">2</div>
                <div class="progress-step completed">3</div>
                <div class="progress-step completed">4</div>
                <div class="progress-step active">5</div>
            </div>

            <div class="installer-card">
                <!-- Header -->
                <div class="installer-header text-center">
                    <div class="flex justify-center mb-6">
                        <i data-lucide="database" class="h-16 w-16 text-white opacity-90"></i>
                    </div>
                    <h1 class="text-4xl font-bold text-white mb-2">Database Configuration</h1>
                    <p class="text-blue-100 text-lg">Set up your database connection</p>
                </div>

                <!-- Content -->
                <div class="p-8">
                    <div class="space-y-8">
                        <!-- Introduction -->
                        <div class="text-center max-w-2xl mx-auto">
                            <p class="text-gray-600 text-lg leading-relaxed">
                                Configure your database settings and create the initial database structure.
                            </p>
                        </div>

                        <!-- Database Configuration Form -->
                        <form action="{{ route('installer.database.save') }}" method="POST" class="space-y-6">
                            @csrf
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <!-- Database Host -->
                                <div class="form-group relative">
                                    <input type="text" id="db_host" name="db_host" value="127.0.0.1" required placeholder=" "
                                        class="form-input block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200">
                                    <label for="db_host" class="floating-label flex items-center text-sm font-medium text-gray-700">
                                        <i data-lucide="server" class="h-4 w-4 mr-2"></i>
                                        Database Host
                                    </label>
                                </div>

                                <!-- Database Port -->
                                <div class="form-group relative">
                                    <input type="text" id="db_port" name="db_port" value="3306" required placeholder=" "
                                        class="form-input block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200">
                                    <label for="db_port" class="floating-label flex items-center text-sm font-medium text-gray-700">
                                        <i data-lucide="git-branch" class="h-4 w-4 mr-2"></i>
                                        Database Port
                                    </label>
                                </div>

                                <!-- Database Name -->
                                <div class="form-group relative">
                                    <input type="text" id="db_name" name="db_name" required placeholder=" "
                                        class="form-input block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200">
                                    <label for="db_name" class="floating-label flex items-center text-sm font-medium text-gray-700">
                                        <i data-lucide="database" class="h-4 w-4 mr-2"></i>
                                        Database Name
                                    </label>
                                </div>

                                <!-- Database Username -->
                                <div class="form-group relative">
                                    <input type="text" id="db_user" name="db_user" required placeholder=" "
                                        class="form-input block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200">
                                    <label for="db_user" class="floating-label flex items-center text-sm font-medium text-gray-700">
                                        <i data-lucide="user" class="h-4 w-4 mr-2"></i>
                                        Database Username
                                    </label>
                                </div>

                                <!-- Database Password -->
                                <div class="form-group relative">
                                    <input type="password" id="db_password" name="db_password" placeholder=" "
                                        class="form-input block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200">
                                    <label for="db_password" class="floating-label flex items-center text-sm font-medium text-gray-700">
                                        <i data-lucide="key" class="h-4 w-4 mr-2"></i>
                                        Database Password
                                    </label>
                                </div>
                            </div>

                            <div class="input-group">
                                <input type="text" id="app_url" name="app_url" placeholder=" "
                                    class="form-input block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200">
                                <label for="app_url"
                                    class="floating-label flex items-center text-sm font-medium text-gray-700">
                                    <i data-lucide="globe" class="h-4 w-4 mr-2"></i>
                                    App URL
                                </label>
                            </div>

                            <div class="input-group">
                                <select id="app_timezone" name="app_timezone" required
                                    class="form-input block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200">
                                    <option value="">Select Timezone</option>
                                    <optgroup label="Common Timezones">
                                        <option value="UTC">UTC</option>
                                        <option value="America/New_York">America/New_York (UTC-5/UTC-4)</option>
                                        <option value="America/Chicago">America/Chicago (UTC-6/UTC-5)</option>
                                        <option value="America/Denver">America/Denver (UTC-7/UTC-6)</option>
                                        <option value="America/Los_Angeles">America/Los_Angeles (UTC-8/UTC-7)</option>
                                        <option value="Europe/London">Europe/London (UTC+0/UTC+1)</option>
                                        <option value="Europe/Berlin">Europe/Berlin (UTC+1/UTC+2)</option>
                                        <option value="Europe/Paris">Europe/Paris (UTC+1/UTC+2)</option>
                                        <option value="Asia/Dubai">Asia/Dubai (UTC+4)</option>
                                        <option value="Asia/Singapore">Asia/Singapore (UTC+8)</option>
                                        <option value="Asia/Tokyo">Asia/Tokyo (UTC+9)</option>
                                        <option value="Australia/Sydney">Australia/Sydney (UTC+10/UTC+11)</option>
                                    </optgroup>
                                </select>
                                <label for="app_timezone"
                                    class="floating-label flex items-center text-sm font-medium text-gray-700">
                                    <i data-lucide="clock" class="h-4 w-4 mr-2"></i>
                                    Application Timezone
                                </label>
                            </div>

                            @if ($errors->any())
                                <div class="bg-red-50 border border-red-200 rounded-xl p-4">
                                    <div class="flex items-start">
                                        <i data-lucide="alert-triangle" class="h-5 w-5 text-red-500 mt-0.5 mr-3"></i>
                                        <div>
                                            <h3 class="text-red-800 font-medium">Configuration Error</h3>
                                            <ul class="mt-2 text-sm text-red-700 space-y-1">
                                                @foreach ($errors->all() as $error)
                                                    <li>• {{ $error }}</li>
                                                @endforeach
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            @endif

                            <!-- Test Connection Status -->
                            <div id="test-connection-status" class="hidden bg-gray-50 border border-gray-200 rounded-xl p-4">
                                <div class="flex items-center">
                                    <i id="status-icon" data-lucide="loader" class="h-5 w-5 mr-3 animate-spin"></i>
                                    <span id="status-message">Testing connection...</span>
                                </div>
                            </div>

                            <!-- Action Buttons -->
                            <div class="flex space-x-4">
                                <a href="{{ route('installer.email-config') }}"
                                    class="flex-1 flex justify-center items-center py-3 px-4 rounded-lg text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-all duration-200">
                                    <i data-lucide="arrow-left" class="h-5 w-5 mr-2"></i>
                                    Back
                                </a>

                                <button type="button" id="test-connection"
                                    class="hover-scale flex-1 flex justify-center items-center py-3 px-4 rounded-lg text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-all duration-200">
                                    <i data-lucide="activity" class="h-5 w-5 mr-2"></i>
                                    Test Connection
                                </button>

                                <button type="submit"
                                    class="hover-scale flex-1 flex justify-center items-center py-3 px-4 rounded-lg text-sm font-medium text-white bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200 shadow-lg">
                                    <i data-lucide="save" class="h-5 w-5 mr-2"></i>
                                    Save and Continue
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Initialize Lucide icons
        lucide.createIcons();

        // Test Connection Handler
        document.getElementById('test-connection').addEventListener('click', function() {
            const statusDiv = document.getElementById('test-connection-status');
            const statusIcon = document.getElementById('status-icon');
            const statusMessage = document.getElementById('status-message');

            // Show testing state
            statusDiv.classList.remove('hidden');
            statusIcon.setAttribute('data-lucide', 'loader');
            statusMessage.textContent = 'Testing connection...';
            lucide.createIcons();

            // Collect form data
            const formData = new FormData(document.querySelector('form'));
            
            // Make API call to test connection
            fetch('{{ route("installer.database.test") }}', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('input[name="_token"]').value
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    statusIcon.setAttribute('data-lucide', 'check-circle');
                    statusMessage.textContent = 'Connection successful!';
                    statusDiv.classList.remove('bg-gray-50', 'border-gray-200');
                    statusDiv.classList.add('bg-green-50', 'border-green-200');
                    statusMessage.classList.add('text-green-700');
                } else {
                    statusIcon.setAttribute('data-lucide', 'x-circle');
                    statusMessage.textContent = 'Connection failed: ' + data.message;
                    statusDiv.classList.remove('bg-gray-50', 'border-gray-200');
                    statusDiv.classList.add('bg-red-50', 'border-red-200');
                    statusMessage.classList.add('text-red-700');
                }
                lucide.createIcons();
            })
            .catch(error => {
                statusIcon.setAttribute('data-lucide', 'x-circle');
                statusMessage.textContent = 'Connection failed: ' + error.message;
                statusDiv.classList.remove('bg-gray-50', 'border-gray-200');
                statusDiv.classList.add('bg-red-50', 'border-red-200');
                statusMessage.classList.add('text-red-700');
                lucide.createIcons();
            });
        });

        document.addEventListener('DOMContentLoaded', () => {
            const timezoneSelect = document.getElementById('app_timezone');
            
            // Detect user's timezone
            const userTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
            
            // Set detected timezone as selected
            if (userTimezone) {
                const options = timezoneSelect.getElementsByTagName('option');
                for (let option of options) {
                    if (option.value === userTimezone) {
                        option.selected = true;
                        break;
                    }
                }
            }

            // Add full timezone list
            fetch('https://raw.githubusercontent.com/moment/moment-timezone/master/data/meta/latest.json')
                .then(response => response.json())
                .then(data => {
                    const zones = Object.keys(data.zones).sort();
                    
                    // Create "All Timezones" optgroup
                    const allZonesGroup = document.createElement('optgroup');
                    allZonesGroup.label = 'All Timezones';
                    
                    zones.forEach(zone => {
                        // Skip if already in common timezones
                        if (!timezoneSelect.querySelector(`option[value="${zone}"]`)) {
                            const option = document.createElement('option');
                            option.value = zone;
                            option.textContent = `${zone}`;
                            allZonesGroup.appendChild(option);
                        }
                    });
                    
                    timezoneSelect.appendChild(allZonesGroup);
                })
                .catch(error => console.warn('Failed to load full timezone list:', error));

            // Handle floating label
            const floatingLabel = timezoneSelect.parentElement.querySelector('.floating-label');
            if (timezoneSelect.value) {
                floatingLabel.classList.add('transform', '-translate-y-6', 'scale-75', 'text-blue-500');
            }

            timezoneSelect.addEventListener('change', () => {
                if (timezoneSelect.value) {
                    floatingLabel.classList.add('transform', '-translate-y-6', 'scale-75', 'text-blue-500');
                } else {
                    floatingLabel.classList.remove('transform', '-translate-y-6', 'scale-75', 'text-blue-500');
                }
            });
        });
    </script>
</body>

</html>
