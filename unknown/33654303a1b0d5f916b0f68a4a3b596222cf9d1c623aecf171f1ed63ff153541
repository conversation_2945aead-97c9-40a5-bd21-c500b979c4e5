<?php

namespace App\Livewire\User;

use Livewire\Component;
use Livewire\WithPagination;
use Livewire\Attributes\On;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use App\Models\Order;
use App\Models\Build;
use App\Models\Component as ComponentModel;
use App\Models\Review;
use App\Services\BuilderService;
use Carbon\Carbon;

class Dashboard extends Component
{
    use WithPagination;
    
    public $activeTab = 'overview';
    public $selectedOrder = null;
    public $dateRange = '30'; // days
    public $showNotifications = true;
    
    // Build management
    public $buildSearch = '';
    public $buildSortBy = 'updated_at';
    public $buildSortDirection = 'desc';
    public $showPublicBuildsOnly = false;
    public $buildCategoryFilter = '';
    
    // Order management
    public $orderSearch = '';
    public $orderStatusFilter = '';
    public $orderSortBy = 'created_at';
    public $orderSortDirection = 'desc';
    public $orderDateFilter = '';
    
    // UI state
    public $loadingStats = false;
    public $showQuickActions = true;
    
    protected $queryString = [
        'activeTab' => ['except' => 'overview'],
        'buildSearch' => ['except' => ''],
        'orderSearch' => ['except' => ''],
        'orderStatusFilter' => ['except' => ''],
        'dateRange' => ['except' => '30'],
    ];
    
    protected $listeners = [
        'buildSaved' => 'refreshBuilds',
        'buildDeleted' => 'refreshBuilds',
        'buildCloned' => 'refreshBuilds',
        'refreshDashboard' => 'refreshData',
    ];

    public function setActiveTab($tab)
    {
        $this->activeTab = $tab;
        $this->selectedOrder = null;
        $this->resetPage();
    }

    // Build Management Methods
    public function updatingBuildSearch()
    {
        $this->resetPage();
    }

    public function editBuild($buildId)
    {
        return redirect()->route('builder.index', ['build' => $buildId]);
    }

    public function cloneBuild($buildId)
    {
        $originalBuild = Build::find($buildId);
        
        if (!$originalBuild) {
            session()->flash('error', 'Build not found.');
            return;
        }
        
        $builderService = app(BuilderService::class);
        
        try {
            $clonedBuild = $builderService->cloneBuild($originalBuild, auth()->user(), [
                'name' => 'Copy of ' . $originalBuild->name,
                'is_public' => false,
            ]);
            
            session()->flash('message', 'Build cloned successfully!');
            $this->dispatch('buildCloned', $clonedBuild->id);
            
        } catch (\Exception $e) {
            session()->flash('error', 'Failed to clone build: ' . $e->getMessage());
        }
    }

    public function deleteBuild($buildId)
    {
        $build = Build::find($buildId);
        
        if (!$build || $build->user_id !== auth()->id()) {
            session()->flash('error', 'Build not found or access denied.');
            return;
        }
        
        $builderService = app(BuilderService::class);
        
        try {
            $builderService->deleteBuild($build, auth()->user());
            session()->flash('message', 'Build deleted successfully.');
            $this->dispatch('buildDeleted', $buildId);
            
        } catch (\Exception $e) {
            session()->flash('error', 'Failed to delete build: ' . $e->getMessage());
        }
    }

    public function toggleBuildVisibility($buildId)
    {
        $build = Build::find($buildId);
        
        if (!$build || $build->user_id !== auth()->id()) {
            session()->flash('error', 'Build not found or access denied.');
            return;
        }
        
        $builderService = app(BuilderService::class);
        $build = $builderService->toggleBuildVisibility($build);
        
        $visibility = $build->is_public ? 'public' : 'private';
        session()->flash('message', "Build is now {$visibility}.");
    }

    public function copyBuildShareUrl($buildId)
    {
        $build = Build::find($buildId);
        
        if (!$build || $build->user_id !== auth()->id()) {
            session()->flash('error', 'Build not found or access denied.');
            return;
        }
        
        $builderService = app(BuilderService::class);
        
        try {
            if ($build->is_public) {
                $shareUrl = $builderService->generateShareableUrl($build);
            } else {
                $shareUrl = $builderService->generatePrivateShareUrl($build);
            }
            
            $this->dispatch('copyToClipboard', $shareUrl);
            session()->flash('message', 'Share URL copied to clipboard!');
            
        } catch (\Exception $e) {
            session()->flash('error', 'Unable to generate share URL.');
        }
    }

    // Order Management Methods
    public function updatingOrderSearch()
    {
        $this->resetPage();
    }

    public function updatingOrderStatusFilter()
    {
        $this->resetPage();
    }

    public function selectOrder($orderId)
    {
        $order = Order::where('user_id', Auth::id())
            ->with(['items.component', 'payment', 'build'])
            ->find($orderId);
            
        $this->selectedOrder = $order;
    }

    public function clearSelectedOrder()
    {
        $this->selectedOrder = null;
    }

    public function reorderItems($orderId)
    {
        $order = Order::where('user_id', Auth::id())->find($orderId);
        
        if (!$order) {
            session()->flash('error', 'Order not found.');
            return;
        }

        $cartService = app(\App\Services\CartService::class);
        
        try {
            foreach ($order->items as $item) {
                if ($item->component && $item->component->stock_quantity > 0) {
                    $cartService->addToCart($item->component, $item->quantity, Auth::user());
                }
            }
            
            session()->flash('message', 'Items added to cart successfully!');
            return redirect()->route('cart.index');
            
        } catch (\Exception $e) {
            session()->flash('error', 'Failed to add items to cart: ' . $e->getMessage());
        }
    }

    public function refreshData()
    {
        // Clear cached data
        $this->clearStatsCache();
        $this->dispatch('$refresh');
    }

    public function updateDateRange($range)
    {
        $this->dateRange = $range;
        $this->clearStatsCache();
    }

    public function toggleNotifications()
    {
        $this->showNotifications = !$this->showNotifications;
    }

    public function markAllNotificationsRead()
    {
        Auth::user()->unreadNotifications()->update(['read_at' => now()]);
        $this->dispatch('notificationsRead');
    }

    private function clearStatsCache()
    {
        $userId = Auth::id();
        Cache::forget("user_stats_{$userId}_{$this->dateRange}");
        Cache::forget("user_activity_{$userId}");
    }

    private function getCachedStats()
    {
        $userId = Auth::id();
        $cacheKey = "user_stats_{$userId}_{$this->dateRange}";
        
        return Cache::remember($cacheKey, now()->addMinutes(15), function () use ($userId) {
            $dateFrom = Carbon::now()->subDays($this->dateRange);
            
            return [
                'total_builds' => Build::where('user_id', $userId)->count(),
                'public_builds' => Build::where('user_id', $userId)->where('is_public', true)->count(),
                'total_orders' => Order::where('user_id', $userId)->count(),
                'total_spent' => Order::where('user_id', $userId)->where('status', 'completed')->sum('total'),
                'pending_orders' => Order::where('user_id', $userId)->where('status', 'pending')->count(),
                'completed_orders' => Order::where('user_id', $userId)->where('status', 'completed')->count(),
                'recent_activity_count' => $this->getRecentActivityCount($userId, $dateFrom),
                'favorite_components' => $this->getFavoriteComponents($userId),
                'build_completion_rate' => $this->getBuildCompletionRate($userId),
                'average_build_price' => $this->getAverageBuildPrice($userId),
            ];
        });
    }

    private function getRecentActivityCount($userId, $dateFrom)
    {
        $buildsCount = Build::where('user_id', $userId)
            ->where('updated_at', '>=', $dateFrom)
            ->count();
            
        $ordersCount = Order::where('user_id', $userId)
            ->where('created_at', '>=', $dateFrom)
            ->count();
            
        return $buildsCount + $ordersCount;
    }

    private function getFavoriteComponents($userId)
    {
        return DB::table('build_components')
            ->join('builds', 'build_components.build_id', '=', 'builds.id')
            ->join('components', 'build_components.component_id', '=', 'components.id')
            ->join('component_categories', 'components.category_id', '=', 'component_categories.id')
            ->where('builds.user_id', $userId)
            ->select('component_categories.name', DB::raw('COUNT(*) as usage_count'))
            ->groupBy('component_categories.id', 'component_categories.name')
            ->orderBy('usage_count', 'desc')
            ->limit(3)
            ->get();
    }

    private function getBuildCompletionRate($userId)
    {
        $totalBuilds = Build::where('user_id', $userId)->count();
        if ($totalBuilds === 0) return 0;
        
        // Get builds with 5 or more components using a subquery
        $completedBuilds = Build::where('user_id', $userId)
            ->whereIn('id', function($query) {
                $query->select('build_id')
                      ->from('build_components')
                      ->groupBy('build_id')
                      ->havingRaw('COUNT(*) >= 5');
            })
            ->count();
            
        return round(($completedBuilds / $totalBuilds) * 100, 1);
    }

    private function getAverageBuildPrice($userId)
    {
        return Build::where('user_id', $userId)
            ->where('total_price', '>', 0)
            ->avg('total_price') ?? 0;
    }

    private function getNotifications()
    {
        return Auth::user()
            ->unreadNotifications()
            ->limit(5)
            ->get()
            ->map(function ($notification) {
                return [
                    'id' => $notification->id,
                    'type' => $notification->type,
                    'data' => $notification->data,
                    'created_at' => $notification->created_at,
                ];
            });
    }

    public function render()
    {
        if (!Auth::check()) {
            return view('livewire.user.dashboard', [
                'builds' => collect(),
                'orders' => collect(),
                'recentBuilds' => collect(),
                'recentOrders' => collect(),
                'stats' => [
                    'total_builds' => 0,
                    'public_builds' => 0,
                    'total_orders' => 0,
                    'total_spent' => 0,
                    'pending_orders' => 0,
                    'completed_orders' => 0,
                    'recent_activity_count' => 0,
                    'favorite_components' => collect(),
                    'build_completion_rate' => 0,
                    'average_build_price' => 0,
                ],
                'notifications' => collect(),
                'quickActions' => collect(),
            ]);
        }

        $user = Auth::user();
        
        // Get cached stats for better performance
        $stats = $this->getCachedStats();

        // Get recent items for overview
        $recentBuilds = Build::where('user_id', $user->id)
            ->with(['components.component.category'])
            ->orderBy('updated_at', 'desc')
            ->limit(5)
            ->get();

        $recentOrders = Order::where('user_id', $user->id)
            ->with(['items.component'])
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get();

        // Get notifications
        $notifications = $this->showNotifications ? $this->getNotifications() : collect();

        // Quick actions based on user activity
        $quickActions = $this->getQuickActions();

        // Get paginated data for tabs
        $builds = collect();
        $orders = collect();

        if ($this->activeTab === 'builds') {
            $buildQuery = Build::where('user_id', $user->id);
            
            if (!empty($this->buildSearch)) {
                $buildQuery->where(function ($q) {
                    $q->where('name', 'like', '%' . $this->buildSearch . '%')
                      ->orWhere('description', 'like', '%' . $this->buildSearch . '%');
                });
            }
            
            if ($this->showPublicBuildsOnly) {
                $buildQuery->where('is_public', true);
            }

            if (!empty($this->buildCategoryFilter)) {
                $buildQuery->whereHas('components.component.category', function($q) {
                    $q->where('slug', $this->buildCategoryFilter);
                });
            }
            
            $buildQuery->orderBy($this->buildSortBy, $this->buildSortDirection);
            $builds = $buildQuery->with(['components.component.category'])->paginate(9, ['*'], 'buildsPage');
        }

        if ($this->activeTab === 'orders') {
            $orderQuery = Order::where('user_id', $user->id)->with(['items.component', 'payment']);
            
            if (!empty($this->orderSearch)) {
                $orderQuery->where(function ($q) {
                    $q->where('order_number', 'like', '%' . $this->orderSearch . '%')
                      ->orWhereHas('items', function ($itemQuery) {
                          $itemQuery->where('name', 'like', '%' . $this->orderSearch . '%');
                      });
                });
            }
            
            if (!empty($this->orderStatusFilter)) {
                $orderQuery->where('status', $this->orderStatusFilter);
            }

            if (!empty($this->orderDateFilter)) {
                $date = Carbon::now();
                switch ($this->orderDateFilter) {
                    case '7d':
                        $orderQuery->where('created_at', '>=', $date->subDays(7));
                        break;
                    case '30d':
                        $orderQuery->where('created_at', '>=', $date->subDays(30));
                        break;
                    case '90d':
                        $orderQuery->where('created_at', '>=', $date->subDays(90));
                        break;
                }
            }
            
            $orderQuery->orderBy($this->orderSortBy, $this->orderSortDirection);
            $orders = $orderQuery->paginate(12, ['*'], 'ordersPage');
        }

        return view('livewire.user.dashboard', compact(
            'builds', 
            'orders', 
            'recentBuilds', 
            'recentOrders', 
            'stats', 
            'notifications',
            'quickActions'
        ));
    }

    private function getQuickActions()
    {
        $actions = collect();
        $user = Auth::user();
        
        // Suggest actions based on user activity
        if ($user->builds()->count() === 0) {
            $actions->push([
                'title' => 'Create Your First Build',
                'description' => 'Start building your dream PC',
                'url' => route('builder.index'),
                'icon' => 'plus',
                'color' => 'blue'
            ]);
        }
        
        if ($user->orders()->where('status', 'pending')->count() > 0) {
            $actions->push([
                'title' => 'Track Your Orders',
                'description' => 'Check the status of your pending orders',
                'action' => 'setActiveTab',
                'params' => ['orders'],
                'icon' => 'truck',
                'color' => 'yellow'
            ]);
        }

        if ($user->builds()->where('is_public', false)->count() > 0) {
            $actions->push([
                'title' => 'Share Your Builds',
                'description' => 'Make your builds public to help others',
                'action' => 'setActiveTab',
                'params' => ['builds'],
                'icon' => 'share',
                'color' => 'green'
            ]);
        }
        
        return $actions->take(3);
    }
}