<?php

namespace App\Jobs;

use App\Services\PriceTrackingService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class UpdateComponentPrices implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public int $timeout = 300; // 5 minutes
    public int $tries = 3;

    /**
     * Execute the job.
     */
    public function handle(PriceTrackingService $priceTrackingService): void
    {
        Log::info('Starting automated price update job');
        
        $results = $priceTrackingService->updateAllPrices();
        
        Log::info('Price update job completed', [
            'updated' => $results['updated'],
            'failed' => $results['failed'],
            'unchanged' => $results['unchanged']
        ]);
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('Price update job failed: ' . $exception->getMessage());
    }
}