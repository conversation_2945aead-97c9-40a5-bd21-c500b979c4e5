<?php

namespace App\Services;

use App\Models\Coupon;
use App\Models\Product;
use Illuminate\Support\Facades\Auth;

class CouponService
{
    /**
     * Validate and apply coupon to cart.
     */
    public function validateAndApplyCoupon(string $code, array $cartItems): array
    {
        $coupon = Coupon::findByCode($code);
        
        if (!$coupon) {
            return $this->errorResponse('Invalid coupon code.');
        }

        $userId = Auth::id();
        if (!$coupon->canBeUsedBy($userId)) {
            return $this->errorResponse($this->getCouponErrorMessage($coupon));
        }

        $productIds = collect($cartItems)->pluck('product_id')->toArray();
        $subtotal = $this->calculateSubtotal($cartItems);

        if (!$coupon->isApplicableToProducts($productIds)) {
            return $this->errorResponse('This coupon is not applicable to selected products.');
        }

        if ($coupon->minimum_amount && $subtotal < $coupon->minimum_amount) {
            return $this->errorResponse(
                "Minimum order amount of ₹" . number_format($coupon->minimum_amount, 2) . " required."
            );
        }

        $discount = $coupon->calculateDiscount($subtotal);

        return $this->successResponse($coupon, $discount, $subtotal);
    }

    /**
     * Calculate subtotal from cart items.
     */
    private function calculateSubtotal(array $cartItems): float
    {
        $subtotal = 0;
        
        foreach ($cartItems as $item) {
            $product = Product::find($item['product_id']);
            if ($product) {
                $subtotal += $product->effective_price * $item['quantity'];
            }
        }
        
        return $subtotal;
    }

    /**
     * Get applicable coupons for user and products.
     */
    public function getApplicableCoupons(array $productIds = [], float $subtotal = 0): array
    {
        $userId = Auth::id();
        $coupons = Coupon::valid()->get();
        $applicable = [];

        foreach ($coupons as $coupon) {
            if ($coupon->canBeUsedBy($userId)) {
                if (empty($productIds) || $coupon->isApplicableToProducts($productIds)) {
                    if (!$coupon->minimum_amount || $subtotal >= $coupon->minimum_amount) {
                        $applicable[] = [
                            'code' => $coupon->code,
                            'name' => $coupon->name,
                            'description' => $coupon->description,
                            'type' => $coupon->type,
                            'formatted_value' => $coupon->formatted_value,
                            'minimum_amount' => $coupon->minimum_amount,
                            'expires_at' => $coupon->expires_at?->format('M d, Y'),
                            'discount' => $coupon->calculateDiscount($subtotal),
                        ];
                    }
                }
            }
        }

        return $applicable;
    }

    /**
     * Get coupon usage statistics.
     */
    public function getCouponStats(Coupon $coupon): array
    {
        return [
            'total_usage' => $coupon->used_count,
            'remaining_usage' => $coupon->usage_limit ? ($coupon->usage_limit - $coupon->used_count) : null,
            'usage_percentage' => $coupon->usage_limit ? round(($coupon->used_count / $coupon->usage_limit) * 100, 2) : 0,
            'total_discount_given' => $coupon->usages()->sum('discount_amount'),
            'unique_users' => $coupon->usages()->distinct('user_id')->count(),
            'recent_usages' => $coupon->usages()
                ->with(['user', 'order'])
                ->latest()
                ->limit(10)
                ->get(),
        ];
    }

    /**
     * Check if coupon code is available.
     */
    public function isCodeAvailable(string $code, ?int $excludeId = null): bool
    {
        $query = Coupon::where('code', strtoupper($code));
        
        if ($excludeId) {
            $query->where('id', '!=', $excludeId);
        }
        
        return !$query->exists();
    }

    /**
     * Generate unique coupon code.
     */
    public function generateUniqueCode(int $length = 8): string
    {
        do {
            $code = strtoupper(substr(str_shuffle('ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'), 0, $length));
        } while (!$this->isCodeAvailable($code));

        return $code;
    }

    /**
     * Get error message for coupon validation failure.
     */
    private function getCouponErrorMessage(Coupon $coupon): string
    {
        if (!$coupon->is_active) {
            return 'This coupon is not active.';
        }

        if ($coupon->starts_at && $coupon->starts_at->gt(now())) {
            return 'This coupon is not yet active.';
        }

        if ($coupon->expires_at && $coupon->expires_at->lt(now())) {
            return 'This coupon has expired.';
        }

        if ($coupon->usage_limit && $coupon->used_count >= $coupon->usage_limit) {
            return 'This coupon has reached its usage limit.';
        }

        $userId = Auth::id();
        if ($coupon->usage_limit_per_user) {
            $userUsage = $coupon->usages()->where('user_id', $userId)->count();
            if ($userUsage >= $coupon->usage_limit_per_user) {
                return 'You have already used this coupon the maximum number of times.';
            }
        }

        return 'This coupon cannot be used.';
    }

    /**
     * Success response format.
     */
    private function successResponse(Coupon $coupon, float $discount, float $subtotal): array
    {
        return [
            'valid' => true,
            'coupon' => $coupon,
            'discount' => $discount,
            'subtotal' => $subtotal,
            'final_total' => $subtotal - $discount,
            'message' => 'Coupon applied successfully!',
        ];
    }

    /**
     * Error response format.
     */
    private function errorResponse(string $message): array
    {
        return [
            'valid' => false,
            'message' => $message,
        ];
    }
}