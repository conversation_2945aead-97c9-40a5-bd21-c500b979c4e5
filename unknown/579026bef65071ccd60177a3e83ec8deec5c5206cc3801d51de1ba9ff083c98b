<?php

namespace Database\Factories;

use App\Models\Coupon;
use App\Models\CouponUsage;
use App\Models\Order;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class CouponUsageFactory extends Factory
{
    protected $model = CouponUsage::class;

    public function definition(): array
    {
        return [
            'coupon_id' => Coupon::factory(),
            'user_id' => User::factory(),
            'order_id' => Order::factory(),
            'discount_amount' => $this->faker->randomFloat(2, 10, 500),
        ];
    }

    public function forCoupon(Coupon $coupon): static
    {
        return $this->state(fn (array $attributes) => [
            'coupon_id' => $coupon->id,
        ]);
    }

    public function forUser(User $user): static
    {
        return $this->state(fn (array $attributes) => [
            'user_id' => $user->id,
        ]);
    }

    public function withDiscount(float $amount): static
    {
        return $this->state(fn (array $attributes) => [
            'discount_amount' => $amount,
        ]);
    }

    public function withoutOrder(): static
    {
        return $this->state(fn (array $attributes) => [
            'order_id' => null,
        ]);
    }
}