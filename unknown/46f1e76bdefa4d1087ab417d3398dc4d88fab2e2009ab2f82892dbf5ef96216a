<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Services\PaymentGatewayService;
use App\Models\Order;
use App\Models\Payment;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Validation\ValidationException;

class PaymentGatewayServiceTest extends TestCase
{
    use RefreshDatabase;

    protected PaymentGatewayService $paymentService;
    protected Order $order;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->paymentService = new PaymentGatewayService();
        
        $user = User::factory()->create();
        $this->order = Order::factory()->create([
            'user_id' => $user->id,
            'total' => 100.00,
            'payment_status' => 'pending',
            'status' => 'pending',
        ]);
    }

    public function test_can_process_successful_credit_card_payment()
    {
        $paymentData = [
            'payment_method' => 'credit_card',
            'card_number' => '****************', // Valid Visa test card
            'card_expiry' => '12/25',
            'card_cvv' => '123',
            'card_name' => '<PERSON> Do<PERSON>',
        ];

        $payment = $this->paymentService->processPayment($this->order, $paymentData);

        $this->assertInstanceOf(Payment::class, $payment);
        $this->assertEquals($this->order->id, $payment->order_id);
        $this->assertEquals('credit_card', $payment->payment_method);
        $this->assertEquals(Payment::STATUS_COMPLETED, $payment->status);
        $this->assertEquals(100.00, $payment->amount);
        $this->assertStringStartsWith('CC_', $payment->transaction_id);

        // Check payment data
        $this->assertEquals('1111', $payment->payment_data['card_last_four']);
        $this->assertEquals('visa', $payment->payment_data['card_type']);
        $this->assertEquals('John Doe', $payment->payment_data['card_name']);

        // Check order was updated
        $this->order->refresh();
        $this->assertEquals('paid', $this->order->payment_status);
        $this->assertEquals('processing', $this->order->status);
    }

    public function test_can_process_failed_credit_card_payment()
    {
        $paymentData = [
            'payment_method' => 'credit_card',
            'card_number' => '****************', // Card ending in 0 (simulated failure)
            'card_expiry' => '12/25',
            'card_cvv' => '123',
            'card_name' => 'John Doe',
        ];

        $payment = $this->paymentService->processPayment($this->order, $paymentData);

        $this->assertEquals(Payment::STATUS_FAILED, $payment->status);

        // Check order status
        $this->order->refresh();
        $this->assertEquals('failed', $this->order->payment_status);
        $this->assertEquals('pending', $this->order->status); // Status shouldn't change on failure
    }

    public function test_can_process_paypal_payment()
    {
        $paymentData = [
            'payment_method' => 'paypal',
            'paypal_email' => '<EMAIL>',
        ];

        $payment = $this->paymentService->processPayment($this->order, $paymentData);

        $this->assertEquals('paypal', $payment->payment_method);
        $this->assertEquals(Payment::STATUS_COMPLETED, $payment->status);
        $this->assertStringStartsWith('PP_', $payment->transaction_id);
        $this->assertEquals('<EMAIL>', $payment->payment_data['paypal_email']);

        // Check order was updated
        $this->order->refresh();
        $this->assertEquals('paid', $this->order->payment_status);
        $this->assertEquals('processing', $this->order->status);
    }

    public function test_can_process_bank_transfer_payment()
    {
        $paymentData = [
            'payment_method' => 'bank_transfer',
            'bank_name' => 'Test Bank',
            'account_number' => '**********',
            'routing_number' => '*********',
        ];

        $payment = $this->paymentService->processPayment($this->order, $paymentData);

        $this->assertEquals('bank_transfer', $payment->payment_method);
        $this->assertEquals(Payment::STATUS_PENDING, $payment->status);
        $this->assertStringStartsWith('BT_', $payment->transaction_id);
        $this->assertEquals('Test Bank', $payment->payment_data['bank_name']);

        // Check order status (should remain pending for bank transfers)
        $this->order->refresh();
        $this->assertEquals('pending', $this->order->payment_status);
    }

    public function test_cannot_process_payment_for_already_paid_order()
    {
        $this->order->update(['payment_status' => 'paid']);

        $paymentData = [
            'payment_method' => 'credit_card',
            'card_number' => '****************',
            'card_expiry' => '12/25',
            'card_cvv' => '123',
            'card_name' => 'John Doe',
        ];

        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Order has already been paid');

        $this->paymentService->processPayment($this->order, $paymentData);
    }

    public function test_cannot_process_payment_for_cancelled_order()
    {
        $this->order->update(['status' => 'canceled']);

        $paymentData = [
            'payment_method' => 'credit_card',
            'card_number' => '****************',
            'card_expiry' => '12/25',
            'card_cvv' => '123',
            'card_name' => 'John Doe',
        ];

        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Cannot process payment for cancelled order');

        $this->paymentService->processPayment($this->order, $paymentData);
    }

    public function test_validates_credit_card_data()
    {
        $invalidPaymentData = [
            'payment_method' => 'credit_card',
            'card_number' => '1234', // Invalid card number
            'card_expiry' => '13/25', // Invalid month
            'card_cvv' => '12', // Invalid CVV
            'card_name' => '', // Empty name
        ];

        $this->expectException(ValidationException::class);

        $this->paymentService->processPayment($this->order, $invalidPaymentData);
    }

    public function test_validates_expired_credit_card()
    {
        $paymentData = [
            'payment_method' => 'credit_card',
            'card_number' => '****************',
            'card_expiry' => '01/20', // Expired date
            'card_cvv' => '123',
            'card_name' => 'John Doe',
        ];

        $this->expectException(ValidationException::class);

        $this->paymentService->processPayment($this->order, $paymentData);
    }

    public function test_validates_luhn_algorithm()
    {
        $paymentData = [
            'payment_method' => 'credit_card',
            'card_number' => '****************', // Invalid Luhn checksum
            'card_expiry' => '12/25',
            'card_cvv' => '123',
            'card_name' => 'John Doe',
        ];

        $this->expectException(ValidationException::class);

        $this->paymentService->processPayment($this->order, $paymentData);
    }

    public function test_detects_card_types()
    {
        $testCases = [
            '****************' => 'visa',
            '****************' => 'mastercard',
            '***************' => 'amex',
            '****************' => 'discover',
            '**********123456' => 'unknown',
        ];

        foreach ($testCases as $cardNumber => $expectedType) {
            $reflection = new \ReflectionClass($this->paymentService);
            $method = $reflection->getMethod('detectCardType');
            $method->setAccessible(true);
            
            $result = $method->invoke($this->paymentService, $cardNumber);
            $this->assertEquals($expectedType, $result, "Failed for card number: {$cardNumber}");
        }
    }

    public function test_can_refund_payment()
    {
        // Create a completed payment
        $payment = Payment::factory()->create([
            'order_id' => $this->order->id,
            'status' => Payment::STATUS_COMPLETED,
            'amount' => 100.00,
        ]);

        $refundedPayment = $this->paymentService->refundPayment($payment, 50.00, 'Customer request');

        $this->assertEquals(Payment::STATUS_REFUNDED, $refundedPayment->status);
        $this->assertEquals(50.00, $refundedPayment->payment_data['refund_amount']);
        $this->assertEquals('Customer request', $refundedPayment->payment_data['refund_reason']);

        // Check order was updated
        $this->order->refresh();
        $this->assertEquals('refunded', $this->order->payment_status);
        $this->assertEquals('canceled', $this->order->status);
    }

    public function test_cannot_refund_non_completed_payment()
    {
        $payment = Payment::factory()->create([
            'order_id' => $this->order->id,
            'status' => Payment::STATUS_PENDING,
        ]);

        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Can only refund completed payments');

        $this->paymentService->refundPayment($payment);
    }

    public function test_cannot_refund_more_than_original_amount()
    {
        $payment = Payment::factory()->create([
            'order_id' => $this->order->id,
            'status' => Payment::STATUS_COMPLETED,
            'amount' => 100.00,
        ]);

        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Refund amount cannot exceed original payment amount');

        $this->paymentService->refundPayment($payment, 150.00);
    }

    public function test_can_confirm_bank_transfer()
    {
        $payment = Payment::factory()->create([
            'order_id' => $this->order->id,
            'payment_method' => Payment::METHOD_BANK_TRANSFER,
            'status' => Payment::STATUS_PENDING,
        ]);

        $confirmedPayment = $this->paymentService->confirmBankTransfer($payment);

        $this->assertEquals(Payment::STATUS_COMPLETED, $confirmedPayment->status);
        $this->assertArrayHasKey('confirmed_at', $confirmedPayment->payment_data);

        // Check order was updated
        $this->order->refresh();
        $this->assertEquals('paid', $this->order->payment_status);
        $this->assertEquals('processing', $this->order->status);
    }

    public function test_cannot_confirm_non_bank_transfer_payment()
    {
        $payment = Payment::factory()->create([
            'order_id' => $this->order->id,
            'payment_method' => Payment::METHOD_CREDIT_CARD,
            'status' => Payment::STATUS_PENDING,
        ]);

        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Can only confirm bank transfer payments');

        $this->paymentService->confirmBankTransfer($payment);
    }

    public function test_cannot_confirm_non_pending_payment()
    {
        $payment = Payment::factory()->create([
            'order_id' => $this->order->id,
            'payment_method' => Payment::METHOD_BANK_TRANSFER,
            'status' => Payment::STATUS_COMPLETED,
        ]);

        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Payment is not pending confirmation');

        $this->paymentService->confirmBankTransfer($payment);
    }

    public function test_can_get_payment_status()
    {
        $payment = Payment::factory()->create([
            'order_id' => $this->order->id,
            'status' => Payment::STATUS_COMPLETED,
            'payment_method' => Payment::METHOD_CREDIT_CARD,
            'transaction_id' => 'TEST123',
            'amount' => 100.00,
        ]);

        $status = $this->paymentService->getPaymentStatus($this->order);

        $this->assertEquals(Payment::STATUS_COMPLETED, $status['status']);
        $this->assertEquals(Payment::METHOD_CREDIT_CARD, $status['payment_method']);
        $this->assertEquals('TEST123', $status['transaction_id']);
        $this->assertEquals(100.00, $status['amount']);
    }

    public function test_get_payment_status_for_order_without_payment()
    {
        $status = $this->paymentService->getPaymentStatus($this->order);

        $this->assertEquals('no_payment', $status['status']);
        $this->assertEquals('No payment found for this order', $status['message']);
    }

    public function test_can_get_supported_payment_methods()
    {
        $methods = $this->paymentService->getSupportedPaymentMethods();

        $this->assertIsArray($methods);
        $this->assertCount(3, $methods);
        
        $methodIds = array_column($methods, 'id');
        $this->assertContains('credit_card', $methodIds);
        $this->assertContains('paypal', $methodIds);
        $this->assertContains('bank_transfer', $methodIds);
    }

    public function test_can_calculate_processing_fees()
    {
        // Credit card: 2.9% + $0.30
        $fee = $this->paymentService->calculateProcessingFee('credit_card', 100.00);
        $this->assertEquals(3.20, $fee); // (100 * 0.029) + 0.30 = 3.20

        // PayPal: 3.4% + $0.30
        $fee = $this->paymentService->calculateProcessingFee('paypal', 100.00);
        $this->assertEquals(3.70, $fee); // (100 * 0.034) + 0.30 = 3.70

        // Bank transfer: No fee
        $fee = $this->paymentService->calculateProcessingFee('bank_transfer', 100.00);
        $this->assertEquals(0, $fee);

        // Unknown method: No fee
        $fee = $this->paymentService->calculateProcessingFee('unknown', 100.00);
        $this->assertEquals(0, $fee);
    }

    public function test_validates_unsupported_payment_method()
    {
        $paymentData = [
            'payment_method' => 'cryptocurrency',
        ];

        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Unsupported payment method: cryptocurrency');

        $this->paymentService->processPayment($this->order, $paymentData);
    }

    public function test_validates_bank_transfer_data()
    {
        $invalidPaymentData = [
            'payment_method' => 'bank_transfer',
            // Missing required fields
        ];

        $this->expectException(ValidationException::class);

        $this->paymentService->processPayment($this->order, $invalidPaymentData);
    }

    public function test_luhn_algorithm_validation()
    {
        $reflection = new \ReflectionClass($this->paymentService);
        $method = $reflection->getMethod('validateLuhn');
        $method->setAccessible(true);

        // Valid card numbers
        $this->assertTrue($method->invoke($this->paymentService, '****************')); // Visa
        $this->assertTrue($method->invoke($this->paymentService, '****************')); // Mastercard
        $this->assertTrue($method->invoke($this->paymentService, '***************')); // Amex

        // Invalid card numbers
        $this->assertFalse($method->invoke($this->paymentService, '****************'));
        $this->assertFalse($method->invoke($this->paymentService, '**********123456'));
        $this->assertFalse($method->invoke($this->paymentService, '123')); // Too short
    }
}