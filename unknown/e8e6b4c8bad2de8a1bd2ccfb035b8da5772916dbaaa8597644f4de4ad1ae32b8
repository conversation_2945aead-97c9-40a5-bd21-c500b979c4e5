<?php

/**
 * <PERSON><PERSON> Dusk Setup Script
 * 
 * This script helps set up <PERSON><PERSON> Dusk for browser testing.
 */

class DuskSetup
{
    public function run(): void
    {
        $this->displayHeader();
        $this->installChromeDriver();
        $this->createDirectories();
        $this->setPermissions();
        $this->runMigrations();
        $this->displayInstructions();
    }

    private function displayHeader(): void
    {
        echo "\n";
        echo "╔══════════════════════════════════════════════════════════════╗\n";
        echo "║                    Laravel Dusk Setup                       ║\n";
        echo "║                                                              ║\n";
        echo "║         Setting up browser testing environment              ║\n";
        echo "╚══════════════════════════════════════════════════════════════╝\n";
        echo "\n";
    }

    private function installChromeDriver(): void
    {
        echo "🔧 Installing ChromeDriver...\n";
        
        $output = [];
        $returnCode = 0;
        
        exec('php artisan dusk:chrome-driver --detect 2>&1', $output, $returnCode);
        
        if ($returnCode === 0) {
            echo "✅ ChromeDriver installed successfully\n";
        } else {
            echo "⚠️  ChromeDriver installation may have issues:\n";
            echo implode("\n", $output) . "\n";
        }
        echo "\n";
    }

    private function createDirectories(): void
    {
        echo "📁 Creating test directories...\n";
        
        $directories = [
            'tests/Browser/screenshots',
            'tests/Browser/console',
            'tests/Browser/source',
            'storage/logs/dusk'
        ];

        foreach ($directories as $dir) {
            if (!is_dir($dir)) {
                mkdir($dir, 0755, true);
                echo "✅ Created directory: {$dir}\n";
            } else {
                echo "ℹ️  Directory already exists: {$dir}\n";
            }
        }
        echo "\n";
    }

    private function setPermissions(): void
    {
        echo "🔐 Setting permissions...\n";
        
        $paths = [
            'tests/Browser/screenshots',
            'tests/Browser/console',
            'tests/Browser/source',
            'storage/logs'
        ];

        foreach ($paths as $path) {
            if (is_dir($path)) {
                chmod($path, 0755);
                echo "✅ Set permissions for: {$path}\n";
            }
        }
        echo "\n";
    }

    private function runMigrations(): void
    {
        echo "🗄️  Running database migrations for testing...\n";
        
        // Set test environment
        putenv('APP_ENV=dusk.local');
        
        $output = [];
        $returnCode = 0;
        
        exec('php artisan migrate:fresh --env=dusk.local --force 2>&1', $output, $returnCode);
        
        if ($returnCode === 0) {
            echo "✅ Database migrations completed\n";
        } else {
            echo "⚠️  Migration issues:\n";
            echo implode("\n", $output) . "\n";
        }
        echo "\n";
    }

    private function displayInstructions(): void
    {
        echo "╔══════════════════════════════════════════════════════════════╗\n";
        echo "║                    SETUP COMPLETE                           ║\n";
        echo "╠══════════════════════════════════════════════════════════════╣\n";
        echo "║                                                              ║\n";
        echo "║  🎉 Laravel Dusk is now set up and ready to use!           ║\n";
        echo "║                                                              ║\n";
        echo "║  Next steps:                                                 ║\n";
        echo "║                                                              ║\n";
        echo "║  1. Run all tests:                                          ║\n";
        echo "║     php run_dusk_tests.php                                  ║\n";
        echo "║                                                              ║\n";
        echo "║  2. Run specific test suite:                                ║\n";
        echo "║     php run_dusk_tests.php --suite payment                  ║\n";
        echo "║                                                              ║\n";
        echo "║  3. Run with visible browser:                               ║\n";
        echo "║     php run_dusk_tests.php --no-headless                    ║\n";
        echo "║                                                              ║\n";
        echo "║  4. Run individual test:                                    ║\n";
        echo "║     php artisan dusk tests/Browser/PaymentFlowTest.php      ║\n";
        echo "║                                                              ║\n";
        echo "║  5. List available test suites:                            ║\n";
        echo "║     php run_dusk_tests.php --list                          ║\n";
        echo "║                                                              ║\n";
        echo "╚══════════════════════════════════════════════════════════════╝\n";
        echo "\n";
        
        echo "📋 Available Test Files:\n";
        $testFiles = glob('tests/Browser/*Test.php');
        foreach ($testFiles as $file) {
            $testName = basename($file, '.php');
            echo "  - {$testName}\n";
        }
        echo "\n";
        
        echo "🔧 Configuration Files:\n";
        echo "  - .env.dusk.local (Dusk environment settings)\n";
        echo "  - tests/DuskTestCase.php (Base test case)\n";
        echo "  - tests/Browser/Pages/ (Page objects)\n";
        echo "  - tests/Browser/Components/ (Reusable components)\n";
        echo "\n";
        
        echo "💡 Tips:\n";
        echo "  - Use --no-headless to see the browser during testing\n";
        echo "  - Screenshots are saved to tests/Browser/screenshots/\n";
        echo "  - Console logs are saved to tests/Browser/console/\n";
        echo "  - Page source is saved to tests/Browser/source/\n";
        echo "\n";
    }
}

// Run setup if called directly
if (php_sapi_name() === 'cli') {
    $setup = new DuskSetup();
    $setup->run();
}