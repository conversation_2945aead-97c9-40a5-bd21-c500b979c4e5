<?php

namespace Tests\Browser;

use App\Models\User;
use Laravel\Dusk\Browser;
use Tests\Browser\Pages\AdminDashboardPage;
use Tests\Browser\Pages\LoginPage;
use Tests\DuskTestCase;

class AdminPanelTest extends DuskTestCase
{
    /**
     * Test admin login and dashboard access.
     */
    public function test_admin_can_access_dashboard(): void
    {
        $admin = User::factory()->create([
            'email' => '<EMAIL>',
            'is_admin' => true,
        ]);

        $this->browse(function (Browser $browser) use ($admin) {
            $browser->visit(new LoginPage)
                    ->loginWith('<EMAIL>', 'password')
                    ->assertPathIs('/admin/dashboard')
                    ->visit(new AdminDashboardPage)
                    ->assertSee('Admin Dashboard')
                    ->assertPresent('@sidebar')
                    ->assertPresent('@mainContent');
        });
    }

    /**
     * Test admin navigation between sections.
     */
    public function test_admin_navigation(): void
    {
        $admin = User::factory()->create([
            'email' => '<EMAIL>',
            'is_admin' => true,
        ]);

        $this->browse(function (Browser $browser) use ($admin) {
            $browser->loginAs($admin)
                    ->visit(new AdminDashboardPage)
                    ->goToTransactions()
                    ->assertPathIs('/admin/transactions')
                    ->back()
                    ->goToGateways()
                    ->assertPathIs('/admin/gateways')
                    ->back()
                    ->goToProducts()
                    ->assertPathIs('/admin/products');
        });
    }

    /**
     * Test admin dashboard statistics display.
     */
    public function test_admin_dashboard_statistics(): void
    {
        $admin = User::factory()->create([
            'email' => '<EMAIL>',
            'is_admin' => true,
        ]);

        $this->browse(function (Browser $browser) use ($admin) {
            $browser->loginAs($admin)
                    ->visit(new AdminDashboardPage)
                    ->assertPresent('@statsCards')
                    ->assertSee('Total Transactions')
                    ->assertSee('Revenue')
                    ->assertSee('Active Gateways');
        });
    }

    /**
     * Test non-admin user cannot access admin panel.
     */
    public function test_regular_user_cannot_access_admin(): void
    {
        $user = User::factory()->create([
            'is_admin' => false,
        ]);

        $this->browse(function (Browser $browser) use ($user) {
            $browser->loginAs($user)
                    ->visit('/admin/dashboard')
                    ->assertPathIsNot('/admin/dashboard')
                    ->assertSee('Unauthorized');
        });
    }

    /**
     * Test admin can view recent transactions.
     */
    public function test_admin_can_view_recent_transactions(): void
    {
        $admin = User::factory()->create([
            'email' => '<EMAIL>',
            'is_admin' => true,
        ]);

        $this->browse(function (Browser $browser) use ($admin) {
            $browser->loginAs($admin)
                    ->visit(new AdminDashboardPage)
                    ->assertPresent('@recentTransactions')
                    ->assertSee('Recent Transactions');
        });
    }
}