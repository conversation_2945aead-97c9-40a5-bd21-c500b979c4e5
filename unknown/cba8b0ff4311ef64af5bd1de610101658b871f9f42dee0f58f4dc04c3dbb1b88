<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <div class="flex items-center justify-between mb-8">
        <h1 class="text-3xl font-bold text-gray-900">Shopping Cart</h1>
        @if($itemCount > 0)
            <div class="text-sm text-gray-500">
                {{ $itemCount }} {{ Str::plural('item', $itemCount) }}
            </div>
        @endif
    </div>
    
    @if($itemCount === 0)
        <!-- Empty Cart State -->
        <div class="bg-white rounded-lg shadow-sm border p-8 text-center">
            <div class="mx-auto w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mb-2">Your cart is empty</h3>
            <p class="text-gray-500 mb-6">Start shopping to add items to your cart.</p>
            <button 
                wire:click="continueShopping"
                class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                </svg>
                Start Shopping
            </button>
        </div>
    @else
        <!-- Cart Content -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Cart Items -->
            <div class="lg:col-span-2 space-y-4">
                <!-- Cart Actions -->
                <div class="bg-white rounded-lg shadow-sm border p-4">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-4">
                            <button 
                                wire:click="validateStock"
                                class="text-sm text-blue-600 hover:text-blue-800 focus:outline-none"
                            >
                                Validate Stock
                            </button>
                            <button 
                                wire:click="updatePrices"
                                class="text-sm text-blue-600 hover:text-blue-800 focus:outline-none"
                            >
                                Update Prices
                            </button>
                        </div>
                        <button 
                            wire:click="clearCart" 
                            class="text-sm text-red-600 hover:text-red-800 focus:outline-none"
                            onclick="return confirm('Are you sure you want to clear your cart?')"
                        >
                            Clear Cart
                        </button>
                    </div>
                </div>
                
                <!-- Cart Items List -->
                <div class="bg-white rounded-lg shadow-sm border overflow-hidden">
                    <div class="divide-y divide-gray-200">
                        @foreach($cartItems as $item)
                            <div class="p-6" wire:key="cart-item-{{ $item->id }}">
                                <div class="flex items-start space-x-4">
                                    <!-- Product Image -->
                                    <div class="flex-shrink-0 w-20 h-20 bg-gray-100 rounded-lg overflow-hidden">
                                        @if($item->component->image_url)
                                            <img src="{{ $item->component->image_url }}" 
                                                 alt="{{ $item->component->name }}" 
                                                 class="w-full h-full object-cover">
                                        @else
                                            <div class="w-full h-full flex items-center justify-center">
                                                <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                                </svg>
                                            </div>
                                        @endif
                                    </div>
                                    
                                    <!-- Product Details -->
                                    <div class="flex-1 min-w-0">
                                        <div class="flex items-start justify-between">
                                            <div class="flex-1">
                                                <h3 class="text-lg font-medium text-gray-900 mb-1">
                                                    {{ $item->component->name }}
                                                </h3>
                                                <p class="text-sm text-gray-500 mb-2">
                                                    {{ $item->component->brand }} 
                                                    @if($item->component->model)
                                                        - {{ $item->component->model }}
                                                    @endif
                                                </p>
                                                <div class="flex items-center space-x-4 text-sm text-gray-500">
                                                    <span>${{ number_format($item->price, 2) }} each</span>
                                                    @if($item->component->stock <= 5)
                                                        <span class="text-orange-600 font-medium">
                                                            Only {{ $item->component->stock }} left
                                                        </span>
                                                    @endif
                                                </div>
                                            </div>
                                            
                                            <!-- Remove Button -->
                                            <button 
                                                wire:click="removeItem({{ $item->id }})" 
                                                class="ml-4 text-red-600 hover:text-red-800 focus:outline-none"
                                                title="Remove item"
                                            >
                                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                                </svg>
                                            </button>
                                        </div>
                                        
                                        <!-- Quantity and Price -->
                                        <div class="flex items-center justify-between mt-4">
                                            <!-- Quantity Controls -->
                                            <div class="flex items-center border border-gray-300 rounded-md">
                                                <button 
                                                    wire:click="decrementQuantity({{ $item->id }})" 
                                                    class="p-2 text-gray-400 hover:text-gray-600 focus:outline-none"
                                                >
                                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4" />
                                                    </svg>
                                                </button>
                                                
                                                <input 
                                                    type="number" 
                                                    value="{{ $item->quantity }}"
                                                    wire:change="updateQuantity({{ $item->id }}, $event.target.value)"
                                                    min="1"
                                                    max="{{ $item->component->stock }}"
                                                    class="w-16 text-center border-0 focus:ring-0 focus:outline-none"
                                                >
                                                
                                                <button 
                                                    wire:click="incrementQuantity({{ $item->id }})" 
                                                    class="p-2 text-gray-400 hover:text-gray-600 focus:outline-none {{ $item->quantity >= $item->component->stock ? 'opacity-50 cursor-not-allowed' : '' }}"
                                                    {{ $item->quantity >= $item->component->stock ? 'disabled' : '' }}
                                                >
                                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                                    </svg>
                                                </button>
                                            </div>
                                            
                                            <!-- Item Total -->
                                            <div class="text-right">
                                                <p class="text-lg font-semibold text-gray-900">
                                                    ${{ number_format($item->price * $item->quantity, 2) }}
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
                
                <!-- Continue Shopping -->
                <div class="flex justify-start">
                    <button 
                        wire:click="continueShopping"
                        class="inline-flex items-center text-blue-600 hover:text-blue-800 focus:outline-none"
                    >
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                        </svg>
                        Continue Shopping
                    </button>
                </div>
            </div>
            
            <!-- Order Summary -->
            <div class="lg:col-span-1">
                <div class="bg-white rounded-lg shadow-sm border p-6 sticky top-6">
                    <h2 class="text-lg font-semibold text-gray-900 mb-6">Order Summary</h2>
                    
                    <div class="space-y-4">
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600">Subtotal ({{ $itemCount }} items)</span>
                            <span class="text-gray-900">${{ number_format($subtotal, 2) }}</span>
                        </div>
                        
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600">Tax ({{ number_format($taxRate * 100, 1) }}%)</span>
                            <span class="text-gray-900">${{ number_format($tax, 2) }}</span>
                        </div>
                        
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600">Shipping</span>
                            <span class="text-gray-900">
                                @if($shipping > 0)
                                    ${{ number_format($shipping, 2) }}
                                @else
                                    <span class="text-green-600">Free</span>
                                @endif
                            </span>
                        </div>
                        
                        @if($subtotal < 100 && $subtotal > 0)
                            <div class="text-xs text-gray-500 bg-blue-50 p-2 rounded">
                                Add ${{ number_format(100 - $subtotal, 2) }} more for free shipping!
                            </div>
                        @endif
                        
                        <div class="border-t pt-4">
                            <div class="flex justify-between font-semibold text-lg">
                                <span class="text-gray-900">Total</span>
                                <span class="text-gray-900">${{ number_format($total, 2) }}</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mt-6 space-y-3">
                        <button 
                            wire:click="proceedToCheckout" 
                            class="w-full px-6 py-3 bg-blue-600 text-white font-medium rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors"
                        >
                            Proceed to Checkout
                        </button>
                        
                        <div class="text-center text-xs text-gray-500">
                            Secure checkout with SSL encryption
                        </div>
                    </div>
                </div>
            </div>
        </div>
    @endif
    
    <!-- Flash Messages -->
    @if(session()->has('cart_message'))
        <div class="fixed bottom-4 right-4 bg-green-100 border-l-4 border-green-500 text-green-700 p-4 rounded shadow-lg z-50" 
             x-data="{ show: true }" 
             x-show="show" 
             x-init="setTimeout(() => show = false, 4000)"
             x-transition:enter="transition ease-out duration-300"
             x-transition:enter-start="opacity-0 transform translate-y-2"
             x-transition:enter-end="opacity-100 transform translate-y-0"
             x-transition:leave="transition ease-in duration-200"
             x-transition:leave-start="opacity-100 transform translate-y-0"
             x-transition:leave-end="opacity-0 transform translate-y-2">
            <div class="flex items-center">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                </svg>
                {{ session('cart_message') }}
            </div>
        </div>
    @endif
    
    @if(session()->has('cart_error'))
        <div class="fixed bottom-4 right-4 bg-red-100 border-l-4 border-red-500 text-red-700 p-4 rounded shadow-lg z-50" 
             x-data="{ show: true }" 
             x-show="show" 
             x-init="setTimeout(() => show = false, 4000)"
             x-transition:enter="transition ease-out duration-300"
             x-transition:enter-start="opacity-0 transform translate-y-2"
             x-transition:enter-end="opacity-100 transform translate-y-0"
             x-transition:leave="transition ease-in duration-200"
             x-transition:leave-start="opacity-100 transform translate-y-0"
             x-transition:leave-end="opacity-0 transform translate-y-2">
            <div class="flex items-center">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                {{ session('cart_error') }}
            </div>
        </div>
    @endif
</div>