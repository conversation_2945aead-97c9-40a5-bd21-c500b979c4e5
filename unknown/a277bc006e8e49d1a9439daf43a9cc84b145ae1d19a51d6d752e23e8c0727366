<?php

namespace Tests\Feature;

use App\Livewire\Shop\ProductList;
use App\Models\Component;
use App\Models\ComponentCategory;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Livewire\Livewire;
use Tests\TestCase;

class SearchFunctionalityTest extends TestCase
{
    use RefreshDatabase;

    protected ComponentCategory $category;
    protected Component $component1;
    protected Component $component2;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->category = ComponentCategory::factory()->create([
            'name' => 'Processors',
            'slug' => 'processors'
        ]);
        
        $this->component1 = Component::factory()->create([
            'name' => 'Intel Core i7-13700K',
            'description' => 'High-performance desktop processor',
            'brand' => 'Intel',
            'model' => 'i7-13700K',
            'price' => 409.99,
            'category_id' => $this->category->id,
            'specs' => [
                'cores' => '16',
                'threads' => '24',
                'base_clock' => '3.4 GHz',
                'boost_clock' => '5.4 GHz',
                'socket' => 'LGA1700'
            ],
            'stock' => 15,
            'is_active' => true
        ]);
        
        $this->component2 = Component::factory()->create([
            'name' => 'AMD Ryzen 7 7700X',
            'description' => 'Excellent gaming and productivity processor',
            'brand' => 'AMD',
            'model' => '7700X',
            'price' => 349.99,
            'category_id' => $this->category->id,
            'specs' => [
                'cores' => '8',
                'threads' => '16',
                'base_clock' => '4.5 GHz',
                'boost_clock' => '5.4 GHz',
                'socket' => 'AM5'
            ],
            'stock' => 8,
            'is_active' => true
        ]);
    }

    public function test_basic_search_functionality()
    {
        Livewire::test(ProductList::class)
            ->set('search', 'Intel')
            ->assertSee('Intel Core i7-13700K')
            ->assertDontSee('AMD Ryzen 7 7700X');
    }

    public function test_search_by_description()
    {
        Livewire::test(ProductList::class)
            ->set('search', 'gaming')
            ->assertSee('AMD Ryzen 7 7700X')
            ->assertDontSee('Intel Core i7-13700K');
    }

    public function test_brand_filter()
    {
        Livewire::test(ProductList::class)
            ->set('selectedBrands', ['AMD'])
            ->assertSee('AMD Ryzen 7 7700X')
            ->assertDontSee('Intel Core i7-13700K');
    }

    public function test_multiple_brand_filter()
    {
        Livewire::test(ProductList::class)
            ->set('selectedBrands', ['AMD', 'Intel'])
            ->assertSee('AMD Ryzen 7 7700X')
            ->assertSee('Intel Core i7-13700K');
    }

    public function test_price_range_filter()
    {
        Livewire::test(ProductList::class)
            ->set('priceMin', 300)
            ->set('priceMax', 380)
            ->assertSee('AMD Ryzen 7 7700X')
            ->assertDontSee('Intel Core i7-13700K');
    }

    public function test_specification_filter()
    {
        Livewire::test(ProductList::class)
            ->set('selectedSpecs', ['socket' => 'AM5'])
            ->assertSee('AMD Ryzen 7 7700X')
            ->assertDontSee('Intel Core i7-13700K');
    }

    public function test_stock_filter()
    {
        // Set one component out of stock
        $this->component2->update(['stock' => 0]);
        
        Livewire::test(ProductList::class)
            ->set('inStockOnly', true)
            ->assertSee('Intel Core i7-13700K')
            ->assertDontSee('AMD Ryzen 7 7700X');
    }

    public function test_price_sorting()
    {
        $component = Livewire::test(ProductList::class)
            ->set('sortBy', 'price')
            ->set('sortDirection', 'asc');
        
        $components = $component->get('components');
        
        // AMD should be first (lower price)
        $this->assertEquals($this->component2->id, $components->items()[0]->id);
    }

    public function test_name_sorting()
    {
        $component = Livewire::test(ProductList::class)
            ->set('sortBy', 'name')
            ->set('sortDirection', 'asc');
        
        $components = $component->get('components');
        
        // AMD should be first alphabetically
        $this->assertEquals($this->component2->id, $components->items()[0]->id);
    }

    public function test_category_filter()
    {
        Livewire::test(ProductList::class)
            ->set('selectedCategory', 'processors')
            ->assertSee('Intel Core i7-13700K')
            ->assertSee('AMD Ryzen 7 7700X');
    }

    public function test_clear_filters()
    {
        Livewire::test(ProductList::class)
            ->set('search', 'Intel')
            ->set('selectedBrands', ['Intel'])
            ->set('priceMin', 400)
            ->call('clearFilters')
            ->assertSet('search', '')
            ->assertSet('selectedBrands', [])
            ->assertSet('priceMin', null)
            ->assertSee('Intel Core i7-13700K')
            ->assertSee('AMD Ryzen 7 7700X');
    }

    public function test_remove_brand_filter()
    {
        Livewire::test(ProductList::class)
            ->set('selectedBrands', ['Intel', 'AMD'])
            ->call('removeBrandFilter', 'Intel')
            ->assertSet('selectedBrands', ['AMD'])
            ->assertSee('AMD Ryzen 7 7700X')
            ->assertDontSee('Intel Core i7-13700K');
    }

    public function test_remove_spec_filter()
    {
        Livewire::test(ProductList::class)
            ->set('selectedSpecs', ['socket' => ['AM5', 'LGA1700']])
            ->call('removeSpecFilter', 'socket', 'AM5')
            ->assertSee('Intel Core i7-13700K')
            ->assertDontSee('AMD Ryzen 7 7700X');
    }

    public function test_search_suggestions()
    {
        $component = Livewire::test(ProductList::class)
            ->set('search', 'Int')
            ->call('getSearchSuggestions');
        
        $suggestions = $component->get('searchSuggestions');
        
        $this->assertNotEmpty($suggestions);
        
        // Check if Intel appears in suggestions
        $hasIntelSuggestion = collect($suggestions)->contains(function ($suggestion) {
            return str_contains($suggestion['value'], 'Intel');
        });
        
        $this->assertTrue($hasIntelSuggestion);
    }

    public function test_select_suggestion()
    {
        Livewire::test(ProductList::class)
            ->call('selectSuggestion', 'Intel Core i7-13700K')
            ->assertSet('search', 'Intel Core i7-13700K')
            ->assertSet('showSuggestions', false)
            ->assertSee('Intel Core i7-13700K');
    }

    public function test_combined_search_and_filters()
    {
        Livewire::test(ProductList::class)
            ->set('search', 'processor')
            ->set('selectedBrands', ['AMD'])
            ->set('priceMax', 400)
            ->assertSee('AMD Ryzen 7 7700X')
            ->assertDontSee('Intel Core i7-13700K');
    }

    public function test_specification_search_in_json()
    {
        Livewire::test(ProductList::class)
            ->set('search', 'LGA1700')
            ->assertSee('Intel Core i7-13700K')
            ->assertDontSee('AMD Ryzen 7 7700X');
    }

    public function test_relevance_sorting_with_search()
    {
        Livewire::test(ProductList::class)
            ->set('search', 'Intel')
            ->set('sortBy', 'relevance')
            ->set('sortDirection', 'desc')
            ->assertSee('Intel Core i7-13700K')
            ->assertDontSee('AMD Ryzen 7 7700X');
    }

    public function test_pagination_with_search()
    {
        // Create more components to test pagination
        Component::factory()->count(15)->create([
            'category_id' => $this->category->id,
            'brand' => 'Intel',
            'is_active' => true,
            'stock' => 5
        ]);
        
        $component = Livewire::test(ProductList::class)
            ->set('search', 'Intel')
            ->set('perPage', 10);
        
        $components = $component->get('components');
        
        $this->assertEquals(10, $components->count());
        $this->assertTrue($components->hasPages());
    }

    public function test_filter_options_update()
    {
        $component = Livewire::test(ProductList::class);
        
        $availableBrands = $component->get('availableBrands');
        
        $this->assertContains('Intel', $availableBrands);
        $this->assertContains('AMD', $availableBrands);
    }

    public function test_url_query_string_persistence()
    {
        $component = Livewire::test(ProductList::class)
            ->set('search', 'Intel')
            ->set('selectedCategory', 'processors')
            ->set('priceMin', 300);
        
        // Check that the properties are set correctly
        $component->assertSet('search', 'Intel')
                  ->assertSet('selectedCategory', 'processors')
                  ->assertSet('priceMin', 300);
    }
}