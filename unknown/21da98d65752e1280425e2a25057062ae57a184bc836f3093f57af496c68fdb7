<?php

namespace Tests\Unit\Models;

use App\Models\Component;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Review;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ReviewTest extends TestCase
{
    use RefreshDatabase;

    public function test_review_belongs_to_user()
    {
        $user = User::factory()->create();
        $component = Component::factory()->create();
        $review = Review::factory()->create([
            'user_id' => $user->id,
            'component_id' => $component->id,
        ]);

        $this->assertInstanceOf(User::class, $review->user);
        $this->assertEquals($user->id, $review->user->id);
    }

    public function test_review_belongs_to_component()
    {
        $user = User::factory()->create();
        $component = Component::factory()->create();
        $review = Review::factory()->create([
            'user_id' => $user->id,
            'component_id' => $component->id,
        ]);

        $this->assertInstanceOf(Component::class, $review->component);
        $this->assertEquals($component->id, $review->component->id);
    }

    public function test_approved_scope_returns_only_approved_reviews()
    {
        $component = Component::factory()->create();
        
        Review::factory()->approved()->create(['component_id' => $component->id]);
        Review::factory()->approved()->create(['component_id' => $component->id]);
        Review::factory()->pending()->create(['component_id' => $component->id]);

        $approvedReviews = Review::approved()->get();
        
        $this->assertCount(2, $approvedReviews);
        $this->assertTrue($approvedReviews->every(fn($review) => $review->is_approved));
    }

    public function test_pending_scope_returns_only_pending_reviews()
    {
        $component = Component::factory()->create();
        
        Review::factory()->approved()->create(['component_id' => $component->id]);
        Review::factory()->pending()->create(['component_id' => $component->id]);
        Review::factory()->pending()->create(['component_id' => $component->id]);

        $pendingReviews = Review::pending()->get();
        
        $this->assertCount(2, $pendingReviews);
        $this->assertTrue($pendingReviews->every(fn($review) => !$review->is_approved));
    }

    public function test_for_component_scope_filters_by_component()
    {
        $component1 = Component::factory()->create();
        $component2 = Component::factory()->create();
        
        Review::factory()->create(['component_id' => $component1->id]);
        Review::factory()->create(['component_id' => $component1->id]);
        Review::factory()->create(['component_id' => $component2->id]);

        $component1Reviews = Review::forComponent($component1->id)->get();
        
        $this->assertCount(2, $component1Reviews);
        $this->assertTrue($component1Reviews->every(fn($review) => $review->component_id === $component1->id));
    }

    public function test_by_rating_scope_filters_by_rating()
    {
        $component = Component::factory()->create();
        
        Review::factory()->rating(5)->create(['component_id' => $component->id]);
        Review::factory()->rating(5)->create(['component_id' => $component->id]);
        Review::factory()->rating(3)->create(['component_id' => $component->id]);

        $fiveStarReviews = Review::byRating(5)->get();
        
        $this->assertCount(2, $fiveStarReviews);
        $this->assertTrue($fiveStarReviews->every(fn($review) => $review->rating === 5));
    }

    public function test_validation_rules_prevent_duplicate_reviews()
    {
        $user = User::factory()->create();
        $component = Component::factory()->create();
        
        Review::factory()->create([
            'user_id' => $user->id,
            'component_id' => $component->id,
        ]);

        $rules = Review::validationRules($user->id, $component->id);
        
        $this->assertArrayHasKey('user_id', $rules);
        
        // Check that the unique rule exists in the user_id validation rules
        $userIdRules = $rules['user_id'];
        $hasUniqueRule = false;
        
        foreach ($userIdRules as $rule) {
            if (is_object($rule) && method_exists($rule, '__toString')) {
                $ruleString = $rule->__toString();
                if (str_contains($ruleString, 'unique:reviews')) {
                    $hasUniqueRule = true;
                    break;
                }
            }
        }
        
        $this->assertTrue($hasUniqueRule, 'Unique rule should be present for user_id');
    }

    public function test_needs_moderation_detects_flagged_words()
    {
        $review = Review::factory()->make([
            'title' => 'This is spam',
            'comment' => 'Fake product, terrible quality',
            'rating' => 1,
        ]);

        $this->assertTrue($review->needsModeration());
    }

    public function test_needs_moderation_detects_short_comments()
    {
        $review = Review::factory()->make([
            'title' => 'Short',
            'comment' => 'Bad.',
            'rating' => 3,
        ]);

        $this->assertTrue($review->needsModeration());
    }

    public function test_needs_moderation_detects_extreme_ratings_without_sufficient_comment()
    {
        $review = Review::factory()->make([
            'title' => 'Great',
            'comment' => 'Good product',
            'rating' => 5,
        ]);

        $this->assertTrue($review->needsModeration());
    }

    public function test_needs_moderation_passes_good_reviews()
    {
        $review = Review::factory()->make([
            'title' => 'Great product with excellent performance',
            'comment' => 'This component exceeded my expectations. Great build quality and performance. Would definitely recommend to others.',
            'rating' => 4,
        ]);

        $this->assertFalse($review->needsModeration());
    }

    public function test_auto_approve_approves_good_reviews()
    {
        $review = Review::factory()->create([
            'title' => 'Great product',
            'comment' => 'This component exceeded my expectations. Great build quality and performance.',
            'rating' => 4,
            'is_approved' => false,
        ]);

        $review->autoApprove();

        $this->assertTrue($review->fresh()->is_approved);
    }

    public function test_auto_approve_does_not_approve_flagged_reviews()
    {
        $review = Review::factory()->create([
            'title' => 'Spam product',
            'comment' => 'This is fake and terrible',
            'rating' => 1,
            'is_approved' => false,
        ]);

        $review->autoApprove();

        $this->assertFalse($review->fresh()->is_approved);
    }

    public function test_get_average_rating_calculates_correctly()
    {
        $component = Component::factory()->create();
        
        Review::factory()->approved()->rating(5)->create(['component_id' => $component->id]);
        Review::factory()->approved()->rating(4)->create(['component_id' => $component->id]);
        Review::factory()->approved()->rating(3)->create(['component_id' => $component->id]);
        Review::factory()->pending()->rating(1)->create(['component_id' => $component->id]); // Should be ignored

        $average = Review::getAverageRating($component->id);
        
        $this->assertEquals(4.0, $average);
    }

    public function test_get_rating_distribution_returns_correct_counts()
    {
        $component = Component::factory()->create();
        
        Review::factory()->approved()->rating(5)->create(['component_id' => $component->id]);
        Review::factory()->approved()->rating(5)->create(['component_id' => $component->id]);
        Review::factory()->approved()->rating(4)->create(['component_id' => $component->id]);
        Review::factory()->approved()->rating(3)->create(['component_id' => $component->id]);

        $distribution = Review::getRatingDistribution($component->id);
        
        $this->assertEquals([
            1 => 0,
            2 => 0,
            3 => 1,
            4 => 1,
            5 => 2,
        ], $distribution);
    }

    public function test_get_review_count_returns_correct_count()
    {
        $component = Component::factory()->create();
        
        Review::factory()->approved()->count(3)->create(['component_id' => $component->id]);
        Review::factory()->pending()->count(2)->create(['component_id' => $component->id]); // Should be ignored

        $count = Review::getReviewCount($component->id);
        
        $this->assertEquals(3, $count);
    }

    public function test_is_verified_purchase_returns_true_for_completed_orders()
    {
        $user = User::factory()->create();
        $component = Component::factory()->create();
        
        $order = Order::factory()->completed()->create([
            'user_id' => $user->id,
        ]);
        
        OrderItem::factory()->create([
            'order_id' => $order->id,
            'component_id' => $component->id,
        ]);

        $review = Review::factory()->create([
            'user_id' => $user->id,
            'component_id' => $component->id,
        ]);

        $this->assertTrue($review->isVerifiedPurchase());
    }

    public function test_is_verified_purchase_returns_false_for_non_completed_orders()
    {
        $user = User::factory()->create();
        $component = Component::factory()->create();
        
        $order = Order::factory()->create([
            'user_id' => $user->id,
            'status' => 'processing',
        ]);
        
        OrderItem::factory()->create([
            'order_id' => $order->id,
            'component_id' => $component->id,
        ]);

        $review = Review::factory()->create([
            'user_id' => $user->id,
            'component_id' => $component->id,
        ]);

        $this->assertFalse($review->isVerifiedPurchase());
    }

    public function test_get_component_review_stats_returns_comprehensive_data()
    {
        $component = Component::factory()->create();
        $user = User::factory()->create();
        
        // Create a completed order for verified purchase
        $order = Order::factory()->completed()->create([
            'user_id' => $user->id,
        ]);
        
        OrderItem::factory()->create([
            'order_id' => $order->id,
            'component_id' => $component->id,
        ]);
        
        Review::factory()->approved()->rating(5)->create([
            'component_id' => $component->id,
            'user_id' => $user->id,
        ]);
        Review::factory()->approved()->rating(4)->create(['component_id' => $component->id]);
        Review::factory()->pending()->rating(1)->create(['component_id' => $component->id]);

        $stats = Review::getComponentReviewStats($component->id);
        
        $this->assertArrayHasKey('average_rating', $stats);
        $this->assertArrayHasKey('total_reviews', $stats);
        $this->assertArrayHasKey('rating_distribution', $stats);
        $this->assertArrayHasKey('verified_purchases', $stats);
        
        $this->assertEquals(4.5, $stats['average_rating']);
        $this->assertEquals(2, $stats['total_reviews']);
        $this->assertEquals(1, $stats['verified_purchases']);
    }
}