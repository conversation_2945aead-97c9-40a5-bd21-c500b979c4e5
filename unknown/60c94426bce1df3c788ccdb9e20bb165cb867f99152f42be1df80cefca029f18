<?php

use App\Models\Comment;
use App\Models\BlogPost;
use App\Models\User;
use Illuminate\Support\Str;
use Illuminate\Foundation\Testing\RefreshDatabase;
use function Pest\Laravel\actingAs;
use function Pest\Laravel\get;
use function Pest\Laravel\post;
use function Pest\Laravel\patch;
use function Pest\Laravel\delete;

uses(RefreshDatabase::class);

beforeEach(function () {
    $this->admin = User::factory()->create(['role' => 'admin']);
    $this->user = User::factory()->create();
    $this->post = BlogPost::factory()->create(['user_id' => $this->user->id]);
});

test('it can display comments list', function () {
    $comments = Comment::factory()->count(3)->create([
        'blog_post_id' => $this->post->id,
        'user_id' => $this->user->id,
    ]);

    actingAs($this->admin)
        ->get(route('admin.comments.index'))
        ->assertStatus(200)
        ->assertViewIs('admin.comments.index')
        ->assertViewHas('comments')
        ->assertSee(Str::limit($comments[0]->content, 50))
        ->assertSee(Str::limit($comments[1]->content, 50))
        ->assertSee(Str::limit($comments[2]->content, 50));
});

test('it can filter comments by status', function () {
    // Create comments with different statuses
    $approvedComment = Comment::factory()->create([
        'is_approved' => true,
        'blog_post_id' => $this->post->id,
        'user_id' => $this->user->id,
    ]);
    $pendingComment = Comment::factory()->create([
        'is_approved' => false,
        'rejected_reason' => null,
        'blog_post_id' => $this->post->id,
        'user_id' => $this->user->id,
    ]);
    $rejectedComment = Comment::factory()->create([
        'is_approved' => false,
        'rejected_reason' => 'Spam',
        'blog_post_id' => $this->post->id,
        'user_id' => $this->user->id,
    ]);

    // Test approved filter
    actingAs($this->admin)
        ->get(route('admin.comments.index', ['status' => 'approved']))
        ->assertStatus(200)
        ->assertViewHas('comments')
        ->assertSee(Str::limit($approvedComment->content, 50))
        ->assertDontSee(Str::limit($pendingComment->content, 50))
        ->assertDontSee(Str::limit($rejectedComment->content, 50));

    // Test pending filter
    actingAs($this->admin)
        ->get(route('admin.comments.index', ['status' => 'pending']))
        ->assertStatus(200)
        ->assertViewHas('comments')
        ->assertDontSee(Str::limit($approvedComment->content, 50))
        ->assertSee(Str::limit($pendingComment->content, 50))
        ->assertDontSee(Str::limit($rejectedComment->content, 50));

    // Test rejected filter
    actingAs($this->admin)
        ->get(route('admin.comments.index', ['status' => 'rejected']))
        ->assertStatus(200)
        ->assertViewHas('comments')
        ->assertDontSee(Str::limit($approvedComment->content, 50))
        ->assertDontSee(Str::limit($pendingComment->content, 50))
        ->assertSee(Str::limit($rejectedComment->content, 50));
});

test('it can search comments', function () {
    $blogPost = BlogPost::factory()->create(['title' => 'Test Post', 'user_id' => $this->user->id]);
    $user = User::factory()->create(['name' => 'John Doe', 'email' => '<EMAIL>']);
    $comment = Comment::factory()->create([
        'content' => 'Test comment content',
        'blog_post_id' => $blogPost->id,
        'user_id' => $user->id
    ]);

    // Search by comment content
    actingAs($this->admin)
        ->get(route('admin.comments.index', ['search' => 'Test comment']))
        ->assertStatus(200)
        ->assertViewHas('comments')
        ->assertSee($comment->content);

    // Search by post title
    actingAs($this->admin)
        ->get(route('admin.comments.index', ['search' => 'Test Post']))
        ->assertStatus(200)
        ->assertViewHas('comments')
        ->assertSee($comment->content);

    // Search by author name
    actingAs($this->admin)
        ->get(route('admin.comments.index', ['search' => 'John Doe']))
        ->assertStatus(200)
        ->assertViewHas('comments')
        ->assertSee($comment->content);

    // Search by author email
    actingAs($this->admin)
        ->get(route('admin.comments.index', ['search' => '<EMAIL>']))
        ->assertStatus(200)
        ->assertViewHas('comments')
        ->assertSee($comment->content);
});

test('it can display single comment details', function () {
    $comment = Comment::factory()->create([
        'blog_post_id' => $this->post->id,
        'user_id' => $this->user->id,
    ]);

    actingAs($this->admin)
        ->get(route('admin.comments.show', $comment))
        ->assertStatus(200)
        ->assertViewIs('admin.comments.show')
        ->assertViewHas('comment')
        ->assertSee($comment->content);
});

test('it can approve comment', function () {
    $comment = Comment::factory()->create([
        'is_approved' => false,
        'rejected_reason' => 'Previous rejection',
        'blog_post_id' => $this->post->id,
        'user_id' => $this->user->id,
    ]);

    actingAs($this->admin)
        ->patch(route('admin.comments.approve', $comment), [
            '_token' => csrf_token(),
            '_method' => 'PATCH'
        ])
        ->assertRedirect(route('admin.comments.index'))
        ->assertSessionHas('success', 'Comment approved successfully.');

    $freshComment = $comment->fresh();
    // dump([
    //     'original' => $comment->toArray(),
    //     'fresh' => $freshComment->toArray(),
    //     'is_approved' => $freshComment->is_approved
    // ]);

    expect($freshComment)
        ->is_approved->toBeTrue()
        ->rejected_reason->toBeNull()
        ->moderated_by->toBe($this->admin->id);
});

test('it can reject comment', function () {
    $comment = Comment::factory()->create([
        'is_approved' => false,
        'blog_post_id' => $this->post->id,
        'user_id' => $this->user->id,
    ]);

    actingAs($this->admin)
        ->patch(route('admin.comments.reject', $comment), [
            'rejected_reason' => 'Inappropriate content',
            '_token' => csrf_token(),
            '_method' => 'PATCH'
        ])
        ->assertRedirect(route('admin.comments.index'))
        ->assertSessionHas('success', 'Comment rejected successfully.');

    expect($comment->fresh())
        ->is_approved->toBeFalse()
        ->rejected_reason->toBe('Inappropriate content')
        ->moderated_by->toBe($this->admin->id);
});

test('it validates rejection reason', function () {
    $comment = Comment::factory()->create([
        'blog_post_id' => $this->post->id,
        'user_id' => $this->user->id,
    ]);

    actingAs($this->admin)
        ->patch(route('admin.comments.reject', $comment), [
            'rejected_reason' => '',
            '_token' => csrf_token(),
            '_method' => 'PATCH'
        ])
        ->assertSessionHasErrors('rejected_reason');
});

test('it can delete comment', function () {
    $comment = Comment::factory()->create([
        'blog_post_id' => $this->post->id,
        'user_id' => $this->user->id,
    ]);

    actingAs($this->admin)
        ->delete(route('admin.comments.destroy', $comment))
        ->assertRedirect(route('admin.comments.index'))
        ->assertSessionHas('success', 'Comment deleted successfully.');

    expect(Comment::find($comment->id))->toBeNull();
});

test('it paginates comments list', function () {
    // Create more comments than the pagination limit
    $comments = Comment::factory()->count(20)->create([
        'blog_post_id' => $this->post->id,
        'user_id' => $this->user->id,
    ]);

    // Test first page
    $response = actingAs($this->admin)
        ->get(route('admin.comments.index'))
        ->assertStatus(200)
        ->assertViewIs('admin.comments.index')
        ->assertViewHas('comments');

    $paginatedComments = $response->viewData('comments');
    
    expect($paginatedComments->count())->toBe(15);
    
    // Assert that the first 15 comments are on the first page
    for ($i = 0; $i < 15; $i++) {
        expect($paginatedComments->contains($comments[$i]))->toBeTrue();
    }

    // Test second page
    $response = actingAs($this->admin)
        ->get(route('admin.comments.index', ['page' => 2]))
        ->assertStatus(200)
        ->assertViewIs('admin.comments.index')
        ->assertViewHas('comments');

    $paginatedComments = $response->viewData('comments');
    
    expect($paginatedComments->count())->toBe(5);
    
    // Assert that the remaining comments are on the second page
    for ($i = 15; $i < 20; $i++) {
        expect($paginatedComments->contains($comments[$i]))->toBeTrue();
    }
});