<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Services\CompatibilityService;
use App\Models\Component;
use App\Models\ComponentCategory;
use App\Models\ComponentCompatibility;
use App\Models\Build;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;

class CompatibilityServiceTest extends TestCase
{
    use RefreshDatabase;
    
    protected CompatibilityService $compatibilityService;
    
    protected function setUp(): void
    {
        parent::setUp();
        $this->compatibilityService = new CompatibilityService();
    }
    
    public function test_can_check_basic_compatibility()
    {
        // Create component categories
        $cpuCategory = ComponentCategory::factory()->create([
            'name' => 'CPU',
            'slug' => 'cpu',
            'is_required' => true,
        ]);
        
        $motherboardCategory = ComponentCategory::factory()->create([
            'name' => 'Motherboard',
            'slug' => 'motherboard',
            'is_required' => true,
        ]);
        
        // Create compatible components
        $cpu = Component::factory()->create([
            'category_id' => $cpuCategory->id,
            'specs' => ['socket' => 'LGA1700'],
        ]);
        
        $motherboard = Component::factory()->create([
            'category_id' => $motherboardCategory->id,
            'specs' => ['socket' => 'LGA1700'],
        ]);
        
        $result = $this->compatibilityService->checkCompatibility([$cpu, $motherboard]);
        
        $this->assertTrue($result->isCompatible());
        $this->assertEmpty($result->getIssues());
    }
    
    public function test_detects_socket_incompatibility()
    {
        // Create component categories
        $cpuCategory = ComponentCategory::factory()->create([
            'name' => 'CPU',
            'slug' => 'cpu',
            'is_required' => true,
        ]);
        
        $motherboardCategory = ComponentCategory::factory()->create([
            'name' => 'Motherboard',
            'slug' => 'motherboard',
            'is_required' => true,
        ]);
        
        // Create incompatible components
        $cpu = Component::factory()->create([
            'category_id' => $cpuCategory->id,
            'specs' => ['socket' => 'LGA1700'],
        ]);
        
        $motherboard = Component::factory()->create([
            'category_id' => $motherboardCategory->id,
            'specs' => ['socket' => 'AM4'],
        ]);
        
        $result = $this->compatibilityService->checkCompatibility([$cpu, $motherboard]);
        
        $this->assertFalse($result->isCompatible());
        $this->assertNotEmpty($result->getIssues());
        
        $issue = $result->getIssues()[0];
        $this->assertStringContainsString('socket', $issue['message']);
    }
    
    public function test_detects_memory_type_incompatibility()
    {
        // Create component categories
        $ramCategory = ComponentCategory::factory()->create([
            'name' => 'Memory (RAM)',
            'slug' => 'memory-ram',
            'is_required' => true,
        ]);
        
        $motherboardCategory = ComponentCategory::factory()->create([
            'name' => 'Motherboard',
            'slug' => 'motherboard',
            'is_required' => true,
        ]);
        
        // Create incompatible components
        $ram = Component::factory()->create([
            'category_id' => $ramCategory->id,
            'specs' => ['memory_type' => 'DDR5'],
        ]);
        
        $motherboard = Component::factory()->create([
            'category_id' => $motherboardCategory->id,
            'specs' => ['memory_type' => 'DDR4'],
        ]);
        
        $result = $this->compatibilityService->checkCompatibility([$ram, $motherboard]);
        
        $this->assertFalse($result->isCompatible());
        $this->assertNotEmpty($result->getIssues());
        
        $issue = $result->getIssues()[0];
        $this->assertStringContainsString('RAM type', $issue['message']);
    }
    
    public function test_detects_missing_required_components()
    {
        // Create required category
        $cpuCategory = ComponentCategory::factory()->create([
            'name' => 'CPU',
            'slug' => 'cpu',
            'is_required' => true,
        ]);
        
        $ramCategory = ComponentCategory::factory()->create([
            'name' => 'Memory (RAM)',
            'slug' => 'memory-ram',
            'is_required' => true,
        ]);
        
        // Create only one component (missing required RAM)
        $cpu = Component::factory()->create([
            'category_id' => $cpuCategory->id,
        ]);
        
        $result = $this->compatibilityService->checkCompatibility([$cpu]);
        
        $this->assertFalse($result->isCompatible());
        $this->assertNotEmpty($result->getIssues());
        
        $missingIssue = collect($result->getIssues())->first(function ($issue) {
            return $issue['type'] === 'missing_required';
        });
        
        $this->assertNotNull($missingIssue);
        $this->assertStringContainsString('Memory (RAM)', $missingIssue['message']);
    }
    
    public function test_generates_power_warnings()
    {
        // Create component categories
        $gpuCategory = ComponentCategory::factory()->create([
            'name' => 'Graphics Card',
            'slug' => 'graphics-card',
        ]);
        
        $psuCategory = ComponentCategory::factory()->create([
            'name' => 'Power Supply',
            'slug' => 'power-supply',
            'is_required' => true,
        ]);
        
        // Create components with power consumption issues
        $gpu = Component::factory()->create([
            'category_id' => $gpuCategory->id,
            'specs' => ['power_consumption' => '400W'],
        ]);
        
        $psu = Component::factory()->create([
            'category_id' => $psuCategory->id,
            'specs' => ['wattage' => '500W'],
        ]);
        
        $result = $this->compatibilityService->checkCompatibility([$gpu, $psu]);
        
        // Should be compatible but have warnings
        $this->assertTrue($result->isCompatible());
        $this->assertNotEmpty($result->getWarnings());
        
        $warning = $result->getWarnings()[0];
        $this->assertStringContainsString('power consumption', $warning['message']);
    }
    
    public function test_can_get_compatible_components()
    {
        // Create component categories
        $cpuCategory = ComponentCategory::factory()->create([
            'name' => 'CPU',
            'slug' => 'cpu',
        ]);
        
        $motherboardCategory = ComponentCategory::factory()->create([
            'name' => 'Motherboard',
            'slug' => 'motherboard',
        ]);
        
        // Create base CPU
        $cpu = Component::factory()->create([
            'category_id' => $cpuCategory->id,
            'specs' => ['socket' => 'LGA1700'],
        ]);
        
        // Create compatible and incompatible motherboards
        $compatibleMb = Component::factory()->create([
            'category_id' => $motherboardCategory->id,
            'specs' => ['socket' => 'LGA1700'],
            'is_active' => true,
            'stock' => 10,
        ]);
        
        $incompatibleMb = Component::factory()->create([
            'category_id' => $motherboardCategory->id,
            'specs' => ['socket' => 'AM4'],
            'is_active' => true,
            'stock' => 10,
        ]);
        
        $compatibleComponents = $this->compatibilityService->getCompatibleComponents($cpu, 'motherboard');
        
        $this->assertCount(1, $compatibleComponents);
        $this->assertEquals($compatibleMb->id, $compatibleComponents->first()->id);
    }
    
    public function test_validates_complete_build()
    {
        // Create user and build
        $user = User::factory()->create();
        $build = Build::factory()->create(['user_id' => $user->id]);
        
        // Create component categories
        $cpuCategory = ComponentCategory::factory()->create([
            'name' => 'CPU',
            'slug' => 'cpu',
            'is_required' => true,
        ]);
        
        $psuCategory = ComponentCategory::factory()->create([
            'name' => 'Power Supply',
            'slug' => 'power-supply',
            'is_required' => true,
        ]);
        
        // Create components
        $cpu = Component::factory()->create([
            'category_id' => $cpuCategory->id,
            'specs' => ['power_consumption' => '100W'],
        ]);
        
        $psu = Component::factory()->create([
            'category_id' => $psuCategory->id,
            'specs' => ['wattage' => '500W'],
        ]);
        
        // Add components to build
        $build->components()->create([
            'component_id' => $cpu->id,
            'category_id' => $cpu->category_id,
            'quantity' => 1,
            'price' => $cpu->price,
        ]);
        
        $build->components()->create([
            'component_id' => $psu->id,
            'category_id' => $psu->category_id,
            'quantity' => 1,
            'price' => $psu->price,
        ]);
        
        $result = $this->compatibilityService->validateBuild($build);
        
        $this->assertInstanceOf(\App\Services\ValidationResult::class, $result);
        $this->assertEquals(100, $result->getPowerConsumption());
        $this->assertEquals(500, $result->getPsuCapacity());
    }
    
    public function test_explicit_compatibility_rules()
    {
        // Create component categories
        $cpuCategory = ComponentCategory::factory()->create([
            'name' => 'CPU',
            'slug' => 'cpu',
        ]);
        
        $motherboardCategory = ComponentCategory::factory()->create([
            'name' => 'Motherboard',
            'slug' => 'motherboard',
        ]);
        
        // Create components
        $cpu = Component::factory()->create(['category_id' => $cpuCategory->id]);
        $motherboard = Component::factory()->create(['category_id' => $motherboardCategory->id]);
        
        // Create explicit incompatibility rule
        ComponentCompatibility::create([
            'component_id' => $cpu->id,
            'compatible_component_id' => $motherboard->id,
            'rule_type' => ComponentCompatibility::RULE_TYPE_INCOMPATIBLE,
            'error_message' => 'These components are explicitly incompatible',
        ]);
        
        $result = $this->compatibilityService->checkCompatibility([$cpu, $motherboard]);
        
        $this->assertFalse($result->isCompatible());
        $this->assertStringContainsString('explicitly incompatible', $result->getIssues()[0]['message']);
    }
}