<?php

namespace Database\Factories;

use App\Models\Component;
use App\Models\Review;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Review>
 */
class ReviewFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Review::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $rating = $this->faker->numberBetween(1, 5);
        
        return [
            'user_id' => User::factory(),
            'component_id' => Component::factory(),
            'rating' => $rating,
            'title' => $this->faker->sentence(4),
            'comment' => $this->faker->paragraph(3),
            'is_approved' => $this->faker->boolean(80), // 80% approved by default
        ];
    }

    /**
     * Indicate that the review is approved.
     */
    public function approved(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_approved' => true,
        ]);
    }

    /**
     * Indicate that the review is pending approval.
     */
    public function pending(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_approved' => false,
        ]);
    }

    /**
     * Create a review with a specific rating.
     */
    public function rating(int $rating): static
    {
        return $this->state(fn (array $attributes) => [
            'rating' => $rating,
        ]);
    }

    /**
     * Create a positive review (4-5 stars).
     */
    public function positive(): static
    {
        return $this->state(fn (array $attributes) => [
            'rating' => $this->faker->numberBetween(4, 5),
            'title' => $this->faker->randomElement([
                'Great product!',
                'Excellent quality',
                'Highly recommended',
                'Perfect for my build',
                'Amazing performance'
            ]),
            'comment' => $this->faker->randomElement([
                'This component exceeded my expectations. Great build quality and performance.',
                'Perfect fit for my gaming rig. No issues whatsoever.',
                'Excellent value for money. Would definitely buy again.',
                'Outstanding performance and reliability. Highly recommended.',
                'Great product with excellent customer support.'
            ]),
        ]);
    }

    /**
     * Create a negative review (1-2 stars).
     */
    public function negative(): static
    {
        return $this->state(fn (array $attributes) => [
            'rating' => $this->faker->numberBetween(1, 2),
            'title' => $this->faker->randomElement([
                'Not what I expected',
                'Poor quality',
                'Had issues',
                'Disappointed',
                'Could be better'
            ]),
            'comment' => $this->faker->randomElement([
                'The product did not meet my expectations. Had several issues.',
                'Quality is not as advertised. Would not recommend.',
                'Had compatibility issues with my setup.',
                'Performance was below average for the price point.',
                'Customer service was unhelpful when I had problems.'
            ]),
        ]);
    }

    /**
     * Create a neutral review (3 stars).
     */
    public function neutral(): static
    {
        return $this->state(fn (array $attributes) => [
            'rating' => 3,
            'title' => $this->faker->randomElement([
                'It\'s okay',
                'Average product',
                'Does the job',
                'Nothing special',
                'Decent quality'
            ]),
            'comment' => $this->faker->randomElement([
                'It works as expected but nothing extraordinary.',
                'Average quality for the price. Gets the job done.',
                'Decent product but there are better alternatives.',
                'It\'s okay, meets basic requirements.',
                'Not bad but not great either. Average experience.'
            ]),
        ]);
    }

    /**
     * Create a review that needs moderation.
     */
    public function needsModeration(): static
    {
        return $this->state(fn (array $attributes) => [
            'rating' => 1,
            'title' => 'Terrible product',
            'comment' => 'This is spam and fake. Worst garbage ever.',
            'is_approved' => false,
        ]);
    }

    /**
     * Create a short review that might need moderation.
     */
    public function shortReview(): static
    {
        return $this->state(fn (array $attributes) => [
            'rating' => $this->faker->numberBetween(1, 5),
            'title' => 'Short',
            'comment' => 'Bad.',
            'is_approved' => false,
        ]);
    }

    /**
     * Create a detailed review.
     */
    public function detailed(): static
    {
        return $this->state(fn (array $attributes) => [
            'title' => $this->faker->sentence(6),
            'comment' => $this->faker->paragraphs(3, true),
            'is_approved' => true,
        ]);
    }
}