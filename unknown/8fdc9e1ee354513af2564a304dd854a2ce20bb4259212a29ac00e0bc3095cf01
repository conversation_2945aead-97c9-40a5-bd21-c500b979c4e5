<?php

namespace Tests\Browser;

use App\Models\User;
use <PERSON><PERSON>\Dusk\Browser;
use Tests\Browser\Pages\LoginPage;
use Tests\Browser\Pages\PaymentPage;
use Tests\DuskTestCase;

class PaymentFlowTest extends DuskTestCase
{
    /**
     * Test basic payment form rendering.
     */
    public function test_payment_form_renders_correctly(): void
    {
        $this->browse(function (Browser $browser) {
            $browser->visit(new PaymentPage)
                    ->assertSee('Make Payment')
                    ->assertPresent('@amount')
                    ->assertPresent('@description')
                    ->assertPresent('@currency')
                    ->assertPresent('@payButton');
        });
    }

    /**
     * Test payment form validation.
     */
    public function test_payment_form_validation(): void
    {
        $this->browse(function (Browser $browser) {
            $browser->visit(new PaymentPage)
                    ->click('@payButton')
                    ->waitForText('Amount is required')
                    ->assertSee('Amount is required');
        });
    }

    /**
     * Test successful payment flow with <PERSON>zorpay.
     */
    public function test_razorpay_payment_flow(): void
    {
        $user = User::factory()->create();

        $this->browse(function (Browser $browser) use ($user) {
            $browser->loginAs($user)
                    ->visit(new PaymentPage)
                    ->fillPaymentForm([
                        'amount' => '100.00',
                        'description' => 'Test payment',
                        'gateway' => 'razorpay'
                    ])
                    ->submitPayment()
                    ->waitFor('@loadingOverlay')
                    ->assertPresent('@loadingOverlay');
        });
    }

    /**
     * Test payment with different gateways.
     */
    public function test_multiple_gateway_selection(): void
    {
        $user = User::factory()->create();

        $this->browse(function (Browser $browser) use ($user) {
            $browser->loginAs($user)
                    ->visit(new PaymentPage);

            // Test Razorpay selection
            $browser->click('@gatewayRazorpay')
                    ->assertChecked('@gatewayRazorpay');

            // Test PayUmoney selection
            $browser->click('@gatewayPayumoney')
                    ->assertChecked('@gatewayPayumoney');

            // Test Cashfree selection
            $browser->click('@gatewayCashfree')
                    ->assertChecked('@gatewayCashfree');
        });
    }

    /**
     * Test payment error handling.
     */
    public function test_payment_error_handling(): void
    {
        $user = User::factory()->create();

        $this->browse(function (Browser $browser) use ($user) {
            $browser->loginAs($user)
                    ->visit(new PaymentPage)
                    ->fillPaymentForm([
                        'amount' => '-100', // Invalid amount
                        'description' => 'Test payment',
                        'gateway' => 'razorpay'
                    ])
                    ->submitPayment()
                    ->waitForText('Please enter a valid amount')
                    ->assertSee('Please enter a valid amount');
        });
    }

    /**
     * Test payment loading states.
     */
    public function test_payment_loading_states(): void
    {
        $user = User::factory()->create();

        $this->browse(function (Browser $browser) use ($user) {
            $browser->loginAs($user)
                    ->visit(new PaymentPage)
                    ->fillPaymentForm([
                        'amount' => '100.00',
                        'description' => 'Test payment',
                        'gateway' => 'razorpay'
                    ])
                    ->submitPayment()
                    ->waitFor('@loadingOverlay', 2)
                    ->assertPresent('@loadingOverlay');
        });
    }
}