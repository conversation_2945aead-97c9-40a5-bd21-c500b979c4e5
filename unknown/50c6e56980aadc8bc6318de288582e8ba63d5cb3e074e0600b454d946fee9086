<div class="container mx-auto py-8">
    <h1 class="text-3xl font-bold mb-6">PC Builder</h1>
    
    <div class="grid grid-cols-1 lg:grid-cols-12 gap-6">
        <!-- Categories Sidebar -->
        <div class="lg:col-span-3 bg-white rounded-lg shadow p-4">
            <h2 class="text-xl font-semibold mb-4">Components</h2>
            <ul class="space-y-2">
                @foreach($categories as $category)
                    <li>
                        <button 
                            wire:click="selectCategory('{{ $category->slug }}')" 
                            class="w-full text-left px-4 py-2 rounded-md {{ $selectedCategory === $category->slug ? 'bg-blue-500 text-white' : 'hover:bg-gray-100' }}"
                        >
                            <div class="flex items-center">
                                @if(isset($selectedComponents[$category->slug]))
                                    <span class="mr-2 text-green-500">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                        </svg>
                                    </span>
                                @elseif($category->is_required)
                                    <span class="mr-2 text-red-500">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                                        </svg>
                                    </span>
                                @else
                                    <span class="mr-2 text-gray-400">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v2H7a1 1 0 100 2h2v2a1 1 0 102 0v-2h2a1 1 0 100-2h-2V7z" clip-rule="evenodd" />
                                        </svg>
                                    </span>
                                @endif
                                {{ $category->name }}
                            </div>
                        </button>
                    </li>
                @endforeach
            </ul>
        </div>
        
        <!-- Main Content Area -->
        <div class="lg:col-span-9 space-y-6">
            <!-- Selected Components Summary -->
            <div class="bg-white rounded-lg shadow p-6">
                <h2 class="text-xl font-semibold mb-4">Your Build</h2>
                
                @if(count($selectedComponents) > 0)
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Component</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Selection</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Price</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                @foreach($categories as $category)
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                            {{ $category->name }}
                                            @if($category->is_required)
                                                <span class="text-red-500 ml-1">*</span>
                                            @endif
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            @if(isset($selectedComponents[$category->slug]))
                                                {{ $selectedComponents[$category->slug]['name'] }}
                                            @else
                                                <span class="text-gray-400">Not selected</span>
                                            @endif
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            @if(isset($selectedComponents[$category->slug]))
                                                ${{ number_format($selectedComponents[$category->slug]['price'], 2) }}
                                            @else
                                                -
                                            @endif
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            @if(isset($selectedComponents[$category->slug]))
                                                <button 
                                                    wire:click="removeComponent('{{ $category->slug }}')" 
                                                    class="text-red-600 hover:text-red-900"
                                                >
                                                    Remove
                                                </button>
                                            @else
                                                <button 
                                                    wire:click="selectCategory('{{ $category->slug }}')" 
                                                    class="text-blue-600 hover:text-blue-900"
                                                >
                                                    Select
                                                </button>
                                            @endif
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                            <tfoot>
                                <tr>
                                    <td colspan="2" class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 text-right">
                                        Total:
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-bold text-gray-900">
                                        ${{ number_format($totalPrice, 2) }}
                                    </td>
                                    <td></td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                    
                    <!-- Compatibility Issues -->
                    @if(count($compatibilityIssues) > 0)
                        <div class="mt-4 p-4 bg-red-50 border border-red-200 rounded-md">
                            <h3 class="text-lg font-medium text-red-800">Compatibility Issues</h3>
                            <ul class="mt-2 list-disc list-inside text-sm text-red-700">
                                @foreach($compatibilityIssues as $issue)
                                    <li>{{ $issue }}</li>
                                @endforeach
                            </ul>
                        </div>
                    @endif
                    
                    <!-- Build Actions -->
                    <div class="mt-6 flex flex-wrap gap-4">
                        @if($isComplete)
                            <button 
                                class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                            >
                                Save Build
                            </button>
                            
                            <button 
                                class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
                            >
                                Add All to Cart
                            </button>
                        @else
                            <div class="text-red-600">
                                Please select all required components to complete your build.
                            </div>
                        @endif
                    </div>
                @else
                    <div class="text-gray-500 text-center py-8">
                        <p>Start building your PC by selecting components from the categories on the left.</p>
                    </div>
                @endif
            </div>
            
            <!-- Component Selection -->
            @if($selectedCategory)
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h2 class="text-xl font-semibold">Select {{ $categories->firstWhere('slug', $selectedCategory)->name }}</h2>
                        
                        <!-- Filters -->
                        <div class="flex items-center space-x-4">
                            <div class="relative">
                                <input 
                                    type="text" 
                                    wire:model.debounce.300ms="searchTerm"
                                    placeholder="Search components..."
                                    class="pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                                >
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <svg class="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd" />
                                    </svg>
                                </div>
                            </div>
                            
                            <label class="flex items-center">
                                <input 
                                    type="checkbox" 
                                    wire:model="inStockOnly"
                                    class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                                >
                                <span class="ml-2 text-sm text-gray-600">In Stock Only</span>
                            </label>
                            
                            <select wire:model="sortBy" class="border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500">
                                <option value="price">Price</option>
                                <option value="name">Name</option>
                                <option value="brand">Brand</option>
                                <option value="created_at">Newest</option>
                            </select>
                            
                            <button 
                                wire:click="$set('sortDirection', '{{ $sortDirection === 'asc' ? 'desc' : 'asc' }}')"
                                class="p-2 border border-gray-300 rounded-md hover:bg-gray-50"
                            >
                                @if($sortDirection === 'asc')
                                    <svg class="h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                        <path d="M3 3a1 1 0 000 2h11a1 1 0 100-2H3zM3 7a1 1 0 000 2h5a1 1 0 000-2H3zM3 11a1 1 0 100 2h4a1 1 0 100-2H3zM13 16a1 1 0 102 0v-5.586l1.293 1.293a1 1 0 001.414-1.414l-3-3a1 1 0 00-1.414 0l-3 3a1 1 0 101.414 1.414L13 10.414V16z" />
                                    </svg>
                                @else
                                    <svg class="h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                        <path d="M3 3a1 1 0 000 2h11a1 1 0 100-2H3zM3 7a1 1 0 000 2h7a1 1 0 100-2H3zM3 11a1 1 0 100 2h4a1 1 0 100-2H3zM15 8a1 1 0 10-2 0v5.586l-1.293-1.293a1 1 0 00-1.414 1.414l3 3a1 1 0 001.414 0l3-3a1 1 0 00-1.414-1.414L15 13.586V8z" />
                                    </svg>
                                @endif
                            </button>
                        </div>
                    </div>
                    
                    @if($components->isEmpty())
                        <div class="text-gray-500 text-center py-8">
                            <p>No components found in this category.</p>
                        </div>
                    @else
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                            @foreach($components as $component)
                                <div class="border rounded-lg overflow-hidden hover:shadow-lg transition-shadow {{ isset($component->is_compatible) && !$component->is_compatible ? 'border-red-300 bg-red-50' : '' }}">
                                    @if($component->image)
                                        <img src="{{ $component->image }}" alt="{{ $component->name }}" class="w-full h-48 object-cover">
                                    @else
                                        <div class="w-full h-48 bg-gray-200 flex items-center justify-center">
                                            <span class="text-gray-400">No image</span>
                                        </div>
                                    @endif
                                    
                                    <div class="p-4">
                                        <div class="flex items-start justify-between mb-2">
                                            <h3 class="font-semibold text-lg">{{ $component->name }}</h3>
                                            
                                            <!-- Compatibility Status -->
                                            @if(isset($component->is_compatible))
                                                @if($component->is_compatible)
                                                    <div class="flex items-center text-green-600" title="Compatible">
                                                        <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                                        </svg>
                                                    </div>
                                                @else
                                                    <div class="flex items-center text-red-600" title="Incompatible">
                                                        <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                                                        </svg>
                                                    </div>
                                                @endif
                                            @endif
                                        </div>
                                        
                                        <p class="text-gray-600 mb-2">${{ number_format($component->price, 2) }}</p>
                                        
                                        <div class="flex items-center justify-between mb-3">
                                            @if($component->stock > 0)
                                                <span class="text-green-600 text-sm">In Stock ({{ $component->stock }})</span>
                                            @else
                                                <span class="text-red-600 text-sm">Out of Stock</span>
                                            @endif
                                            
                                            @if($component->brand)
                                                <span class="text-gray-500 text-sm">{{ $component->brand }}</span>
                                            @endif
                                        </div>
                                        
                                        <!-- Compatibility Warnings -->
                                        @if(isset($component->compatibility_warnings) && !empty($component->compatibility_warnings))
                                            <div class="mb-3 p-2 bg-yellow-50 border border-yellow-200 rounded text-xs">
                                                <div class="flex items-center text-yellow-800 mb-1">
                                                    <svg class="h-4 w-4 mr-1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                                        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                                                    </svg>
                                                    Warnings:
                                                </div>
                                                @foreach($component->compatibility_warnings as $warning)
                                                    <div class="text-yellow-700">• {{ $warning }}</div>
                                                @endforeach
                                            </div>
                                        @endif
                                        
                                        <div class="mt-4">
                                            <button 
                                                wire:click="addComponent({{ $component->id }}, '{{ $selectedCategory }}')" 
                                                class="w-full px-4 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors
                                                    {{ $component->stock <= 0 ? 'bg-gray-400 text-white cursor-not-allowed' : 
                                                       (isset($component->is_compatible) && !$component->is_compatible ? 'bg-red-600 hover:bg-red-700 text-white focus:ring-red-500' : 
                                                        'bg-blue-600 hover:bg-blue-700 text-white focus:ring-blue-500') }}"
                                                {{ $component->stock <= 0 ? 'disabled' : '' }}
                                            >
                                                @if($component->stock <= 0)
                                                    Out of Stock
                                                @elseif(isset($component->is_compatible) && !$component->is_compatible)
                                                    Select (Incompatible)
                                                @else
                                                    Select
                                                @endif
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @endif
                </div>
            @endif
        </div>
    </div>
</div>