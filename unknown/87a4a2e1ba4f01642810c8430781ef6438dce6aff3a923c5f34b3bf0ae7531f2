<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\ComponentCategory>
 */
class ComponentCategoryFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $categories = [
            ['name' => 'CPU', 'icon' => 'cpu', 'is_required' => true],
            ['name' => 'Motherboard', 'icon' => 'motherboard', 'is_required' => true],
            ['name' => 'Memory (RAM)', 'icon' => 'memory', 'is_required' => true],
            ['name' => 'Storage', 'icon' => 'storage', 'is_required' => true],
            ['name' => 'Graphics Card', 'icon' => 'gpu', 'is_required' => false],
            ['name' => 'Power Supply', 'icon' => 'psu', 'is_required' => true],
            ['name' => 'Case', 'icon' => 'case', 'is_required' => true],
            ['name' => 'CPU Cooler', 'icon' => 'cooler', 'is_required' => false],
        ];
        
        $category = $this->faker->randomElement($categories);
        $name = $category['name'];
        
        return [
            'name' => $name,
            'slug' => \Str::slug($name) . '-' . $this->faker->unique()->randomNumber(4),
            'description' => $this->faker->sentence(),
            'icon' => $category['icon'],
            'display_order' => $this->faker->numberBetween(1, 10),
            'is_required' => $category['is_required'],
        ];
    }
}
