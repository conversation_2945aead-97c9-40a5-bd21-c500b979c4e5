<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Services\GatewaySettingsService;
use App\Services\PaymentGatewayFactory;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Illuminate\Validation\ValidationException;
use Illuminate\Support\Facades\Log;

class GatewayController extends Controller
{
    public function __construct(
        private GatewaySettingsService $gatewaySettingsService,
        private PaymentGatewayFactory $gatewayFactory
    ) {}

    /**
     * Display all gateway settings
     */
    public function index(): View
    {
        $supportedGateways = $this->gatewayFactory->getSupportedGateways();
        
        // Convert $supportedGateways to a collection if it's an array
        if (is_array($supportedGateways)) {
            $supportedGateways = collect($supportedGateways);
        }
        
        $settings = $this->gatewaySettingsService->getAllSettings();
        
        // Convert $settings to a collection if it's an array
        if (is_array($settings)) {
            $settings = collect($settings);
        }
        
        $gateways = $settings; // For backward compatibility with tests
        
        return view('admin.gateways.index', compact('supportedGateways', 'settings', 'gateways'));
    }

    /**
     * Show specific gateway settings
     */
    public function show(string $gateway): View
    {
        if (!$this->gatewayFactory->isSupported($gateway)) {
            abort(404, 'Gateway not supported');
        }

        $setting = $this->gatewaySettingsService->getGatewaySettings($gateway);
        $testResult = $this->gatewaySettingsService->testGatewayConfiguration($gateway);
        $gatewayName = $gateway; // For backward compatibility with tests
        
        // For backward compatibility with tests
        $gateway_obj = $setting;
        
        // The test is expecting a variable named 'gateway' that contains the setting object
        return view('admin.gateways.show', compact('gateway', 'setting', 'testResult', 'gatewayName', 'gateway_obj'))
            ->with('gateway', $setting); // This is for backward compatibility with tests
    }

    /**
     * Update gateway configuration
     */
    public function update(Request $request, string $gateway)
    {
        if (!$this->gatewayFactory->isSupported($gateway)) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Gateway not supported'
                ], 404);
            }
            abort(404, 'Gateway not supported');
        }

        try {
            $validatedData = $this->validateGatewayUpdate($request, $gateway);
            
            // Log admin action before update
            Log::info('Gateway settings updated by admin', [
                'admin_id' => auth()->id(),
                'admin_email' => auth()->user()->email,
                'gateway' => $gateway,
                'changes' => $this->sanitizeSettingsForLogging($validatedData),
                'ip' => $request->ip(),
                'timestamp' => now()->toISOString()
            ]);
            
            $this->gatewaySettingsService->updateGatewaySettings($gateway, $validatedData);

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Gateway settings updated successfully'
                ]);
            }

            return redirect()->route('admin.gateways.show', $gateway)
                            ->with('success', 'Gateway settings updated successfully');
        } catch (ValidationException $e) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $e->validator->errors()
                ], 422);
            }
            return redirect()->back()
                            ->withErrors($e->validator)
                            ->withInput();
        } catch (\Exception $e) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to update gateway settings: ' . $e->getMessage()
                ], 500);
            }
            return redirect()->back()
                            ->with('error', 'Failed to update gateway settings: ' . $e->getMessage())
                            ->withInput();
        }
    }

    /**
     * Toggle gateway enable/disable status
     */
    public function toggle(Request $request, string $gateway): JsonResponse
    {
        if (!$this->gatewayFactory->isSupported($gateway)) {
            return response()->json([
                'success' => false,
                'message' => 'Gateway not supported'
            ], 404);
        }

        try {
            $request->validate([
                'enabled' => 'required|boolean'
            ]);

            $enabled = $request->boolean('enabled');
            
            // Log admin action
            Log::info('Gateway toggled by admin', [
                'admin_id' => auth()->id(),
                'admin_email' => auth()->user()->email,
                'gateway' => $gateway,
                'action' => $enabled ? 'enabled' : 'disabled',
                'ip' => $request->ip(),
                'timestamp' => now()->toISOString()
            ]);
            
            $setting = $this->gatewaySettingsService->toggleGateway($gateway, $enabled);

            return response()->json([
                'success' => true,
                'message' => $enabled ? 'Gateway enabled successfully' : 'Gateway disabled successfully',
                'data' => [
                    'gateway' => $gateway,
                    'is_enabled' => $setting->is_enabled
                ]
            ]);
        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->validator->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to toggle gateway: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Switch gateway between test and live mode
     */
    public function switchMode(Request $request, string $gateway): JsonResponse
    {
        if (!$this->gatewayFactory->isSupported($gateway)) {
            return response()->json([
                'success' => false,
                'message' => 'Gateway not supported'
            ], 404);
        }

        try {
            $request->validate([
                'test_mode' => 'required|boolean'
            ]);

            $testMode = $request->boolean('test_mode');
            
            // Log admin action
            Log::info('Gateway mode switched by admin', [
                'admin_id' => auth()->id(),
                'admin_email' => auth()->user()->email,
                'gateway' => $gateway,
                'mode' => $testMode ? 'test' : 'live',
                'ip' => $request->ip(),
                'timestamp' => now()->toISOString()
            ]);
            
            $setting = $this->gatewaySettingsService->switchMode($gateway, $testMode);

            return response()->json([
                'success' => true,
                'message' => $testMode ? 'Switched to test mode' : 'Switched to live mode',
                'data' => [
                    'gateway' => $gateway,
                    'is_test_mode' => $setting->is_test_mode
                ]
            ]);
        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->validator->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to switch mode: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Test gateway configuration
     */
    public function test(string $gateway): JsonResponse
    {
        if (!$this->gatewayFactory->isSupported($gateway)) {
            return response()->json([
                'success' => false,
                'message' => 'Gateway not supported'
            ], 404);
        }

        try {
            $result = $this->gatewaySettingsService->testGatewayConfiguration($gateway);
            
            return response()->json([
                'success' => $result['success'],
                'message' => $result['message'],
                'gateway' => $gateway
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Test failed: ' . $e->getMessage(),
                'gateway' => $gateway
            ], 500);
        }
    }

    /**
     * Get enabled gateways list
     */
    public function enabled(): JsonResponse
    {
        try {
            $enabledGateways = $this->gatewaySettingsService->getEnabledGateways();
            
            return response()->json([
                'success' => true,
                'data' => $enabledGateways
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve enabled gateways: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Validate gateway update request
     */
    private function validateGatewayUpdate(Request $request, string $gateway): array
    {
        $baseRules = [
            'is_enabled' => 'boolean',
            'is_test_mode' => 'boolean',
            'settings' => 'required|array'
        ];

        // Add gateway-specific validation rules
        $settingsRules = $this->getGatewayValidationRules($gateway);
        
        foreach ($settingsRules as $key => $rule) {
            $baseRules["settings.{$key}"] = $rule;
        }

        return $request->validate($baseRules);
    }

    /**
     * Get validation rules for specific gateway
     */
    private function getGatewayValidationRules(string $gateway): array
    {
        return match ($gateway) {
            'razorpay' => [
                'key_id' => 'required|string|max:255',
                'key_secret' => 'required|string|max:255',
                'webhook_secret' => 'nullable|string|max:255'
            ],
            'payumoney' => [
                'merchant_key' => 'required|string|max:255',
                'salt' => 'required|string|max:255',
                'auth_header' => 'nullable|string|max:255'
            ],
            'cashfree' => [
                'app_id' => 'required|string|max:255',
                'secret_key' => 'required|string|max:255',
                'client_id' => 'required|string|max:255',
                'client_secret' => 'required|string|max:255'
            ],
            default => []
        };
    }
    
    /**
     * Sanitize settings for logging (remove sensitive data)
     */
    private function sanitizeSettingsForLogging(array $data): array
    {
        $sanitized = $data;
        
        // Remove sensitive fields from settings
        if (isset($sanitized['settings'])) {
            $sensitiveFields = [
                'key_secret',
                'webhook_secret',
                'salt',
                'auth_header',
                'secret_key',
                'client_secret'
            ];
            
            foreach ($sensitiveFields as $field) {
                if (isset($sanitized['settings'][$field])) {
                    $sanitized['settings'][$field] = '[REDACTED]';
                }
            }
        }
        
        return $sanitized;
    }
}