<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Component;
use App\Models\ComponentCategory;
use App\Models\InventoryAlert;
use App\Models\StockMovement;
use App\Services\InventoryService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Livewire\Livewire;
use Tests\TestCase;

class InventoryManagementTest extends TestCase
{
    use RefreshDatabase;

    protected User $admin;
    protected Component $component;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->admin = User::factory()->create(['role' => 'admin']);
        
        $category = ComponentCategory::factory()->create();
        $this->component = Component::factory()->create([
            'category_id' => $category->id,
            'stock' => 5, // Low stock
            'is_active' => true
        ]);
    }

    public function test_admin_can_view_inventory_management_page()
    {
        $this->actingAs($this->admin)
            ->get('/admin/inventory')
            ->assertStatus(200)
            ->assertSee('Inventory Management');
    }

    public function test_non_admin_cannot_access_inventory_management()
    {
        $user = User::factory()->create(['role' => 'user']);
        
        $this->actingAs($user)
            ->get('/admin/inventory')
            ->assertStatus(403);
    }

    public function test_inventory_overview_displays_correct_stats()
    {
        // Create some test data
        ComponentCategory::factory()->create();
        
        Component::factory()->create([
            'stock' => 0, // Out of stock
            'is_active' => true
        ]);
        
        Component::factory()->create([
            'stock' => 25, // Normal stock
            'is_active' => true
        ]);

        Livewire::actingAs($this->admin)
            ->test('admin.inventory-management')
            ->assertSet('activeTab', 'overview')
            ->assertSee('Total Components')
            ->assertSee('Low Stock')
            ->assertSee('Out of Stock');
    }

    public function test_can_view_inventory_alerts()
    {
        // Create an alert
        $alert = InventoryAlert::factory()->create([
            'component_id' => $this->component->id,
            'type' => 'low_stock',
            'is_resolved' => false
        ]);

        Livewire::actingAs($this->admin)
            ->test('admin.inventory-management')
            ->call('setActiveTab', 'alerts')
            ->assertSet('activeTab', 'alerts')
            ->assertSee($this->component->name)
            ->assertSee('Low stock');
    }

    public function test_can_resolve_inventory_alert()
    {
        $alert = InventoryAlert::factory()->create([
            'component_id' => $this->component->id,
            'type' => 'low_stock',
            'is_resolved' => false
        ]);

        Livewire::actingAs($this->admin)
            ->test('admin.inventory-management')
            ->call('setActiveTab', 'alerts')
            ->call('resolveAlert', $alert->id)
            ->assertDispatched('alert-resolved');

        $this->assertTrue($alert->fresh()->is_resolved);
    }

    public function test_can_view_alert_details()
    {
        $alert = InventoryAlert::factory()->create([
            'component_id' => $this->component->id,
            'type' => 'low_stock',
            'is_resolved' => false
        ]);

        Livewire::actingAs($this->admin)
            ->test('admin.inventory-management')
            ->call('viewAlert', $alert->id)
            ->assertSet('showAlertModal', true)
            ->assertSet('selectedAlert.id', $alert->id);
    }

    public function test_can_view_low_stock_components()
    {
        Livewire::actingAs($this->admin)
            ->test('admin.inventory-management')
            ->call('setActiveTab', 'low-stock')
            ->assertSet('activeTab', 'low-stock')
            ->assertSee($this->component->name);
    }

    public function test_can_view_out_of_stock_components()
    {
        // Create an out of stock component
        $outOfStockComponent = Component::factory()->create([
            'stock' => 0,
            'is_active' => true
        ]);

        Livewire::actingAs($this->admin)
            ->test('admin.inventory-management')
            ->call('setActiveTab', 'out-of-stock')
            ->assertSet('activeTab', 'out-of-stock')
            ->assertSee($outOfStockComponent->name);
    }

    public function test_inventory_service_integration()
    {
        $inventoryService = app(InventoryService::class);
        
        // Test stock update creates movement record
        $inventoryService->updateStock($this->component, 15, 'manual_restock');
        
        $this->assertDatabaseHas('stock_movements', [
            'component_id' => $this->component->id,
            'quantity_change' => 10,
            'previous_stock' => 5,
            'new_stock' => 15,
            'reason' => 'manual_restock'
        ]);
    }

    public function test_low_stock_alert_creation()
    {
        // Create a component with higher stock first
        $component = Component::factory()->create([
            'stock' => 15,
            'is_active' => true
        ]);
        
        $inventoryService = app(InventoryService::class);
        $inventoryService->setLowStockThreshold(10);
        
        // Update stock to trigger low stock alert (from 15 to 8, crossing threshold)
        $inventoryService->updateStock($component, 8, 'sale');
        
        $this->assertDatabaseHas('inventory_alerts', [
            'component_id' => $component->id,
            'type' => 'low_stock',
            'current_stock' => 8,
            'is_resolved' => false
        ]);
    }

    public function test_out_of_stock_alert_creation()
    {
        $inventoryService = app(InventoryService::class);
        
        // Update stock to trigger out of stock alert
        $inventoryService->updateStock($this->component, 0, 'sale');
        
        $this->assertDatabaseHas('inventory_alerts', [
            'component_id' => $this->component->id,
            'type' => 'out_of_stock',
            'current_stock' => 0,
            'is_resolved' => false
        ]);
    }

    public function test_inventory_analytics_generation()
    {
        // Create some test data
        StockMovement::factory()->create([
            'component_id' => $this->component->id,
            'type' => 'sale'
        ]);
        
        InventoryAlert::factory()->create([
            'component_id' => $this->component->id,
            'type' => 'low_stock'
        ]);

        $inventoryService = app(InventoryService::class);
        $analytics = $inventoryService->getInventoryAnalytics(30);

        $this->assertIsArray($analytics);
        $this->assertArrayHasKey('total_movements', $analytics);
        $this->assertArrayHasKey('total_alerts', $analytics);
        $this->assertArrayHasKey('alert_resolution_rate', $analytics);
        $this->assertGreaterThan(0, $analytics['total_movements']);
        $this->assertGreaterThan(0, $analytics['total_alerts']);
    }
}