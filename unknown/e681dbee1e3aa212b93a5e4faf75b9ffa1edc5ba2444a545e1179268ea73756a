<?php

namespace Tests\Unit\Services;

use Tests\TestCase;
use App\Services\Payment\Gateways\PayUmoneyService;
use App\Models\GatewaySetting;
use App\Models\Transaction;
use App\Models\User;
use App\Services\Payment\Exceptions\GatewayConfigurationException;
use App\Services\Payment\Exceptions\InvalidPaymentDataException;
use App\Services\Payment\Exceptions\PaymentGatewayException;
use App\Services\Payment\Exceptions\WebhookVerificationException;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Support\Facades\Log;
use Mockery;

class PayUmoneyServiceTest extends TestCase
{
    use DatabaseTransactions;

    protected PayUmoneyService $payumoneyService;
    protected GatewaySetting $gatewaySetting;
    protected User $user;
    protected Transaction $transaction;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test user
        $this->user = User::factory()->create([
            'email' => '<EMAIL>'
        ]);

        // Create gateway setting for testing
        $this->gatewaySetting = new GatewaySetting();
        $this->gatewaySetting->gateway_name = GatewaySetting::GATEWAY_PAYUMONEY;
        $this->gatewaySetting->is_enabled = true;
        $this->gatewaySetting->is_test_mode = true;
        $this->gatewaySetting->settings = [
            'merchant_key' => 'test_merchant_key',
            'salt' => 'test_salt_key',
            'auth_header' => 'test_auth_header'
        ];
        $this->gatewaySetting->save();

        // Create test transaction
        $this->transaction = Transaction::create([
            'user_id' => $this->user->id,
            'gateway_name' => GatewaySetting::GATEWAY_PAYUMONEY,
            'amount' => 100.00,
            'currency' => 'INR',
            'status' => Transaction::STATUS_PENDING,
            'transaction_id' => 'TXN_' . uniqid(),
        ]);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_constructor_loads_configuration_successfully()
    {
        $service = new PayUmoneyService();
        
        $this->assertEquals(GatewaySetting::GATEWAY_PAYUMONEY, $service->getGatewayName());
        $this->assertTrue($service->isEnabled());
    }

    public function test_constructor_throws_exception_when_configuration_not_found()
    {
        // Delete the gateway setting
        $this->gatewaySetting->delete();

        $this->expectException(GatewayConfigurationException::class);
        $this->expectExceptionMessage('PayUmoney gateway configuration not found');

        new PayUmoneyService();
    }

    public function test_constructor_throws_exception_when_credentials_missing()
    {
        // Update settings to remove credentials
        $this->gatewaySetting->forceFill([
            'settings' => json_encode([
                'merchant_key' => '',
                'salt' => '',
                'auth_header' => 'test_auth_header'
            ])
        ]);
        $this->gatewaySetting->save();

        $this->expectException(GatewayConfigurationException::class);
        $this->expectExceptionMessage('PayUmoney API credentials not configured');

        new PayUmoneyService();
    }

    public function test_create_payment_success()
    {
        $service = new PayUmoneyService();

        $paymentData = [
            'amount' => 100.00,
            'transaction_id' => $this->transaction->transaction_id,
            'product_info' => 'Test Product',
            'firstname' => 'John',
            'email' => '<EMAIL>',
            'phone' => '**********'
        ];

        $result = $service->createPayment($paymentData);

        $this->assertTrue($result['success']);
        $this->assertEquals('POST', $result['method']);
        $this->assertTrue($result['test_mode']);
        $this->assertStringContainsString('test.payu.in', $result['payment_url']);
        
        // Verify payment data structure
        $this->assertArrayHasKey('payment_data', $result);
        $paymentFormData = $result['payment_data'];
        
        $this->assertEquals('test_merchant_key', $paymentFormData['key']);
        $this->assertEquals($this->transaction->transaction_id, $paymentFormData['txnid']);
        $this->assertEquals('100.00', $paymentFormData['amount']);
        $this->assertEquals('Test Product', $paymentFormData['productinfo']);
        $this->assertEquals('John', $paymentFormData['firstname']);
        $this->assertEquals('<EMAIL>', $paymentFormData['email']);
        $this->assertEquals('**********', $paymentFormData['phone']);
        $this->assertEquals('payu_paisa', $paymentFormData['service_provider']);
        
        // Verify hash is generated
        $this->assertArrayHasKey('hash', $paymentFormData);
        $this->assertNotEmpty($paymentFormData['hash']);
        
        // Verify UDF fields
        $this->assertEquals($this->user->id, $paymentFormData['udf1']);
        $this->assertEquals($this->transaction->id, $paymentFormData['udf2']);
    }

    public function test_create_payment_uses_live_url_in_production()
    {
        // Set gateway to live mode
        $this->gatewaySetting->update(['is_test_mode' => false]);
        
        $service = new PayUmoneyService();

        $paymentData = [
            'amount' => 100.00,
            'transaction_id' => $this->transaction->transaction_id,
        ];

        $result = $service->createPayment($paymentData);

        $this->assertFalse($result['test_mode']);
        $this->assertStringContainsString('secure.payu.in', $result['payment_url']);
    }

    public function test_create_payment_validates_required_fields()
    {
        $service = new PayUmoneyService();

        $this->expectException(InvalidPaymentDataException::class);
        $this->expectExceptionMessage('Missing required field: amount');

        $service->createPayment([
            'transaction_id' => $this->transaction->transaction_id,
        ]);
    }

    public function test_create_payment_validates_amount()
    {
        $service = new PayUmoneyService();

        $this->expectException(InvalidPaymentDataException::class);
        $this->expectExceptionMessage('Amount must be a positive number');

        $service->createPayment([
            'amount' => -100,
            'transaction_id' => $this->transaction->transaction_id,
        ]);
    }

    public function test_create_payment_fails_with_invalid_transaction()
    {
        $service = new PayUmoneyService();

        $this->expectException(PaymentGatewayException::class);
        $this->expectExceptionMessage('Payment creation failed: Transaction not found');

        $service->createPayment([
            'amount' => 100.00,
            'transaction_id' => 'invalid_transaction_id',
        ]);
    }

    public function test_verify_payment_success()
    {
        $service = new PayUmoneyService();

        // Prepare verification data
        $verificationData = [
            'status' => 'success',
            'txnid' => $this->transaction->transaction_id,
            'amount' => '100.00',
            'productinfo' => 'Payment',
            'firstname' => 'Customer',
            'email' => '<EMAIL>',
            'payuMoneyId' => 'payu_123456',
            'mode' => 'CC',
            'bank_ref_num' => 'bank_ref_123',
            'bankcode' => 'CC',
            'key' => 'test_merchant_key',
            'udf1' => '',
            'udf2' => '',
            'udf3' => '',
            'udf4' => '',
            'udf5' => '',
        ];

        // Generate valid hash for success - using the exact format from PayUmoneyService->verifyHash
        $hashString = $this->gatewaySetting->settings['salt'] . '|success|||||' . 
                     ($verificationData['udf5'] ?? '') . '|' . 
                     ($verificationData['udf4'] ?? '') . '|' . 
                     ($verificationData['udf3'] ?? '') . '|' . 
                     ($verificationData['udf2'] ?? '') . '|' . 
                     ($verificationData['udf1'] ?? '') . '|' . 
                     $verificationData['email'] . '|' . 
                     $verificationData['firstname'] . '|' . 
                     $verificationData['productinfo'] . '|' . 
                     $verificationData['amount'] . '|' . 
                     $verificationData['txnid'] . '|' . 
                     $verificationData['key'];
        $verificationData['hash'] = strtolower(hash('sha512', $hashString));

        $result = $service->verifyPayment($this->transaction->transaction_id, $verificationData);

        $this->assertTrue($result);

        // Check transaction was updated
        $this->transaction->refresh();
        $this->assertEquals(Transaction::STATUS_COMPLETED, $this->transaction->status);
        $this->assertEquals('payu_123456', $this->transaction->gateway_transaction_id);
        $this->assertArrayHasKey('payumoney_txnid', $this->transaction->payment_details);
        $this->assertTrue($this->transaction->payment_details['hash_verified']);
    }

    public function test_verify_payment_failure()
    {
        $service = new PayUmoneyService();

        // Prepare verification data for failure
        $verificationData = [
            'status' => 'failure',
            'txnid' => $this->transaction->transaction_id,
            'amount' => '100.00',
            'productinfo' => 'Payment',
            'firstname' => 'Customer',
            'email' => '<EMAIL>',
            'error' => 'Payment failed due to insufficient funds',
            'key' => 'test_merchant_key',
            'udf1' => '',
            'udf2' => '',
            'udf3' => '',
            'udf4' => '',
            'udf5' => '',
        ];

        // Generate valid hash for failure - using the exact format from PayUmoneyService->verifyHash
        $hashString = $this->gatewaySetting->settings['salt'] . '|failure|||||||||<EMAIL>|Customer|Payment|100.00|' . 
                     $this->transaction->transaction_id . '|test_merchant_key';
        $verificationData['hash'] = strtolower(hash('sha512', $hashString));
        
        // Set the transaction to pending to ensure the test verifies it changes to failed
        $this->transaction->update(['status' => Transaction::STATUS_PENDING]);

        // Verify the transaction is pending before verification
        $this->assertEquals(Transaction::STATUS_PENDING, $this->transaction->status);

        $result = $service->verifyPayment($this->transaction->transaction_id, $verificationData);

        $this->assertFalse($result);

        // Manually fetch the transaction from the database to ensure we have the latest data
        $updatedTransaction = Transaction::where('transaction_id', $this->transaction->transaction_id)->first();
        
        // Debug output
        echo "\nTransaction status after verification: {$updatedTransaction->status}\n";
        echo "Expected status: " . Transaction::STATUS_FAILED . "\n";
        
        // The transaction status should be updated to failed even when the result is false
        // This is because the hash verification passed but the status was 'failure'
        $this->assertEquals(Transaction::STATUS_FAILED, $updatedTransaction->status);
        $this->assertEquals('Payment failed due to insufficient funds', $updatedTransaction->failure_reason);
    }

    public function test_verify_payment_fails_with_missing_fields()
    {
        $service = new PayUmoneyService();

        $result = $service->verifyPayment($this->transaction->transaction_id, [
            'status' => 'success',
            'txnid' => $this->transaction->transaction_id,
            // Missing amount and hash
        ]);

        $this->assertFalse($result);
    }

    public function test_verify_payment_fails_with_invalid_transaction()
    {
        $service = new PayUmoneyService();

        $result = $service->verifyPayment('invalid_transaction_id', [
            'status' => 'success',
            'txnid' => 'invalid_transaction_id',
            'amount' => '100.00',
            'hash' => 'test_hash'
        ]);

        $this->assertFalse($result);
    }

    public function test_verify_payment_fails_with_invalid_hash()
    {
        $service = new PayUmoneyService();

        $verificationData = [
            'status' => 'success',
            'txnid' => $this->transaction->transaction_id,
            'amount' => '100.00',
            'productinfo' => 'Payment',
            'firstname' => 'Customer',
            'email' => '<EMAIL>',
            'key' => 'test_merchant_key',
            'hash' => 'invalid_hash',
            'udf1' => '',
            'udf2' => '',
            'udf3' => '',
            'udf4' => '',
            'udf5' => '',
        ];

        $result = $service->verifyPayment($this->transaction->transaction_id, $verificationData);

        $this->assertFalse($result);
    }

    public function test_verify_payment_fails_with_transaction_id_mismatch()
    {
        $service = new PayUmoneyService();

        $verificationData = [
            'status' => 'success',
            'txnid' => 'different_transaction_id',
            'amount' => '100.00',
            'hash' => 'test_hash'
        ];

        $result = $service->verifyPayment($this->transaction->transaction_id, $verificationData);

        $this->assertFalse($result);
    }

    public function test_verify_payment_fails_with_amount_mismatch()
    {
        $service = new PayUmoneyService();

        $verificationData = [
            'status' => 'success',
            'txnid' => $this->transaction->transaction_id,
            'amount' => '200.00', // Different amount
            'hash' => 'test_hash'
        ];

        $result = $service->verifyPayment($this->transaction->transaction_id, $verificationData);

        $this->assertFalse($result);
    }

    public function test_handle_webhook_success()
    {
        $service = new PayUmoneyService();

        // Use reflection to access the config property
        $reflection = new \ReflectionClass($service);
        $configProperty = $reflection->getProperty('config');
        $configProperty->setAccessible(true);
        $config = $configProperty->getValue($service);

        // Prepare webhook payload
        $webhookPayload = [
            'status' => 'success',
            'txnid' => $this->transaction->transaction_id,
            'amount' => '100.00',
            'productinfo' => 'Payment',
            'firstname' => 'Customer',
            'email' => '<EMAIL>',
            'payuMoneyId' => 'payu_123456',
            'mode' => 'CC',
            'bank_ref_num' => 'bank_ref_123',
            'key' => 'test_merchant_key',
            'udf1' => '',
            'udf2' => '',
            'udf3' => '',
            'udf4' => '',
            'udf5' => '',
        ];

        // Generate valid hash for success - using the exact format from PayUmoneyService->verifyHash
        $hashString = $config['salt'] . '|success|||||' . $webhookPayload['udf5'] . '|' . $webhookPayload['udf4'] . '|' . $webhookPayload['udf3'] . '|' . $webhookPayload['udf2'] . '|' . $webhookPayload['udf1'] . '|' . $webhookPayload['email'] . '|' . $webhookPayload['firstname'] . '|' . $webhookPayload['productinfo'] . '|' . $webhookPayload['amount'] . '|' . $webhookPayload['txnid'] . '|' . $webhookPayload['key'];
        $webhookPayload['hash'] = strtolower(hash('sha512', $hashString));

        $result = $service->handleWebhook($webhookPayload);

        $this->assertTrue($result['success']);
        $this->assertEquals('Callback processed successfully', $result['message']);
        $this->assertEquals(Transaction::STATUS_COMPLETED, $result['transaction_status']);

        // Check transaction was updated
        $this->transaction->refresh();
        $this->assertEquals(Transaction::STATUS_COMPLETED, $this->transaction->status);
        $this->assertTrue($this->transaction->webhook_verified);
        $this->assertEquals('payu_123456', $this->transaction->gateway_transaction_id);
    }

    public function test_handle_webhook_failure()
    {
        $service = new PayUmoneyService();

        // Use reflection to access the config property
        $reflection = new \ReflectionClass($service);
        $configProperty = $reflection->getProperty('config');
        $configProperty->setAccessible(true);
        $config = $configProperty->getValue($service);

        // Prepare webhook payload for failure
        $webhookPayload = [
            'status' => 'failure',
            'txnid' => $this->transaction->transaction_id,
            'amount' => '100.00',
            'productinfo' => 'Payment',
            'firstname' => 'Customer',
            'email' => '<EMAIL>',
            'error' => 'Payment failed',
            'key' => 'test_merchant_key',
            'udf1' => '',
            'udf2' => '',
            'udf3' => '',
            'udf4' => '',
            'udf5' => '',
        ];


        // Generate valid hash for failure - using the exact format from PayUmoneyService->verifyHash
        $hashString = $config['salt'] . '|failure|||||' . $webhookPayload['udf5'] . '|' . $webhookPayload['udf4'] . '|' . $webhookPayload['udf3'] . '|' . $webhookPayload['udf2'] . '|' . $webhookPayload['udf1'] . '|' . $webhookPayload['email'] . '|' . $webhookPayload['firstname'] . '|' . $webhookPayload['productinfo'] . '|' . $webhookPayload['amount'] . '|' . $webhookPayload['txnid'] . '|' . $webhookPayload['key'];
        $webhookPayload['hash'] = strtolower(hash('sha512', $hashString));

        $result = $service->handleWebhook($webhookPayload);

        $this->assertTrue($result['success']);
        $this->assertEquals('Callback processed successfully', $result['message']);
        $this->assertEquals(Transaction::STATUS_FAILED, $result['transaction_status']);

        // Check transaction was updated
        $this->transaction->refresh();
        $this->assertEquals(Transaction::STATUS_FAILED, $this->transaction->status);
        $this->assertTrue($this->transaction->webhook_verified);
        $this->assertEquals('Payment failed', $this->transaction->failure_reason);
    }

    public function test_handle_webhook_pending()
    {
        $service = new PayUmoneyService();

        // Use reflection to access the config property
        $reflection = new \ReflectionClass($service);
        $configProperty = $reflection->getProperty('config');
        $configProperty->setAccessible(true);
        $config = $configProperty->getValue($service);

        // Prepare webhook payload for pending
        $webhookPayload = [
            'status' => 'pending',
            'txnid' => $this->transaction->transaction_id,
            'amount' => '100.00',
            'productinfo' => 'Payment',
            'firstname' => 'Customer',
            'email' => '<EMAIL>',
            'key' => 'test_merchant_key',
            'udf1' => '',
            'udf2' => '',
            'udf3' => '',
            'udf4' => '',
            'udf5' => '',
        ];

        // Generate valid hash for pending - using the exact format from PayUmoneyService->verifyHash
        $hashString = $config['salt'] . '|pending|||||' . $webhookPayload['udf5'] . '|' . $webhookPayload['udf4'] . '|' . $webhookPayload['udf3'] . '|' . $webhookPayload['udf2'] . '|' . $webhookPayload['udf1'] . '|' . $webhookPayload['email'] . '|' . $webhookPayload['firstname'] . '|' . $webhookPayload['productinfo'] . '|' . $webhookPayload['amount'] . '|' . $webhookPayload['txnid'] . '|' . $webhookPayload['key'];
        $webhookPayload['hash'] = strtolower(hash('sha512', $hashString));

        $result = $service->handleWebhook($webhookPayload);

        $this->assertTrue($result['success']);
        $this->assertEquals('Callback processed successfully', $result['message']);
        $this->assertEquals(Transaction::STATUS_PROCESSING, $result['transaction_status']);

        // Check transaction was updated
        $this->transaction->refresh();
        $this->assertEquals(Transaction::STATUS_PROCESSING, $this->transaction->status);
        $this->assertTrue($this->transaction->webhook_verified);
    }

    public function test_handle_webhook_fails_with_invalid_hash()
    {
        $this->expectException(WebhookVerificationException::class);
        $this->expectExceptionMessage('PayUmoney callback hash verification failed');

        $service = new PayUmoneyService();

        // Prepare webhook payload with invalid hash
        $webhookPayload = [
            'status' => 'success',
            'txnid' => 'test_txn_123',
            'amount' => '100.00',
            'productinfo' => 'Payment',
            'firstname' => 'Customer',
            'email' => '<EMAIL>',
            'key' => 'test_merchant_key',
            'hash' => 'invalid_hash_value',
            'udf1' => '',
            'udf2' => '',
            'udf3' => '',
            'udf4' => '',
            'udf5' => '',
        ];

        $service->handleWebhook($webhookPayload);
    }

    public function test_handle_webhook_fails_with_missing_transaction_id()
    {
        $this->expectException(PaymentGatewayException::class);
        $this->expectExceptionMessage('Callback processing failed: Transaction ID missing in callback');

        $service = new PayUmoneyService();

        // Use reflection to access the config property
        $reflection = new \ReflectionClass($service);
        $configProperty = $reflection->getProperty('config');
        $configProperty->setAccessible(true);
        $config = $configProperty->getValue($service);

        // Prepare webhook payload without transaction ID but with valid hash structure
        $webhookPayload = [
            'status' => 'success',
            'amount' => '100.00',
            'productinfo' => 'Payment',
            'firstname' => 'Customer',
            'email' => '<EMAIL>',
            'key' => 'test_merchant_key',
            'udf1' => '',
            'udf2' => '',
            'udf3' => '',
            'udf4' => '',
            'udf5' => '',
            'txnid' => '', // Empty transaction ID
        ];
        
        // Generate valid hash with empty txnid - using the exact format from PayUmoneyService->verifyHash
        $hashString = $config['salt'] . '|success|||||' . $webhookPayload['udf5'] . '|' . $webhookPayload['udf4'] . '|' . $webhookPayload['udf3'] . '|' . $webhookPayload['udf2'] . '|' . $webhookPayload['udf1'] . '|' . $webhookPayload['email'] . '|' . $webhookPayload['firstname'] . '|' . $webhookPayload['productinfo'] . '|' . $webhookPayload['amount'] . '|' . $webhookPayload['txnid'] . '|' . $webhookPayload['key'];
        $webhookPayload['hash'] = strtolower(hash('sha512', $hashString));

        $service->handleWebhook($webhookPayload);
    }

    public function test_handle_webhook_returns_false_for_missing_transaction()
    {
        $service = new PayUmoneyService();

        // Use reflection to access the config property
        $reflection = new \ReflectionClass($service);
        $configProperty = $reflection->getProperty('config');
        $configProperty->setAccessible(true);
        $config = $configProperty->getValue($service);

        // Prepare webhook payload with non-existent transaction
        $webhookPayload = [
            'status' => 'success',
            'txnid' => 'non_existent_transaction',
            'amount' => '100.00',
            'productinfo' => 'Payment',
            'firstname' => 'Customer',
            'email' => '<EMAIL>',
            'key' => 'test_merchant_key',
            'udf1' => '',
            'udf2' => '',
            'udf3' => '',
            'udf4' => '',
            'udf5' => '',
        ];

        // Generate valid hash - using the exact format from PayUmoneyService->verifyHash
        $hashString = $config['salt'] . '|success|||||' . $webhookPayload['udf5'] . '|' . $webhookPayload['udf4'] . '|' . $webhookPayload['udf3'] . '|' . $webhookPayload['udf2'] . '|' . $webhookPayload['udf1'] . '|' . $webhookPayload['email'] . '|' . $webhookPayload['firstname'] . '|' . $webhookPayload['productinfo'] . '|' . $webhookPayload['amount'] . '|' . $webhookPayload['txnid'] . '|' . $webhookPayload['key'];
        $webhookPayload['hash'] = strtolower(hash('sha512', $hashString));

        $result = $service->handleWebhook($webhookPayload);

        $this->assertFalse($result['success']);
        $this->assertEquals('Transaction not found', $result['message']);
    }

    public function test_get_payment_status_returns_transaction_status()
    {
        $service = new PayUmoneyService();

        // Test with different transaction statuses
        $statuses = [
            Transaction::STATUS_PENDING,
            Transaction::STATUS_PROCESSING,
            Transaction::STATUS_COMPLETED,
            Transaction::STATUS_FAILED,
            Transaction::STATUS_CANCELLED
        ];

        foreach ($statuses as $status) {
            $this->transaction->update(['status' => $status]);
            
            $result = $service->getPaymentStatus($this->transaction->transaction_id);
            
            $this->assertEquals($status, $result);
        }
    }

    public function test_get_payment_status_returns_pending_for_missing_transaction()
    {
        $service = new PayUmoneyService();

        $status = $service->getPaymentStatus('invalid_transaction_id');

        $this->assertEquals(Transaction::STATUS_PENDING, $status);
    }

    public function test_gateway_name_and_enabled_status()
    {
        $service = new PayUmoneyService();

        $this->assertEquals(GatewaySetting::GATEWAY_PAYUMONEY, $service->getGatewayName());
        $this->assertTrue($service->isEnabled());

        // Disable gateway
        $this->gatewaySetting->update(['is_enabled' => false]);
        
        $service = new PayUmoneyService();
        $this->assertFalse($service->isEnabled());
    }

    public function test_hash_generation()
    {
        $service = new PayUmoneyService();
        
        // Use reflection to test private method
        $reflection = new \ReflectionClass($service);
        $generateHashMethod = $reflection->getMethod('generateHash');
        $generateHashMethod->setAccessible(true);

        $paymentData = [
            'key' => 'test_merchant_key',
            'txnid' => 'test_txn_123',
            'amount' => '100.00',
            'productinfo' => 'Test Product',
            'firstname' => 'John',
            'email' => '<EMAIL>',
            'udf1' => 'udf1_value',
            'udf2' => 'udf2_value',
            'udf3' => '',
            'udf4' => '',
            'udf5' => '',
        ];

        $hash = $generateHashMethod->invoke($service, $paymentData);

        // Expected hash string
        $expectedHashString = 'test_merchant_key|test_txn_123|100.00|Test Product|John|<EMAIL>|udf1_value|udf2_value||||||||test_salt_key';
        $expectedHash = strtolower(hash('sha512', $expectedHashString));

        $this->assertEquals($expectedHash, $hash);
    }

    public function test_hash_verification_success()
    {
        $service = new PayUmoneyService();
        
        // Use reflection to test private method
        $reflection = new \ReflectionClass($service);
        $verifyHashMethod = $reflection->getMethod('verifyHash');
        $verifyHashMethod->setAccessible(true);

        $responseData = [
            'status' => 'success',
            'key' => 'test_merchant_key',
            'txnid' => 'test_txn_123',
            'amount' => '100.00',
            'productinfo' => 'Test Product',
            'firstname' => 'John',
            'email' => '<EMAIL>',
            'udf1' => '',
            'udf2' => '',
            'udf3' => '',
            'udf4' => '',
            'udf5' => '',
        ];

        // Access the config property directly
        $configProperty = $reflection->getProperty('config');
        $configProperty->setAccessible(true);
        $config = $configProperty->getValue($service);
        
        // Generate correct hash - using the exact format from PayUmoneyService->verifyHash
        $hashString = $config['salt'] . '|success|||||' . $responseData['udf5'] . '|' . $responseData['udf4'] . '|' . $responseData['udf3'] . '|' . $responseData['udf2'] . '|' . $responseData['udf1'] . '|' . $responseData['email'] . '|' . $responseData['firstname'] . '|' . $responseData['productinfo'] . '|' . $responseData['amount'] . '|' . $responseData['txnid'] . '|' . $responseData['key'];
        $responseData['hash'] = strtolower(hash('sha512', $hashString));

        $result = $verifyHashMethod->invoke($service, $responseData);

        $this->assertTrue($result);
    }

    public function test_hash_verification_failure()
    {
        $service = new PayUmoneyService();
        
        // Use reflection to test private method
        $reflection = new \ReflectionClass($service);
        $verifyHashMethod = $reflection->getMethod('verifyHash');
        $verifyHashMethod->setAccessible(true);

        $responseData = [
            'status' => 'success',
            'key' => 'test_merchant_key',
            'txnid' => 'test_txn_123',
            'amount' => '100.00',
            'productinfo' => 'Test Product',
            'firstname' => 'John',
            'email' => '<EMAIL>',
            'udf1' => '',
            'udf2' => '',
            'udf3' => '',
            'udf4' => '',
            'udf5' => '',
            'hash' => 'incorrect_hash_value',
        ];

        $result = $verifyHashMethod->invoke($service, $responseData);

        $this->assertFalse($result);
    }

    public function test_hash_verification_with_missing_hash()
    {
        $service = new PayUmoneyService();
        
        // Use reflection to test private method
        $reflection = new \ReflectionClass($service);
        $verifyHashMethod = $reflection->getMethod('verifyHash');
        $verifyHashMethod->setAccessible(true);

        $responseData = [
            'status' => 'success',
            'key' => 'test_merchant_key',
            'txnid' => 'test_txn_123',
            'amount' => '100.00',
            // hash is missing
        ];

        $result = $verifyHashMethod->invoke($service, $responseData);

        $this->assertFalse($result);
    }

    public function test_payment_data_validation()
    {
        $service = new PayUmoneyService();
        
        // Use reflection to test private method
        $reflection = new \ReflectionClass($service);
        $validateMethod = $reflection->getMethod('validatePaymentData');
        $validateMethod->setAccessible(true);

        // Test missing transaction_id
        $this->expectException(InvalidPaymentDataException::class);
        $this->expectExceptionMessage('Missing required field: transaction_id');

        $validateMethod->invoke($service, [
            'amount' => 100.00,
        ]);
    }

    public function test_payment_data_validation_invalid_amount()
    {
        $service = new PayUmoneyService();
        
        // Use reflection to test private method
        $reflection = new \ReflectionClass($service);
        $validateMethod = $reflection->getMethod('validatePaymentData');
        $validateMethod->setAccessible(true);

        $this->expectException(InvalidPaymentDataException::class);
        $this->expectExceptionMessage('Amount must be a positive number');

        $validateMethod->invoke($service, [
            'amount' => 'invalid_amount',
            'transaction_id' => 'test_txn_123',
        ]);
    }

    public function test_payment_data_validation_zero_amount()
    {
        $service = new PayUmoneyService();
        
        // Use reflection to test private method
        $reflection = new \ReflectionClass($service);
        $validateMethod = $reflection->getMethod('validatePaymentData');
        $validateMethod->setAccessible(true);

        $this->expectException(InvalidPaymentDataException::class);
        $this->expectExceptionMessage('Missing required field: amount');

        $validateMethod->invoke($service, [
            'amount' => 0.0,
            'transaction_id' => 'test_txn_123',
        ]);
    }

    public function test_payment_data_validation_success()
    {
        $service = new PayUmoneyService();
        
        // Use reflection to test private method
        $reflection = new \ReflectionClass($service);
        $validateMethod = $reflection->getMethod('validatePaymentData');
        $validateMethod->setAccessible(true);

        // Should not throw any exception
        $validateMethod->invoke($service, [
            'amount' => 100.00,
            'transaction_id' => 'test_txn_123',
        ]);

        $this->assertTrue(true); // If we reach here, validation passed
    }
}