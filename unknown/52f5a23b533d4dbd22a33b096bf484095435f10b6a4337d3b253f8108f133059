<?php

namespace Database\Factories;

use App\Models\Order;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\EmailLog>
 */
class EmailLogFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $types = ['order_confirmation', 'build_shared', 'order_status_update'];
        $statuses = ['pending', 'sent', 'failed'];
        
        return [
            'type' => $this->faker->randomElement($types),
            'recipient' => $this->faker->safeEmail(),
            'subject' => $this->faker->sentence(),
            'status' => $this->faker->randomElement($statuses),
            'related_id' => $this->faker->numberBetween(1, 100),
            'related_type' => Order::class,
            'attempts' => $this->faker->numberBetween(1, 5),
            'error_message' => $this->faker->optional()->sentence(),
            'sent_at' => $this->faker->optional()->dateTimeBetween('-1 month', 'now'),
            'delivered_at' => $this->faker->optional()->dateTimeBetween('-1 month', 'now'),
            'failed_at' => $this->faker->optional()->dateTimeBetween('-1 month', 'now'),
        ];
    }

    /**
     * Indicate that the email was sent successfully.
     */
    public function sent(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'sent',
            'delivered_at' => $this->faker->dateTimeBetween('-1 week', 'now'),
            'error_message' => null,
            'failed_at' => null,
        ]);
    }

    /**
     * Indicate that the email failed to send.
     */
    public function failed(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'failed',
            'error_message' => $this->faker->sentence(),
            'failed_at' => $this->faker->dateTimeBetween('-1 week', 'now'),
            'delivered_at' => null,
        ]);
    }

    /**
     * Indicate that the email is pending.
     */
    public function pending(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'pending',
            'error_message' => null,
            'delivered_at' => null,
            'failed_at' => null,
        ]);
    }
}
