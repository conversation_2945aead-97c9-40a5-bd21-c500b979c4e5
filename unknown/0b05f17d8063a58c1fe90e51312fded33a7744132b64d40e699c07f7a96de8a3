<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Services\BuilderService;
use App\Models\Component;
use App\Models\ComponentCategory;
use App\Models\Build;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;

class BuilderServiceCompatibilityTest extends TestCase
{
    use RefreshDatabase;
    
    protected BuilderService $builderService;
    
    protected function setUp(): void
    {
        parent::setUp();
        $this->builderService = new BuilderService();
    }
    
    public function test_can_validate_build()
    {
        // Create user and build
        $user = User::factory()->create();
        $build = Build::factory()->create(['user_id' => $user->id]);
        
        // Create component categories
        $cpuCategory = ComponentCategory::factory()->create([
            'name' => 'CPU',
            'slug' => 'cpu',
            'is_required' => true,
        ]);
        
        // Create component
        $cpu = Component::factory()->create([
            'category_id' => $cpuCategory->id,
            'specs' => ['power_consumption' => '100W'],
        ]);
        
        // Add component to build
        $build->components()->create([
            'component_id' => $cpu->id,
            'category_id' => $cpu->category_id,
            'quantity' => 1,
            'price' => $cpu->price,
        ]);
        
        $result = $this->builderService->validateBuild($build);
        
        $this->assertInstanceOf(\App\Services\ValidationResult::class, $result);
        $this->assertEquals(100, $result->getPowerConsumption());
    }
    
    public function test_can_add_component_to_build_with_compatibility_check()
    {
        // Create user and build
        $user = User::factory()->create();
        $build = Build::factory()->create(['user_id' => $user->id]);
        
        // Create component category
        $cpuCategory = ComponentCategory::factory()->create([
            'name' => 'CPU',
            'slug' => 'cpu',
        ]);
        
        // Create component with stock
        $cpu = Component::factory()->create([
            'category_id' => $cpuCategory->id,
            'stock' => 10,
        ]);
        
        $result = $this->builderService->addComponentToBuild($build, $cpu, 1, null, true);
        
        $this->assertTrue($result['success']);
        $this->assertEquals('Component added successfully', $result['message']);
        $this->assertNotNull($result['buildComponent']);
        $this->assertIsArray($result['warnings']);
    }
    
    public function test_prevents_adding_component_with_insufficient_stock()
    {
        // Create user and build
        $user = User::factory()->create();
        $build = Build::factory()->create(['user_id' => $user->id]);
        
        // Create component category
        $cpuCategory = ComponentCategory::factory()->create([
            'name' => 'CPU',
            'slug' => 'cpu',
        ]);
        
        // Create component with no stock
        $cpu = Component::factory()->create([
            'category_id' => $cpuCategory->id,
            'stock' => 0,
        ]);
        
        $result = $this->builderService->addComponentToBuild($build, $cpu, 1, null, true);
        
        $this->assertFalse($result['success']);
        $this->assertStringContainsString('Insufficient stock', $result['message']);
    }
    
    public function test_prevents_adding_duplicate_category_component()
    {
        // Create user and build
        $user = User::factory()->create();
        $build = Build::factory()->create(['user_id' => $user->id]);
        
        // Create component category
        $cpuCategory = ComponentCategory::factory()->create([
            'name' => 'CPU',
            'slug' => 'cpu',
        ]);
        
        // Create two CPUs
        $cpu1 = Component::factory()->create([
            'category_id' => $cpuCategory->id,
            'stock' => 10,
        ]);
        
        $cpu2 = Component::factory()->create([
            'category_id' => $cpuCategory->id,
            'stock' => 10,
        ]);
        
        // Add first CPU
        $result1 = $this->builderService->addComponentToBuild($build, $cpu1, 1, null, true);
        $this->assertTrue($result1['success']);
        
        // Try to add second CPU (should fail)
        $result2 = $this->builderService->addComponentToBuild($build, $cpu2, 1, null, true);
        $this->assertFalse($result2['success']);
        $this->assertStringContainsString('already contains a CPU', $result2['message']);
    }
    
    public function test_can_remove_component_from_build()
    {
        // Create user and build
        $user = User::factory()->create();
        $build = Build::factory()->create(['user_id' => $user->id]);
        
        // Create component category
        $cpuCategory = ComponentCategory::factory()->create([
            'name' => 'CPU',
            'slug' => 'cpu',
        ]);
        
        // Create component
        $cpu = Component::factory()->create([
            'category_id' => $cpuCategory->id,
            'stock' => 10,
        ]);
        
        // Add component to build
        $build->addComponent($cpu, 1);
        
        $result = $this->builderService->removeComponentFromBuild($build, $cpu->id);
        
        $this->assertTrue($result['success']);
        $this->assertEquals('Component removed successfully', $result['message']);
        
        // Verify component was removed
        $this->assertEquals(0, $build->components()->count());
    }
    
    public function test_can_get_compatible_components_for_build()
    {
        // Create user and build
        $user = User::factory()->create();
        $build = Build::factory()->create(['user_id' => $user->id]);
        
        // Create component categories
        $cpuCategory = ComponentCategory::factory()->create([
            'name' => 'CPU',
            'slug' => 'cpu',
        ]);
        
        $motherboardCategory = ComponentCategory::factory()->create([
            'name' => 'Motherboard',
            'slug' => 'motherboard',
        ]);
        
        // Create CPU with specific socket
        $cpu = Component::factory()->create([
            'category_id' => $cpuCategory->id,
            'specs' => ['socket' => 'LGA1700'],
            'stock' => 10,
        ]);
        
        // Create compatible and incompatible motherboards
        $compatibleMb = Component::factory()->create([
            'category_id' => $motherboardCategory->id,
            'specs' => ['socket' => 'LGA1700'],
            'is_active' => true,
            'stock' => 10,
        ]);
        
        $incompatibleMb = Component::factory()->create([
            'category_id' => $motherboardCategory->id,
            'specs' => ['socket' => 'AM4'],
            'is_active' => true,
            'stock' => 10,
        ]);
        
        // Add CPU to build
        $build->addComponent($cpu, 1);
        
        $compatibleComponents = $this->builderService->getCompatibleComponentsForBuild($build, 'motherboard');
        
        $this->assertCount(1, $compatibleComponents);
        $this->assertEquals($compatibleMb->id, $compatibleComponents->first()->id);
    }
    
    public function test_can_calculate_build_power()
    {
        // Create user and build
        $user = User::factory()->create();
        $build = Build::factory()->create(['user_id' => $user->id]);
        
        // Create component categories
        $cpuCategory = ComponentCategory::factory()->create([
            'name' => 'CPU',
            'slug' => 'cpu',
        ]);
        
        $psuCategory = ComponentCategory::factory()->create([
            'name' => 'Power Supply',
            'slug' => 'power-supply',
        ]);
        
        // Create components
        $cpu = Component::factory()->create([
            'category_id' => $cpuCategory->id,
            'specs' => ['power_consumption' => '100W'],
        ]);
        
        $psu = Component::factory()->create([
            'category_id' => $psuCategory->id,
            'specs' => ['wattage' => '500W'],
        ]);
        
        // Add components to build
        $build->addComponent($cpu, 1);
        $build->addComponent($psu, 1);
        
        $powerInfo = $this->builderService->calculateBuildPower($build);
        
        $this->assertEquals(100, $powerInfo['total_power']);
        $this->assertEquals(500, $powerInfo['psu_power']);
        $this->assertEquals(20.0, $powerInfo['efficiency']);
        $this->assertEquals('oversized', $powerInfo['recommendation']);
        $this->assertCount(1, $powerInfo['breakdown']);
    }
    
    public function test_can_get_build_completion_status()
    {
        // Create user and build
        $user = User::factory()->create();
        $build = Build::factory()->create(['user_id' => $user->id]);
        
        // Create required component categories
        $cpuCategory = ComponentCategory::factory()->create([
            'name' => 'CPU',
            'slug' => 'cpu',
            'is_required' => true,
        ]);
        
        $ramCategory = ComponentCategory::factory()->create([
            'name' => 'Memory (RAM)',
            'slug' => 'memory-ram',
            'is_required' => true,
        ]);
        
        // Create component
        $cpu = Component::factory()->create([
            'category_id' => $cpuCategory->id,
        ]);
        
        // Add only CPU to build (missing RAM)
        $build->addComponent($cpu, 1);
        
        $status = $this->builderService->getBuildCompletionStatus($build);
        
        $this->assertFalse($status['is_complete']);
        $this->assertEquals(50.0, $status['completion_percentage']);
        $this->assertEquals(2, $status['total_required']);
        $this->assertEquals(1, $status['completed_required']);
        $this->assertCount(1, $status['missing_categories']);
    }
    
    public function test_check_component_compatibility_with_build()
    {
        // Create user and build
        $user = User::factory()->create();
        $build = Build::factory()->create(['user_id' => $user->id]);
        
        // Create component categories
        $cpuCategory = ComponentCategory::factory()->create([
            'name' => 'CPU',
            'slug' => 'cpu',
        ]);
        
        $motherboardCategory = ComponentCategory::factory()->create([
            'name' => 'Motherboard',
            'slug' => 'motherboard',
        ]);
        
        // Create compatible components
        $cpu = Component::factory()->create([
            'category_id' => $cpuCategory->id,
            'specs' => ['socket' => 'LGA1700'],
        ]);
        
        $motherboard = Component::factory()->create([
            'category_id' => $motherboardCategory->id,
            'specs' => ['socket' => 'LGA1700'],
        ]);
        
        // Add CPU to build
        $build->addComponent($cpu, 1);
        
        $result = $this->builderService->checkComponentCompatibilityWithBuild($build, $motherboard);
        
        $this->assertTrue($result['compatible']);
        $this->assertEquals('', $result['message']);
        $this->assertIsArray($result['warnings']);
    }
}