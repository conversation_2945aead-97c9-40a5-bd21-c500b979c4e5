<?php

namespace Database\Factories;

use App\Models\BlogPostCategory;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

class BlogPostCategoryFactory extends Factory
{
    protected $model = BlogPostCategory::class;

    public function definition()
    {
        $name = $this->faker->unique()->words(2, true);
        return [
            'name' => $name,
            'slug' => Str::slug($name),
            'description' => $this->faker->sentence(),
            'display_order' => $this->faker->numberBetween(1, 10),
        ];
    }
} 