<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Cart;
use App\Models\CartItem;
use App\Models\User;
use App\Models\Component;

class CartSeeder extends Seeder
{
    public function run(): void
    {
        $users = User::take(5)->get();
        
        // Get components, fallback to all components if active scope doesn't work
        $components = Component::where('is_active', true)->get();
        if ($components->isEmpty()) {
            $components = Component::all();
        }
        
        // Skip if no components available
        if ($components->isEmpty()) {
            $this->command->warn('No components found. Skipping cart seeding.');
            return;
        }

        foreach ($users as $user) {
            // Create a cart for each user
            $cart = Cart::create([
                'user_id' => $user->id,
                'total' => 0,
            ]);

            // Add random components to each cart (ensure we don't request more than available)
            $maxItems = min(4, $components->count());
            $numItems = rand(1, $maxItems);
            $selectedComponents = $components->random($numItems);

            foreach ($selectedComponents as $component) {
                $quantity = rand(1, 2);
                $cart->addItem($component, $quantity);
            }
        }

        // Create some guest carts (session-based)
        for ($i = 0; $i < 3; $i++) {
            $cart = Cart::create([
                'session_id' => 'guest_session_' . uniqid(),
                'total' => 0,
            ]);

            $maxItems = min(3, $components->count());
            $numItems = rand(1, $maxItems);
            $selectedComponents = $components->random($numItems);

            foreach ($selectedComponents as $component) {
                $quantity = rand(1, 2);
                $cart->addItem($component, $quantity);
            }
        }
    }
}