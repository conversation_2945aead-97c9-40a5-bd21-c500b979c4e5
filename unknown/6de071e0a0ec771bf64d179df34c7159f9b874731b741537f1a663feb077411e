@props([
    'gateway' => '',
    'name' => '',
    'description' => '',
    'logo' => '',
    'enabled' => true,
    'selected' => false
])

<div class="payment-gateway-card relative {{ $enabled ? 'cursor-pointer' : 'cursor-not-allowed opacity-50' }}">
    <input 
        type="radio" 
        name="gateway" 
        value="{{ $gateway }}" 
        id="gateway-{{ $gateway }}"
        class="sr-only peer"
        {{ $enabled ? '' : 'disabled' }}
        {{ $selected ? 'checked' : '' }}
    >
    
    <label 
        for="gateway-{{ $gateway }}" 
        class="gateway-label block w-full p-4 border-2 rounded-lg transition-all duration-200 
               {{ $enabled ? 'hover:border-indigo-300 hover:shadow-md' : '' }}
               peer-checked:border-indigo-500 peer-checked:bg-indigo-50 peer-checked:shadow-md
               border-gray-200 bg-white"
    >
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-3">
                @if($logo)
                    <img src="{{ $logo }}" alt="{{ $name }} logo" class="w-8 h-8 object-contain">
                @else
                    <div class="w-8 h-8 bg-gradient-to-br from-indigo-400 to-indigo-600 rounded-md flex items-center justify-center">
                        <span class="text-white font-bold text-sm">{{ substr($name, 0, 1) }}</span>
                    </div>
                @endif
                
                <div>
                    <h3 class="font-semibold text-gray-900">{{ $name }}</h3>
                    @if($description)
                        <p class="text-sm text-gray-600">{{ $description }}</p>
                    @endif
                </div>
            </div>
            
            <div class="flex items-center space-x-2">
                @if(!$enabled)
                    <span class="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full">Disabled</span>
                @endif
                
                <div class="w-5 h-5 border-2 border-gray-300 rounded-full peer-checked:border-indigo-500 peer-checked:bg-indigo-500 flex items-center justify-center">
                    <div class="w-2 h-2 bg-white rounded-full opacity-0 peer-checked:opacity-100 transition-opacity"></div>
                </div>
            </div>
        </div>
        
        @if($enabled)
            <div class="mt-3 text-xs text-gray-500">
                Click to select this payment method
            </div>
        @endif
    </label>
</div>