<?php

namespace App\Livewire\Builder;

use App\Models\Build;
use App\Models\User;
use App\Services\BuilderService;
use Illuminate\Support\Str;
use Livewire\Component;

class SaveBuildForm extends Component
{
    public $buildId = null;
    public $name = '';
    public $description = '';
    public $isPublic = false;
    public $selectedComponents = [];
    public $totalPrice = 0;
    public $showForm = false;
    public $isEditing = false;
    
    protected $rules = [
        'name' => 'required|string|max:255',
        'description' => 'nullable|string|max:1000',
        'isPublic' => 'boolean',
    ];
    
    protected $messages = [
        'name.required' => 'Build name is required.',
        'name.max' => 'Build name cannot exceed 255 characters.',
        'description.max' => 'Description cannot exceed 1000 characters.',
    ];
    
    public function mount($buildId = null, $selectedComponents = [], $totalPrice = 0)
    {
        $this->buildId = $buildId;
        $this->selectedComponents = $selectedComponents;
        $this->totalPrice = $totalPrice;
        
        if ($this->buildId) {
            $this->loadBuild();
            $this->isEditing = true;
        }
    }
    
    public function loadBuild()
    {
        if (!$this->buildId) {
            return;
        }
        
        $build = Build::find($this->buildId);
        
        if ($build && (auth()->check() && $build->user_id === auth()->id())) {
            $this->name = $build->name;
            $this->description = $build->description ?? '';
            $this->isPublic = $build->is_public;
        }
    }
    
    public function toggleForm()
    {
        $this->showForm = !$this->showForm;
        
        if ($this->showForm && !$this->isEditing && empty($this->name)) {
            $this->name = 'My PC Build ' . now()->format('M j, Y');
        }
    }
    
    public function saveBuild()
    {
        $this->validate();
        
        if (!auth()->check()) {
            session()->flash('error', 'You must be logged in to save builds.');
            return;
        }
        
        $builderService = app(BuilderService::class);
        
        try {
            if ($this->isEditing && $this->buildId) {
                // Update existing build
                $build = Build::findOrFail($this->buildId);
                
                if ($build->user_id !== auth()->id()) {
                    session()->flash('error', 'You can only edit your own builds.');
                    return;
                }
                
                $build = $builderService->updateBuild($build, [
                    'name' => $this->name,
                    'description' => $this->description,
                    'is_public' => $this->isPublic,
                ]);
                
                session()->flash('message', 'Build updated successfully!');
            } else {
                // Create new build
                $components = collect($this->selectedComponents)->map(function ($component, $categorySlug) {
                    return [
                        'component_id' => $component['component_id'],
                        'quantity' => $component['quantity'] ?? 1,
                        'price' => $component['price'],
                    ];
                })->values()->toArray();
                
                $build = $builderService->createBuild([
                    'name' => $this->name,
                    'description' => $this->description,
                    'is_public' => $this->isPublic,
                    'components' => $components,
                ], auth()->user());
                
                $this->buildId = $build->id;
                $this->isEditing = true;
                
                session()->flash('message', 'Build saved successfully!');
            }
            
            $this->showForm = false;
            $this->dispatch('buildSaved', $build->id);
            
        } catch (\Exception $e) {
            session()->flash('error', 'Failed to save build: ' . $e->getMessage());
        }
    }
    
    public function generateShareUrl()
    {
        if (!$this->buildId) {
            return null;
        }
        
        $build = Build::find($this->buildId);
        
        if (!$build) {
            return null;
        }
        
        $builderService = app(BuilderService::class);
        
        try {
            if ($build->is_public) {
                return $builderService->generateShareableUrl($build);
            } else {
                return $builderService->generatePrivateShareUrl($build);
            }
        } catch (\Exception $e) {
            return null;
        }
    }
    
    public function copyShareUrl()
    {
        $shareUrl = $this->generateShareUrl();
        
        if ($shareUrl) {
            $this->dispatch('copyToClipboard', $shareUrl);
            session()->flash('message', 'Share URL copied to clipboard!');
        } else {
            session()->flash('error', 'Unable to generate share URL.');
        }
    }
    
    public function toggleVisibility()
    {
        if (!$this->buildId) {
            return;
        }
        
        $build = Build::find($this->buildId);
        
        if ($build && $build->user_id === auth()->id()) {
            $builderService = app(BuilderService::class);
            $build = $builderService->toggleBuildVisibility($build);
            
            $this->isPublic = $build->is_public;
            
            $visibility = $build->is_public ? 'public' : 'private';
            session()->flash('message', "Build is now {$visibility}.");
        }
    }
    
    public function deleteBuild()
    {
        if (!$this->buildId) {
            return;
        }
        
        $build = Build::find($this->buildId);
        
        if ($build && $build->user_id === auth()->id()) {
            $builderService = app(BuilderService::class);
            
            try {
                $builderService->deleteBuild($build, auth()->user());
                
                session()->flash('message', 'Build deleted successfully.');
                $this->dispatch('buildDeleted', $this->buildId);
                
                // Reset form
                $this->reset(['buildId', 'name', 'description', 'isPublic', 'showForm', 'isEditing']);
                
            } catch (\Exception $e) {
                session()->flash('error', 'Failed to delete build: ' . $e->getMessage());
            }
        }
    }
    
    public function render()
    {
        return view('livewire.builder.save-build-form');
    }
}