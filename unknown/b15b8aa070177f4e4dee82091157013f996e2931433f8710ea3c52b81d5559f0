<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== CHECKING DATABASE SCHEMA ===\n\n";

try {
    // Check ComponentCategory table structure
    echo "1. ComponentCategory table columns:\n";
    $columns = \Schema::getColumnListing('component_categories');
    foreach ($columns as $column) {
        echo "   - $column\n";
    }
    
    // Get a sample record to see the structure
    echo "\n2. Sample ComponentCategory record:\n";
    $sample = \App\Models\ComponentCategory::first();
    if ($sample) {
        echo "   ID: {$sample->id}\n";
        echo "   Name: {$sample->name}\n";
        echo "   Slug: {$sample->slug}\n";
        echo "   Attributes: " . implode(', ', array_keys($sample->getAttributes())) . "\n";
    }
    
} catch (Exception $e) {
    echo "ERROR: " . $e->getMessage() . "\n";
}

echo "\n=== SCHEMA CHECK COMPLETE ===\n";