<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Mail\TestConfigMail;
use App\Services\MailConfigService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Session;

class MailConfigController extends Controller
{
    protected $mailConfigService;

    public function __construct(MailConfigService $mailConfigService)
    {   
        $this->mailConfigService = $mailConfigService;
    }

    /**
     * Display mail configuration form
     */
    public function index()
    {   
        $mailConfig = $this->mailConfigService->getCurrentConfig();
        return view('admin.settings.mail-config', compact('mailConfig'));
    }

    /**
     * Update mail configuration
     */
    public function update(Request $request)
    {
        $validated = $request->validate([
            'mail_driver' => 'required|string',
            'mail_host' => 'required|string',
            'mail_port' => 'required|numeric',
            'mail_username' => 'nullable|string',
            'mail_password' => 'nullable|string',
            'mail_encryption' => 'nullable|string',
            'mail_from_address' => 'required|email',
            'mail_from_name' => 'required|string',
        ]);

        try {
            $this->mailConfigService->updateMailConfig($validated);
            return redirect()->route('admin.mail-config.index')
                ->with('success', 'Mail configuration updated successfully.');
        } catch (\Exception $e) {
            Log::error('Failed to update mail configuration', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return redirect()->back()
                ->with('error', 'Failed to update mail configuration: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Send a test email to verify configuration
     */
    public function sendTestEmail(Request $request)
    {
        $validated = $request->validate([
            'test_email' => 'required|email'
        ]);

        try {
            // Reload mail config to ensure we're using the latest settings
            $this->mailConfigService->loadMailConfig();
            
            // Check if mail configuration is valid
            if (!$this->mailConfigService->verifyMailConfig()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Mail configuration is incomplete. Please check your settings.'
                ]);
            }

            // Send test email
            Mail::to($validated['test_email'])->send(new TestConfigMail());
            
            return response()->json([
                'success' => true,
                'message' => 'Test email sent successfully. Please check your inbox.'
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to send test email', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to send test email: ' . $e->getMessage()
            ]);
        }
    }
}