<?php

namespace Tests\Unit\Services;

use Tests\TestCase;
use App\Models\User;
use App\Models\Cart;
use App\Models\CartItem;
use App\Models\Component;
use App\Models\ComponentCategory;
use App\Models\Coupon;
use App\Services\CartService;
use Illuminate\Foundation\Testing\RefreshDatabase;

class CartServiceCouponTest extends TestCase
{
    use RefreshDatabase;

    protected $user;
    protected $cartService;
    protected $component1;
    protected $component2;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = User::factory()->create();
        $this->cartService = app(CartService::class);

        // Create component categories
        $cpuCategory = ComponentCategory::factory()->create(['name' => 'CPU']);
        $gpuCategory = ComponentCategory::factory()->create(['name' => 'GPU']);

        // Create components
        $this->component1 = Component::factory()->create([
            'category_id' => $cpuCategory->id,
            'name' => 'Intel Core i7-12700K',
            'price' => 399.99,
            'stock' => 10,
            'is_active' => true
        ]);

        $this->component2 = Component::factory()->create([
            'category_id' => $gpuCategory->id,
            'name' => 'NVIDIA RTX 4070',
            'price' => 599.99,
            'stock' => 5,
            'is_active' => true
        ]);
    }

    /** @test */
    public function it_applies_percentage_coupon_correctly()
    {
        // Create a 10% coupon
        $coupon = Coupon::create([
            'code' => 'SAVE10',
            'name' => '10% Off',
            'type' => 'percentage',
            'value' => 10,
            'is_active' => true,
            'starts_at' => null,
            'expires_at' => null,
        ]);

        // Act as the user
        $this->actingAs($this->user);

        // Add items to cart
        $this->cartService->addItem($this->component1->id, 1);
        $this->cartService->addItem($this->component2->id, 1);

        // Get cart total
        $cart = $this->cartService->getCart($this->user);
        $expectedTotal = 399.99 + 599.99; // 999.98
        
        $this->assertEquals($expectedTotal, (float) $cart->total, 'Cart total is incorrect');

        // Apply coupon
        $result = $this->cartService->applyCoupon('SAVE10', $this->user);

        // Expected discount: 10% of 999.98 = 99.998
        $expectedDiscount = ($expectedTotal * 10) / 100;
        
        $this->assertEqualsWithDelta($expectedDiscount, $result['discount_amount'], 0.01, 'Discount amount is incorrect');
        $this->assertEquals('SAVE10', $result['coupon_code']);
    }

    /** @test */
    public function it_calculates_discount_correctly_with_decimal_values()
    {
        // Test the coupon discount calculation directly
        $coupon = Coupon::create([
            'code' => 'TEST10',
            'name' => '10% Off',
            'type' => 'percentage',
            'value' => 10,
            'is_active' => true,
        ]);

        $subtotal = 999.98;
        $discount = $coupon->calculateDiscount($subtotal);
        
        // 10% of 999.98 = 99.998
        $expectedDiscount = 99.998;
        
        $this->assertEqualsWithDelta($expectedDiscount, $discount, 0.001, 'Discount calculation is incorrect');
    }
}
