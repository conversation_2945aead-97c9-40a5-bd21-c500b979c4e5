<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\Product;
use App\Models\Transaction;

echo "=== Product Integration Test ===\n";

// Test 1: Check if products exist
$productCount = Product::count();
echo "Products in database: {$productCount}\n";

if ($productCount > 0) {
    // Test 2: Get first product and test accessors
    $product = Product::first();
    echo "\nFirst Product Details:\n";
    echo "Name: {$product->name}\n";
    echo "SKU: {$product->sku}\n";
    echo "Price: {$product->price}\n";
    echo "Effective Price: {$product->effective_price}\n";
    echo "Formatted Price: {$product->formatted_price}\n";
    echo "Stock Status: {$product->getStockStatus()}\n";
    echo "Is Available: " . ($product->isAvailable() ? 'Yes' : 'No') . "\n";
    
    if ($product->is_on_sale) {
        echo "On Sale: Yes ({$product->discount_percentage}% off)\n";
    } else {
        echo "On Sale: No\n";
    }

    // Test 3: Test stock update functionality
    echo "\n=== Testing Stock Update ===\n";
    $originalStock = $product->stock_quantity;
    echo "Original stock: {$originalStock}\n";
    
    // Update stock (simulate a sale)
    $product->updateStock(2, 'sale');
    $product->refresh();
    echo "After sale of 2 units: {$product->stock_quantity}\n";
    
    // Check stock movements
    $stockMovements = $product->stockMovements()->count();
    echo "Stock movements recorded: {$stockMovements}\n";
    
    // Test 4: Test relationships
    echo "\n=== Testing Relationships ===\n";
    $transactionCount = $product->transactions()->count();
    echo "Transactions for this product: {$transactionCount}\n";
    
    // Test 5: Test scopes
    echo "\n=== Testing Scopes ===\n";
    $activeProducts = Product::active()->count();
    $featuredProducts = Product::featured()->count();
    $inStockProducts = Product::inStock()->count();
    
    echo "Active products: {$activeProducts}\n";
    echo "Featured products: {$featuredProducts}\n";
    echo "In stock products: {$inStockProducts}\n";
    
    if ($product->category) {
        $categoryProducts = Product::byCategory($product->category)->count();
        echo "Products in '{$product->category}' category: {$categoryProducts}\n";
    }
    
} else {
    echo "No products found. Creating sample products...\n";
    Product::factory(5)->create();
    echo "Created 5 sample products.\n";
}

echo "\n=== Integration Test Complete ===\n";