<?php

namespace Tests\Feature\Admin;

use App\Models\GatewaySetting;
use App\Models\User;
use App\Services\GatewaySettingsService;
use App\Services\PaymentGatewayFactory;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class GatewayControllerTest extends TestCase
{
    use RefreshDatabase;

    private User $adminUser;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create an admin user
        $this->adminUser = User::factory()->create([
            'email' => '<EMAIL>',
            'role' => User::ROLE_ADMIN
        ]);
    }

    public function test_index_displays_gateway_settings()
    {
        // Create some gateway settings
        GatewaySetting::create([
            'gateway_name' => 'razorpay',
            'is_enabled' => true,
            'is_test_mode' => true,
            'settings' => ['key_id' => 'test_key', 'key_secret' => 'test_secret']
        ]);

        $response = $this->actingAs($this->adminUser)
                        ->get(route('admin.gateways.index'));

        $response->assertStatus(200);
        $response->assertViewIs('admin.gateways.index');
        $response->assertViewHas('gateways');
    }

    public function test_show_displays_specific_gateway()
    {
        GatewaySetting::create([
            'gateway_name' => 'razorpay',
            'is_enabled' => true,
            'is_test_mode' => true,
            'settings' => ['key_id' => 'test_key', 'key_secret' => 'test_secret']
        ]);

        $response = $this->actingAs($this->adminUser)
                        ->get(route('admin.gateways.show', 'razorpay'));

        $response->assertStatus(200);
        $response->assertViewIs('admin.gateways.show');
        $response->assertViewHas('gateway');
        $response->assertViewHas('setting');
    }

    public function test_toggle_enables_gateway()
    {
        GatewaySetting::create([
            'gateway_name' => 'razorpay',
            'is_enabled' => false,
            'is_test_mode' => true,
            'settings' => ['key_id' => 'test_key', 'key_secret' => 'test_secret']
        ]);

        $response = $this->actingAs($this->adminUser)
                        ->postJson(route('admin.gateways.toggle', 'razorpay'), [
                            'enabled' => true
                        ]);

        $response->assertStatus(200);
        $response->assertJson([
            'success' => true,
            'data' => [
                'gateway' => 'razorpay',
                'is_enabled' => true
            ]
        ]);

        $this->assertDatabaseHas('gateway_settings', [
            'gateway_name' => 'razorpay',
            'is_enabled' => true
        ]);
    }

    public function test_switch_mode_changes_test_mode()
    {
        GatewaySetting::create([
            'gateway_name' => 'razorpay',
            'is_enabled' => true,
            'is_test_mode' => true,
            'settings' => ['key_id' => 'test_key', 'key_secret' => 'test_secret']
        ]);

        $response = $this->actingAs($this->adminUser)
                        ->postJson(route('admin.gateways.switch-mode', 'razorpay'), [
                            'test_mode' => false
                        ]);

        $response->assertStatus(200);
        $response->assertJson([
            'success' => true,
            'data' => [
                'gateway' => 'razorpay',
                'is_test_mode' => false
            ]
        ]);

        $this->assertDatabaseHas('gateway_settings', [
            'gateway_name' => 'razorpay',
            'is_test_mode' => false
        ]);
    }

    public function test_update_validates_gateway_settings()
    {
        $response = $this->actingAs($this->adminUser)
                        ->put(route('admin.gateways.update', 'razorpay'), [
                            'is_enabled' => true,
                            'is_test_mode' => true,
                            'settings' => [
                                'key_id' => '', // Empty required field
                                'key_secret' => 'test_secret'
                            ]
                        ]);

        // Should redirect back with validation errors
        $response->assertStatus(302);
        $response->assertSessionHasErrors(['settings.key_id']);
    }

    public function test_test_gateway_configuration()
    {
        GatewaySetting::create([
            'gateway_name' => 'razorpay',
            'is_enabled' => true,
            'is_test_mode' => true,
            'settings' => ['key_id' => 'test_key', 'key_secret' => 'test_secret']
        ]);

        $response = $this->actingAs($this->adminUser)
                        ->postJson(route('admin.gateways.test', 'razorpay'));

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'success',
            'message',
            'gateway'
        ]);
    }

    public function test_enabled_returns_enabled_gateways()
    {
        GatewaySetting::create([
            'gateway_name' => 'razorpay',
            'is_enabled' => true,
            'is_test_mode' => true,
            'settings' => ['key_id' => 'test_key', 'key_secret' => 'test_secret']
        ]);

        GatewaySetting::create([
            'gateway_name' => 'payumoney',
            'is_enabled' => false,
            'is_test_mode' => true,
            'settings' => ['merchant_key' => 'test_key', 'salt' => 'test_salt']
        ]);

        $response = $this->actingAs($this->adminUser)
                        ->getJson(route('admin.gateways.enabled'));

        $response->assertStatus(200);
        $response->assertJson([
            'success' => true,
            'data' => ['razorpay']
        ]);
    }

    public function test_unsupported_gateway_returns_404()
    {
        $response = $this->actingAs($this->adminUser)
                        ->get(route('admin.gateways.show', 'unsupported'));

        $response->assertStatus(404);
    }
}