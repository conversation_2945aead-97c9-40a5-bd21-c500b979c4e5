<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Component>
 */
class ComponentFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $brands = ['Intel', 'AMD', 'NVIDIA', 'ASUS', 'MSI', 'Gigabyte', 'Corsair', 'G.Skill', 'Samsung', 'Western Digital'];
        $name = $this->faker->words(3, true);
        
        return [
            'name' => $name,
            'slug' => \Str::slug($name . '-' . $this->faker->randomNumber(4)),
            'description' => $this->faker->paragraph(),
            'category_id' => \App\Models\ComponentCategory::factory(),
            'brand' => $this->faker->randomElement($brands),
            'model' => $this->faker->bothify('??-####'),
            'price' => $this->faker->randomFloat(2, 50, 2000),
            'stock' => $this->faker->numberBetween(0, 100),
            'image' => $this->faker->imageUrl(400, 400, 'technics'),
            'specs' => [
                'power_consumption' => $this->faker->numberBetween(50, 300) . 'W',
                'dimensions' => $this->faker->numberBetween(100, 400) . 'mm x ' . $this->faker->numberBetween(100, 400) . 'mm',
                'weight' => $this->faker->randomFloat(2, 0.5, 5) . 'kg',
            ],
            'is_featured' => $this->faker->boolean(20),
            'is_active' => $this->faker->boolean(90),
        ];
    }
}
