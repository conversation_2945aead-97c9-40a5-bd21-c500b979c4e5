<?php

namespace Tests\Feature\Integration;

use App\Models\Component;
use App\Models\ComponentCategory;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class BasicWorkflowTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;
    protected User $admin;
    protected ComponentCategory $category;
    protected Component $component;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->user = User::factory()->create(['role' => 'user']);
        $this->admin = User::factory()->create(['role' => 'admin']);
        
        $this->category = ComponentCategory::factory()->create([
            'name' => 'CPU',
            'slug' => 'cpu'
        ]);
        
        $this->component = Component::factory()->create([
            'category_id' => $this->category->id,
            'name' => 'Test CPU',
            'brand' => 'Intel',
            'price' => 299.99,
            'stock' => 10,
            'is_active' => true
        ]);
    }

    /** @test */
    public function user_can_browse_shop()
    {
        $response = $this->get(route('shop.index'));
        $response->assertStatus(200);
        $response->assertSee('Shop');
    }

    /** @test */
    public function user_can_view_product_details()
    {
        $response = $this->get(route('shop.component', $this->component->id));
        $response->assertStatus(200);
        $response->assertSee($this->component->name);
        $response->assertSee('$299.99');
    }

    /** @test */
    public function user_can_access_builder()
    {
        $response = $this->get(route('builder.index'));
        $response->assertStatus(200);
    }

    /** @test */
    public function user_can_add_item_to_cart()
    {
        $this->actingAs($this->user);
        
        $response = $this->post(route('shop.cart.add'), [
            'component_id' => $this->component->id,
            'quantity' => 1
        ]);
        
        $response->assertStatus(302);
        $response->assertSessionHas('message', 'Item added to cart successfully!');
    }

    /** @test */
    public function user_can_view_cart()
    {
        $this->actingAs($this->user);
        
        // Add item to cart first
        $this->post(route('shop.cart.add'), [
            'component_id' => $this->component->id,
            'quantity' => 1
        ]);
        
        $response = $this->get(route('shop.cart'));
        $response->assertStatus(200);
    }

    /** @test */
    public function admin_can_access_dashboard()
    {
        $this->actingAs($this->admin);
        
        $response = $this->get(route('admin.dashboard'));
        $response->assertStatus(200);
    }

    /** @test */
    public function admin_can_access_components_management()
    {
        $this->actingAs($this->admin);
        
        $response = $this->get(route('admin.components.index'));
        $response->assertStatus(200);
        $response->assertSee('Manage Components');
    }

    /** @test */
    public function admin_can_access_orders_management()
    {
        $this->actingAs($this->admin);
        
        $response = $this->get(route('admin.orders.index'));
        $response->assertStatus(200);
    }

    /** @test */
    public function regular_user_cannot_access_admin_areas()
    {
        $this->actingAs($this->user);
        
        $response = $this->get(route('admin.dashboard'));
        $response->assertStatus(403);
        
        $response = $this->get(route('admin.components.index'));
        $response->assertStatus(403);
    }

    /** @test */
    public function guest_user_is_redirected_to_login_for_admin_areas()
    {
        $response = $this->get(route('admin.dashboard'));
        $response->assertRedirect(route('admin.login'));
        
        $response = $this->get(route('admin.components.index'));
        $response->assertRedirect(route('admin.login'));
    }

    /** @test */
    public function components_are_displayed_correctly()
    {
        $response = $this->get(route('shop.index'));
        $response->assertStatus(200);
        $response->assertSee($this->component->name);
        $response->assertSee($this->component->brand);
        $response->assertSee('$299.99');
    }

    /** @test */
    public function out_of_stock_components_are_handled()
    {
        $this->component->update(['stock' => 0]);
        
        $response = $this->get(route('shop.component', $this->component->id));
        $response->assertStatus(200);
        $response->assertSee('Out of Stock');
    }

    /** @test */
    public function inactive_components_are_not_displayed()
    {
        $this->component->update(['is_active' => false]);
        
        $response = $this->get(route('shop.index'));
        $response->assertStatus(200);
        $response->assertDontSee($this->component->name);
    }
}