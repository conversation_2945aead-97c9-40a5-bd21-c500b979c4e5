<?php

namespace Tests\Unit\Models;

use App\Models\Product;
use App\Models\ProductReview;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ProductReviewTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        $this->artisan('migrate');
    }

    /** @test */
    public function it_can_create_a_review()
    {
        $user = User::factory()->create();
        $product = Product::factory()->create();

        $review = ProductReview::factory()->create([
            'user_id' => $user->id,
            'product_id' => $product->id,
            'rating' => 5,
            'title' => 'Great product!',
            'comment' => 'I love this product.',
        ]);

        $this->assertDatabaseHas('product_reviews', [
            'user_id' => $user->id,
            'product_id' => $product->id,
            'rating' => 5,
            'title' => 'Great product!',
        ]);
    }

    /** @test */
    public function it_has_user_and_product_relationships()
    {
        $user = User::factory()->create();
        $product = Product::factory()->create();
        $review = ProductReview::factory()->create([
            'user_id' => $user->id,
            'product_id' => $product->id,
        ]);

        $this->assertEquals($user->id, $review->user->id);
        $this->assertEquals($product->id, $review->product->id);
    }

    /** @test */
    public function it_has_approved_scope()
    {
        ProductReview::factory()->create(['is_approved' => true]);
        ProductReview::factory()->create(['is_approved' => false]);

        $approvedReviews = ProductReview::approved()->get();

        $this->assertCount(1, $approvedReviews);
        $this->assertTrue($approvedReviews->first()->is_approved);
    }

    /** @test */
    public function it_has_pending_scope()
    {
        ProductReview::factory()->create(['is_approved' => true]);
        ProductReview::factory()->create(['is_approved' => false]);

        $pendingReviews = ProductReview::pending()->get();

        $this->assertCount(1, $pendingReviews);
        $this->assertFalse($pendingReviews->first()->is_approved);
    }

    /** @test */
    public function it_has_for_product_scope()
    {
        $product1 = Product::factory()->create();
        $product2 = Product::factory()->create();

        ProductReview::factory()->create(['product_id' => $product1->id]);
        ProductReview::factory()->create(['product_id' => $product2->id]);

        $product1Reviews = ProductReview::forProduct($product1->id)->get();

        $this->assertCount(1, $product1Reviews);
        $this->assertEquals($product1->id, $product1Reviews->first()->product_id);
    }

    /** @test */
    public function it_has_by_rating_scope()
    {
        ProductReview::factory()->create(['rating' => 5]);
        ProductReview::factory()->create(['rating' => 3]);
        ProductReview::factory()->create(['rating' => 5]);

        $fiveStarReviews = ProductReview::byRating(5)->get();

        $this->assertCount(2, $fiveStarReviews);
        $this->assertTrue($fiveStarReviews->every(fn($review) => $review->rating === 5));
    }

    /** @test */
    public function it_has_verified_purchase_scope()
    {
        ProductReview::factory()->create(['is_verified_purchase' => true]);
        ProductReview::factory()->create(['is_verified_purchase' => false]);

        $verifiedReviews = ProductReview::verifiedPurchase()->get();

        $this->assertCount(1, $verifiedReviews);
        $this->assertTrue($verifiedReviews->first()->is_verified_purchase);
    }

    /** @test */
    public function it_detects_content_needing_moderation()
    {
        $flaggedReview = ProductReview::factory()->create([
            'title' => 'This is spam',
            'comment' => 'This product is fake and terrible.',
        ]);

        $shortReview = ProductReview::factory()->create([
            'comment' => 'Bad',
        ]);

        $extremeRatingShortComment = ProductReview::factory()->create([
            'rating' => 1,
            'comment' => 'Bad product',
        ]);

        $capsReview = ProductReview::factory()->create([
            'comment' => 'THIS IS ALL CAPS AND LOOKS LIKE SPAM',
        ]);

        $goodReview = ProductReview::factory()->create([
            'title' => 'Great product',
            'comment' => 'I really enjoyed using this product. The quality is excellent and delivery was fast.',
            'rating' => 4,
        ]);

        $this->assertTrue($flaggedReview->needsModeration());
        $this->assertTrue($shortReview->needsModeration());
        $this->assertTrue($extremeRatingShortComment->needsModeration());
        $this->assertTrue($capsReview->needsModeration());
        $this->assertFalse($goodReview->needsModeration());
    }

    /** @test */
    public function it_auto_approves_good_reviews()
    {
        $goodReview = ProductReview::factory()->create([
            'title' => 'Great product',
            'comment' => 'I really enjoyed using this product. The quality is excellent.',
            'rating' => 4,
            'is_approved' => false,
        ]);

        $badReview = ProductReview::factory()->create([
            'title' => 'Spam review',
            'comment' => 'This is fake and terrible spam.',
            'is_approved' => false,
        ]);

        $goodReview->autoApprove();
        $badReview->autoApprove();

        $this->assertTrue($goodReview->fresh()->is_approved);
        $this->assertFalse($badReview->fresh()->is_approved);
    }

    /** @test */
    public function it_gets_average_rating_for_product()
    {
        $product = Product::factory()->create();

        ProductReview::factory()->create([
            'product_id' => $product->id,
            'rating' => 4,
            'is_approved' => true,
        ]);

        ProductReview::factory()->create([
            'product_id' => $product->id,
            'rating' => 5,
            'is_approved' => true,
        ]);

        ProductReview::factory()->create([
            'product_id' => $product->id,
            'rating' => 2,
            'is_approved' => false, // Should not count
        ]);

        $average = ProductReview::getAverageRating($product->id);

        $this->assertEquals(4.5, $average);
    }

    /** @test */
    public function it_gets_rating_distribution_for_product()
    {
        $product = Product::factory()->create();

        ProductReview::factory()->create([
            'product_id' => $product->id,
            'rating' => 5,
            'is_approved' => true,
        ]);

        ProductReview::factory()->create([
            'product_id' => $product->id,
            'rating' => 5,
            'is_approved' => true,
        ]);

        ProductReview::factory()->create([
            'product_id' => $product->id,
            'rating' => 4,
            'is_approved' => true,
        ]);

        $distribution = ProductReview::getRatingDistribution($product->id);

        $this->assertEquals([
            1 => 0,
            2 => 0,
            3 => 0,
            4 => 1,
            5 => 2,
        ], $distribution);
    }

    /** @test */
    public function it_gets_review_count_for_product()
    {
        $product = Product::factory()->create();

        ProductReview::factory()->count(3)->create([
            'product_id' => $product->id,
            'is_approved' => true,
        ]);

        ProductReview::factory()->create([
            'product_id' => $product->id,
            'is_approved' => false, // Should not count
        ]);

        $count = ProductReview::getReviewCount($product->id);

        $this->assertEquals(3, $count);
    }

    /** @test */
    public function it_gets_verified_review_count_for_product()
    {
        $product = Product::factory()->create();

        ProductReview::factory()->count(2)->create([
            'product_id' => $product->id,
            'is_approved' => true,
            'is_verified_purchase' => true,
        ]);

        ProductReview::factory()->create([
            'product_id' => $product->id,
            'is_approved' => true,
            'is_verified_purchase' => false,
        ]);

        $count = ProductReview::getVerifiedReviewCount($product->id);

        $this->assertEquals(2, $count);
    }

    /** @test */
    public function it_gets_product_review_stats()
    {
        $product = Product::factory()->create();

        ProductReview::factory()->create([
            'product_id' => $product->id,
            'rating' => 5,
            'is_approved' => true,
            'is_verified_purchase' => true,
        ]);

        ProductReview::factory()->create([
            'product_id' => $product->id,
            'rating' => 4,
            'is_approved' => true,
            'is_verified_purchase' => false,
        ]);

        $stats = ProductReview::getProductReviewStats($product->id);

        $this->assertArrayHasKey('average_rating', $stats);
        $this->assertArrayHasKey('total_reviews', $stats);
        $this->assertArrayHasKey('verified_reviews', $stats);
        $this->assertArrayHasKey('rating_distribution', $stats);
        $this->assertArrayHasKey('recent_reviews', $stats);

        $this->assertEquals(4.5, $stats['average_rating']);
        $this->assertEquals(2, $stats['total_reviews']);
        $this->assertEquals(1, $stats['verified_reviews']);
    }

    /** @test */
    public function it_gets_product_reviews_with_filters()
    {
        $product = Product::factory()->create();

        ProductReview::factory()->create([
            'product_id' => $product->id,
            'rating' => 5,
            'is_approved' => true,
            'is_verified_purchase' => true,
            'images' => ['image1.jpg'],
        ]);

        ProductReview::factory()->create([
            'product_id' => $product->id,
            'rating' => 4,
            'is_approved' => true,
            'is_verified_purchase' => false,
            'images' => null,
        ]);

        // Test rating filter
        $fiveStarReviews = ProductReview::getProductReviews($product->id, ['rating' => 5]);
        $this->assertCount(1, $fiveStarReviews);

        // Test verified only filter
        $verifiedReviews = ProductReview::getProductReviews($product->id, ['verified_only' => true]);
        $this->assertCount(1, $verifiedReviews);

        // Test with images filter
        $reviewsWithImages = ProductReview::getProductReviews($product->id, ['with_images' => true]);
        $this->assertCount(1, $reviewsWithImages);
    }

    /** @test */
    public function it_gets_review_summary()
    {
        $user = User::factory()->create(['name' => 'John Doe']);
        $product = Product::factory()->create();

        $review = ProductReview::factory()->create([
            'user_id' => $user->id,
            'product_id' => $product->id,
            'rating' => 5,
            'title' => 'Great product',
            'comment' => 'I love this product',
            'is_verified_purchase' => true,
            'images' => ['image1.jpg', 'image2.jpg'],
        ]);

        $summary = $review->getSummary();

        $this->assertArrayHasKey('id', $summary);
        $this->assertArrayHasKey('rating', $summary);
        $this->assertArrayHasKey('title', $summary);
        $this->assertArrayHasKey('comment', $summary);
        $this->assertArrayHasKey('user_name', $summary);
        $this->assertArrayHasKey('is_verified_purchase', $summary);
        $this->assertArrayHasKey('created_at', $summary);
        $this->assertArrayHasKey('images', $summary);

        $this->assertEquals('John Doe', $summary['user_name']);
        $this->assertTrue($summary['is_verified_purchase']);
        $this->assertCount(2, $summary['images']);
    }

    /** @test */
    public function it_validates_review_creation_rules()
    {
        $user = User::factory()->create();
        $product = Product::factory()->create();

        $rules = ProductReview::validationRules($user->id, $product->id);

        $this->assertArrayHasKey('rating', $rules);
        $this->assertArrayHasKey('title', $rules);
        $this->assertArrayHasKey('comment', $rules);
        $this->assertArrayHasKey('images', $rules);
        $this->assertArrayHasKey('user_id', $rules);
        $this->assertArrayHasKey('product_id', $rules);

        // Test that rating is required and between 1-5
        $this->assertContains('required', $rules['rating']);
        $this->assertContains('min:1', $rules['rating']);
        $this->assertContains('max:5', $rules['rating']);
    }
}