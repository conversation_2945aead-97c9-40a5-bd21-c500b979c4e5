<div class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <h2 class="text-2xl font-bold text-gray-900">Inventory Management</h2>
    </div>

    <!-- Tabs -->
    <div class="border-b border-gray-200">
        <nav class="-mb-px flex space-x-8">
            <button wire:click="setActiveTab('overview')" 
                    class="py-2 px-1 border-b-2 font-medium text-sm {{ $activeTab === 'overview' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300' }}">
                Overview
            </button>
            <button wire:click="setActiveTab('alerts')" 
                    class="py-2 px-1 border-b-2 font-medium text-sm {{ $activeTab === 'alerts' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300' }}">
                Alerts
            </button>
            <button wire:click="setActiveTab('low-stock')" 
                    class="py-2 px-1 border-b-2 font-medium text-sm {{ $activeTab === 'low-stock' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300' }}">
                Low Stock
            </button>
            <button wire:click="setActiveTab('out-of-stock')" 
                    class="py-2 px-1 border-b-2 font-medium text-sm {{ $activeTab === 'out-of-stock' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300' }}">
                Out of Stock
            </button>
        </nav>
    </div>

    <!-- Content -->
    @if($activeTab === 'overview')
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <!-- Stats Cards -->
            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                                <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                </svg>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">Total Components</dt>
                                <dd class="text-lg font-medium text-gray-900">{{ $report['total_components'] ?? 0 }}</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
                                <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92z"/>
                                </svg>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">Low Stock</dt>
                                <dd class="text-lg font-medium text-gray-900">{{ $report['low_stock'] ?? 0 }}</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-red-500 rounded-md flex items-center justify-center">
                                <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"/>
                                </svg>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">Out of Stock</dt>
                                <dd class="text-lg font-medium text-gray-900">{{ $report['out_of_stock'] ?? 0 }}</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                                <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z"/>
                                </svg>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">Total Value</dt>
                                <dd class="text-lg font-medium text-gray-900">${{ number_format($report['total_inventory_value'] ?? 0, 2) }}</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Analytics -->
        @if(isset($analytics))
        <div class="bg-white shadow rounded-lg p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Analytics (Last 30 Days)</h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                    <h4 class="text-sm font-medium text-gray-500 mb-2">Stock Movements</h4>
                    <p class="text-2xl font-bold text-gray-900">{{ $analytics['total_movements'] }}</p>
                </div>
                <div>
                    <h4 class="text-sm font-medium text-gray-500 mb-2">Total Alerts</h4>
                    <p class="text-2xl font-bold text-gray-900">{{ $analytics['total_alerts'] }}</p>
                </div>
                <div>
                    <h4 class="text-sm font-medium text-gray-500 mb-2">Alert Resolution Rate</h4>
                    <p class="text-2xl font-bold text-gray-900">{{ $analytics['alert_resolution_rate'] }}%</p>
                </div>
            </div>
        </div>
        @endif

    @elseif($activeTab === 'alerts')
        <div class="bg-white shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Active Inventory Alerts</h3>
                @if($alerts->count() > 0)
                    <div class="space-y-4">
                        @foreach($alerts as $alert)
                            <div class="border border-gray-200 rounded-lg p-4 {{ $alert->type === 'out_of_stock' ? 'border-red-300 bg-red-50' : 'border-yellow-300 bg-yellow-50' }}">
                                <div class="flex justify-between items-start">
                                    <div>
                                        <h4 class="font-medium text-gray-900">{{ $alert->component->name }}</h4>
                                        <p class="text-sm text-gray-600">{{ ucfirst(str_replace('_', ' ', $alert->type)) }}</p>
                                        <p class="text-sm text-gray-500">Current Stock: {{ $alert->current_stock }}</p>
                                        <p class="text-sm text-gray-500">Created: {{ $alert->created_at->format('M j, Y g:i A') }}</p>
                                    </div>
                                    <div class="flex space-x-2">
                                        <button wire:click="viewAlert({{ $alert->id }})" 
                                                class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                            View
                                        </button>
                                        <button wire:click="resolveAlert({{ $alert->id }})" 
                                                class="text-green-600 hover:text-green-800 text-sm font-medium">
                                            Resolve
                                        </button>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <p class="text-gray-500">No active alerts.</p>
                @endif
            </div>
        </div>

    @elseif($activeTab === 'low-stock' || $activeTab === 'out-of-stock')
        <div class="bg-white shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">
                    {{ $activeTab === 'low-stock' ? 'Low Stock Components' : 'Out of Stock Components' }}
                </h3>
                @if($components->count() > 0)
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Component</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Stock</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Price</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                @foreach($components as $component)
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900">{{ $component->name }}</div>
                                            <div class="text-sm text-gray-500">{{ $component->brand }} {{ $component->model }}</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            {{ $component->category->name ?? 'N/A' }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full {{ $component->stock === 0 ? 'bg-red-100 text-red-800' : 'bg-yellow-100 text-yellow-800' }}">
                                                {{ $component->stock }}
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            ${{ number_format($component->price, 2) }}
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                @else
                    <p class="text-gray-500">No {{ $activeTab === 'low-stock' ? 'low stock' : 'out of stock' }} components.</p>
                @endif
            </div>
        </div>
    @endif

    <!-- Alert Modal -->
    @if($showAlertModal && $selectedAlert)
        <div class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
                <div class="mt-3">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Alert Details</h3>
                    <div class="space-y-3">
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Component</label>
                            <p class="text-sm text-gray-900">{{ $selectedAlert->component->name }}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Alert Type</label>
                            <p class="text-sm text-gray-900">{{ ucfirst(str_replace('_', ' ', $selectedAlert->type)) }}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Current Stock</label>
                            <p class="text-sm text-gray-900">{{ $selectedAlert->current_stock }}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Threshold</label>
                            <p class="text-sm text-gray-900">{{ $selectedAlert->threshold }}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Created</label>
                            <p class="text-sm text-gray-900">{{ $selectedAlert->created_at->format('M j, Y g:i A') }}</p>
                        </div>
                    </div>
                    <div class="flex justify-end space-x-3 mt-6">
                        <button wire:click="closeModal" 
                                class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400">
                            Close
                        </button>
                        <button wire:click="resolveAlert({{ $selectedAlert->id }})" 
                                class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700">
                            Resolve Alert
                        </button>
                    </div>
                </div>
            </div>
        </div>
    @endif
</div>