<?php

namespace Tests\Feature;

use App\Livewire\User\Dashboard;
use App\Models\User;
use App\Models\Order;
use App\Models\Build;
use App\Models\Component;
use App\Models\ComponentCategory;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Livewire\Livewire;
use Tests\TestCase;

class UserDashboardTest extends TestCase
{
    use RefreshDatabase;

    private User $user;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->user = User::factory()->create();
    }

    public function test_dashboard_loads_with_correct_stats()
    {
        // Create test data
        Build::factory()->count(3)->create(['user_id' => $this->user->id, 'is_public' => false]);
        Build::factory()->create(['user_id' => $this->user->id, 'is_public' => true]);
        Order::factory()->count(2)->create(['user_id' => $this->user->id, 'status' => 'completed', 'total' => 100]);

        Livewire::actingAs($this->user)
            ->test(Dashboard::class)
            ->assertSee('4') // Total builds
            ->assertSee('1') // Public builds  
            ->assertSee('2') // Total orders
            ->assertSee('$200'); // Total spent
    }

    public function test_tab_switching_works()
    {
        Livewire::actingAs($this->user)
            ->test(Dashboard::class)
            ->assertSet('activeTab', 'overview')
            ->call('setActiveTab', 'builds')
            ->assertSet('activeTab', 'builds')
            ->call('setActiveTab', 'orders')
            ->assertSet('activeTab', 'orders')
            ->call('setActiveTab', 'settings')
            ->assertSet('activeTab', 'settings');
    }

    public function test_overview_shows_recent_builds()
    {
        $build = Build::factory()->create([
            'user_id' => $this->user->id,
            'name' => 'Gaming PC Build',
            'total_price' => 1500.00,
        ]);

        Livewire::actingAs($this->user)
            ->test(Dashboard::class)
            ->assertSee('Gaming PC Build')
            ->assertSee('$1,500.00');
    }

    public function test_overview_shows_recent_orders()
    {
        $order = Order::factory()->create([
            'user_id' => $this->user->id,
            'order_number' => 'PCB20240101001',
            'total' => 999.99,
            'status' => 'completed',
        ]);

        Livewire::actingAs($this->user)
            ->test(Dashboard::class)
            ->assertSee('PCB20240101001')
            ->assertSee('$999.99')
            ->assertSee('Completed');
    }

    public function test_build_management_functions()
    {
        $build = Build::factory()->create([
            'user_id' => $this->user->id,
            'name' => 'Test Build',
            'is_public' => false,
        ]);

        $component = Livewire::actingAs($this->user)
            ->test(Dashboard::class)
            ->call('toggleBuildVisibility', $build->id);

        $build->refresh();
        $this->assertTrue($build->is_public);
    }

    public function test_build_cloning_works()
    {
        $originalBuild = Build::factory()->create([
            'user_id' => $this->user->id,
            'name' => 'Original Build',
        ]);

        $this->markTestSkipped('Requires BuilderService mock');
        
        // This test would verify build cloning functionality
        // but requires proper mocking of BuilderService
    }

    public function test_build_deletion_works()
    {
        $build = Build::factory()->create([
            'user_id' => $this->user->id,
            'name' => 'Build to Delete',
        ]);

        $this->markTestSkipped('Requires BuilderService mock');
        
        // This test would verify build deletion functionality
        // but requires proper mocking of BuilderService
    }

    public function test_order_selection_works()
    {
        $order = Order::factory()->create([
            'user_id' => $this->user->id,
            'order_number' => 'PCB20240101001',
        ]);

        Livewire::actingAs($this->user)
            ->test(Dashboard::class)
            ->call('selectOrder', $order->id)
            ->assertSet('selectedOrder.id', $order->id);
    }

    public function test_search_functionality()
    {
        Build::factory()->create([
            'user_id' => $this->user->id,
            'name' => 'Gaming Build',
        ]);

        Build::factory()->create([
            'user_id' => $this->user->id,
            'name' => 'Office Build',
        ]);

        // The dashboard uses the SavedBuilds component for the builds tab
        // So we need to test the search functionality there instead
        $this->markTestSkipped('Search functionality is handled by SavedBuilds component');
    }

    public function test_unauthenticated_user_sees_login_prompt()
    {
        Livewire::test(Dashboard::class)
            ->assertSee('Please log in')
            ->assertSee('You need to be logged in to access your dashboard');
    }

    public function test_empty_states_display_correctly()
    {
        Livewire::actingAs($this->user)
            ->test(Dashboard::class)
            ->assertSee('No builds yet')
            ->assertSee('No orders yet')
            ->assertSee('Create your first build')
            ->assertSee('Start shopping');
    }

    public function test_user_cannot_access_other_users_data()
    {
        $otherUser = User::factory()->create();
        $otherBuild = Build::factory()->create([
            'user_id' => $otherUser->id,
            'name' => 'Other User Build',
        ]);

        Livewire::actingAs($this->user)
            ->test(Dashboard::class)
            ->call('setActiveTab', 'builds')
            ->assertDontSee('Other User Build');
    }

    public function test_build_share_url_generation()
    {
        $build = Build::factory()->create([
            'user_id' => $this->user->id,
            'name' => 'Shareable Build',
            'is_public' => true,
        ]);

        $this->markTestSkipped('Requires BuilderService mock');
        
        // This test would verify share URL generation
        // but requires proper mocking of BuilderService
    }

    public function test_reorder_functionality()
    {
        $order = Order::factory()->create([
            'user_id' => $this->user->id,
        ]);

        $this->markTestSkipped('Requires CartService mock');
        
        // This test would verify reorder functionality
        // but requires proper mocking of CartService
    }
}