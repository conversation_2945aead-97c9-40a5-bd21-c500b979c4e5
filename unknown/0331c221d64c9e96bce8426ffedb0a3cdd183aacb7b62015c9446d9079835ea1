<?php

namespace App\View\Components;

use Closure;
use Illuminate\Contracts\View\View;
use Illuminate\View\Component;

class Breadcrumb extends Component
{
    public $segments;
    public $pageTitle;

    /**
     * Create a new component instance.
     *
     * @param array $segments
     * @return void
     */
    public function __construct($segments, $pageTitle)
    {
        $this->segments = $this->formatSegments($segments);
        $this->pageTitle = $pageTitle;
    }

    /**
     * Format the segments to improve display names.
     *
     * @param array $segments
     * @return array
     */
    private function formatSegments($segments)
    {
        return array_map(function ($segment) {
            return [
                'url' => $segment['url'],
                'name' => $this->formatName($segment['name'])
            ];
        }, $segments);
    }

    /**
     * Format a single segment name.
     *
     * @param string $name
     * @return string
     */
    private function formatName($name)
    {
        return ucwords(str_replace('-', ' ', $name));
    }

    /**
     * Get the view / contents that represent the component.
     *
     * @return View|Closure|string
     */
    public function render(): View|Closure|string
    {
        return view('components.breadcrumb', [
            'segments' => $this->segments,
            'pageTitle' => $this->pageTitle,
        ]);
    }
}
