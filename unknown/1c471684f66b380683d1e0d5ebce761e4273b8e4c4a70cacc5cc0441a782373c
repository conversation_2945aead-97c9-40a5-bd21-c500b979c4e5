<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\BuildComponent>
 */
class BuildComponentFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $component = \App\Models\Component::factory()->create();
        
        return [
            'build_id' => \App\Models\Build::factory(),
            'component_id' => $component->id,
            'category_id' => $component->category_id,
            'quantity' => $this->faker->numberBetween(1, 4),
            'price' => $component->price,
        ];
    }
}
