<div class="max-w-7xl mx-auto">
    @auth
        <div class="space-y-6">
            <!-- Header -->
            <div class="flex justify-between items-center">
                <h1 class="text-2xl font-bold text-gray-900">Order History</h1>
                <a 
                    href="{{ route('shop.index') }}"
                    class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                >
                    Continue Shopping
                </a>
            </div>

            @if($selectedOrder)
                <!-- Order Detail View -->
                <div class="bg-white shadow-lg rounded-lg overflow-hidden">
                    <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                        <div>
                            <h2 class="text-xl font-semibold text-gray-900">Order #{{ $selectedOrder->order_number }}</h2>
                            <p class="text-sm text-gray-500">Placed on {{ $selectedOrder->created_at->format('F j, Y \a\t g:i A') }}</p>
                        </div>
                        <button 
                            wire:click="clearSelectedOrder"
                            class="text-gray-400 hover:text-gray-600"
                        >
                            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </button>
                    </div>

                    <div class="p-6">
                        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                            <!-- Order Items -->
                            <div class="lg:col-span-2">
                                <h3 class="text-lg font-medium text-gray-900 mb-4">Order Items</h3>
                                <div class="space-y-4">
                                    @foreach($selectedOrder->items as $item)
                                        <div class="flex items-center space-x-4 p-4 border border-gray-200 rounded-lg">
                                            @if($item->component && $item->component->image_url)
                                                <img 
                                                    src="{{ $item->component->image_url }}" 
                                                    alt="{{ $item->name }}"
                                                    class="w-16 h-16 object-cover rounded"
                                                >
                                            @else
                                                <div class="w-16 h-16 bg-gray-200 rounded flex items-center justify-center">
                                                    <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z" />
                                                    </svg>
                                                </div>
                                            @endif
                                            
                                            <div class="flex-1">
                                                <h4 class="font-medium text-gray-900">{{ $item->name }}</h4>
                                                @if($item->options)
                                                    <p class="text-sm text-gray-500">
                                                        {{ $item->options['brand'] ?? '' }} {{ $item->options['model'] ?? '' }}
                                                    </p>
                                                @endif
                                                <p class="text-sm text-gray-500">Quantity: {{ $item->quantity }}</p>
                                            </div>
                                            
                                            <div class="text-right">
                                                <p class="font-medium text-gray-900">${{ number_format($item->price, 2) }}</p>
                                                <p class="text-sm text-gray-500">each</p>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            </div>

                            <!-- Order Summary & Actions -->
                            <div class="space-y-6">
                                <!-- Order Status -->
                                <div class="bg-gray-50 p-4 rounded-lg">
                                    <h3 class="text-lg font-medium text-gray-900 mb-2">Order Status</h3>
                                    <div class="flex items-center space-x-2">
                                        @php
                                            $statusColors = [
                                                'pending' => 'bg-yellow-100 text-yellow-800',
                                                'processing' => 'bg-blue-100 text-blue-800',
                                                'completed' => 'bg-green-100 text-green-800',
                                                'cancelled' => 'bg-red-100 text-red-800',
                                                'refunded' => 'bg-gray-100 text-gray-800',
                                            ];
                                        @endphp
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $statusColors[$selectedOrder->status] ?? 'bg-gray-100 text-gray-800' }}">
                                            {{ ucfirst($selectedOrder->status) }}
                                        </span>
                                    </div>
                                    
                                    @if($selectedOrder->tracking_number)
                                        <div class="mt-3">
                                            <p class="text-sm text-gray-600">Tracking Number:</p>
                                            <p class="font-mono text-sm">{{ $selectedOrder->tracking_number }}</p>
                                            @if($selectedOrder->tracking_url)
                                                <button 
                                                    wire:click="trackOrder({{ $selectedOrder->id }})"
                                                    class="mt-1 text-blue-600 hover:text-blue-800 text-sm"
                                                >
                                                    Track Package →
                                                </button>
                                            @endif
                                        </div>
                                    @endif
                                </div>

                                <!-- Order Summary -->
                                <div class="bg-gray-50 p-4 rounded-lg">
                                    <h3 class="text-lg font-medium text-gray-900 mb-2">Order Summary</h3>
                                    <div class="space-y-2 text-sm">
                                        <div class="flex justify-between">
                                            <span>Subtotal:</span>
                                            <span>${{ number_format($selectedOrder->subtotal, 2) }}</span>
                                        </div>
                                        @if($selectedOrder->tax > 0)
                                            <div class="flex justify-between">
                                                <span>Tax:</span>
                                                <span>${{ number_format($selectedOrder->tax, 2) }}</span>
                                            </div>
                                        @endif
                                        @if($selectedOrder->shipping > 0)
                                            <div class="flex justify-between">
                                                <span>Shipping:</span>
                                                <span>${{ number_format($selectedOrder->shipping, 2) }}</span>
                                            </div>
                                        @endif
                                        @if($selectedOrder->discount > 0)
                                            <div class="flex justify-between text-green-600">
                                                <span>Discount:</span>
                                                <span>-${{ number_format($selectedOrder->discount, 2) }}</span>
                                            </div>
                                        @endif
                                        <div class="border-t pt-2 flex justify-between font-medium">
                                            <span>Total:</span>
                                            <span>${{ number_format($selectedOrder->total, 2) }}</span>
                                        </div>
                                    </div>
                                </div>

                                <!-- Shipping Address -->
                                @if($selectedOrder->shipping_address)
                                    <div class="bg-gray-50 p-4 rounded-lg">
                                        <h3 class="text-lg font-medium text-gray-900 mb-2">Shipping Address</h3>
                                        <div class="text-sm text-gray-600">
                                            <p>{{ $selectedOrder->shipping_name }}</p>
                                            <p>{{ $selectedOrder->shipping_address }}</p>
                                            <p>{{ $selectedOrder->shipping_city }}, {{ $selectedOrder->shipping_state }} {{ $selectedOrder->shipping_zipcode }}</p>
                                            <p>{{ $selectedOrder->shipping_country }}</p>
                                        </div>
                                    </div>
                                @endif

                                <!-- Actions -->
                                <div class="space-y-2">
                                    <button 
                                        wire:click="reorderItems({{ $selectedOrder->id }})"
                                        class="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                                    >
                                        Reorder Items
                                    </button>
                                    
                                    <button 
                                        wire:click="downloadInvoice({{ $selectedOrder->id }})"
                                        class="w-full px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                                    >
                                        Download Invoice
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            @else
                <!-- Orders List View -->
                <div class="bg-white shadow rounded-lg">
                    <!-- Filters -->
                    <div class="p-6 border-b border-gray-200">
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div>
                                <label for="search" class="block text-sm font-medium text-gray-700 mb-1">Search Orders</label>
                                <input 
                                    type="text" 
                                    id="search"
                                    wire:model.debounce.300ms="search"
                                    placeholder="Order number or item name..."
                                    class="w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                                >
                            </div>
                            
                            <div>
                                <label for="statusFilter" class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                                <select 
                                    id="statusFilter"
                                    wire:model="statusFilter"
                                    class="w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                                >
                                    <option value="">All Statuses</option>
                                    <option value="pending">Pending</option>
                                    <option value="processing">Processing</option>
                                    <option value="completed">Completed</option>
                                    <option value="cancelled">Cancelled</option>
                                    <option value="refunded">Refunded</option>
                                </select>
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Sort By</label>
                                <div class="flex space-x-2">
                                    <select 
                                        wire:model="sortBy"
                                        class="flex-1 border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                                    >
                                        <option value="created_at">Date</option>
                                        <option value="order_number">Order Number</option>
                                        <option value="total">Total</option>
                                        <option value="status">Status</option>
                                    </select>
                                    <button 
                                        wire:click="$set('sortDirection', '{{ $sortDirection === 'asc' ? 'desc' : 'asc' }}')"
                                        class="px-3 py-2 border border-gray-300 rounded-md hover:bg-gray-50"
                                    >
                                        @if($sortDirection === 'asc')
                                            ↑
                                        @else
                                            ↓
                                        @endif
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Orders List -->
                    @if($orders->isEmpty())
                        <div class="p-12 text-center">
                            <svg class="mx-auto h-12 w-12 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M8 11v6h8v-6M8 11H6a2 2 0 00-2 2v6a2 2 0 002 2h12a2 2 0 002-2v-6a2 2 0 00-2-2h-2" />
                            </svg>
                            <h3 class="mt-2 text-sm font-medium text-gray-900">No orders found</h3>
                            <p class="mt-1 text-sm text-gray-500">
                                @if(!empty($search) || !empty($statusFilter))
                                    Try adjusting your search or filter criteria.
                                @else
                                    You haven't placed any orders yet.
                                @endif
                            </p>
                            <div class="mt-6">
                                <a 
                                    href="{{ route('shop.index') }}"
                                    class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
                                >
                                    Start Shopping
                                </a>
                            </div>
                        </div>
                    @else
                        <div class="overflow-hidden">
                            @foreach($orders as $order)
                                <div class="border-b border-gray-200 hover:bg-gray-50 transition-colors">
                                    <div class="p-6">
                                        <div class="flex items-center justify-between">
                                            <div class="flex-1">
                                                <div class="flex items-center space-x-4">
                                                    <div>
                                                        <p class="text-sm font-medium text-gray-900">Order #{{ $order->order_number }}</p>
                                                        <p class="text-sm text-gray-500">{{ $order->created_at->format('M j, Y') }}</p>
                                                    </div>
                                                    
                                                    <div>
                                                        @php
                                                            $statusColors = [
                                                                'pending' => 'bg-yellow-100 text-yellow-800',
                                                                'processing' => 'bg-blue-100 text-blue-800',
                                                                'completed' => 'bg-green-100 text-green-800',
                                                                'cancelled' => 'bg-red-100 text-red-800',
                                                                'refunded' => 'bg-gray-100 text-gray-800',
                                                            ];
                                                        @endphp
                                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $statusColors[$order->status] ?? 'bg-gray-100 text-gray-800' }}">
                                                            {{ ucfirst($order->status) }}
                                                        </span>
                                                    </div>
                                                </div>
                                                
                                                <div class="mt-2">
                                                    <p class="text-sm text-gray-600">
                                                        {{ $order->items->count() }} item{{ $order->items->count() !== 1 ? 's' : '' }} • 
                                                        ${{ number_format($order->total, 2) }}
                                                    </p>
                                                </div>
                                            </div>
                                            
                                            <div class="flex items-center space-x-2">
                                                <button 
                                                    wire:click="selectOrder({{ $order->id }})"
                                                    class="px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                                                >
                                                    View Details
                                                </button>
                                                
                                                @if($order->status === 'completed')
                                                    <button 
                                                        wire:click="reorderItems({{ $order->id }})"
                                                        class="px-3 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                                                    >
                                                        Reorder
                                                    </button>
                                                @endif
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                        
                        <!-- Pagination -->
                        <div class="px-6 py-3 border-t border-gray-200">
                            {{ $orders->links() }}
                        </div>
                    @endif
                </div>
            @endif
        </div>
    @else
        <!-- Not Authenticated -->
        <div class="bg-white rounded-lg shadow p-12 text-center">
            <svg class="mx-auto h-12 w-12 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900">Please log in</h3>
            <p class="mt-1 text-sm text-gray-500">You need to be logged in to view your order history.</p>
            <div class="mt-6">
                <a 
                    href="{{ route('login') }}"
                    class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
                >
                    Log In
                </a>
            </div>
        </div>
    @endauth

    <!-- Flash Messages -->
    @if (session()->has('message'))
        <div class="mt-4 p-4 bg-green-50 border border-green-200 rounded-md">
            <div class="flex">
                <svg class="h-5 w-5 text-green-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                </svg>
                <p class="ml-3 text-sm text-green-700">{{ session('message') }}</p>
            </div>
        </div>
    @endif

    @if (session()->has('error'))
        <div class="mt-4 p-4 bg-red-50 border border-red-200 rounded-md">
            <div class="flex">
                <svg class="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                </svg>
                <p class="ml-3 text-sm text-red-700">{{ session('error') }}</p>
            </div>
        </div>
    @endif
</div> 