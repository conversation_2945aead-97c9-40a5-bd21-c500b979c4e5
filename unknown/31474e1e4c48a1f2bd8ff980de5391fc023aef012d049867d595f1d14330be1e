<?php

namespace Tests\Browser;

use <PERSON><PERSON>\Dusk\Browser;
use Tests\DuskTestCase;

class ExampleTest extends DuskTestCase
{
    /**
     * A basic browser test example.
     */
    public function test_basic_example(): void
    {
        $this->browse(function (Browser $browser) {
            $browser->visit('/')
                    ->assertSee('NEXUS')
                    ->assertPresent('body');
        });
    }

    /**
     * Test that the application loads without errors.
     */
    public function test_application_loads(): void
    {
        $this->browse(function (Browser $browser) {
            $browser->visit('/')
                    ->assertPresent('html')
                    ->assertMissing('.error-page');
        });
    }

    /**
     * Test responsive design elements.
     */
    public function test_responsive_design(): void
    {
        $this->browse(function (Browser $browser) {
            // Test desktop view
            $browser->resize(1920, 1080)
                    ->visit('/')
                    ->assertPresent('html');

            // Test tablet view
            $browser->resize(768, 1024)
                    ->refresh()
                    ->assertPresent('html');

            // Test mobile view
            $browser->resize(375, 667)
                    ->refresh()
                    ->assertPresent('html');
        });
    }
}
