<?php

namespace Tests\Browser\Pages;

use <PERSON><PERSON>\Dusk\Browser;

class ProductsPage extends Page
{
    /**
     * Get the URL for the page.
     */
    public function url(): string
    {
        return '/products';
    }

    /**
     * Assert that the browser is on the page.
     */
    public function assert(Browser $browser): void
    {
        $browser->assertPathIs($this->url())
                ->assertSee('Products');
    }

    /**
     * Get the element shortcuts for the page.
     */
    public function elements(): array
    {
        return [
            '@searchInput' => 'input[name="search"]',
            '@searchButton' => 'button[type="submit"]',
            '@categoryFilter' => 'select[name="category"]',
            '@sortBy' => 'select[name="sort"]',
            '@productGrid' => '.grid',
            '@productCard' => '.bg-white.rounded-lg.shadow-md',
            '@productTitle' => 'h3.font-semibold',
            '@productPrice' => '.text-lg.font-bold',
            '@addToCartButton' => '.bg-green-600',
            '@viewProductButton' => '.bg-blue-600',
            '@paginationLinks' => '.pagination',
            '@noResults' => '.col-span-full',
            '@featuredOnly' => 'input[name="featured"]',
            '@onSale' => 'input[name="on_sale"]',
            '@applyFilters' => 'button[type="submit"]',
        ];
    }

    /**
     * Search for products.
     */
    public function searchProducts(Browser $browser, string $query): void
    {
        $browser->type('@searchInput', $query)
                ->click('@searchButton');
    }

    /**
     * Filter products by category.
     */
    public function filterByCategory(Browser $browser, string $category): void
    {
        $browser->select('@categoryFilter', $category);
    }

    /**
     * Add first product to cart.
     */
    public function addFirstProductToCart(Browser $browser): void
    {
        $browser->within('@productGrid', function ($browser) {
            $browser->click('@addToCartButton');
        });
    }

    /**
     * View first product details.
     */
    public function viewFirstProduct(Browser $browser): void
    {
        $browser->within('@productGrid', function ($browser) {
            $browser->click('@viewProductButton');
        });
    }
}