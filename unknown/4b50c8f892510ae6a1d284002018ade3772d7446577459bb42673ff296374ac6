<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Update any existing data that might have the old values
        DB::table('orders')->where('status', 'shipped')->update(['status' => 'completed']);
        DB::table('orders')->where('status', 'delivered')->update(['status' => 'completed']);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Update data back to old values
        DB::table('orders')->where('status', 'completed')->update(['status' => 'delivered']);
    }
};

// Now alter the enum to match the model constants
DB::statement("ALTER TABLE orders MODIFY COLUMN status ENUM('pending', 'processing', 'completed', 'canceled', 'refunded') DEFAULT 'pending'");