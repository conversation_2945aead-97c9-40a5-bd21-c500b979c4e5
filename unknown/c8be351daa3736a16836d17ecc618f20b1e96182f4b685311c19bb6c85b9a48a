<?php

namespace Tests\Unit\Services;

use App\Models\EmailLog;
use App\Models\Order;
use App\Services\EmailTrackingService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class EmailTrackingServiceTest extends TestCase
{
    use RefreshDatabase;

    private EmailTrackingService $service;

    protected function setUp(): void
    {
        parent::setUp();
        $this->service = new EmailTrackingService();
    }

    public function test_log_email_attempt_creates_email_log()
    {
        $emailLog = $this->service->logEmailAttempt(
            'order_confirmation',
            '<EMAIL>',
            'Order Confirmation - PCB123',
            123,
            Order::class
        );

        $this->assertInstanceOf(EmailLog::class, $emailLog);
        $this->assertEquals('order_confirmation', $emailLog->type);
        $this->assertEquals('<EMAIL>', $emailLog->recipient);
        $this->assertEquals('Order Confirmation - PCB123', $emailLog->subject);
        $this->assertEquals('pending', $emailLog->status);
        $this->assertEquals(123, $emailLog->related_id);
        $this->assertEquals(Order::class, $emailLog->related_type);
        $this->assertEquals(1, $emailLog->attempts);
        $this->assertNotNull($emailLog->sent_at);
    }

    public function test_mark_email_sent_updates_status()
    {
        $emailLog = EmailLog::factory()->create([
            'status' => 'pending',
            'delivered_at' => null,
        ]);

        $this->service->markEmailSent($emailLog);

        $emailLog->refresh();
        $this->assertEquals('sent', $emailLog->status);
        $this->assertNotNull($emailLog->delivered_at);
    }

    public function test_mark_email_failed_updates_status_and_error()
    {
        $emailLog = EmailLog::factory()->create([
            'status' => 'pending',
            'attempts' => 1,
            'error_message' => null,
            'failed_at' => null,
        ]);

        $this->service->markEmailFailed($emailLog, 'SMTP connection failed', 3);

        $emailLog->refresh();
        $this->assertEquals('failed', $emailLog->status);
        $this->assertEquals('SMTP connection failed', $emailLog->error_message);
        $this->assertEquals(3, $emailLog->attempts);
        $this->assertNotNull($emailLog->failed_at);
    }

    public function test_increment_attempt_increases_count()
    {
        $emailLog = EmailLog::factory()->create(['attempts' => 1]);

        $this->service->incrementAttempt($emailLog);

        $emailLog->refresh();
        $this->assertEquals(2, $emailLog->attempts);
    }

    public function test_get_email_stats_returns_aggregated_data()
    {
        // Create test data
        EmailLog::factory()->create([
            'type' => 'order_confirmation',
            'status' => 'sent',
            'attempts' => 1,
            'created_at' => now()->subDays(5),
        ]);

        EmailLog::factory()->create([
            'type' => 'order_confirmation',
            'status' => 'sent',
            'attempts' => 2,
            'created_at' => now()->subDays(10),
        ]);

        EmailLog::factory()->create([
            'type' => 'order_confirmation',
            'status' => 'failed',
            'attempts' => 3,
            'created_at' => now()->subDays(15),
        ]);

        EmailLog::factory()->create([
            'type' => 'build_shared',
            'status' => 'sent',
            'attempts' => 1,
            'created_at' => now()->subDays(20),
        ]);

        $stats = $this->service->getEmailStats(30);

        $this->assertArrayHasKey('order_confirmation', $stats);
        $this->assertArrayHasKey('build_shared', $stats);
        
        $this->assertEquals(2, $stats['order_confirmation']['sent']['count']);
        $this->assertEquals(1.5, $stats['order_confirmation']['sent']['avg_attempts']);
        
        $this->assertEquals(1, $stats['order_confirmation']['failed']['count']);
        $this->assertEquals(3.0, $stats['order_confirmation']['failed']['avg_attempts']);
        
        $this->assertEquals(1, $stats['build_shared']['sent']['count']);
        $this->assertEquals(1.0, $stats['build_shared']['sent']['avg_attempts']);
    }

    public function test_get_failed_emails_returns_failed_emails_with_multiple_attempts()
    {
        // Create emails with different statuses and attempt counts
        $failedEmail1 = EmailLog::factory()->create([
            'status' => 'failed',
            'attempts' => 3,
            'failed_at' => now()->subHours(2),
        ]);

        $failedEmail2 = EmailLog::factory()->create([
            'status' => 'failed',
            'attempts' => 5,
            'failed_at' => now()->subHours(1),
        ]);

        // This should not be included (not enough attempts)
        EmailLog::factory()->create([
            'status' => 'failed',
            'attempts' => 2,
            'failed_at' => now()->subHours(3),
        ]);

        // This should not be included (not failed)
        EmailLog::factory()->create([
            'status' => 'sent',
            'attempts' => 4,
        ]);

        $failedEmails = $this->service->getFailedEmails();

        $this->assertCount(2, $failedEmails);
        $this->assertTrue($failedEmails->contains($failedEmail2)); // Most recent first
        $this->assertTrue($failedEmails->contains($failedEmail1));
    }

    public function test_cleanup_old_logs_deletes_old_entries()
    {
        // Create old logs (older than 90 days)
        EmailLog::factory()->create(['created_at' => now()->subDays(100)]);
        EmailLog::factory()->create(['created_at' => now()->subDays(95)]);
        
        // Create recent logs (within 90 days)
        EmailLog::factory()->create(['created_at' => now()->subDays(30)]);
        EmailLog::factory()->create(['created_at' => now()->subDays(10)]);

        $this->assertEquals(4, EmailLog::count());

        $deletedCount = $this->service->cleanupOldLogs(90);

        $this->assertEquals(2, $deletedCount);
        $this->assertEquals(2, EmailLog::count());
    }

    public function test_cleanup_old_logs_with_custom_retention_period()
    {
        // Create logs of different ages
        EmailLog::factory()->create(['created_at' => now()->subDays(40)]);
        EmailLog::factory()->create(['created_at' => now()->subDays(20)]);
        EmailLog::factory()->create(['created_at' => now()->subDays(10)]);

        $this->assertEquals(3, EmailLog::count());

        $deletedCount = $this->service->cleanupOldLogs(30);

        $this->assertEquals(1, $deletedCount); // Only the 40-day-old log should be deleted
        $this->assertEquals(2, EmailLog::count());
    }
}