<?php

/**
 * Comprehensive Database Seeder Runner
 * 
 * This script runs all database seeders in the correct order
 * and provides detailed feedback on the seeding process.
 */

require_once 'vendor/autoload.php';

use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\DB;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "🌱 Starting Database Seeding Process...\n";
echo "=====================================\n\n";

// Check database connection
try {
    DB::connection()->getPdo();
    echo "✅ Database connection successful\n\n";
} catch (Exception $e) {
    echo "❌ Database connection failed: " . $e->getMessage() . "\n";
    exit(1);
}

// List of seeders in execution order
$seeders = [
    'Core System Data' => [
        'UserSeeder' => 'Creates admin and test users',
        'SettingsSeeder' => 'Sets up application settings',
        'GatewaySettingsSeeder' => 'Configures payment gateways',
    ],
    'Component & Product Data' => [
        'ComponentCategorySeeder' => 'Creates component categories (CPU, GPU, etc.)',
        'ComponentSeeder' => 'Creates sample components',
        'ComponentCompatibilitySeeder' => 'Sets up compatibility rules',
        'ProductCategorySeeder' => 'Creates product categories',
    ],
    'Build System' => [
        'BuildSeeder' => 'Creates sample PC builds',
        'BuildComponentSeeder' => 'Links components to builds',
    ],
    'E-commerce Data' => [
        'CartSeeder' => 'Creates shopping carts with items',
        'OrderSeeder' => 'Creates customer orders',
        'PaymentSeeder' => 'Creates payment records',
        'TransactionSeeder' => 'Creates payment transactions',
    ],
    'Coupon System' => [
        'CouponSeeder' => 'Creates discount coupons',
        'CouponUsageSeeder' => 'Creates coupon usage records',
    ],
    'Inventory Management' => [
        'PriceHistorySeeder' => 'Creates price change history',
        'StockMovementSeeder' => 'Creates inventory movement records',
        'InventoryAlertSeeder' => 'Creates stock alerts',
    ],
    'Communication Logs' => [
        'EmailLogSeeder' => 'Creates email delivery logs',
    ],
];

$totalSeeders = 0;
foreach ($seeders as $group) {
    $totalSeeders += count($group);
}

$currentSeeder = 0;
$startTime = microtime(true);

foreach ($seeders as $groupName => $groupSeeders) {
    echo "📂 {$groupName}\n";
    echo str_repeat('-', strlen($groupName) + 3) . "\n";
    
    foreach ($groupSeeders as $seederClass => $description) {
        $currentSeeder++;
        $progress = round(($currentSeeder / $totalSeeders) * 100);
        
        echo sprintf("[%d/%d] (%d%%) Running %s...", 
            $currentSeeder, $totalSeeders, $progress, $seederClass);
        
        try {
            $seederStart = microtime(true);
            Artisan::call('db:seed', ['--class' => "Database\\Seeders\\{$seederClass}"]);
            $seederTime = round((microtime(true) - $seederStart) * 1000);
            
            echo " ✅ ({$seederTime}ms)\n";
            echo "   └─ {$description}\n";
            
        } catch (Exception $e) {
            echo " ❌ FAILED\n";
            echo "   └─ Error: " . $e->getMessage() . "\n";
            
            // Show more context for common errors
            if (strpos($e->getMessage(), 'items available') !== false) {
                echo "   └─ This appears to be a random selection issue - not enough data available\n";
            }
            
            // Ask if user wants to continue
            echo "\nDo you want to continue with remaining seeders? (y/n): ";
            $handle = fopen("php://stdin", "r");
            $line = fgets($handle);
            fclose($handle);
            
            if (trim($line) !== 'y' && trim($line) !== 'Y') {
                echo "Seeding process aborted.\n";
                exit(1);
            }
        }
    }
    echo "\n";
}

$totalTime = round((microtime(true) - $startTime) * 1000);

echo "🎉 Database Seeding Completed!\n";
echo "==============================\n";
echo "Total time: {$totalTime}ms\n";
echo "Seeders run: {$currentSeeder}/{$totalSeeders}\n\n";

// Display summary statistics
echo "📊 Database Summary:\n";
echo "-------------------\n";

try {
    $stats = [
        'Users' => DB::table('users')->count(),
        'Components' => DB::table('components')->count(),
        'Component Categories' => DB::table('component_categories')->count(),
        'Builds' => DB::table('builds')->count(),
        'Orders' => DB::table('orders')->count(),
        'Transactions' => DB::table('transactions')->count(),
        'Coupons' => DB::table('coupons')->count(),
        'Price History Records' => DB::table('price_histories')->count(),
        'Stock Movements' => DB::table('stock_movements')->count(),
        'Inventory Alerts' => DB::table('inventory_alerts')->count(),
        'Email Logs' => DB::table('email_logs')->count(),
    ];
    
    foreach ($stats as $table => $count) {
        echo sprintf("%-25s: %d records\n", $table, $count);
    }
    
} catch (Exception $e) {
    echo "Could not retrieve statistics: " . $e->getMessage() . "\n";
}

echo "\n✨ Your PC Builder application is now ready with sample data!\n";
echo "\nNext steps:\n";
echo "- Visit your application to see the seeded data\n";
echo "- Check the admin panel for management features\n";
echo "- Test the PC builder with the sample components\n";
echo "- Review payment gateway configurations\n";