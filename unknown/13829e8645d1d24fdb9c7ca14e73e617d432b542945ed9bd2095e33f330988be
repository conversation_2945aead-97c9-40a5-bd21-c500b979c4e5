<?php

/**
 * Comprehensive Test Suite Runner
 * 
 * This script runs the complete test suite for the PC Builder E-commerce application
 * including integration tests, performance tests, and browser tests.
 */

echo "🚀 Starting Comprehensive Test Suite for PC Builder E-commerce\n";
echo "================================================================\n\n";

$testSuites = [
    'Integration Tests' => [
        'description' => 'Basic workflow integration tests',
        'command' => 'php artisan test tests/Feature/Integration/BasicWorkflowTest.php',
        'timeout' => 300 // 5 minutes
    ],
    // 'Performance Tests' => [
    //     'description' => 'Performance and load testing',
    //     'command' => 'php artisan test tests/Performance/',
    //     'timeout' => 600 // 10 minutes
    // ],
    // 'Browser Tests' => [
    //     'description' => 'Laravel Dusk browser tests',
    //     'command' => 'php artisan dusk',
    //     'timeout' => 900 // 15 minutes
    // ],
    'Unit Tests' => [
        'description' => 'Unit tests for services and models',
        'command' => 'php artisan test tests/Unit/',
        'timeout' => 180 // 3 minutes
    ],
    'Feature Tests' => [
        'description' => 'Feature tests for components',
        'command' => 'php artisan test tests/Feature/ --exclude-group=integration',
        'timeout' => 300 // 5 minutes
    ]
];

$results = [];
$totalStartTime = microtime(true);

foreach ($testSuites as $suiteName => $suite) {
    echo "📋 Running {$suiteName}\n";
    echo "   {$suite['description']}\n";
    echo "   Command: {$suite['command']}\n";
    
    $startTime = microtime(true);
    
    // Execute the test command
    $output = [];
    $returnCode = 0;
    
    exec($suite['command'] . ' 2>&1', $output, $returnCode);
    
    $endTime = microtime(true);
    $duration = round(($endTime - $startTime), 2);
    
    $results[$suiteName] = [
        'success' => $returnCode === 0,
        'duration' => $duration,
        'output' => implode("\n", $output)
    ];
    
    if ($returnCode === 0) {
        echo "   ✅ PASSED ({$duration}s)\n";
    } else {
        echo "   ❌ FAILED ({$duration}s)\n";
        echo "   Error output:\n";
        echo "   " . str_replace("\n", "\n   ", implode("\n", array_slice($output, -10))) . "\n";
    }
    
    echo "\n";
}

$totalEndTime = microtime(true);
$totalDuration = round(($totalEndTime - $totalStartTime), 2);

// Summary
echo "📊 Test Suite Summary\n";
echo "====================\n";

$passedCount = 0;
$failedCount = 0;

foreach ($results as $suiteName => $result) {
    $status = $result['success'] ? '✅ PASSED' : '❌ FAILED';
    $duration = $result['duration'];
    
    echo sprintf("%-25s %s (%ss)\n", $suiteName, $status, $duration);
    
    if ($result['success']) {
        $passedCount++;
    } else {
        $failedCount++;
        // Show more detailed error info for failed tests
        echo "   Error details:\n";
        $errorLines = explode("\n", $result['output']);
        $relevantLines = array_filter($errorLines, function($line) {
            return strpos($line, 'FAILED') !== false || 
                   strpos($line, 'Error') !== false || 
                   strpos($line, 'Exception') !== false ||
                   strpos($line, 'Expected') !== false ||
                   strpos($line, 'Failed asserting') !== false;
        });
        
        foreach (array_slice($relevantLines, 0, 3) as $line) {
            echo "   " . trim($line) . "\n";
        }
        echo "\n";
    }
}

echo "\n";
echo "Total Duration: {$totalDuration}s\n";
echo "Passed: {$passedCount}\n";
echo "Failed: {$failedCount}\n";

if ($failedCount === 0) {
    echo "\n🎉 All test suites passed! The PC Builder E-commerce application is ready for deployment.\n";
    exit(0);
} else {
    echo "\n⚠️  Some test suites failed. Please review the output above and fix the issues.\n";
    exit(1);
}