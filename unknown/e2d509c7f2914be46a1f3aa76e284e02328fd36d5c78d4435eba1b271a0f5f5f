<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\Product;
use App\Models\Transaction;
use App\Models\User;

echo "=== Payment-Product Integration Test ===\n";

// Get or create a user
$user = User::first();
if (!$user) {
    echo "No users found. Please create a user first.\n";
    exit(1);
}

// Get a product
$product = Product::where('manage_stock', true)->where('stock_quantity', '>', 0)->first();
if (!$product) {
    echo "No suitable product found. Creating one...\n";
    $product = Product::factory()->create([
        'manage_stock' => true,
        'stock_quantity' => 10,
        'status' => 'active',
        'in_stock' => true
    ]);
}

echo "User: {$user->name} (ID: {$user->id})\n";
echo "Product: {$product->name} (ID: {$product->id})\n";
echo "Product Stock: {$product->stock_quantity}\n";
echo "Product Price: {$product->formatted_price}\n";

// Create a transaction
$transaction = Transaction::create([
    'user_id' => $user->id,
    'product_id' => $product->id,
    'quantity' => 2,
    'gateway_name' => 'razorpay',
    'amount' => $product->effective_price * 2,
    'currency' => 'INR',
    'status' => Transaction::STATUS_PENDING,
    'transaction_id' => 'test_' . uniqid(),
]);

echo "\nTransaction created:\n";
echo "ID: {$transaction->id}\n";
echo "Amount: {$transaction->formatted_amount}\n";
echo "Status: {$transaction->status}\n";
echo "Quantity: {$transaction->quantity}\n";

// Test relationships
echo "\nTesting relationships:\n";
echo "Transaction User: {$transaction->user->name}\n";
echo "Transaction Product: {$transaction->product->name}\n";
echo "Product Transactions Count: {$product->transactions()->count()}\n";
echo "User Transactions Count: {$user->transactions()->count()}\n";

// Simulate completing the transaction and updating stock
echo "\nSimulating transaction completion...\n";
$transaction->update(['status' => Transaction::STATUS_COMPLETED]);

// Update product stock
$product->updateStock($transaction->quantity, 'sale');
$product->refresh();

echo "Updated product stock: {$product->stock_quantity}\n";
echo "Stock movements for product: {$product->stockMovements()->count()}\n";

// Test transaction scopes
echo "\nTesting transaction scopes:\n";
echo "Completed transactions: " . Transaction::where('status', Transaction::STATUS_COMPLETED)->count() . "\n";
echo "Transactions by user: " . Transaction::byUser($user->id)->count() . "\n";
echo "Transactions by product: " . Transaction::where('product_id', $product->id)->count() . "\n";

echo "\n=== Payment-Product Integration Test Complete ===\n";