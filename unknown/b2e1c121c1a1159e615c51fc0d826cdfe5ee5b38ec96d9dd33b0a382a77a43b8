<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\Product;

$product = Product::first();
echo "Product: {$product->name}\n";
echo "Manage Stock: " . ($product->manage_stock ? 'Yes' : 'No') . "\n";
echo "Current Stock: {$product->stock_quantity}\n";

if (!$product->manage_stock) {
    echo "Enabling stock management...\n";
    $product->update(['manage_stock' => true]);
    $product->refresh();
}

echo "Testing stock update...\n";
$originalStock = $product->stock_quantity;
echo "Original stock: {$originalStock}\n";

$result = $product->updateStock(3, 'sale');
echo "Update result: " . ($result ? 'Success' : 'Failed') . "\n";

$product->refresh();
echo "New stock: {$product->stock_quantity}\n";

$movements = $product->stockMovements()->get();
echo "Stock movements: " . $movements->count() . "\n";

if ($movements->count() > 0) {
    $movement = $movements->first();
    echo "Last movement: {$movement->type}, quantity_change: {$movement->quantity_change}, previous: {$movement->previous_stock}, new: {$movement->new_stock}\n";
}