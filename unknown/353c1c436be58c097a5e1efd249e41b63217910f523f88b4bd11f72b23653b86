<div>
    <h2 class="text-xl font-bold mb-4">Notifications</h2>
    @if (empty($notifications))
        <div class="text-gray-500">No notifications at this time.</div>
    @else
        <ul class="divide-y divide-gray-200">
            @foreach ($notifications as $note)
                <li class="py-3 flex items-center">
                    <span class="mr-3">
                        @if ($note['type'] === 'order')
                            <svg class="w-5 h-5 text-blue-500" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" d="M3 7h18M3 12h18M3 17h18" /></svg>
                        @else
                            <svg class="w-5 h-5 text-green-500" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" d="M9 17v-2a4 4 0 014-4h4m0 0V7a4 4 0 00-4-4H7a4 4 0 00-4 4v10a4 4 0 004 4h4" /></svg>
                        @endif
                    </span>
                    <span class="flex-1">{{ $note['message'] }}</span>
                    <span class="text-xs text-gray-400 ml-2">{{ \Carbon\Carbon::parse($note['date'])->diffForHumans() }}</span>
                </li>
            @endforeach
        </ul>
    @endif
</div> 