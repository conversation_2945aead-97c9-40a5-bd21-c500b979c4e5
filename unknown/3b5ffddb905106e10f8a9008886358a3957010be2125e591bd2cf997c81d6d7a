<?php

namespace App\Livewire\Admin;

use App\Models\Component;
use App\Models\InventoryAlert;
use App\Services\InventoryService;
use Livewire\Component as LivewireComponent;
use Livewire\WithPagination;

class InventoryManagement extends LivewireComponent
{
    use WithPagination;

    protected string $layout = 'layouts.app';

    public $activeTab = 'overview';
    public $selectedAlert = null;
    public $showAlertModal = false;

    protected InventoryService $inventoryService;

    public function boot(InventoryService $inventoryService)
    {
        $this->inventoryService = $inventoryService;
    }

    public function setActiveTab($tab)
    {
        $this->activeTab = $tab;
        $this->resetPage();
    }

    public function viewAlert($alertId)
    {
        $this->selectedAlert = InventoryAlert::with('component')->find($alertId);
        $this->showAlertModal = true;
    }

    public function resolveAlert($alertId)
    {
        $alert = InventoryAlert::find($alertId);
        if ($alert) {
            $this->inventoryService->resolveAlert($alert);
            $this->dispatch('alert-resolved');
        }
        $this->showAlertModal = false;
    }

    public function closeModal()
    {
        $this->showAlertModal = false;
        $this->selectedAlert = null;
    }

    public function render()
    {
        $data = [];

        switch ($this->activeTab) {
            case 'overview':
                $data['report'] = $this->inventoryService->getInventoryReport();
                $data['analytics'] = $this->inventoryService->getInventoryAnalytics(30);
                break;
            
            case 'alerts':
                $data['alerts'] = $this->inventoryService->getInventoryAlerts(true);
                break;
            
            case 'low-stock':
                $data['components'] = $this->inventoryService->getLowStockComponents();
                break;
            
            case 'out-of-stock':
                $data['components'] = $this->inventoryService->getOutOfStockComponents();
                break;
        }

        return view('livewire.admin.inventory-management', $data);
    }
}