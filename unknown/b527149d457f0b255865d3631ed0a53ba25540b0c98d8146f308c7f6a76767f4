<?php

/**
 * Simple Server Connection Test
 * 
 * This script tests if the Laravel server is running and pages are accessible
 * without using Dusk (which can be problematic with Chrome/ChromeDriver issues).
 */

class ServerConnectionTest
{
    private $baseUrl = 'http://127.0.0.1:8000';
    private $testResults = [];

    public function run(): void
    {
        $this->displayHeader();
        
        $this->testServerConnection();
        $this->testHomePage();
        $this->testProductsPage();
        $this->testPaymentPage();
        
        $this->displayResults();
    }

    private function displayHeader(): void
    {
        echo "\n";
        echo "╔══════════════════════════════════════════════════════════════╗\n";
        echo "║                Server Connection Test                       ║\n";
        echo "║                                                              ║\n";
        echo "║    Testing basic server connectivity and page responses     ║\n";
        echo "╚══════════════════════════════════════════════════════════════╝\n";
        echo "\n";
    }

    private function testServerConnection(): void
    {
        echo "🔍 Testing server connection...\n";
        
        $connection = @fsockopen('127.0.0.1', 8000, $errno, $errstr, 5);
        
        if ($connection) {
            fclose($connection);
            $this->testResults['server'] = ['status' => 'PASS', 'message' => 'Server is running'];
            echo "✅ Server is running on port 8000\n";
        } else {
            $this->testResults['server'] = ['status' => 'FAIL', 'message' => "Cannot connect to server: $errstr"];
            echo "❌ Server is not running on port 8000\n";
            echo "   Error: $errstr\n";
            echo "   Please start the server with: php artisan serve\n";
        }
        echo "\n";
    }

    private function testHomePage(): void
    {
        echo "🏠 Testing home page...\n";
        $this->testUrl('/', 'home');
    }

    private function testProductsPage(): void
    {
        echo "📦 Testing products page...\n";
        $this->testUrl('/products', 'products');
    }

    private function testPaymentPage(): void
    {
        echo "💳 Testing payment page...\n";
        $this->testUrl('/payment/create', 'payment');
    }

    private function testUrl(string $path, string $testName): void
    {
        $url = $this->baseUrl . $path;
        
        $context = stream_context_create([
            'http' => [
                'timeout' => 10,
                'method' => 'GET',
                'header' => [
                    'User-Agent: ServerConnectionTest/1.0',
                    'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8'
                ]
            ]
        ]);
        
        $response = @file_get_contents($url, false, $context);
        
        if ($response === false) {
            $this->testResults[$testName] = ['status' => 'FAIL', 'message' => 'Failed to fetch page'];
            echo "❌ Failed to load $path\n";
        } else {
            $httpCode = $this->getHttpResponseCode($http_response_header);
            
            if ($httpCode >= 200 && $httpCode < 400) {
                $this->testResults[$testName] = ['status' => 'PASS', 'message' => "HTTP $httpCode - Page loaded successfully"];
                echo "✅ $path loaded successfully (HTTP $httpCode)\n";
                
                // Check for common error indicators
                if (strpos($response, '500') !== false || strpos($response, 'Internal Server Error') !== false) {
                    echo "⚠️  Warning: Page may contain server errors\n";
                }
                
                if (strpos($response, '404') !== false || strpos($response, 'Not Found') !== false) {
                    echo "⚠️  Warning: Page may contain 404 errors\n";
                }
                
                // Check for expected content
                if ($testName === 'home' && strpos($response, 'NEXUS') !== false) {
                    echo "✅ Home page contains expected content (NEXUS)\n";
                }
                
                if ($testName === 'products' && strpos($response, 'Products') !== false) {
                    echo "✅ Products page contains expected content\n";
                }
                
                if ($testName === 'payment' && (strpos($response, 'Payment') !== false || strpos($response, 'Login') !== false)) {
                    echo "✅ Payment page accessible (may redirect to login)\n";
                }
                
            } else {
                $this->testResults[$testName] = ['status' => 'FAIL', 'message' => "HTTP $httpCode"];
                echo "❌ $path returned HTTP $httpCode\n";
            }
        }
        echo "\n";
    }

    private function getHttpResponseCode(array $headers): int
    {
        if (empty($headers)) {
            return 0;
        }
        
        $statusLine = $headers[0];
        if (preg_match('/HTTP\/\d\.\d\s+(\d+)/', $statusLine, $matches)) {
            return (int) $matches[1];
        }
        
        return 0;
    }

    private function displayResults(): void
    {
        $total = count($this->testResults);
        $passed = count(array_filter($this->testResults, fn($result) => $result['status'] === 'PASS'));
        $failed = $total - $passed;

        echo "╔══════════════════════════════════════════════════════════════╗\n";
        echo "║                        TEST SUMMARY                         ║\n";
        echo "╠══════════════════════════════════════════════════════════════╣\n";
        echo sprintf("║ Total Tests: %-3d | Passed: %-3d | Failed: %-3d          ║\n", $total, $passed, $failed);
        echo "╚══════════════════════════════════════════════════════════════╝\n";

        if ($failed > 0) {
            echo "\n❌ Failed Tests:\n";
            foreach ($this->testResults as $test => $result) {
                if ($result['status'] === 'FAIL') {
                    echo "  - {$test}: {$result['message']}\n";
                }
            }
        }

        if ($passed === $total) {
            echo "\n🎉 All tests passed! Your server is running correctly.\n";
            echo "You can now run Dusk tests with:\n";
            echo "  php run_dusk_with_server.php --suite basic\n";
        } else {
            echo "\n💡 Recommendations:\n";
            if (isset($this->testResults['server']) && $this->testResults['server']['status'] === 'FAIL') {
                echo "  1. Start the Laravel server: php artisan serve\n";
                echo "  2. Make sure the server is running on port 8000\n";
            }
            echo "  3. Check your Laravel application for errors\n";
            echo "  4. Ensure database migrations are run: php artisan migrate\n";
        }

        echo "\n";
    }
}

// Run the test if called directly
if (php_sapi_name() === 'cli') {
    $test = new ServerConnectionTest();
    $test->run();
}