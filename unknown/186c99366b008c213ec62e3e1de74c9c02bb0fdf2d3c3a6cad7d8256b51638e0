@extends('layouts.app')

@section('title', 'Track Order')

@section('content')
<div class="py-12">
    <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg p-6">
            <div class="flex justify-between items-center mb-6">
                <h1 class="text-2xl font-semibold">Track Order</h1>
                
                <a href="{{ route('orders.show', $order->id) }}" class="inline-flex items-center px-4 py-2 bg-gray-200 border border-transparent rounded-md font-semibold text-xs text-gray-700 uppercase tracking-widest hover:bg-gray-300 active:bg-gray-400 focus:outline-none focus:border-gray-500 focus:ring focus:ring-gray-300 disabled:opacity-25 transition">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                    </svg>
                    Back to Order Details
                </a>
            </div>
            
            <div class="bg-gray-50 rounded-lg p-6 mb-8">
                <div class="flex justify-between items-start">
                    <div>
                        <h2 class="text-xl font-semibold mb-2">Order #{{ $order->order_number }}</h2>
                        <p class="text-gray-600">Placed on {{ $order->created_at->format('F j, Y, g:i a') }}</p>
                    </div>
                    
                    <span class="px-3 py-1 inline-flex text-sm leading-5 font-semibold rounded-full 
                        @if($order->status == 'completed') bg-green-100 text-green-800 
                        @elseif($order->status == 'cancelled') bg-red-100 text-red-800 
                        @elseif($order->status == 'processing') bg-blue-100 text-blue-800 
                        @else bg-yellow-100 text-yellow-800 @endif">
                        {{ ucfirst($order->status) }}
                    </span>
                </div>
            </div>
            
            <div class="mb-8">
                <h2 class="text-xl font-semibold mb-6">Shipment Status</h2>
                
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                    <div class="bg-white p-4 border rounded-lg">
                        <h3 class="font-medium text-gray-500 mb-1">Current Status</h3>
                        <p class="text-lg font-semibold">{{ ucfirst($trackingInfo['status']) }}</p>
                    </div>
                    
                    <div class="bg-white p-4 border rounded-lg">
                        <h3 class="font-medium text-gray-500 mb-1">Current Location</h3>
                        <p class="text-lg font-semibold">{{ $trackingInfo['current_location'] }}</p>
                    </div>
                    
                    <div class="bg-white p-4 border rounded-lg">
                        <h3 class="font-medium text-gray-500 mb-1">Estimated Delivery</h3>
                        <p class="text-lg font-semibold">{{ $trackingInfo['estimated_delivery'] }}</p>
                    </div>
                </div>
                
                <div class="relative">
                    <!-- Progress Bar -->
                    <div class="hidden sm:block absolute left-0 inset-y-0 w-full py-6">
                        <div class="h-0.5 w-full bg-gray-200 absolute top-1/2 transform -translate-y-1/2"></div>
                    </div>
                    
                    <!-- Progress Steps -->
                    <div class="relative flex justify-between">
                        @php
                            $statuses = ['Order Placed', 'Processing', 'Shipped', 'Out for Delivery', 'Delivered'];
                            $currentStatusIndex = array_search(ucfirst($trackingInfo['status']), $statuses) !== false ? 
                                array_search(ucfirst($trackingInfo['status']), $statuses) : 0;
                        @endphp
                        
                        @foreach($statuses as $index => $status)
                            <div class="flex flex-col items-center relative">
                                <div class="rounded-full h-12 w-12 flex items-center justify-center 
                                    @if($index <= $currentStatusIndex) 
                                        bg-indigo-600 text-white 
                                    @else 
                                        bg-gray-200 text-gray-600 
                                    @endif 
                                    z-10">
                                    @if($index < $currentStatusIndex)
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                        </svg>
                                    @else
                                        {{ $index + 1 }}
                                    @endif
                                </div>
                                <div class="text-center mt-2">
                                    <span class="font-medium @if($index <= $currentStatusIndex) text-indigo-600 @else text-gray-500 @endif">
                                        {{ $status }}
                                    </span>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
            
            <div class="mb-8">
                <h2 class="text-xl font-semibold mb-4">Tracking History</h2>
                
                <div class="flow-root">
                    <ul class="-mb-8">
                        @foreach($trackingInfo['history'] as $index => $event)
                            <li>
                                <div class="relative pb-8">
                                    @if($index !== count($trackingInfo['history']) - 1)
                                        <span class="absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200" aria-hidden="true"></span>
                                    @endif
                                    <div class="relative flex space-x-3">
                                        <div>
                                            <span class="h-8 w-8 rounded-full bg-indigo-500 flex items-center justify-center ring-8 ring-white">
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                                </svg>
                                            </span>
                                        </div>
                                        <div class="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
                                            <div>
                                                <p class="text-sm text-gray-500">{{ $event['status'] }} <span class="font-medium text-gray-900">at {{ $event['location'] }}</span></p>
                                            </div>
                                            <div class="text-right text-sm whitespace-nowrap text-gray-500">
                                                <time>{{ $event['date'] }}</time>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </li>
                        @endforeach
                    </ul>
                </div>
            </div>
            
            <div class="bg-gray-50 p-4 rounded-lg">
                <p class="text-sm text-gray-500">For any questions about your order, please contact our customer support at <a href="mailto:<EMAIL>" class="text-indigo-600 hover:text-indigo-900"><EMAIL></a> or call us at <span class="text-gray-900">(555) 123-4567</span>.</p>
            </div>
        </div>
    </div>
</div>
@endsection