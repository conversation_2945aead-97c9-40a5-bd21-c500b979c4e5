@extends('layouts.app')
@section('content')
    <div class="bg-gray-900 text-white min-h-screen">
        <!-- Hero Section -->
        <section class="relative h-screen flex items-center justify-center overflow-hidden bg-circuit">
            <!-- Animated Background Grid -->
            <div class="absolute inset-0 bg-gray-900">
                <div class="absolute inset-0 bg-gradient-to-br from-blue-900/20 via-purple-900/20 to-gray-900"></div>
            </div>

            <!-- Floating Particles -->
            <div id="particles" class="absolute inset-0 pointer-events-none"></div>

            <!-- Floating 3D Components -->
            <div class="floating-component top-20 left-20 w-24 h-24" id="gpu">
                <div
                    class="w-full h-full bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg transform rotate-12 relative">
                    <div class="absolute inset-2 bg-gray-800 rounded"></div>
                    <div class="absolute top-1 left-1 w-4 h-4 bg-green-400 rounded-full"></div>
                    <div class="absolute bottom-1 right-1 w-6 h-2 bg-blue-400 rounded"></div>
                </div>
            </div>

            <div class="floating-component top-32 right-32 w-20 h-20" id="cpu">
                <div
                    class="w-full h-full bg-gradient-to-br from-green-500 to-blue-600 rounded-lg transform -rotate-6 relative">
                    <div class="absolute inset-1 bg-gray-800 rounded grid grid-cols-4 gap-1 p-1">
                        <div class="bg-yellow-400 rounded-full"></div>
                        <div class="bg-green-400 rounded-full"></div>
                        <div class="bg-blue-400 rounded-full"></div>
                        <div class="bg-purple-400 rounded-full"></div>
                    </div>
                </div>
            </div>

            <div class="floating-component bottom-32 left-32 w-28 h-16" id="motherboard">
                <div
                    class="w-full h-full bg-gradient-to-br from-purple-500 to-pink-600 rounded-lg transform rotate-3 relative">
                    <div class="absolute inset-1 bg-gray-800 rounded"></div>
                    <div class="absolute top-1 left-1 w-2 h-2 bg-red-400 rounded-full"></div>
                    <div class="absolute top-1 right-1 w-2 h-2 bg-blue-400 rounded-full"></div>
                    <div class="absolute bottom-1 left-2 w-6 h-1 bg-green-400 rounded"></div>
                </div>
            </div>

            <div class="floating-component bottom-20 right-20 w-16 h-20" id="ram">
                <div
                    class="w-full h-full bg-gradient-to-br from-cyan-500 to-blue-600 rounded-lg transform -rotate-12 relative">
                    <div class="absolute inset-1 bg-gray-800 rounded"></div>
                    <div class="absolute top-1 left-1 right-1 h-1 bg-green-400 rounded"></div>
                    <div class="absolute bottom-1 left-1 right-1 h-1 bg-blue-400 rounded"></div>
                </div>
            </div>

            <!-- Main Content -->
            <div class="relative z-10 text-center max-w-4xl px-6">
                <h1 class="text-6xl md:text-8xl font-black mb-6 glow-text gradient-text" id="headline">
                    Build Your Dream Rig
                </h1>

                <p class="text-xl md:text-2xl mb-8 text-gray-300 max-w-2xl mx-auto opacity-0" id="subheading">
                    Optimized for Performance, Assembled for You
                </p>

                <button
                    class="neon-border bg-transparent hover:bg-blue-600/20 text-white px-12 py-4 rounded-full text-lg font-bold transition-all duration-300 transform hover:scale-105 opacity-0"
                    id="cta">
                    Start Building
                </button>

                <!-- Tech Specs Display -->
                <div class="mt-12 grid grid-cols-3 gap-8 max-w-2xl mx-auto opacity-0" id="specs">
                    <div class="text-center">
                        <div class="text-3xl font-bold text-blue-400 mb-2">∞</div>
                        <div class="text-sm text-gray-400">Configurations</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl font-bold text-purple-400 mb-2">24/7</div>
                        <div class="text-sm text-gray-400">Support</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl font-bold text-green-400 mb-2">Pro</div>
                        <div class="text-sm text-gray-400">Grade</div>
                    </div>
                </div>
            </div>

            <!-- Scroll Indicator -->
            <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 opacity-0" id="scroll-indicator">
                <div class="w-6 h-10 border-2 border-blue-400 rounded-full relative">
                    <div
                        class="w-1 h-3 bg-blue-400 rounded-full absolute top-2 left-1/2 transform -translate-x-1/2 animate-bounce">
                    </div>
                </div>
            </div>
        </section>
    </div>
@endsection

@push('scripts')
    <script>
        // Initialize GSAP
        gsap.registerPlugin(ScrollTrigger);

        // Create floating particles
        function createParticles() {
            const particleContainer = document.getElementById('particles');
            for (let i = 0; i < 50; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.top = Math.random() * 100 + '%';
                particle.style.animationDelay = Math.random() * 4 + 's';
                particle.style.animationDuration = (Math.random() * 3 + 2) + 's';
                particleContainer.appendChild(particle);
            }
        }

        // Initialize animations
        function initAnimations() {
            // Animate floating components
            gsap.set('.floating-component', {
                transformOrigin: 'center center',
                z: 0
            });

            // Continuous floating animation for components
            gsap.to('#gpu', {
                y: -30,
                x: 20,
                rotationX: 15,
                rotationY: 25,
                duration: 4,
                repeat: -1,
                yoyo: true,
                ease: 'sine.inOut'
            });

            gsap.to('#cpu', {
                y: 25,
                x: -15,
                rotationX: -20,
                rotationY: -15,
                duration: 3.5,
                repeat: -1,
                yoyo: true,
                ease: 'sine.inOut'
            });

            gsap.to('#motherboard', {
                y: -20,
                x: 15,
                rotationX: 10,
                rotationY: 20,
                duration: 4.5,
                repeat: -1,
                yoyo: true,
                ease: 'sine.inOut'
            });

            gsap.to('#ram', {
                y: 35,
                x: -25,
                rotationX: -15,
                rotationY: 30,
                duration: 3,
                repeat: -1,
                yoyo: true,
                ease: 'sine.inOut'
            });

            // Main content animation timeline
            const tl = gsap.timeline();

            tl.from('#headline', {
                    y: 100,
                    opacity: 0,
                    duration: 1.2,
                    ease: 'power3.out'
                })
                .to('#subheading', {
                    opacity: 1,
                    y: 0,
                    duration: 0.8,
                    ease: 'power2.out'
                }, '-=0.5')
                .to('#cta', {
                    opacity: 1,
                    y: 0,
                    duration: 0.8,
                    ease: 'power2.out'
                }, '-=0.3')
                .to('#specs', {
                    opacity: 1,
                    y: 0,
                    duration: 0.8,
                    ease: 'power2.out'
                }, '-=0.3')
                .to('#scroll-indicator', {
                    opacity: 1,
                    duration: 0.5,
                    ease: 'power2.out'
                }, '-=0.2');

            // Hover animations for components
            document.querySelectorAll('.floating-component').forEach(component => {
                component.addEventListener('mouseenter', () => {
                    gsap.to(component, {
                        scale: 1.2,
                        rotationY: '+=360',
                        duration: 0.6,
                        ease: 'power2.out'
                    });
                });

                component.addEventListener('mouseleave', () => {
                    gsap.to(component, {
                        scale: 1,
                        duration: 0.4,
                        ease: 'power2.out'
                    });
                });
            });

            // CTA button hover effect
            const ctaButton = document.getElementById('cta');
            ctaButton.addEventListener('mouseenter', () => {
                gsap.to(ctaButton, {
                    boxShadow: '0 0 40px rgba(59, 130, 246, 0.6)',
                    duration: 0.3
                });
            });

            ctaButton.addEventListener('mouseleave', () => {
                gsap.to(ctaButton, {
                    boxShadow: '0 0 20px rgba(59, 130, 246, 0.3)',
                    duration: 0.3
                });
            });

            // Scroll-triggered animations
            ScrollTrigger.create({
                trigger: 'body',
                start: 'top top',
                end: 'bottom top',
                onUpdate: self => {
                    const progress = self.progress;
                    gsap.to('.floating-component', {
                        y: -progress * 200,
                        rotationX: progress * 360,
                        duration: 0.3,
                        ease: 'none'
                    });
                }
            });
        }

        // Initialize everything when DOM is loaded
        document.addEventListener('DOMContentLoaded', () => {
            createParticles();
            initAnimations();
        });

        // Add click handler for CTA
        document.getElementById('cta').addEventListener('click', () => {
            gsap.to(window, {
                scrollTo: window.innerHeight,
                duration: 1,
                ease: 'power2.inOut'
            });
        });
    </script>
@endpush
@push('styles')
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&display=swap');

        body {
            font-family: 'Orbitron', monospace;
            overflow-x: hidden;
        }

        .glow-text {
            text-shadow: 0 0 20px rgba(59, 130, 246, 0.5);
        }

        .neon-border {
            border: 2px solid transparent;
            background: linear-gradient(135deg, #3b82f6, #8b5cf6) padding-box,
                linear-gradient(135deg, #3b82f6, #8b5cf6) border-box;
            animation: pulse-border 2s ease-in-out infinite alternate;
        }

        @keyframes pulse-border {
            0% {
                box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
            }

            100% {
                box-shadow: 0 0 30px rgba(139, 92, 246, 0.5);
            }
        }

        .floating-component {
            position: absolute;
            will-change: transform;
            filter: drop-shadow(0 0 20px rgba(59, 130, 246, 0.3));
        }

        .bg-circuit {
            background-image:
                radial-gradient(circle at 20% 20%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, rgba(139, 92, 246, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 60%, rgba(16, 185, 129, 0.1) 0%, transparent 50%);
        }

        .particle {
            position: absolute;
            width: 2px;
            height: 2px;
            background: linear-gradient(45deg, #3b82f6, #8b5cf6);
            border-radius: 50%;
            animation: float 4s ease-in-out infinite;
        }

        @keyframes float {

            0%,
            100% {
                transform: translateY(0px) rotate(0deg);
            }

            50% {
                transform: translateY(-20px) rotate(180deg);
            }
        }

        .gradient-text {
            background: linear-gradient(135deg, #3b82f6, #8b5cf6, #06b6d4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: gradient-shift 3s ease-in-out infinite;
        }

        @keyframes gradient-shift {

            0%,
            100% {
                background-position: 0% 50%;
            }

            50% {
                background-position: 100% 50%;
            }
        }
    </style>
@endpush
