<?php

namespace App\Services\Payment\Gateways;

use App\Models\GatewaySetting;
use App\Models\Transaction;
use App\Services\Payment\Contracts\PaymentGatewayInterface;
use App\Services\Payment\Exceptions\GatewayConfigurationException;
use App\Services\Payment\Exceptions\InvalidPaymentDataException;
use App\Services\Payment\Exceptions\PaymentGatewayException;
use App\Services\Payment\Exceptions\WebhookVerificationException;
use Illuminate\Support\Facades\Log;
use Razorpay\Api\Api;
use Razorpay\Api\Errors\BadRequestError;
use Razorpay\Api\Errors\ServerError;
use Exception;

class RazorpayService implements PaymentGatewayInterface
{
    private Api $razorpay;
    private ?GatewaySetting $gatewaySetting;
    private array $config;

    public function __construct()
    {
        $this->loadConfiguration();
        $this->initializeRazorpayApi();
    }

    /**
     * Load gateway configuration from database
     */
    private function loadConfiguration(): void
    {
        $this->gatewaySetting = GatewaySetting::byGateway(GatewaySetting::GATEWAY_RAZORPAY)->first();
        
        if (!$this->gatewaySetting) {
            throw new GatewayConfigurationException(
                'Razorpay gateway configuration not found',
                'RAZORPAY_CONFIG_NOT_FOUND'
            );
        }

        // Handle both encrypted and plain JSON settings (for testing)
        $settings = $this->gatewaySetting->settings;
        if (is_string($settings)) {
            $settings = json_decode($settings, true);
        }
        $this->config = $settings ?? [];
        
        if (empty($this->config['key_id']) || empty($this->config['key_secret'])) {
            throw new GatewayConfigurationException(
                'Razorpay API credentials not configured',
                'RAZORPAY_CREDENTIALS_MISSING'
            );
        }
    }

    /**
     * Initialize Razorpay API client
     */
    private function initializeRazorpayApi(): void
    {
        try {
            $this->razorpay = new Api($this->config['key_id'], $this->config['key_secret']);
        } catch (Exception $e) {
            throw new GatewayConfigurationException(
                'Failed to initialize Razorpay API: ' . $e->getMessage(),
                'RAZORPAY_INIT_FAILED'
            );
        }
    }

    /**
     * Create a payment order with Razorpay
     */
    public function createPayment(array $data): array
    {
        $this->validatePaymentData($data);

        try {
            $orderData = [
                'amount' => $data['amount'] * 100, // Convert to paise
                'currency' => $data['currency'] ?? 'INR',
                'receipt' => $data['transaction_id'],
                'notes' => [
                    'user_id' => $data['user_id'],
                    'transaction_id' => $data['transaction_id'],
                ]
            ];

            $order = $this->razorpay->order->create($orderData);

            Log::info('Razorpay order created successfully', [
                'order_id' => $order['id'],
                'transaction_id' => $data['transaction_id'],
                'amount' => $data['amount']
            ]);

            return [
                'success' => true,
                'gateway_order_id' => $order['id'],
                'amount' => $order['amount'],
                'currency' => $order['currency'],
                'key_id' => $this->config['key_id'],
                'order_data' => $order,
                'test_mode' => $this->gatewaySetting->is_test_mode
            ];

        } catch (BadRequestError $e) {
            Log::error('Razorpay bad request error', [
                'error' => $e->getMessage(),
                'transaction_id' => $data['transaction_id'] ?? null
            ]);

            throw new InvalidPaymentDataException(
                'Invalid payment data: ' . $e->getMessage(),
                'RAZORPAY_BAD_REQUEST',
                ['razorpay_error' => $e->getMessage()]
            );

        } catch (ServerError $e) {
            Log::error('Razorpay server error', [
                'error' => $e->getMessage(),
                'transaction_id' => $data['transaction_id'] ?? null
            ]);

            throw new PaymentGatewayException(
                'Razorpay server error: ' . $e->getMessage(),
                'RAZORPAY_SERVER_ERROR',
                ['razorpay_error' => $e->getMessage()]
            );

        } catch (Exception $e) {
            Log::error('Razorpay payment creation failed', [
                'error' => $e->getMessage(),
                'transaction_id' => $data['transaction_id'] ?? null
            ]);

            throw new PaymentGatewayException(
                'Payment creation failed: ' . $e->getMessage(),
                'RAZORPAY_PAYMENT_FAILED',
                ['razorpay_error' => $e->getMessage()]
            );
        }
    }

    /**
     * Verify payment with Razorpay
     */
    public function verifyPayment(string $transactionId, array $data): bool
    {
        try {
            $transaction = Transaction::where('transaction_id', $transactionId)->first();
            
            if (!$transaction) {
                Log::error('Transaction not found for verification', ['transaction_id' => $transactionId]);
                return false;
            }

            // Required fields for verification
            $requiredFields = ['razorpay_order_id', 'razorpay_payment_id', 'razorpay_signature'];
            foreach ($requiredFields as $field) {
                if (empty($data[$field])) {
                    Log::error('Missing required field for payment verification', [
                        'field' => $field,
                        'transaction_id' => $transactionId
                    ]);
                    return false;
                }
            }

            // Verify payment signature
            $attributes = [
                'razorpay_order_id' => $data['razorpay_order_id'],
                'razorpay_payment_id' => $data['razorpay_payment_id'],
                'razorpay_signature' => $data['razorpay_signature']
            ];

            $this->razorpay->utility->verifyPaymentSignature($attributes);

            // Fetch payment details from Razorpay
            $payment = $this->razorpay->payment->fetch($data['razorpay_payment_id']);

            // Update transaction with payment details
            $transaction->update([
                'gateway_transaction_id' => $data['razorpay_payment_id'],
                'status' => $payment['status'] === 'captured' ? Transaction::STATUS_COMPLETED : Transaction::STATUS_FAILED,
                'payment_details' => array_merge($transaction->payment_details ?? [], [
                    'razorpay_order_id' => $data['razorpay_order_id'],
                    'razorpay_payment_id' => $data['razorpay_payment_id'],
                    'razorpay_signature' => $data['razorpay_signature'],
                    'payment_method' => $payment['method'] ?? null,
                    'bank' => $payment['bank'] ?? null,
                    'wallet' => $payment['wallet'] ?? null,
                    'vpa' => $payment['vpa'] ?? null,
                    'verified_at' => now()->toISOString()
                ])
            ]);

            Log::info('Razorpay payment verified successfully', [
                'transaction_id' => $transactionId,
                'razorpay_payment_id' => $data['razorpay_payment_id'],
                'status' => $payment['status']
            ]);

            return $payment['status'] === 'captured';

        } catch (BadRequestError $e) {
            Log::error('Razorpay payment verification failed - bad request', [
                'error' => $e->getMessage(),
                'transaction_id' => $transactionId
            ]);
            return false;

        } catch (Exception $e) {
            Log::error('Razorpay payment verification failed', [
                'error' => $e->getMessage(),
                'transaction_id' => $transactionId
            ]);
            return false;
        }
    }

    /**
     * Handle webhook from Razorpay
     */
    public function handleWebhook(array $payload): array
    {
        try {
            // Verify webhook signature
            $this->verifyWebhookSignature($payload);

            $event = $payload['event'] ?? '';
            $paymentEntity = $payload['payload']['payment']['entity'] ?? [];

            Log::info('Processing Razorpay webhook', [
                'event' => $event,
                'payment_id' => $paymentEntity['id'] ?? null
            ]);

            // Handle different webhook events
            switch ($event) {
                case 'payment.captured':
                    return $this->handlePaymentCaptured($paymentEntity);
                
                case 'payment.failed':
                    return $this->handlePaymentFailed($paymentEntity);
                
                case 'payment.authorized':
                    return $this->handlePaymentAuthorized($paymentEntity);
                
                default:
                    Log::info('Unhandled Razorpay webhook event', ['event' => $event]);
                    return [
                        'success' => true,
                        'message' => 'Event not handled',
                        'event' => $event
                    ];
            }

        } catch (WebhookVerificationException $e) {
            Log::error('Razorpay webhook verification failed', [
                'error' => $e->getMessage(),
                'payload' => $payload
            ]);
            throw $e;

        } catch (Exception $e) {
            Log::error('Razorpay webhook processing failed', [
                'error' => $e->getMessage(),
                'payload' => $payload
            ]);

            throw new PaymentGatewayException(
                'Webhook processing failed: ' . $e->getMessage(),
                'RAZORPAY_WEBHOOK_FAILED',
                ['error' => $e->getMessage()]
            );
        }
    }

    /**
     * Get payment status from Razorpay
     */
    public function getPaymentStatus(string $transactionId): string
    {
        try {
            $transaction = Transaction::where('transaction_id', $transactionId)->first();
            
            if (!$transaction || !$transaction->gateway_transaction_id) {
                return Transaction::STATUS_PENDING;
            }

            $payment = $this->razorpay->payment->fetch($transaction->gateway_transaction_id);
            
            return match($payment['status']) {
                'captured' => Transaction::STATUS_COMPLETED,
                'failed' => Transaction::STATUS_FAILED,
                'authorized' => Transaction::STATUS_PROCESSING,
                default => Transaction::STATUS_PENDING
            };

        } catch (Exception $e) {
            Log::error('Failed to get payment status from Razorpay', [
                'error' => $e->getMessage(),
                'transaction_id' => $transactionId
            ]);
            
            return Transaction::STATUS_PENDING;
        }
    }

    /**
     * Get gateway name
     */
    public function getGatewayName(): string
    {
        return GatewaySetting::GATEWAY_RAZORPAY;
    }

    /**
     * Check if gateway is enabled
     */
    public function isEnabled(): bool
    {
        return $this->gatewaySetting->is_enabled;
    }

    /**
     * Validate payment data
     */
    private function validatePaymentData(array $data): void
    {
        $requiredFields = ['amount', 'currency', 'user_id', 'transaction_id'];
        
        foreach ($requiredFields as $field) {
            if (!isset($data[$field]) || empty($data[$field])) {
                throw new InvalidPaymentDataException(
                    "Missing required field: {$field}",
                    'MISSING_REQUIRED_FIELD',
                    ['field' => $field]
                );
            }
        }

        if (!is_numeric($data['amount']) || $data['amount'] <= 0) {
            throw new InvalidPaymentDataException(
                'Amount must be a positive number',
                'INVALID_AMOUNT',
                ['amount' => $data['amount']]
            );
        }

        if (strlen($data['currency']) !== 3) {
            throw new InvalidPaymentDataException(
                'Currency must be a 3-character code',
                'INVALID_CURRENCY',
                ['currency' => $data['currency']]
            );
        }
    }

    /**
     * Verify webhook signature
     */
    private function verifyWebhookSignature(array $payload): void
    {
        $webhookSecret = $this->config['webhook_secret'] ?? '';
        
        if (empty($webhookSecret)) {
            throw new WebhookVerificationException(
                'Webhook secret not configured',
                'WEBHOOK_SECRET_MISSING'
            );
        }

        $webhookSignature = $_SERVER['HTTP_X_RAZORPAY_SIGNATURE'] ?? '';
        
        if (empty($webhookSignature)) {
            throw new WebhookVerificationException(
                'Webhook signature missing',
                'WEBHOOK_SIGNATURE_MISSING'
            );
        }

        $webhookBody = json_encode($payload);
        $expectedSignature = hash_hmac('sha256', $webhookBody, $webhookSecret);

        if (!hash_equals($expectedSignature, $webhookSignature)) {
            throw new WebhookVerificationException(
                'Webhook signature verification failed',
                'WEBHOOK_SIGNATURE_INVALID'
            );
        }
    }

    /**
     * Handle payment captured webhook
     */
    private function handlePaymentCaptured(array $paymentEntity): array
    {
        $paymentId = $paymentEntity['id'];
        $orderId = $paymentEntity['order_id'] ?? '';

        $transaction = Transaction::where('gateway_transaction_id', $paymentId)
            ->orWhere(function($query) use ($orderId) {
                $query->whereJsonContains('payment_details->razorpay_order_id', $orderId);
            })
            ->first();

        if (!$transaction) {
            Log::warning('Transaction not found for captured payment', [
                'payment_id' => $paymentId,
                'order_id' => $orderId
            ]);
            return ['success' => false, 'message' => 'Transaction not found'];
        }

        $transaction->update([
            'status' => Transaction::STATUS_COMPLETED,
            'gateway_transaction_id' => $paymentId,
            'webhook_verified' => true,
            'payment_details' => array_merge($transaction->payment_details ?? [], [
                'webhook_captured_at' => now()->toISOString(),
                'payment_method' => $paymentEntity['method'] ?? null,
                'amount_captured' => $paymentEntity['amount'] ?? null
            ])
        ]);

        Log::info('Payment captured via webhook', [
            'transaction_id' => $transaction->transaction_id,
            'payment_id' => $paymentId
        ]);

        return ['success' => true, 'message' => 'Payment captured'];
    }

    /**
     * Handle payment failed webhook
     */
    private function handlePaymentFailed(array $paymentEntity): array
    {
        $paymentId = $paymentEntity['id'];
        $orderId = $paymentEntity['order_id'] ?? '';

        $transaction = Transaction::where('gateway_transaction_id', $paymentId)
            ->orWhere(function($query) use ($orderId) {
                $query->whereJsonContains('payment_details->razorpay_order_id', $orderId);
            })
            ->first();

        if (!$transaction) {
            Log::warning('Transaction not found for failed payment', [
                'payment_id' => $paymentId,
                'order_id' => $orderId
            ]);
            return ['success' => false, 'message' => 'Transaction not found'];
        }

        $transaction->update([
            'status' => Transaction::STATUS_FAILED,
            'gateway_transaction_id' => $paymentId,
            'webhook_verified' => true,
            'failure_reason' => $paymentEntity['error_description'] ?? 'Payment failed',
            'payment_details' => array_merge($transaction->payment_details ?? [], [
                'webhook_failed_at' => now()->toISOString(),
                'error_code' => $paymentEntity['error_code'] ?? null,
                'error_description' => $paymentEntity['error_description'] ?? null
            ])
        ]);

        Log::info('Payment failed via webhook', [
            'transaction_id' => $transaction->transaction_id,
            'payment_id' => $paymentId,
            'error' => $paymentEntity['error_description'] ?? 'Unknown error'
        ]);

        return ['success' => true, 'message' => 'Payment failure processed'];
    }

    /**
     * Handle payment authorized webhook
     */
    private function handlePaymentAuthorized(array $paymentEntity): array
    {
        $paymentId = $paymentEntity['id'];
        $orderId = $paymentEntity['order_id'] ?? '';

        $transaction = Transaction::where('gateway_transaction_id', $paymentId)
            ->orWhere(function($query) use ($orderId) {
                $query->whereJsonContains('payment_details->razorpay_order_id', $orderId);
            })
            ->first();

        if (!$transaction) {
            Log::warning('Transaction not found for authorized payment', [
                'payment_id' => $paymentId,
                'order_id' => $orderId
            ]);
            return ['success' => false, 'message' => 'Transaction not found'];
        }

        $transaction->update([
            'status' => Transaction::STATUS_PROCESSING,
            'gateway_transaction_id' => $paymentId,
            'webhook_verified' => true,
            'payment_details' => array_merge($transaction->payment_details ?? [], [
                'webhook_authorized_at' => now()->toISOString(),
                'payment_method' => $paymentEntity['method'] ?? null
            ])
        ]);

        Log::info('Payment authorized via webhook', [
            'transaction_id' => $transaction->transaction_id,
            'payment_id' => $paymentId
        ]);

        return ['success' => true, 'message' => 'Payment authorization processed'];
    }
}