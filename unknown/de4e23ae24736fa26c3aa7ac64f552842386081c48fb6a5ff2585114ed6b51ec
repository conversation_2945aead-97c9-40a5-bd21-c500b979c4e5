<?php

namespace Tests\Feature;

use App\Livewire\Builder\ComponentSelector;
use App\Livewire\Builder\CompatibilityChecker;
use App\Livewire\Builder\BuildSummary;
use App\Models\Component;
use App\Models\ComponentCategory;
use App\Models\Build;
use App\Models\User;
use App\Services\CompatibilityService;
use App\Services\CompatibilityResult;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Livewire\Livewire;
use Tests\TestCase;

beforeEach(function () {
    // Create test categories
    $this->motherboardCategory = ComponentCategory::factory()->create([
        'name' => 'Motherboard',
        'slug' => 'motherboard',
        'is_required' => true,
        'display_order' => 1,
    ]);
    
    $this->cpuCategory = ComponentCategory::factory()->create([
        'name' => 'CPU',
        'slug' => 'cpu',
        'is_required' => true,
        'display_order' => 2,
    ]);
    
    // Create test components
    $this->motherboard1 = Component::factory()->create([
        'category_id' => $this->motherboardCategory->id,
        'name' => 'ASUS ROG Strix B550-F',
        'brand' => 'ASUS',
        'price' => 189.99,
        'stock' => 10,
        'is_active' => true,
    ]);
    
    $this->motherboard2 = Component::factory()->create([
        'category_id' => $this->motherboardCategory->id,
        'name' => 'MSI B450 Tomahawk',
        'brand' => 'MSI',
        'price' => 99.99,
        'stock' => 5,
        'is_active' => true,
    ]);
    
    $this->cpu = Component::factory()->create([
        'category_id' => $this->cpuCategory->id,
        'name' => 'AMD Ryzen 5 5600X',
        'brand' => 'AMD',
        'price' => 299.99,
        'stock' => 3,
        'is_active' => true,
    ]);
    
    $this->outOfStockComponent = Component::factory()->create([
        'category_id' => $this->motherboardCategory->id,
        'name' => 'Out of Stock Motherboard',
        'brand' => 'Generic',
        'price' => 150.00,
        'stock' => 0,
        'is_active' => true,
    ]);
});

it('component selector displays components for category', function () {
    Livewire::test(ComponentSelector::class, [
        'categorySlug' => 'motherboard'
    ])
        ->assertSee('ASUS ROG Strix B550-F')
        ->assertSee('MSI B450 Tomahawk')
        ->assertSee('$189.99')
        ->assertSee('$99.99');
});

it('component selector can search components', function () {
    Livewire::test(ComponentSelector::class, [
        'categorySlug' => 'motherboard'
    ])
        ->set('search', 'ASUS')
        ->assertSee('ASUS ROG Strix B550-F')
        ->assertDontSee('MSI B450 Tomahawk');
});

it('component selector can filter by brand', function () {
    Livewire::test(ComponentSelector::class, [
        'categorySlug' => 'motherboard'
    ])
        ->set('selectedBrands', ['MSI'])
        ->assertSee('MSI B450 Tomahawk')
        ->assertDontSee('ASUS ROG Strix B550-F');
});

it('component selector can filter by price range', function () {
    Livewire::test(ComponentSelector::class, [
        'categorySlug' => 'motherboard'
    ])
        ->set('priceMin', 100)
        ->set('priceMax', 200)
        ->assertSee('ASUS ROG Strix B550-F')
        ->assertDontSee('MSI B450 Tomahawk');
});

it('component selector can filter in stock only', function () {
    Livewire::test(ComponentSelector::class, [
        'categorySlug' => 'motherboard'
    ])
        ->set('inStockOnly', true)
        ->assertSee('MSI B450 Tomahawk')
        ->assertDontSee('Out of Stock Motherboard');
});

it('component selector can sort components', function () {
    Livewire::test(ComponentSelector::class, [
        'categorySlug' => 'motherboard'
    ])
        ->set('sortBy', 'price')
        ->set('sortDirection', 'asc')
        ->assertSeeInOrder(['MSI B450 Tomahawk', 'ASUS ROG Strix B550-F']);
});

it('component selector can select component', function () {
    Livewire::test(ComponentSelector::class, [
        'categorySlug' => 'motherboard'
    ])
        ->call('selectComponent', $this->motherboard1->id)
        ->assertSet('selectedComponentId', $this->motherboard1->id);
});

it('component selector shows stock information', function () {
    Livewire::test(ComponentSelector::class, [
        'categorySlug' => 'motherboard'
    ])
        ->assertSee('In Stock (10)')
        ->assertSee('In Stock (5)')
        ->assertSee('Out of Stock');
});

it('component selector disables out of stock components', function () {
    Livewire::test(ComponentSelector::class, [
        'categorySlug' => 'motherboard'
    ])
        ->assertSeeHtml('disabled');
});

it('compatibility checker shows unknown status with no components', function () {
    Livewire::test(CompatibilityChecker::class)
        ->assertSee('Add more components to check compatibility')
        ->assertSeeHtml('text-gray-400');
});

it('compatibility checker shows compatible status', function () {
    // Mock the CompatibilityService to return a compatible result
    $mockService = \Mockery::mock(CompatibilityService::class);
    $mockService->shouldReceive('checkCompatibility')
        ->andReturn(new CompatibilityResult([
            'compatible' => true,
            'has_errors' => false,
            'has_warnings' => false,
            'issues' => [],
            'warnings' => [],
            'error_messages' => [],
            'warning_messages' => [],
            'components' => collect([$this->motherboard1, $this->cpu]),
        ]));
    
    $this->app->instance(CompatibilityService::class, $mockService);

    Livewire::test(CompatibilityChecker::class, [
        'components' => [$this->motherboard1->id, $this->cpu->id]
    ])
        ->assertSee('All components are compatible')
        ->assertSeeHtml('text-green-600');
});