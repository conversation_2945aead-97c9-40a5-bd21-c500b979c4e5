<?php

namespace Tests\Feature;

use App\Jobs\SendOrderConfirmation;
use App\Jobs\SendBuildShared;
use App\Jobs\SendOrderStatusUpdate;
use App\Mail\OrderConfirmation;
use App\Mail\BuildShared;
use App\Mail\OrderStatusUpdate;
use App\Models\Order;
use App\Models\Build;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Queue;
use Tests\TestCase;

class EmailNotificationTest extends TestCase
{
    use RefreshDatabase;

    public function test_order_confirmation_email_is_queued_and_sent()
    {
        Mail::fake();
        Queue::fake();
        $user = User::factory()->create();
        $order = Order::factory()->for($user)->create();
        SendOrderConfirmation::dispatch($order);
        Queue::assertPushed(SendOrderConfirmation::class);
        Mail::assertNothingSent();
        (new SendOrderConfirmation($order))->handle(app(\App\Services\EmailTrackingService::class));
        Mail::assertQueued(OrderConfirmation::class);
    }

    public function test_build_shared_email_is_queued_and_sent()
    {
        Mail::fake();
        Queue::fake();
        $user = User::factory()->create();
        $build = Build::factory()->for($user)->create();
        $recipient = '<EMAIL>';
        SendBuildShared::dispatch($build, $recipient);
        Queue::assertPushed(SendBuildShared::class);
        (new SendBuildShared($build, $recipient))->handle();
        Mail::assertQueued(BuildShared::class);
    }

    public function test_order_status_update_email_is_queued_and_sent()
    {
        Mail::fake();
        Queue::fake();
        $user = User::factory()->create();
        $order = Order::factory()->for($user)->create(['status' => 'processing']);
        SendOrderStatusUpdate::dispatch($order, 'pending');
        Queue::assertPushed(SendOrderStatusUpdate::class);
        (new SendOrderStatusUpdate($order, 'pending'))->handle(app(\App\Services\EmailTrackingService::class));
        Mail::assertQueued(OrderStatusUpdate::class);
    }
} 