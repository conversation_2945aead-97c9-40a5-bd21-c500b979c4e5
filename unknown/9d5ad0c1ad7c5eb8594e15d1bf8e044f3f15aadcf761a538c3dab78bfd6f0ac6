<?php

namespace App\Services;

use App\Models\Component;
use App\Models\StockMovement;
use App\Models\InventoryAlert;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Exception;

class InventoryService
{
    protected int $lowStockThreshold = 10;
    protected int $outOfStockThreshold = 0;

    public function updateStock(Component $component, int $quantity, string $reason = 'manual'): bool
    {
        try {
            $previousStock = $component->stock;
            
            DB::transaction(function () use ($component, $quantity, $reason, $previousStock) {
                $component->update(['stock' => $quantity]);
                
                $this->logStockChange($component, $previousStock, $quantity, $reason);
                
                // Check for low stock alerts
                if ($quantity <= $this->lowStockThreshold && $previousStock > $this->lowStockThreshold) {
                    $this->triggerLowStockAlert($component);
                }
                
                // Check for out of stock alerts
                if ($quantity <= $this->outOfStockThreshold && $previousStock > $this->outOfStockThreshold) {
                    $this->triggerOutOfStockAlert($component);
                }
            });
            
            return true;
        } catch (Exception $e) {
            Log::error("Failed to update stock for component {$component->id}: " . $e->getMessage());
            return false;
        }
    }

    public function adjustStock(Component $component, int $adjustment, string $reason = 'adjustment'): bool
    {
        $newStock = max(0, $component->stock + $adjustment);
        return $this->updateStock($component, $newStock, $reason);
    }

    public function reserveStock(Component $component, int $quantity): bool
    {
        if ($component->stock < $quantity) {
            return false;
        }
        
        return $this->adjustStock($component, -$quantity, 'reserved');
    }

    public function releaseStock(Component $component, int $quantity): bool
    {
        return $this->adjustStock($component, $quantity, 'released');
    }

    public function getLowStockComponents(): Collection
    {
        return Component::where('is_active', true)
            ->where('stock', '>', $this->outOfStockThreshold)
            ->where('stock', '<=', $this->lowStockThreshold)
            ->with('category')
            ->get();
    }

    public function getOutOfStockComponents(): Collection
    {
        return Component::where('is_active', true)
            ->where('stock', '<=', $this->outOfStockThreshold)
            ->with('category')
            ->get();
    }

    public function getInventoryReport(): array
    {
        $totalComponents = Component::where('is_active', true)->count();
        $inStockComponents = Component::where('is_active', true)
            ->where('stock', '>', $this->lowStockThreshold)
            ->count();
        $lowStockComponents = $this->getLowStockComponents()->count();
        $outOfStockComponents = $this->getOutOfStockComponents()->count();
        
        $totalValue = Component::where('is_active', true)
            ->selectRaw('SUM(price * stock) as total_value')
            ->value('total_value') ?? 0;

        return [
            'total_components' => $totalComponents,
            'in_stock' => $inStockComponents,
            'low_stock' => $lowStockComponents,
            'out_of_stock' => $outOfStockComponents,
            'total_inventory_value' => round($totalValue, 2),
            'stock_levels' => [
                'healthy' => $inStockComponents,
                'warning' => $lowStockComponents,
                'critical' => $outOfStockComponents
            ]
        ];
    }

    public function getComponentsByStockLevel(string $level): Collection
    {
        return match ($level) {
            'low' => $this->getLowStockComponents(),
            'out' => $this->getOutOfStockComponents(),
            'healthy' => Component::where('is_active', true)
                ->where('stock', '>', $this->lowStockThreshold)
                ->with('category')
                ->get(),
            default => collect()
        };
    }

    public function bulkUpdateStock(array $updates): array
    {
        $results = [
            'updated' => 0,
            'failed' => 0,
            'errors' => []
        ];

        foreach ($updates as $update) {
            try {
                $component = Component::findOrFail($update['component_id']);
                $success = $this->updateStock(
                    $component,
                    $update['stock'],
                    $update['reason'] ?? 'bulk_update'
                );
                
                if ($success) {
                    $results['updated']++;
                } else {
                    $results['failed']++;
                    $results['errors'][] = "Failed to update component {$component->id}";
                }
            } catch (Exception $e) {
                $results['failed']++;
                $results['errors'][] = "Component {$update['component_id']}: " . $e->getMessage();
            }
        }

        return $results;
    }

    public function syncWithSupplier(array $supplierData): array
    {
        $results = [
            'updated' => 0,
            'new' => 0,
            'failed' => 0,
            'errors' => []
        ];

        foreach ($supplierData as $item) {
            try {
                $component = Component::where('model', $item['model'])
                    ->where('brand', $item['brand'])
                    ->first();

                if ($component) {
                    // Update existing component
                    $this->updateStock($component, $item['stock'], 'supplier_sync');
                    
                    // Update price if provided
                    if (isset($item['price']) && $item['price'] != $component->price) {
                        $component->update(['price' => $item['price']]);
                    }
                    
                    $results['updated']++;
                } else {
                    // This would create new components in a real implementation
                    // For now, we'll just log it
                    Log::info("New component from supplier: {$item['brand']} {$item['model']}");
                    $results['new']++;
                }
            } catch (Exception $e) {
                $results['failed']++;
                $results['errors'][] = "Item {$item['model']}: " . $e->getMessage();
            }
        }

        return $results;
    }

    public function getStockMovementHistory(Component $component, int $days = 30): Collection
    {
        return StockMovement::forComponent($component->id)
            ->recent($days)
            ->orderBy('created_at', 'desc')
            ->get();
    }

    public function getInventoryAlerts(bool $unresolvedOnly = true): Collection
    {
        $query = InventoryAlert::with('component.category');
        
        if ($unresolvedOnly) {
            $query->unresolved();
        }
        
        return $query->orderBy('created_at', 'desc')->get();
    }

    public function resolveAlert(InventoryAlert $alert): bool
    {
        try {
            $alert->resolve();
            return true;
        } catch (Exception $e) {
            Log::error("Failed to resolve alert {$alert->id}: " . $e->getMessage());
            return false;
        }
    }

    public function getInventoryAnalytics(int $days = 30): array
    {
        $movements = StockMovement::recent($days)->get();
        $alerts = InventoryAlert::recent($days)->get();
        
        return [
            'total_movements' => $movements->count(),
            'movements_by_type' => $movements->groupBy('type')->map->count(),
            'total_alerts' => $alerts->count(),
            'alerts_by_type' => $alerts->groupBy('type')->map->count(),
            'most_active_components' => $this->getMostActiveComponents($days),
            'stock_trends' => $this->getStockTrends($days),
            'alert_resolution_rate' => $this->getAlertResolutionRate($days)
        ];
    }

    public function getMostActiveComponents(int $days = 30): Collection
    {
        return StockMovement::recent($days)
            ->select('component_id', DB::raw('COUNT(*) as movement_count'))
            ->groupBy('component_id')
            ->orderBy('movement_count', 'desc')
            ->limit(10)
            ->with('component')
            ->get();
    }

    public function getStockTrends(int $days = 30): array
    {
        $movements = StockMovement::recent($days)
            ->selectRaw('DATE(created_at) as date, SUM(quantity_change) as net_change')
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        return [
            'daily_changes' => $movements->pluck('net_change', 'date')->toArray(),
            'trend_direction' => $this->calculateTrendDirection($movements)
        ];
    }

    public function getAlertResolutionRate(int $days = 30): float
    {
        $totalAlerts = InventoryAlert::recent($days)->count();
        $resolvedAlerts = InventoryAlert::recent($days)->where('is_resolved', true)->count();
        
        return $totalAlerts > 0 ? round(($resolvedAlerts / $totalAlerts) * 100, 2) : 0;
    }

    public function setLowStockThreshold(int $threshold): void
    {
        $this->lowStockThreshold = $threshold;
    }

    public function setOutOfStockThreshold(int $threshold): void
    {
        $this->outOfStockThreshold = $threshold;
    }

    protected function determineMovementType(string $reason, int $quantityChange): string
    {
        return match ($reason) {
            'order_fulfillment', 'sale' => 'sale',
            'supplier_sync', 'supplier_delivery' => 'supplier_sync',
            'return' => 'return',
            'damage', 'damaged' => 'damage',
            'restock', 'restocked' => 'restock',
            default => $quantityChange > 0 ? 'restock' : 'adjustment'
        };
    }

    protected function calculateTrendDirection(Collection $movements): string
    {
        if ($movements->count() < 2) {
            return 'stable';
        }

        $recent = $movements->take(-7)->avg('net_change');
        $previous = $movements->take(-14)->skip(-7)->avg('net_change');

        if ($recent > $previous * 1.1) {
            return 'increasing';
        } elseif ($recent < $previous * 0.9) {
            return 'decreasing';
        }

        return 'stable';
    }

    protected function logStockChange(Component $component, int $previousStock, int $newStock, string $reason): void
    {
        $quantityChange = $newStock - $previousStock;
        
        // Create stock movement record
        StockMovement::create([
            'component_id' => $component->id,
            'quantity_change' => $quantityChange,
            'previous_stock' => $previousStock,
            'new_stock' => $newStock,
            'type' => $this->determineMovementType($reason, $quantityChange),
            'reason' => $reason,
            'metadata' => [
                'component_name' => $component->name,
                'timestamp' => now()->toISOString()
            ]
        ]);

        Log::info("Stock updated for component {$component->id}", [
            'component_name' => $component->name,
            'previous_stock' => $previousStock,
            'new_stock' => $newStock,
            'change' => $quantityChange,
            'reason' => $reason
        ]);
    }

    protected function triggerLowStockAlert(Component $component): void
    {
        // Create inventory alert
        InventoryAlert::create([
            'component_id' => $component->id,
            'type' => 'low_stock',
            'current_stock' => $component->stock,
            'threshold' => $this->lowStockThreshold,
            'metadata' => [
                'component_name' => $component->name,
                'category' => $component->category->name ?? 'Unknown'
            ]
        ]);

        Log::warning("Low stock alert for component {$component->id}", [
            'component_name' => $component->name,
            'current_stock' => $component->stock,
            'threshold' => $this->lowStockThreshold
        ]);
    }

    protected function triggerOutOfStockAlert(Component $component): void
    {
        // Create inventory alert
        InventoryAlert::create([
            'component_id' => $component->id,
            'type' => 'out_of_stock',
            'current_stock' => $component->stock,
            'threshold' => $this->outOfStockThreshold,
            'metadata' => [
                'component_name' => $component->name,
                'category' => $component->category->name ?? 'Unknown'
            ]
        ]);

        Log::critical("Out of stock alert for component {$component->id}", [
            'component_name' => $component->name,
            'current_stock' => $component->stock
        ]);
    }
}