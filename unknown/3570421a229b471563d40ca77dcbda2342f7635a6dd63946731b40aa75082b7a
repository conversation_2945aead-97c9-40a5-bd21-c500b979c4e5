@extends('layouts.app')
@section('content')
    <div class="bg-gray-900 text-white overflow-x-hidden">
        <div x-data="buildJourney()" x-init="init()" @scroll.window="updateProgress()" class="min-h-screen relative">
            <!-- Progress Bar -->
            <div class="fixed top-0 left-0 w-full h-1 bg-gray-800 z-50">
                <div class="h-full progress-bar transition-all duration-300" :style="`width: ${scrollProgress}%`"></div>
            </div>

            <!-- Hero Section -->
            <section class="relative min-h-screen flex items-center justify-center gradient-bg overflow-hidden">
                <!-- Floating Background Elements -->
                <div class="absolute inset-0 overflow-hidden">
                    <div
                        class="absolute top-1/4 left-1/4 w-64 h-64 bg-purple-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 floating-animation">
                    </div>
                    <div class="absolute top-3/4 right-1/4 w-72 h-72 bg-blue-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 floating-animation"
                        style="animation-delay: 2s;"></div>
                    <div class="absolute bottom-1/4 left-1/3 w-56 h-56 bg-indigo-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 floating-animation"
                        style="animation-delay: 4s;"></div>
                </div>

                <div class="container mx-auto px-6 relative z-10">
                    <div class="grid lg:grid-cols-2 gap-12 items-center">
                        <!-- Left Side - Main Content -->
                        <div class="space-y-8 slide-in-left">
                            <div class="space-y-4">
                                <h1 class="text-6xl lg:text-7xl font-bold leading-tight">
                                    <span class="block text-white">Your Build,</span>
                                    <span
                                        class="block bg-gradient-to-r from-cyan-400 to-purple-400 bg-clip-text text-transparent">Your
                                        Journey</span>
                                </h1>
                                <p class="text-xl text-gray-200 max-w-lg leading-relaxed">
                                    From dreaming of the perfect setup to powering on your custom masterpiece. Every
                                    component tells a story, every cable has purpose.
                                </p>
                            </div>

                            <!-- CTA Buttons -->
                            <div class="flex flex-wrap gap-4">
                                <button
                                    class="bg-white text-gray-900 px-8 py-4 rounded-lg font-semibold hover:bg-gray-100 transition-all duration-300 glow-effect hover:scale-105">
                                    Explore Build Logs
                                </button>
                                <button
                                    class="border-2 border-white text-white px-8 py-4 rounded-lg font-semibold hover:bg-white hover:text-gray-900 transition-all duration-300 hover:scale-105">
                                    Start from Scratch
                                </button>
                            </div>

                            <!-- Quick Stats -->
                            <div class="grid grid-cols-3 gap-6 pt-8">
                                <div class="text-center">
                                    <div class="text-3xl font-bold text-cyan-400">500+</div>
                                    <div class="text-sm text-gray-300">Builds Completed</div>
                                </div>
                                <div class="text-center">
                                    <div class="text-3xl font-bold text-purple-400">24/7</div>
                                    <div class="text-sm text-gray-300">Expert Support</div>
                                </div>
                                <div class="text-center">
                                    <div class="text-3xl font-bold text-blue-400">∞</div>
                                    <div class="text-sm text-gray-300">Possibilities</div>
                                </div>
                            </div>
                        </div>

                        <!-- Right Side - Interactive Timeline -->
                        <div class="space-y-6 slide-in-right">
                            <div class="text-center mb-8">
                                <h3 class="text-2xl font-bold text-white mb-2">Build Journey</h3>
                                <div class="w-full bg-gray-700 rounded-full h-2">
                                    <div class="progress-bar h-2 rounded-full transition-all duration-500"
                                        :style="`width: ${(currentStep / (steps.length - 1)) * 100}%`"></div>
                                </div>
                            </div>

                            <!-- Timeline Steps -->
                            <div class="space-y-4">
                                <template x-for="(step, index) in steps" :key="index">
                                    <div class="component-card p-6 rounded-xl cursor-pointer transition-all duration-300 hover:scale-105"
                                        :class="{ 'pulse-glow': currentStep === index, 'opacity-50': currentStep < index }"
                                        @click="setStep(index)">
                                        <div class="flex items-center space-x-4">
                                            <div class="w-12 h-12 rounded-full flex items-center justify-center text-2xl transition-all duration-300"
                                                :class="currentStep >= index ?
                                                    'bg-gradient-to-r from-cyan-400 to-purple-400 text-white' :
                                                    'bg-gray-600 text-gray-400'">
                                                <span x-text="step.icon"></span>
                                            </div>
                                            <div class="flex-1">
                                                <h4 class="font-semibold text-lg" x-text="step.title"></h4>
                                                <p class="text-gray-300 text-sm" x-text="step.description"></p>
                                            </div>
                                            <div class="text-right"
                                                :class="currentStep >= index ? 'text-cyan-400' : 'text-gray-500'">
                                                <div class="text-sm font-medium" x-text="step.status"></div>
                                            </div>
                                        </div>
                                    </div>
                                </template>
                            </div>

                            <!-- Auto-advance Toggle -->
                            <div class="flex items-center justify-center space-x-3 pt-4">
                                <label class="flex items-center space-x-2 cursor-pointer">
                                    <input type="checkbox" x-model="autoAdvance"
                                        class="w-4 h-4 text-purple-600 bg-gray-700 border-gray-600 rounded focus:ring-purple-500">
                                    <span class="text-sm text-gray-300">Auto-advance journey</span>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Scroll Indicator -->
                <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 text-center fade-in">
                    <div class="text-sm text-gray-300 mb-2">Scroll to explore</div>
                    <div class="w-6 h-10 border-2 border-white rounded-full flex justify-center">
                        <div class="w-1 h-3 bg-white rounded-full mt-2 animate-bounce"></div>
                    </div>
                </div>
            </section>

            <!-- Additional Content Section -->
            <section class="py-20 bg-gray-900">
                <div class="container mx-auto px-6">
                    <div class="text-center mb-16">
                        <h2 class="text-4xl font-bold mb-4">Ready to Start Your Journey?</h2>
                        <p class="text-xl text-gray-400">Join thousands of builders who've brought their dream PCs to life
                        </p>
                    </div>

                    <div class="grid md:grid-cols-3 gap-8">
                        <div class="bg-gray-800 p-8 rounded-xl text-center hover:bg-gray-700 transition-all duration-300">
                            <div class="text-4xl mb-4">🎯</div>
                            <h3 class="text-xl font-semibold mb-2">Choose Your Path</h3>
                            <p class="text-gray-400">Gaming, workstation, or content creation - we've got you covered</p>
                        </div>

                        <div class="bg-gray-800 p-8 rounded-xl text-center hover:bg-gray-700 transition-all duration-300">
                            <div class="text-4xl mb-4">🔧</div>
                            <h3 class="text-xl font-semibold mb-2">Expert Guidance</h3>
                            <p class="text-gray-400">Step-by-step tutorials and 24/7 support from our team</p>
                        </div>

                        <div class="bg-gray-800 p-8 rounded-xl text-center hover:bg-gray-700 transition-all duration-300">
                            <div class="text-4xl mb-4">🚀</div>
                            <h3 class="text-xl font-semibold mb-2">Launch Your Build</h3>
                            <p class="text-gray-400">From components to completion in record time</p>
                        </div>
                    </div>
                </div>
            </section>
        </div>

    </div>
@endsection
@push('scripts')
    <script>
        function buildJourney() {
            return {
                currentStep: 0,
                scrollProgress: 0,
                autoAdvance: true,
                autoAdvanceInterval: null,
                steps: [{
                        icon: '🎯',
                        title: 'Planning & Research',
                        description: 'Define your needs and budget',
                        status: 'Complete'
                    },
                    {
                        icon: '🔧',
                        title: 'Component Selection',
                        description: 'Choose the perfect parts',
                        status: 'In Progress'
                    },
                    {
                        icon: '📦',
                        title: 'Parts Arrival',
                        description: 'Unboxing the magic',
                        status: 'Pending'
                    },
                    {
                        icon: '⚡',
                        title: 'Assembly Process',
                        description: 'Building your masterpiece',
                        status: 'Pending'
                    },
                    {
                        icon: '🌈',
                        title: 'RGB & Finishing',
                        description: 'Adding the final touches',
                        status: 'Pending'
                    },
                    {
                        icon: '🚀',
                        title: 'First Boot',
                        description: 'The moment of truth',
                        status: 'Pending'
                    }
                ],

                init() {
                    this.startAutoAdvance();
                },

                setStep(index) {
                    this.currentStep = index;
                    this.updateStepStatus();
                },

                updateStepStatus() {
                    this.steps.forEach((step, index) => {
                        if (index < this.currentStep) {
                            step.status = 'Complete';
                        } else if (index === this.currentStep) {
                            step.status = 'In Progress';
                        } else {
                            step.status = 'Pending';
                        }
                    });
                },

                startAutoAdvance() {
                    if (this.autoAdvance) {
                        this.autoAdvanceInterval = setInterval(() => {
                            if (this.autoAdvance) {
                                this.currentStep = (this.currentStep + 1) % this.steps.length;
                                this.updateStepStatus();
                            }
                        }, 3000);
                    }
                },

                stopAutoAdvance() {
                    if (this.autoAdvanceInterval) {
                        clearInterval(this.autoAdvanceInterval);
                        this.autoAdvanceInterval = null;
                    }
                },

                updateProgress() {
                    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
                    const scrollHeight = document.documentElement.scrollHeight - window.innerHeight;
                    this.scrollProgress = (scrollTop / scrollHeight) * 100;
                },

                $watch: {
                    autoAdvance(newVal) {
                        if (newVal) {
                            this.startAutoAdvance();
                        } else {
                            this.stopAutoAdvance();
                        }
                    }
                }
            }
        }
    </script>
@endpush
@push('styles')
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

        body {
            font-family: 'Inter', sans-serif;
        }

        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .glow-effect {
            box-shadow: 0 0 20px rgba(102, 126, 234, 0.3);
        }

        .component-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .progress-bar {
            background: linear-gradient(90deg, #667eea, #764ba2);
        }

        .floating-animation {
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {

            0%,
            100% {
                transform: translateY(0px);
            }

            50% {
                transform: translateY(-10px);
            }
        }

        .pulse-glow {
            animation: pulse-glow 2s ease-in-out infinite alternate;
        }

        @keyframes pulse-glow {
            from {
                box-shadow: 0 0 20px rgba(102, 126, 234, 0.4);
            }

            to {
                box-shadow: 0 0 30px rgba(102, 126, 234, 0.8);
            }
        }

        .fade-in {
            animation: fadeIn 0.6s ease-out forwards;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .slide-in-left {
            animation: slideInLeft 0.8s ease-out forwards;
        }

        @keyframes slideInLeft {
            from {
                opacity: 0;
                transform: translateX(-50px);
            }

            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .slide-in-right {
            animation: slideInRight 0.8s ease-out forwards;
        }

        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(50px);
            }

            to {
                opacity: 1;
                transform: translateX(0);
            }
        }
    </style>
@endpush
