<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Symfony\Component\HttpFoundation\Response;

class WebhookSecurityFilter
{
    /**
     * Suspicious patterns that might indicate malicious attempts
     */
    protected array $suspiciousPatterns = [
        'script',
        'javascript',
        'eval(',
        'exec(',
        'system(',
        'shell_exec',
        'passthru',
        'base64_decode',
        'file_get_contents',
        'curl_exec',
        'wget',
        'union select',
        'drop table',
        'insert into',
        'update set',
        'delete from',
        '../',
        '..\\',
        '/etc/passwd',
        '/proc/',
        'cmd.exe',
        'powershell'
    ];
    
    /**
     * Maximum allowed payload size (in bytes)
     */
    protected int $maxPayloadSize = 1048576; // 1MB
    
    /**
     * Handle an incoming request.
     * Requirements: 6.1, 6.2, 6.3, 6.4
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Check payload size
        if ($this->isPayloadTooLarge($request)) {
            $this->logSecurityViolation($request, 'payload_too_large');
            return response()->json(['error' => 'Payload too large'], 413);
        }
        
        // Check for suspicious content
        if ($this->containsSuspiciousContent($request)) {
            $this->logSecurityViolation($request, 'suspicious_content');
            $this->blockIpTemporarily($request->ip());
            return response()->json(['error' => 'Invalid request'], 400);
        }
        
        // Check for blocked IPs
        if ($this->isIpBlocked($request->ip())) {
            $this->logSecurityViolation($request, 'blocked_ip_attempt');
            return response()->json(['error' => 'Access denied'], 403);
        }
        
        // Check request method
        if (!in_array($request->method(), ['POST', 'PUT'])) {
            $this->logSecurityViolation($request, 'invalid_method');
            return response()->json(['error' => 'Method not allowed'], 405);
        }
        
        // Check Content-Type
        if (!$this->hasValidContentType($request)) {
            $this->logSecurityViolation($request, 'invalid_content_type');
            return response()->json(['error' => 'Invalid content type'], 400);
        }
        
        // Check for required headers based on gateway
        if (!$this->hasRequiredHeaders($request)) {
            $this->logSecurityViolation($request, 'missing_required_headers');
            return response()->json(['error' => 'Missing required headers'], 400);
        }
        
        // Check for repeated identical requests (replay attack protection)
        if ($this->isReplayAttack($request)) {
            $this->logSecurityViolation($request, 'replay_attack');
            return response()->json(['error' => 'Duplicate request'], 409);
        }
        
        return $next($request);
    }
    
    /**
     * Check if payload is too large
     */
    protected function isPayloadTooLarge(Request $request): bool
    {
        $contentLength = $request->header('Content-Length');
        
        if ($contentLength && (int)$contentLength > $this->maxPayloadSize) {
            return true;
        }
        
        return strlen($request->getContent()) > $this->maxPayloadSize;
    }
    
    /**
     * Check for suspicious content in request
     */
    protected function containsSuspiciousContent(Request $request): bool
    {
        $content = strtolower($request->getContent());
        $headers = strtolower(json_encode($request->headers->all()));
        $queryString = strtolower($request->getQueryString() ?? '');
        
        $allContent = $content . ' ' . $headers . ' ' . $queryString;
        
        foreach ($this->suspiciousPatterns as $pattern) {
            if (str_contains($allContent, strtolower($pattern))) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Check if IP is temporarily blocked
     */
    protected function isIpBlocked(string $ip): bool
    {
        return Cache::has("blocked_ip:{$ip}");
    }
    
    /**
     * Temporarily block an IP address
     */
    protected function blockIpTemporarily(string $ip, int $minutes = 60): void
    {
        Cache::put("blocked_ip:{$ip}", true, $minutes * 60);
        
        Log::warning('IP temporarily blocked due to suspicious webhook activity', [
            'ip' => $ip,
            'blocked_until' => now()->addMinutes($minutes)->toISOString(),
            'timestamp' => now()->toISOString()
        ]);
    }
    
    /**
     * Check if request has valid content type
     */
    protected function hasValidContentType(Request $request): bool
    {
        $contentType = $request->header('Content-Type');
        
        if (!$contentType) {
            return false;
        }
        
        $validTypes = [
            'application/json',
            'application/x-www-form-urlencoded',
            'text/plain'
        ];
        
        foreach ($validTypes as $type) {
            if (str_starts_with(strtolower($contentType), $type)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Check for required headers based on gateway
     */
    protected function hasRequiredHeaders(Request $request): bool
    {
        $path = $request->path();
        
        if (str_contains($path, 'razorpay')) {
            return $request->hasHeader('X-Razorpay-Event-Id') || 
                   $request->hasHeader('X-Razorpay-Signature');
        }
        
        if (str_contains($path, 'cashfree')) {
            return $request->hasHeader('X-Cashfree-Signature') || 
                   $request->hasHeader('X-Cashfree-Timestamp');
        }
        
        if (str_contains($path, 'payumoney')) {
            // PayUmoney doesn't use specific headers, check for required POST data
            return $request->has(['txnid', 'status', 'hash']);
        }
        
        return true; // Unknown gateway, let it pass for now
    }
    
    /**
     * Check for replay attacks by detecting duplicate requests
     */
    protected function isReplayAttack(Request $request): bool
    {
        $gateway = $this->extractGatewayFromPath($request->path());
        $payload = $request->getContent();
        
        // Create a unique hash for this request
        $requestHash = hash('sha256', $gateway . '|' . $payload . '|' . $request->ip());
        $cacheKey = "webhook_request:{$requestHash}";
        
        if (Cache::has($cacheKey)) {
            return true; // Duplicate request detected
        }
        
        // Store request hash for 5 minutes to prevent replays
        Cache::put($cacheKey, true, 300);
        
        return false;
    }
    
    /**
     * Extract gateway name from request path
     */
    protected function extractGatewayFromPath(string $path): string
    {
        if (str_contains($path, 'razorpay')) {
            return 'razorpay';
        } elseif (str_contains($path, 'payumoney')) {
            return 'payumoney';
        } elseif (str_contains($path, 'cashfree')) {
            return 'cashfree';
        }
        
        return 'unknown';
    }
    
    /**
     * Log security violation
     */
    protected function logSecurityViolation(Request $request, string $violationType): void
    {
        $gateway = $this->extractGatewayFromPath($request->path());
        
        Log::warning('Webhook security filter violation', [
            'violation_type' => $violationType,
            'gateway' => $gateway,
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'path' => $request->path(),
            'method' => $request->method(),
            'content_length' => strlen($request->getContent()),
            'content_type' => $request->header('Content-Type'),
            'headers' => $this->sanitizeHeaders($request->headers->all()),
            'timestamp' => now()->toISOString()
        ]);
        
        // Log to security channel
        Log::channel('security')->warning('Webhook security filter violation', [
            'type' => $violationType,
            'gateway' => $gateway,
            'ip' => $request->ip(),
            'timestamp' => now()->toISOString()
        ]);
        
        // Increment violation counter for this IP
        $violationKey = "webhook_violations:{$request->ip()}";
        $violations = Cache::get($violationKey, 0) + 1;
        Cache::put($violationKey, $violations, 3600); // Store for 1 hour
        
        // Block IP if too many violations
        if ($violations >= 5) {
            $this->blockIpTemporarily($request->ip(), 120); // Block for 2 hours
        }
    }
    
    /**
     * Sanitize headers for logging
     */
    protected function sanitizeHeaders(array $headers): array
    {
        $sensitiveHeaders = [
            'authorization',
            'x-razorpay-signature',
            'x-cashfree-signature',
            'x-payumoney-signature'
        ];
        
        foreach ($sensitiveHeaders as $header) {
            if (isset($headers[$header])) {
                $headers[$header] = ['[REDACTED]'];
            }
        }
        
        return $headers;
    }
}