<?php

namespace Database\Factories;

use App\Models\Cart;
use App\Models\Component;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\CartItem>
 */
class CartItemFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $component = Component::factory()->create();
        
        return [
            'cart_id' => Cart::factory(),
            'component_id' => $component->id,
            'quantity' => $this->faker->numberBetween(1, 5),
            'price' => $component->price,
        ];
    }
}