<?php

namespace App\Jobs;

use App\Mail\OrderConfirmation;
use App\Models\Order;
use App\Services\EmailTrackingService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class SendOrderConfirmation implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public $tries = 3;

    /**
     * The maximum number of seconds the job can run.
     *
     * @var int
     */
    public $timeout = 60;

    /**
     * Create a new job instance.
     */
    public function __construct(
        public Order $order
    ) {
        // Set the queue for email jobs
        $this->onQueue('emails');
    }

    /**
     * Execute the job.
     */
    public function handle(EmailTrackingService $emailTrackingService): void
    {
        $emailLog = null;
        
        try {
            // Check if user has email notifications enabled
            if (!$this->order->user->getNotificationSetting('order_confirmation', true)) {
                Log::info('Order confirmation email skipped - user disabled notifications', [
                    'order_id' => $this->order->id,
                    'user_id' => $this->order->user_id,
                ]);
                return;
            }

            // Log the email attempt
            $emailLog = $emailTrackingService->logEmailAttempt(
                'order_confirmation',
                $this->order->user->email,
                'Order Confirmation - ' . $this->order->order_number,
                $this->order->id,
                Order::class
            );

            // Update attempt count if this is a retry
            if ($this->attempts() > 1) {
                $emailTrackingService->incrementAttempt($emailLog);
            }

            // Send the email
            Mail::to($this->order->user->email)
                ->send(new OrderConfirmation($this->order));

            // Mark as sent
            $emailTrackingService->markEmailSent($emailLog);

            Log::info('Order confirmation email sent successfully', [
                'order_id' => $this->order->id,
                'order_number' => $this->order->order_number,
                'user_email' => $this->order->user->email,
                'email_log_id' => $emailLog->id,
            ]);

        } catch (\Exception $e) {
            // Mark as failed if we have an email log
            if ($emailLog) {
                $emailTrackingService->markEmailFailed($emailLog, $e->getMessage(), $this->attempts());
            }

            Log::error('Failed to send order confirmation email', [
                'order_id' => $this->order->id,
                'order_number' => $this->order->order_number,
                'error' => $e->getMessage(),
                'attempt' => $this->attempts(),
                'email_log_id' => $emailLog?->id,
            ]);

            // Re-throw the exception to trigger retry mechanism
            throw $e;
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('Order confirmation email job failed permanently', [
            'order_id' => $this->order->id,
            'order_number' => $this->order->order_number,
            'error' => $exception->getMessage(),
            'attempts' => $this->attempts(),
        ]);

        // Could implement additional failure handling here, such as:
        // - Sending to a dead letter queue
        // - Notifying administrators
        // - Creating a manual task for customer service
    }

    /**
     * Calculate the number of seconds to wait before retrying the job.
     */
    public function backoff(): array
    {
        return [30, 120, 300]; // 30 seconds, 2 minutes, 5 minutes
    }
}