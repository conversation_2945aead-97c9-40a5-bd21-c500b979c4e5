<div>
    @if($build)
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
                <h2 class="text-xl font-semibold">Build Summary</h2>
                
                <div class="flex space-x-2">
                    <button 
                        wire:click="saveBuild" 
                        class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                    >
                        Save Build
                    </button>
                    
                    @if($isComplete)
                        <button 
                            class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
                            onclick="window.location.href = '{{ route('checkout.index', ['build_id' => $buildId]) }}'"
                        >
                            Checkout
                        </button>
                    @endif
                </div>
            </div>
            
            <!-- Build Details Form -->
            <div class="mb-6 p-4 border rounded-lg bg-gray-50">
                <div class="mb-4">
                    <label for="buildName" class="block text-sm font-medium text-gray-700 mb-1">Build Name</label>
                    <input 
                        type="text" 
                        id="buildName" 
                        wire:model.defer="build.name" 
                        class="w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        placeholder="My Awesome PC Build"
                    >
                    @error('build.name') <span class="text-red-600 text-xs">{{ $message }}</span> @enderror
                </div>
                
                <div class="mb-4">
                    <label for="buildDescription" class="block text-sm font-medium text-gray-700 mb-1">Description</label>
                    <textarea 
                        id="buildDescription" 
                        wire:model.defer="build.description" 
                        rows="3" 
                        class="w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        placeholder="Describe your build..."
                    ></textarea>
                    @error('build.description') <span class="text-red-600 text-xs">{{ $message }}</span> @enderror
                </div>
                
                <div>
                    <label class="flex items-center">
                        <input 
                            type="checkbox" 
                            wire:model.defer="build.is_public" 
                            class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        >
                        <span class="ml-2 text-gray-700">Make this build public</span>
                    </label>
                </div>
            </div>
            
            <!-- Compatibility Issues -->
            @if(count($compatibilityIssues) > 0)
                <div class="mb-6 p-4 border border-red-300 rounded-lg bg-red-50">
                    <h3 class="font-medium text-red-800 mb-2">Compatibility Issues</h3>
                    <ul class="list-disc list-inside text-red-700 space-y-1">
                        @foreach($compatibilityIssues as $issue)
                            <li>{{ $issue }}</li>
                        @endforeach
                    </ul>
                </div>
            @endif
            
            <!-- Components List -->
            <div class="mb-6">
                <h3 class="font-medium text-gray-900 mb-4">Selected Components</h3>
                
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Component</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Price</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quantity</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Subtotal</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            @foreach($categories as $category)
                                @php
                                    $buildComponent = $buildComponents->first(function($item) use ($category) {
                                        return $item->category_id === $category->id;
                                    });
                                @endphp
                                
                                <tr class="{{ $category->is_required && !$buildComponent ? 'bg-yellow-50' : '' }}">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                        {{ $category->name }}
                                        @if($category->is_required)
                                            <span class="text-red-500">*</span>
                                        @endif
                                    </td>
                                    
                                    @if($buildComponent)
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            {{ $buildComponent->component->name }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            ${{ number_format($buildComponent->price, 2) }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            {{ $buildComponent->quantity }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            ${{ number_format($buildComponent->price * $buildComponent->quantity, 2) }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            <button 
                                                wire:click="removeComponent({{ $buildComponent->id }})" 
                                                class="text-red-600 hover:text-red-900 focus:outline-none"
                                            >
                                                Remove
                                            </button>
                                        </td>
                                    @else
                                        <td colspan="5" class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            <a 
                                                href="#" 
                                                onclick="window.livewire.emit('selectCategory', '{{ $category->slug }}')" 
                                                class="text-blue-600 hover:text-blue-900"
                                            >
                                                Select a {{ $category->name }}
                                            </a>
                                        </td>
                                    @endif
                                </tr>
                            @endforeach
                        </tbody>
                        <tfoot>
                            <tr class="bg-gray-50">
                                <td colspan="4" class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 text-right">
                                    Total:
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                    ${{ number_format($totalPrice, 2) }}
                                </td>
                                <td></td>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
            
            <!-- Build Status -->
            <div class="flex items-center justify-between p-4 border rounded-lg {{ $isComplete ? 'bg-green-50 border-green-300' : 'bg-yellow-50 border-yellow-300' }}">
                <div>
                    <h3 class="font-medium {{ $isComplete ? 'text-green-800' : 'text-yellow-800' }} mb-1">
                        Build Status: {{ $isComplete ? 'Complete' : 'Incomplete' }}
                    </h3>
                    <p class="text-sm {{ $isComplete ? 'text-green-700' : 'text-yellow-700' }}">
                        {{ $isComplete ? 'Your build is complete and ready for checkout!' : 'Please add all required components to complete your build.' }}
                    </p>
                </div>
                
                @if($isComplete)
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                @else
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-yellow-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                    </svg>
                @endif
            </div>
        </div>
    @else
        <div class="bg-white rounded-lg shadow p-6 text-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900">No build selected</h3>
            <p class="mt-1 text-sm text-gray-500">Start by creating a new build or selecting an existing one.</p>
        </div>
    @endif
    
    <!-- Flash Messages -->
    @if(session()->has('message'))
        <div class="fixed bottom-4 right-4 bg-green-100 border-l-4 border-green-500 text-green-700 p-4 rounded shadow-md" 
             x-data="{ show: true }" 
             x-show="show" 
             x-init="setTimeout(() => show = false, 3000)">
            <p>{{ session('message') }}</p>
        </div>
    @endif
</div>