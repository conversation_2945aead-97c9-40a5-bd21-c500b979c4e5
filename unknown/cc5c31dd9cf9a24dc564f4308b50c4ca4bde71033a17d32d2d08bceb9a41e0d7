<?php

namespace Tests\Unit\Services;

use App\Models\Coupon;
use App\Models\Product;
use App\Models\ProductCategory;
use App\Models\User;
use App\Services\CouponService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Auth;
use Tests\TestCase;

class CouponServiceTest extends TestCase
{
    use RefreshDatabase;

    protected CouponService $couponService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->artisan('migrate');
        $this->couponService = new CouponService();
    }

    /** @test */
    public function it_validates_and_applies_valid_coupon()
    {
        $user = User::factory()->create();
        Auth::login($user);

        $product = Product::factory()->create([
            'price' => 100.00,
            'sale_price' => null,
        ]);
        $coupon = Coupon::factory()->create([
            'code' => 'VALID20',
            'type' => 'percentage',
            'value' => 20,
            'is_active' => true,
            'starts_at' => null,
            'expires_at' => null,
            'usage_limit' => null,
            'usage_limit_per_user' => null,
            'minimum_amount' => null,
        ]);

        $cartItems = [
            ['product_id' => $product->id, 'quantity' => 2]
        ];

        $result = $this->couponService->validateAndApplyCoupon('VALID20', $cartItems);

        $this->assertTrue($result['valid']);
        $this->assertEquals(40.0, $result['discount']); // 20% of 200
        $this->assertEquals(200.0, $result['subtotal']);
        $this->assertEquals(160.0, $result['final_total']);
    }

    /** @test */
    public function it_rejects_invalid_coupon_code()
    {
        $user = User::factory()->create();
        Auth::login($user);

        $product = Product::factory()->create(['price' => 100]);
        $cartItems = [
            ['product_id' => $product->id, 'quantity' => 1]
        ];

        $result = $this->couponService->validateAndApplyCoupon('INVALID', $cartItems);

        $this->assertFalse($result['valid']);
        $this->assertEquals('Invalid coupon code.', $result['message']);
    }

    /** @test */
    public function it_rejects_coupon_below_minimum_amount()
    {
        $user = User::factory()->create();
        Auth::login($user);

        $product = Product::factory()->create(['price' => 50]);
        $coupon = Coupon::factory()->create([
            'code' => 'MIN100',
            'type' => 'fixed',
            'value' => 20,
            'minimum_amount' => 100,
            'is_active' => true,
            'starts_at' => null,
            'expires_at' => null,
            'usage_limit' => null,
            'usage_limit_per_user' => null,
        ]);

        $cartItems = [
            ['product_id' => $product->id, 'quantity' => 1]
        ];

        $result = $this->couponService->validateAndApplyCoupon('MIN100', $cartItems);

        $this->assertFalse($result['valid']);
        $this->assertStringContainsString('Minimum order amount', $result['message']);
    }

    /** @test */
    public function it_rejects_coupon_not_applicable_to_products()
    {
        $user = User::factory()->create();
        Auth::login($user);

        $product1 = Product::factory()->create(['price' => 100]);
        $product2 = Product::factory()->create(['price' => 100]);

        $coupon = Coupon::factory()->create([
            'code' => 'SPECIFIC',
            'type' => 'fixed',
            'value' => 20,
            'applicable_products' => [$product1->id],
            'is_active' => true,
            'starts_at' => null,
            'expires_at' => null,
            'usage_limit' => null,
            'usage_limit_per_user' => null,
            'minimum_amount' => null,
        ]);

        $cartItems = [
            ['product_id' => $product2->id, 'quantity' => 1]
        ];

        $result = $this->couponService->validateAndApplyCoupon('SPECIFIC', $cartItems);

        $this->assertFalse($result['valid']);
        $this->assertEquals('This coupon is not applicable to selected products.', $result['message']);
    }

    /** @test */
    public function it_calculates_subtotal_correctly()
    {
        $product1 = Product::factory()->create([
            'price' => 100.00,
            'sale_price' => 80.00,
        ]);
        $product2 = Product::factory()->create([
            'price' => 50.00,
            'sale_price' => null,
        ]);

        $cartItems = [
            ['product_id' => $product1->id, 'quantity' => 2], // 80 * 2 = 160
            ['product_id' => $product2->id, 'quantity' => 1], // 50 * 1 = 50
        ];

        $reflection = new \ReflectionClass($this->couponService);
        $method = $reflection->getMethod('calculateSubtotal');
        $method->setAccessible(true);

        $subtotal = $method->invoke($this->couponService, $cartItems);

        $this->assertEquals(210.0, $subtotal); // 160 + 50
    }

    /** @test */
    public function it_gets_applicable_coupons()
    {
        $user = User::factory()->create();
        Auth::login($user);

        $category = ProductCategory::factory()->create();
        $product = Product::factory()->create([
            'price' => 100.00,
            'sale_price' => null,
            'category_id' => $category->id,
        ]);

        // Valid coupon for all products
        $allProductsCoupon = Coupon::factory()->create([
            'code' => 'ALL20',
            'name' => 'All Products 20%',
            'type' => 'percentage',
            'value' => 20,
            'is_active' => true,
            'starts_at' => null,
            'expires_at' => null,
            'usage_limit' => null,
            'usage_limit_per_user' => null,
            'minimum_amount' => null,
        ]);

        // Valid coupon for specific category
        $categoryCoupon = Coupon::factory()->create([
            'code' => 'CAT15',
            'name' => 'Category 15%',
            'type' => 'percentage',
            'value' => 15,
            'applicable_categories' => [$category->id],
            'is_active' => true,
            'starts_at' => null,
            'expires_at' => null,
            'usage_limit' => null,
            'usage_limit_per_user' => null,
            'minimum_amount' => null,
        ]);

        // Coupon with minimum amount not met
        $minAmountCoupon = Coupon::factory()->create([
            'code' => 'MIN500',
            'name' => 'Minimum 500',
            'type' => 'fixed',
            'value' => 50,
            'minimum_amount' => 500,
            'is_active' => true,
            'starts_at' => null,
            'expires_at' => null,
            'usage_limit' => null,
            'usage_limit_per_user' => null,
        ]);

        // Inactive coupon
        $inactiveCoupon = Coupon::factory()->create([
            'code' => 'INACTIVE',
            'is_active' => false,
        ]);

        $applicable = $this->couponService->getApplicableCoupons([$product->id], 200);

        $this->assertCount(2, $applicable);
        
        $codes = collect($applicable)->pluck('code')->toArray();
        $this->assertContains('ALL20', $codes);
        $this->assertContains('CAT15', $codes);
        $this->assertNotContains('MIN500', $codes);
        $this->assertNotContains('INACTIVE', $codes);
    }

    /** @test */
    public function it_gets_coupon_stats()
    {
        $coupon = Coupon::factory()->create([
            'usage_limit' => 100,
            'used_count' => 25,
        ]);

        $user1 = User::factory()->create();
        $user2 = User::factory()->create();

        // Create usage records
        $coupon->usages()->create([
            'user_id' => $user1->id,
            'discount_amount' => 50,
        ]);

        $coupon->usages()->create([
            'user_id' => $user2->id,
            'discount_amount' => 30,
        ]);

        $stats = $this->couponService->getCouponStats($coupon);

        $this->assertEquals(25, $stats['total_usage']);
        $this->assertEquals(75, $stats['remaining_usage']);
        $this->assertEquals(25.0, $stats['usage_percentage']);
        $this->assertEquals(80, $stats['total_discount_given']);
        $this->assertEquals(2, $stats['unique_users']);
        $this->assertCount(2, $stats['recent_usages']);
    }

    /** @test */
    public function it_checks_code_availability()
    {
        Coupon::factory()->create(['code' => 'EXISTING']);

        $this->assertFalse($this->couponService->isCodeAvailable('EXISTING'));
        $this->assertTrue($this->couponService->isCodeAvailable('AVAILABLE'));
    }

    /** @test */
    public function it_checks_code_availability_excluding_specific_coupon()
    {
        $coupon = Coupon::factory()->create(['code' => 'EXISTING']);

        // Should return true when excluding the existing coupon
        $this->assertTrue($this->couponService->isCodeAvailable('EXISTING', $coupon->id));
        
        // Should return false for other coupons
        $this->assertFalse($this->couponService->isCodeAvailable('EXISTING', 999));
    }

    /** @test */
    public function it_generates_unique_coupon_code()
    {
        // Create some existing codes
        Coupon::factory()->create(['code' => 'AAAAAAAA']);
        Coupon::factory()->create(['code' => 'BBBBBBBB']);

        $code = $this->couponService->generateUniqueCode(8);

        $this->assertEquals(8, strlen($code));
        $this->assertTrue($this->couponService->isCodeAvailable($code));
        $this->assertMatchesRegularExpression('/^[A-Z0-9]+$/', $code);
    }

    /** @test */
    public function it_handles_coupon_with_sale_price_products()
    {
        $user = User::factory()->create();
        Auth::login($user);

        $product = Product::factory()->create([
            'price' => 100.00,
            'sale_price' => 80.00, // Effective price is 80
        ]);

        $coupon = Coupon::factory()->create([
            'code' => 'SALE20',
            'type' => 'percentage',
            'value' => 20,
            'is_active' => true,
            'starts_at' => null,
            'expires_at' => null,
            'usage_limit' => null,
            'usage_limit_per_user' => null,
            'minimum_amount' => null,
        ]);

        $cartItems = [
            ['product_id' => $product->id, 'quantity' => 1]
        ];

        $result = $this->couponService->validateAndApplyCoupon('SALE20', $cartItems);

        $this->assertTrue($result['valid']);
        $this->assertEquals(80.0, $result['subtotal']); // Uses sale price
        $this->assertEquals(16.0, $result['discount']); // 20% of 80
        $this->assertEquals(64.0, $result['final_total']); // 80 - 16
    }

    /** @test */
    public function it_handles_mixed_cart_with_category_specific_coupon()
    {
        $user = User::factory()->create();
        Auth::login($user);

        $category1 = ProductCategory::factory()->create();
        $category2 = ProductCategory::factory()->create();

        $product1 = Product::factory()->create([
            'price' => 100,
            'category_id' => $category1->id,
        ]);

        $product2 = Product::factory()->create([
            'price' => 50,
            'category_id' => $category2->id,
        ]);

        $coupon = Coupon::factory()->create([
            'code' => 'CAT1ONLY',
            'type' => 'percentage',
            'value' => 20,
            'applicable_categories' => [$category1->id],
            'is_active' => true,
            'starts_at' => null,
            'expires_at' => null,
            'usage_limit' => null,
            'usage_limit_per_user' => null,
            'minimum_amount' => null,
        ]);

        $cartItems = [
            ['product_id' => $product1->id, 'quantity' => 1], // Eligible
            ['product_id' => $product2->id, 'quantity' => 1], // Not eligible
        ];

        $result = $this->couponService->validateAndApplyCoupon('CAT1ONLY', $cartItems);

        $this->assertTrue($result['valid']); // Should be valid because at least one product is eligible
    }
}