<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Artisan;

class InstallationMiddleware
{
   protected $allowedRoutes = [
       'install*',
       '_debugbar*', 
       '_ignition*',
       'livewire*',
       'sanctum*'
   ];

   public function handle(Request $request, Closure $next)
   {
       $envCreated = false;
       
       // Check and create .env file first
       if (!file_exists(base_path('.env'))) {
           $envCreated = true;
           if (file_exists(base_path('.env.example'))) {
               // Copy .env.example and update key
               $envContent = file_get_contents(base_path('.env.example'));
               $key = 'base64:'.base64_encode(random_bytes(32));
               $envContent = preg_replace('/APP_KEY=.*/', 'APP_KEY='.$key, $envContent);
               file_put_contents(base_path('.env'), $envContent);
           } else {
               // Create minimal .env file with essential configurations
               $key = 'base64:'.base64_encode(random_bytes(32));
               $envContent = implode("\n", [
                   "APP_NAME=Laravel",
                   "APP_ENV=local",
                   "APP_KEY={$key}",
                   "APP_DEBUG=true",
                   "APP_URL=http://localhost",
                   "",
                   "LOG_CHANNEL=stack",
                   "LOG_LEVEL=debug",
                   "",
                   "DB_CONNECTION=mysql",
                   "DB_HOST=127.0.0.1",
                   "DB_PORT=3306",
                   "DB_DATABASE=laravel",
                   "DB_USERNAME=root",
                   "DB_PASSWORD=",
                   "",
                   "BROADCAST_DRIVER=log",
                   "CACHE_DRIVER=file",
                   "FILESYSTEM_DISK=local",
                   "QUEUE_CONNECTION=sync",
                   "SESSION_DRIVER=file",
                   "SESSION_LIFETIME=120",
                   ""
               ]);
               file_put_contents(base_path('.env'), $envContent);
           }

           // Clear config cache and reload environment
           $this->reloadEnvironment();
       }

       // Generate key if not exists
       if (!config('app.key')) {
           Artisan::call('key:generate', ['--force' => true]);
           $this->reloadEnvironment();
       }

       // Force redirect to installer if .env was just created
       if ($envCreated && !$request->is('install*')) {
           return redirect()->route('installer.welcome');
       }

       $installed = File::exists(storage_path('installed'));

       if ($installed && $request->is('install*')) {
           return redirect('/');
       }

       if (!$installed && !$request->is('install*')) {
           return redirect()->route('installer.welcome');
       }

       if ($request->is('install*')) {
           Config::set('session.driver', 'file');
           Config::set('session.files', storage_path('framework/sessions'));
           session()->setId(uniqid());
       }

       if (!$installed) {
           Config::set('database.default', 'null');
           Config::set('database.connections.null', [
               'driver' => 'null',
               'database' => ':memory:',
               'prefix' => '',
               'name' => 'null'
           ]);
           Config::set('cache.default', 'file');
           Config::set('queue.default', 'sync');
           Config::set('themes.active', null);
           Config::set('themes.default', null);
           Config::set('themes.enabled', false);

           if (!$this->isAllowedRoute($request)) {
               return redirect()->route('installer.welcome');
           }
       }

       return $next($request);
   }

   protected function reloadEnvironment()
   {
       // Clear all cached config files
       if (file_exists(app()->getCachedConfigPath())) {
           unlink(app()->getCachedConfigPath());
       }
       if (file_exists(app()->bootstrapPath('cache/config.php'))) {
           unlink(app()->bootstrapPath('cache/config.php'));
       }
       
       // Clear cached routes
       if (file_exists(app()->getCachedRoutesPath())) {
           unlink(app()->getCachedRoutesPath());
       }

       // Clear application cache
       if (function_exists('opcache_reset')) {
           opcache_reset();
       }
       
       // Reload the environment
       $app = app();
       $app->make('config')->set('app.key', env('APP_KEY'));
       
       try {
           // Reload environment variables
           \Dotenv\Dotenv::createImmutable(app()->environmentPath())->load();
           
           // Force config reload
           $app->make('config')->set('app.key', env('APP_KEY'));
           
           // Clear and reload config cache
           Artisan::call('config:clear');
           
       } catch (\Exception $e) {
           // Log error but continue
           \Log::error('Error reloading environment: ' . $e->getMessage());
       }
   }

   protected function isAllowedRoute(Request $request): bool 
   {
       return collect($this->allowedRoutes)->contains(function($route) use ($request) {
           return $request->is($route);
       });
   }
}