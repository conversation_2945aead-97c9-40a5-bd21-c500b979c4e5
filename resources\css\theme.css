:root {
    /* Primary colors (Blue) */
    --color-primary-400: #60a5fa; /* blue-400 */
    --color-primary-500: #3b82f6; /* blue-500 */
    --color-primary-600: #2563eb; /* blue-600 */
    --color-primary-900: #1e3a8a; /* blue-900 */
    
    /* Secondary colors (Cyan) */
    --color-secondary-400: #22d3ee; /* cyan-400 */
    --color-secondary-500: #06b6d4; /* cyan-500 */
    --color-secondary-600: #0891b2; /* cyan-600 */
    --color-secondary-900: #164e63; /* cyan-900 */
    
    /* Accent colors (Purple) */
    --color-accent-400: #c084fc; /* purple-400 */
    --color-accent-500: #a855f7; /* purple-500 */
    --color-accent-600: #9333ea; /* purple-600 */
    --color-accent-900: #581c87; /* purple-900 */
    
    /* Success colors (Green) */
    --color-success-400: #4ade80; /* green-400 */
    --color-success-500: #22c55e; /* green-500 */
    --color-success-600: #16a34a; /* green-600 */
    --color-success-900: #14532d; /* green-900 */
    
    /* Warning colors (Orange) */
    --color-warning-400: #fb923c; /* orange-400 */
    --color-warning-500: #f97316; /* orange-500 */
    --color-warning-600: #ea580c; /* orange-600 */
    --color-warning-900: #7c2d12; /* orange-900 */
    
    /* Danger colors (Red) */
    --color-danger-400: #f87171; /* red-400 */
    --color-danger-500: #ef4444; /* red-500 */
    --color-danger-600: #dc2626; /* red-600 */
    --color-danger-900: #7f1d1d; /* red-900 */
    
    /* Additional colors */
    --color-teal-500: #14b8a6; /* teal-500 */
    --color-pink-500: #ec4899; /* pink-500 */
}

/* Opacity variants for borders and shadows */
.border-primary-900\/30 { border-color: rgba(30, 58, 138, 0.3); }
.border-primary-900\/50 { border-color: rgba(30, 58, 138, 0.5); }
.hover\:border-primary-500\/30:hover { border-color: rgba(59, 130, 246, 0.3); }
.hover\:border-primary-500\/50:hover { border-color: rgba(59, 130, 246, 0.5); }
.hover\:shadow-primary-500\/20:hover { box-shadow: 0 0 15px rgba(59, 130, 246, 0.2); }
.bg-primary-500\/10 { background-color: rgba(59, 130, 246, 0.1); }
.bg-primary-500\/20 { background-color: rgba(59, 130, 246, 0.2); }
.group-hover\:bg-primary-500\/30:hover { background-color: rgba(59, 130, 246, 0.3); }

.border-secondary-900\/30 { border-color: rgba(22, 78, 99, 0.3); }
.border-secondary-900\/50 { border-color: rgba(22, 78, 99, 0.5); }
.hover\:border-secondary-500\/30:hover { border-color: rgba(6, 182, 212, 0.3); }
.hover\:border-secondary-500\/50:hover { border-color: rgba(6, 182, 212, 0.5); }
.hover\:shadow-secondary-500\/20:hover { box-shadow: 0 0 15px rgba(6, 182, 212, 0.2); }
.bg-secondary-500\/20 { background-color: rgba(6, 182, 212, 0.2); }
.group-hover\:bg-secondary-500\/30:hover { background-color: rgba(6, 182, 212, 0.3); }

.border-accent-900\/30 { border-color: rgba(88, 28, 135, 0.3); }
.border-accent-900\/50 { border-color: rgba(88, 28, 135, 0.5); }
.hover\:border-accent-500\/30:hover { border-color: rgba(168, 85, 247, 0.3); }
.hover\:border-accent-500\/50:hover { border-color: rgba(168, 85, 247, 0.5); }
.hover\:shadow-accent-500\/20:hover { box-shadow: 0 0 15px rgba(168, 85, 247, 0.2); }
.bg-accent-500\/20 { background-color: rgba(168, 85, 247, 0.2); }
.group-hover\:bg-accent-500\/30:hover { background-color: rgba(168, 85, 247, 0.3); }

.border-success-900\/30 { border-color: rgba(20, 83, 45, 0.3); }
.border-success-900\/50 { border-color: rgba(20, 83, 45, 0.5); }
.hover\:border-success-500\/30:hover { border-color: rgba(34, 197, 94, 0.3); }
.hover\:border-success-500\/50:hover { border-color: rgba(34, 197, 94, 0.5); }
.hover\:shadow-success-500\/20:hover { box-shadow: 0 0 15px rgba(34, 197, 94, 0.2); }
.bg-success-500\/20 { background-color: rgba(34, 197, 94, 0.2); }
.group-hover\:bg-success-500\/30:hover { background-color: rgba(34, 197, 94, 0.3); }

/* Gradient utilities */
.from-primary-400 { --tw-gradient-from: var(--color-primary-400); }
.from-primary-500 { --tw-gradient-from: var(--color-primary-500); }
.from-primary-600 { --tw-gradient-from: var(--color-primary-600); }
.to-primary-500 { --tw-gradient-to: var(--color-primary-500); }
.to-primary-600 { --tw-gradient-to: var(--color-primary-600); }

.from-secondary-400 { --tw-gradient-from: var(--color-secondary-400); }
.from-secondary-500 { --tw-gradient-from: var(--color-secondary-500); }
.to-secondary-500 { --tw-gradient-to: var(--color-secondary-500); }
.to-secondary-600 { --tw-gradient-to: var(--color-secondary-600); }
.via-secondary-400 { --tw-gradient-stops: var(--tw-gradient-from), var(--color-secondary-400), var(--tw-gradient-to); }

.from-accent-500 { --tw-gradient-from: var(--color-accent-500); }
.to-accent-500 { --tw-gradient-to: var(--color-accent-500); }

.from-success-500 { --tw-gradient-from: var(--color-success-500); }
.to-success-500 { --tw-gradient-to: var(--color-success-500); }
.to-teal-500 { --tw-gradient-to: var(--color-teal-500); }

.from-warning-500 { --tw-gradient-from: var(--color-warning-500); }

.to-danger-500 { --tw-gradient-to: var(--color-danger-500); }
.to-pink-500 { --tw-gradient-to: var(--color-pink-500); }

/* Custom utility classes */
.cyber-grid {
    background-image:
        linear-gradient(rgba(59, 130, 246, 0.1) 1px, transparent 1px),
        linear-gradient(90deg, rgba(59, 130, 246, 0.1) 1px, transparent 1px);
    background-size: 20px 20px;
    animation: grid-move 20s linear infinite;
}

.glow {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
}

.neon-text {
    text-shadow: 0 0 10px rgba(59, 130, 246, 0.5);
}

.tooltip {
    border: 1px solid rgba(59, 130, 246, 0.3);
}

.stat-card {
    border: 1px solid rgba(59, 130, 246, 0.2);
}

.component-icon:hover {
    filter: drop-shadow(0 0 15px rgba(59, 130, 246, 0.6));
}