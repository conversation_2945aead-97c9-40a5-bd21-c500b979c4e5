<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Component;
use App\Models\ComponentCategory;
use Illuminate\Http\Request;

class ComponentController extends Controller
{
    /**
     * Display a listing of the components.
     */
    public function index()
    {
        return view('admin.components.index');
    }

    /**
     * Show the form for creating a new component.
     */
    public function create()
    {
        $categories = ComponentCategory::all();
        return view('admin.components.create', compact('categories'));
    }

    /**
     * Store a newly created component in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'category_id' => 'required|exists:component_categories,id',
            'brand' => 'required|string|max:255',
            'model' => 'nullable|string|max:255',
            'price' => 'required|numeric|min:0',
            'stock' => 'required|integer|min:0',
            'is_active' => 'boolean',
            'is_featured' => 'boolean',
            'specifications' => 'nullable|array',
            'compatibility_data' => 'nullable|array'
        ]);

        $component = Component::create([
            'name' => $request->name,
            'slug' => \Illuminate\Support\Str::slug($request->name),
            'description' => $request->description,
            'category_id' => $request->category_id,
            'brand' => $request->brand,
            'model' => $request->model,
            'price' => $request->price,
            'stock' => $request->stock,
            'is_active' => $request->boolean('is_active', true),
            'is_featured' => $request->boolean('is_featured', false),
            'specs' => $request->specifications ?? [],
            'compatibility_data' => $request->compatibility_data ?? []
        ]);

        return response()->json(['success' => true, 'component' => $component], 201);
    }

    /**
     * Show the form for editing the specified component.
     */
    public function edit(Component $component)
    {
        $categories = ComponentCategory::all();
        return view('admin.components.edit', compact('component', 'categories'));
    }

    /**
     * Update the specified component in storage.
     */
    public function update(Request $request, Component $component)
    {
        $request->validate([
            'name' => 'sometimes|string|max:255',
            'description' => 'nullable|string',
            'category_id' => 'sometimes|exists:component_categories,id',
            'brand' => 'sometimes|string|max:255',
            'model' => 'nullable|string|max:255',
            'price' => 'sometimes|numeric|min:0',
            'stock' => 'sometimes|integer|min:0',
            'is_active' => 'sometimes|boolean',
            'is_featured' => 'sometimes|boolean',
            'specifications' => 'nullable|array',
            'compatibility_data' => 'nullable|array'
        ]);

        $component->update($request->only([
            'name', 'description', 'category_id', 'brand', 'model', 
            'price', 'stock', 'is_active', 'is_featured'
        ]));

        if ($request->has('specifications')) {
            $component->specs = $request->specifications;
            $component->save();
        }

        if ($request->has('compatibility_data')) {
            $component->compatibility_data = $request->compatibility_data;
            $component->save();
        }

        return response()->json(['success' => true, 'component' => $component]);
    }

    /**
     * Remove the specified component from storage.
     */
    public function destroy(Component $component)
    {
        $component->delete();
        return response()->json(['success' => true, 'message' => 'Component deleted successfully']);
    }

    /**
     * Upload image for component.
     */
    public function uploadImage(Request $request, Component $component)
    {
        $request->validate([
            'image' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048'
        ]);

        if ($request->hasFile('image')) {
            $imagePath = $request->file('image')->store('components', 'public');
            $component->update(['image' => $imagePath]);
        }

        return response()->json(['success' => true, 'image_path' => $component->image]);
    }

    /**
     * Toggle component status.
     */
    public function toggleStatus(Component $component)
    {
        $component->update(['is_active' => !$component->is_active]);
        return response()->json(['success' => true, 'is_active' => $component->is_active]);
    }

    /**
     * Bulk actions on components.
     */
    public function bulkAction(Request $request)
    {
        $request->validate([
            'action' => 'required|string|in:toggle_featured,toggle_active,delete',
            'component_ids' => 'required|array',
            'component_ids.*' => 'exists:components,id',
            'value' => 'sometimes|boolean'
        ]);

        $components = Component::whereIn('id', $request->component_ids);

        switch ($request->action) {
            case 'toggle_featured':
                $components->update(['is_featured' => $request->boolean('value', true)]);
                break;
            case 'toggle_active':
                $components->update(['is_active' => $request->boolean('value', true)]);
                break;
            case 'delete':
                $components->delete();
                break;
        }

        return response()->json(['success' => true, 'message' => 'Bulk action completed successfully']);
    }

    /**
     * Bulk price update.
     */
    public function bulkPriceUpdate(Request $request)
    {
        $request->validate([
            'component_ids' => 'required|array',
            'component_ids.*' => 'exists:components,id',
            'price_adjustment' => 'required|numeric',
            'adjustment_type' => 'required|string|in:percentage_increase,percentage_decrease,fixed_increase,fixed_decrease'
        ]);

        $components = Component::whereIn('id', $request->component_ids)->get();

        foreach ($components as $component) {
            $newPrice = $component->price;

            switch ($request->adjustment_type) {
                case 'percentage_increase':
                    $newPrice = $component->price * (1 + $request->price_adjustment / 100);
                    break;
                case 'percentage_decrease':
                    $newPrice = $component->price * (1 - $request->price_adjustment / 100);
                    break;
                case 'fixed_increase':
                    $newPrice = $component->price + $request->price_adjustment;
                    break;
                case 'fixed_decrease':
                    $newPrice = $component->price - $request->price_adjustment;
                    break;
            }

            $component->update(['price' => round(max(0, $newPrice), 2)]);
        }

        return response()->json(['success' => true, 'message' => 'Prices updated successfully']);
    }

    /**
     * Export components data.
     */
    public function export(Request $request)
    {
        $format = $request->get('format', 'csv');
        $categoryId = $request->get('category');

        $query = Component::with('category');
        
        if ($categoryId) {
            $query->where('category_id', $categoryId);
        }

        $components = $query->get();

        if ($format === 'csv') {
            $csvData = "Name,Category,Brand,Price,Stock,Status\n";
            
            foreach ($components as $component) {
                $csvData .= sprintf(
                    "%s,%s,%s,%.2f,%d,%s\n",
                    $component->name,
                    $component->category->name ?? 'N/A',
                    $component->brand,
                    $component->price,
                    $component->stock,
                    $component->is_active ? 'Active' : 'Inactive'
                );
            }

            $response = response($csvData, 200, [
                'Content-Disposition' => 'attachment; filename="components.csv"'
            ]);
            $response->header('Content-Type', 'text/csv');
            return $response;
        }

        return response()->json(['components' => $components]);
    }
}