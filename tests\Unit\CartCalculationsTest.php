<?php

namespace Tests\Unit;

use App\Models\Cart;
use App\Models\CartItem;
use App\Models\Component;
use App\Models\ComponentCategory;
use App\Models\User;
use App\Services\CartService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class CartCalculationsTest extends TestCase
{
    use RefreshDatabase;

    protected CartService $cartService;
    protected User $user;
    protected Component $component1;
    protected Component $component2;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->cartService = new CartService();
        
        // Create test user
        $this->user = User::factory()->create();
        
        // Create test component category
        $category = ComponentCategory::factory()->create([
            'name' => 'CPU',
            'slug' => 'cpu',
        ]);
        
        // Create test components
        $this->component1 = Component::factory()->create([
            'name' => 'Test CPU',
            'category_id' => $category->id,
            'price' => 299.99,
            'stock' => 10,
            'is_active' => true,
            'specs' => ['weight' => 2.5],
        ]);

        $this->component2 = Component::factory()->create([
            'name' => 'Test GPU',
            'category_id' => $category->id,
            'price' => 599.99,
            'stock' => 5,
            'is_active' => true,
            'specs' => ['weight' => 3.2],
        ]);
    }

    public function test_get_subtotal_returns_correct_amount()
    {
        $this->startSession();
        
        $this->cartService->addToCart($this->component1, 2); // 2 * 299.99 = 599.98
        $this->cartService->addToCart($this->component2, 1); // 1 * 599.99 = 599.99
        
        $subtotal = $this->cartService->getSubtotal();
        
        $this->assertEquals(1199.97, $subtotal);
    }

    public function test_get_tax_amount_calculates_correctly()
    {
        $this->startSession();
        
        $this->cartService->addToCart($this->component1, 1); // 299.99
        
        $tax = $this->cartService->getTaxAmount(null, 0.08); // 8% tax
        
        $this->assertEquals(24.00, $tax); // 299.99 * 0.08 = 23.9992, rounded to 24.00
    }

    public function test_get_shipping_cost_free_over_100()
    {
        $this->startSession();
        
        $this->cartService->addToCart($this->component1, 1); // 299.99 (over $100)
        
        $shipping = $this->cartService->getShippingCost();
        
        $this->assertEquals(0, $shipping);
    }

    public function test_get_shipping_cost_standard_under_100()
    {
        $this->startSession();
        
        $cheapComponent = Component::factory()->create([
            'category_id' => $this->component1->category_id,
            'price' => 50.00,
            'stock' => 10,
            'is_active' => true,
        ]);
        
        $this->cartService->addToCart($cheapComponent, 1); // 50.00 (under $100)
        
        $shipping = $this->cartService->getShippingCost();
        
        $this->assertEquals(9.99, $shipping);
    }

    public function test_get_cart_totals_returns_complete_breakdown()
    {
        $this->startSession();
        
        $this->cartService->addToCart($this->component1, 1); // 299.99
        
        $totals = $this->cartService->getCartTotals(null, 0.08);
        
        $this->assertEquals(299.99, $totals['subtotal']);
        $this->assertEquals(24.00, $totals['tax']); // 299.99 * 0.08 rounded
        $this->assertEquals(0, $totals['shipping']); // Free shipping over $100
        $this->assertEquals(323.99, $totals['total']); // 299.99 + 24.00 + 0
    }

    public function test_validate_cart_stock_identifies_issues()
    {
        $this->startSession();
        
        // Add items to cart
        $this->cartService->addToCart($this->component1, 5);
        $this->cartService->addToCart($this->component2, 3);
        
        // Reduce stock to create issues
        $this->component1->update(['stock' => 2]);
        $this->component2->update(['is_active' => false]);
        
        $issues = $this->cartService->validateCartStock();
        
        $this->assertCount(2, $issues);
        
        // Check first issue (insufficient stock)
        $this->assertEquals('Insufficient stock', $issues[0]['issue']);
        $this->assertEquals(5, $issues[0]['current_quantity']);
        $this->assertEquals(2, $issues[0]['available_quantity']);
        
        // Check second issue (inactive component)
        $this->assertEquals('Component is no longer available', $issues[1]['issue']);
        $this->assertEquals(3, $issues[1]['current_quantity']);
        $this->assertEquals(0, $issues[1]['available_quantity']);
    }

    public function test_fix_cart_stock_issues_adjusts_quantities()
    {
        $this->startSession();
        
        // Add items to cart
        $this->cartService->addToCart($this->component1, 5);
        $this->cartService->addToCart($this->component2, 3);
        
        // Reduce stock to create issues
        $this->component1->update(['stock' => 2]);
        $this->component2->update(['is_active' => false]);
        
        $fixed = $this->cartService->fixCartStockIssues();
        
        $this->assertCount(2, $fixed);
        
        // Check that first item quantity was adjusted
        $this->assertEquals('quantity_adjusted', $fixed[0]['action']);
        $this->assertEquals(2, $fixed[0]['new_quantity']);
        
        // Check that second item was removed
        $this->assertEquals('item_removed', $fixed[1]['action']);
        
        // Verify cart state
        $cart = $this->cartService->getCart();
        $items = $cart->items;
        
        $this->assertEquals(1, $items->count());
        $this->assertEquals(2, $items->first()->quantity);
    }

    public function test_cleanup_expired_carts_removes_old_carts()
    {
        // Create old session carts
        $oldCart1 = new Cart([
            'session_id' => 'old-session-1',
            'user_id' => null,
        ]);
        $oldCart1->timestamps = false;
        $oldCart1->created_at = now()->subDays(10);
        $oldCart1->updated_at = now()->subDays(10);
        $oldCart1->save();
        
        $oldCart2 = new Cart([
            'session_id' => 'old-session-2',
            'user_id' => null,
        ]);
        $oldCart2->timestamps = false;
        $oldCart2->created_at = now()->subDays(8);
        $oldCart2->updated_at = now()->subDays(8);
        $oldCart2->save();
        
        // Create recent cart
        $recentCart = new Cart([
            'session_id' => 'recent-session',
            'user_id' => null,
        ]);
        $recentCart->timestamps = false;
        $recentCart->created_at = now()->subDays(3);
        $recentCart->updated_at = now()->subDays(3);
        $recentCart->save();
        
        $deletedCount = $this->cartService->cleanupExpiredCarts(7);
        
        $this->assertEquals(2, $deletedCount);
        $this->assertDatabaseMissing('carts', ['id' => $oldCart1->id]);
        $this->assertDatabaseMissing('carts', ['id' => $oldCart2->id]);
        $this->assertDatabaseHas('carts', ['id' => $recentCart->id]);
    }

    public function test_cleanup_empty_carts_removes_carts_without_items()
    {
        // Create empty carts
        $emptyCart1 = Cart::create(['session_id' => 'empty-1', 'user_id' => null]);
        $emptyCart2 = Cart::create(['session_id' => 'empty-2', 'user_id' => null]);
        
        // Create cart with items
        $cartWithItems = Cart::create(['session_id' => 'with-items', 'user_id' => null]);
        $cartWithItems->items()->create([
            'component_id' => $this->component1->id,
            'quantity' => 1,
            'price' => $this->component1->price,
        ]);
        
        $deletedCount = $this->cartService->cleanupEmptyCarts();
        
        $this->assertEquals(2, $deletedCount);
        $this->assertDatabaseMissing('carts', ['id' => $emptyCart1->id]);
        $this->assertDatabaseMissing('carts', ['id' => $emptyCart2->id]);
        $this->assertDatabaseHas('carts', ['id' => $cartWithItems->id]);
    }

    public function test_update_cart_prices_updates_changed_prices()
    {
        $this->startSession();
        
        $this->cartService->addToCart($this->component1, 2);
        $this->cartService->addToCart($this->component2, 1);
        
        // Change component prices
        $this->component1->update(['price' => 349.99]);
        $this->component2->update(['price' => 649.99]);
        
        $updated = $this->cartService->updateCartPrices();
        
        $this->assertCount(2, $updated);
        
        // Check first component price update
        $this->assertEquals(299.99, $updated[0]['old_price']);
        $this->assertEquals(349.99, $updated[0]['new_price']);
        
        // Check second component price update
        $this->assertEquals(599.99, $updated[1]['old_price']);
        $this->assertEquals(649.99, $updated[1]['new_price']);
        
        // Verify cart total was updated
        $expectedTotal = (349.99 * 2) + (649.99 * 1); // 1349.97
        $this->assertEquals(1349.97, $this->cartService->getTotal());
    }

    public function test_has_items_returns_correct_boolean()
    {
        $this->startSession();
        
        $this->assertFalse($this->cartService->hasItems());
        
        $this->cartService->addToCart($this->component1, 1);
        
        $this->assertTrue($this->cartService->hasItems());
    }

    public function test_get_cart_weight_calculates_total_weight()
    {
        $this->startSession();
        
        $this->cartService->addToCart($this->component1, 2); // 2 * 2.5 = 5.0
        $this->cartService->addToCart($this->component2, 1); // 1 * 3.2 = 3.2
        
        $weight = $this->cartService->getCartWeight();
        
        $this->assertEquals(8.2, $weight);
    }

    public function test_get_cart_weight_uses_default_weight_when_not_specified()
    {
        $this->startSession();
        
        $componentWithoutWeight = Component::factory()->create([
            'category_id' => $this->component1->category_id,
            'price' => 100.00,
            'stock' => 10,
            'is_active' => true,
            'specs' => [], // No weight specified
        ]);
        
        $this->cartService->addToCart($componentWithoutWeight, 2);
        
        $weight = $this->cartService->getCartWeight();
        
        $this->assertEquals(2.0, $weight); // 2 * 1.0 (default weight)
    }

    public function test_validate_cart_stock_returns_empty_array_when_no_issues()
    {
        $this->startSession();
        
        $this->cartService->addToCart($this->component1, 2);
        $this->cartService->addToCart($this->component2, 1);
        
        $issues = $this->cartService->validateCartStock();
        
        $this->assertEmpty($issues);
    }

    public function test_update_cart_prices_returns_empty_array_when_no_changes()
    {
        $this->startSession();
        
        $this->cartService->addToCart($this->component1, 1);
        
        $updated = $this->cartService->updateCartPrices();
        
        $this->assertEmpty($updated);
    }

    public function test_cart_calculations_work_for_authenticated_user()
    {
        $this->actingAs($this->user);
        
        $this->cartService->addToCart($this->component1, 1, $this->user);
        
        $totals = $this->cartService->getCartTotals($this->user, 0.08);
        
        $this->assertEquals(299.99, $totals['subtotal']);
        $this->assertEquals(24.00, $totals['tax']);
        $this->assertEquals(0, $totals['shipping']);
        $this->assertEquals(323.99, $totals['total']);
    }
}