<?php

namespace Database\Factories;

use App\Models\Component;
use App\Models\InventoryAlert;
use Illuminate\Database\Eloquent\Factories\Factory;

class InventoryAlertFactory extends Factory
{
    protected $model = InventoryAlert::class;

    public function definition(): array
    {
        $currentStock = $this->faker->numberBetween(0, 20);
        $type = $this->faker->randomElement(['low_stock', 'out_of_stock', 'restock_needed']);
        
        $threshold = match ($type) {
            'low_stock' => $this->faker->numberBetween($currentStock + 1, 15),
            'out_of_stock' => 0,
            'restock_needed' => $this->faker->numberBetween(5, 10),
            default => 10
        };

        return [
            'component_id' => Component::factory(),
            'type' => $type,
            'current_stock' => $currentStock,
            'threshold' => $threshold,
            'is_resolved' => $this->faker->boolean(30), // 30% chance of being resolved
            'resolved_at' => $this->faker->optional(0.3)->dateTimeBetween('-1 week', 'now'),
            'metadata' => [
                'component_name' => $this->faker->words(3, true),
                'category' => $this->faker->word()
            ]
        ];
    }

    public function lowStock(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'low_stock',
            'current_stock' => $this->faker->numberBetween(1, 10),
            'threshold' => $this->faker->numberBetween(10, 15)
        ]);
    }

    public function outOfStock(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'out_of_stock',
            'current_stock' => 0,
            'threshold' => 0
        ]);
    }

    public function unresolved(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_resolved' => false,
            'resolved_at' => null
        ]);
    }

    public function resolved(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_resolved' => true,
            'resolved_at' => $this->faker->dateTimeBetween('-1 week', 'now')
        ]);
    }
}