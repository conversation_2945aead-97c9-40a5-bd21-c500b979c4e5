<div class="container-fluid py-4">
    {{-- Header --}}
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">PC Components Management</h1>
            <p class="text-muted">Manage your PC building components inventory</p>
        </div>
        <button type="button" class="btn btn-primary" wire:click="create">
            <i class="fas fa-plus me-2"></i>Add Component
        </button>
    </div>

    {{-- Filters --}}
    <div class="card mb-4">
        <div class="card-body">
            <div class="row g-3">
                <div class="col-md-3">
                    <label class="form-label">Search</label>
                    <input type="text" class="form-control" wire:model.live="search"
                        placeholder="Search components...">
                </div>
                <div class="col-md-2">
                    <label class="form-label">Category</label>
                    <select class="form-select" wire:model.live="categoryFilter">
                        <option value="">All Categories</option>
                        @foreach ($categories as $category)
                            <option value="{{ $category->id }}">{{ $category->name }}</option>
                        @endforeach
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">Brand</label>
                    <input type="text" class="form-control" wire:model.live="brandFilter"
                        placeholder="Filter by brand">
                </div>
                <div class="col-md-2">
                    <label class="form-label">Status</label>
                    <select class="form-select" wire:model.live="statusFilter">
                        <option value="">All Status</option>
                        <option value="active">Active</option>
                        <option value="inactive">Inactive</option>
                        <option value="featured">Featured</option>
                        <option value="out_of_stock">Out of Stock</option>
                    </select>
                </div>
                <div class="col-md-3 d-flex align-items-end">
                    <button type="button" class="btn btn-outline-secondary" wire:click="$refresh">
                        <i class="fas fa-sync-alt me-2"></i>Refresh
                    </button>
                </div>
            </div>
        </div>
    </div>

    {{-- Components Table --}}
    <div class="card">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead class="table-light">
                        <tr>
                            <th wire:click="sortBy('name')" style="cursor: pointer;">
                                Name
                                @if ($sortBy === 'name')
                                    <i class="fas fa-sort-{{ $sortDirection === 'asc' ? 'up' : 'down' }} ms-1"></i>
                                @endif
                            </th>
                            <th>Image</th>
                            <th wire:click="sortBy('brand')" style="cursor: pointer;">
                                Brand
                                @if ($sortBy === 'brand')
                                    <i class="fas fa-sort-{{ $sortDirection === 'asc' ? 'up' : 'down' }} ms-1"></i>
                                @endif
                            </th>
                            <th>Category</th>
                            <th wire:click="sortBy('price')" style="cursor: pointer;">
                                Price
                                @if ($sortBy === 'price')
                                    <i class="fas fa-sort-{{ $sortDirection === 'asc' ? 'up' : 'down' }} ms-1"></i>
                                @endif
                            </th>
                            <th wire:click="sortBy('stock')" style="cursor: pointer;">
                                Stock
                                @if ($sortBy === 'stock')
                                    <i class="fas fa-sort-{{ $sortDirection === 'asc' ? 'up' : 'down' }} ms-1"></i>
                                @endif
                            </th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($components as $component)
                            <tr>
                                <td>
                                    <div>
                                        <strong>{{ $component->name }}</strong>
                                        <br>
                                        <small class="text-muted">{{ $component->model }}</small>
                                    </div>
                                </td>
                                <td>
                                    @if ($component->image)
                                        <img src="{{ Storage::url($component->image) }}" alt="{{ $component->name }}"
                                            class="img-thumbnail" style="width: 50px; height: 50px; object-fit: cover;">
                                    @else
                                        <div class="bg-light d-flex align-items-center justify-content-center"
                                            style="width: 50px; height: 50px;">
                                            <i class="fas fa-image text-muted"></i>
                                        </div>
                                    @endif
                                </td>
                                <td>{{ $component->brand }}</td>
                                <td>
                                    <span class="badge bg-secondary">{{ $component->category->name ?? 'N/A' }}</span>
                                </td>
                                <td>${{ number_format($component->price, 2) }}</td>
                                <td>
                                    <span class="badge bg-{{ $component->stock > 0 ? 'success' : 'danger' }}">
                                        {{ $component->stock }}
                                    </span>
                                </td>
                                <td>
                                    <div class="d-flex gap-1">
                                        <span class="badge bg-{{ $component->is_active ? 'success' : 'secondary' }}">
                                            {{ $component->is_active ? 'Active' : 'Inactive' }}
                                        </span>
                                        @if ($component->is_featured)
                                            <span class="badge bg-warning">Featured</span>
                                        @endif
                                    </div>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <button type="button" class="btn btn-sm btn-outline-primary"
                                            wire:click="edit({{ $component->id }})">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-warning"
                                            wire:click="toggleFeatured({{ $component->id }})">
                                            <i class="fas fa-star"></i>
                                        </button>
                                        <button type="button"
                                            class="btn btn-sm btn-outline-{{ $component->is_active ? 'secondary' : 'success' }}"
                                            wire:click="toggleActive({{ $component->id }})">
                                            <i class="fas fa-power-off"></i>
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-danger"
                                            wire:click="confirmDelete({{ $component->id }})">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="8" class="text-center py-4">
                                    <div class="text-muted">
                                        <i class="fas fa-inbox fa-3x mb-3"></i>
                                        <p>No components found</p>
                                    </div>
                                </td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>

            {{-- Pagination --}}
            @if ($components->hasPages())
                <div class="d-flex justify-content-between align-items-center mt-3">
                    <div class="text-muted">
                        Showing {{ $components->firstItem() }} to {{ $components->lastItem() }}
                        of {{ $components->total() }} results
                    </div>
                    {{ $components->links() }}
                </div>
            @endif
        </div>
    </div>

    {{-- Create/Edit Modal --}}
    @if ($showModal)
        <div class="modal fade show d-block" tabindex="-1" style="background-color: rgba(0,0,0,0.5);">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            {{ $editingComponentId ? 'Edit Component' : 'Create Component' }}
                        </h5>
                        <button type="button" class="btn-close" wire:click="closeModal"></button>
                    </div>
                    <form wire:submit="save">
                        <div class="modal-body">
                            <div class="row g-3">
                                {{-- Basic Information --}}
                                <div class="col-md-6">
                                    <label class="form-label">Name *</label>
                                    <input type="text" class="form-control @error('name') is-invalid @enderror"
                                        wire:model="name" required>
                                    @error('name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="col-md-6">
                                    <label class="form-label">Slug *</label>
                                    <input type="text" class="form-control @error('slug') is-invalid @enderror"
                                        wire:model="slug" required>
                                    @error('slug')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="col-12">
                                    <label class="form-label">Description</label>
                                    <textarea class="form-control @error('description') is-invalid @enderror" wire:model="description" rows="3"></textarea>
                                    @error('description')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="col-md-4">
                                    <label class="form-label">Category *</label>
                                    <select class="form-select @error('category_id') is-invalid @enderror"
                                        wire:model="category_id" required>
                                        <option value="">Select Category</option>
                                        @foreach ($categories as $category)
                                            <option value="{{ $category->id }}">{{ $category->name }}</option>
                                        @endforeach
                                    </select>
                                    @error('category_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="col-md-4">
                                    <label class="form-label">Brand *</label>
                                    <input type="text" class="form-control @error('brand') is-invalid @enderror"
                                        wire:model="brand" required>
                                    @error('brand')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="col-md-4">
                                    <label class="form-label">Model *</label>
                                    <input type="text" class="form-control @error('model') is-invalid @enderror"
                                        wire:model="model" required>
                                    @error('model')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="col-md-6">
                                    <label class="form-label">Price *</label>
                                    <div class="input-group">
                                        <span class="input-group-text">$</span>
                                        <input type="number" step="0.01"
                                            class="form-control @error('price') is-invalid @enderror"
                                            wire:model="price" required>
                                    </div>
                                    @error('price')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="col-md-6">
                                    <label class="form-label">Stock *</label>
                                    <input type="number" class="form-control @error('stock') is-invalid @enderror"
                                        wire:model="stock" required>
                                    @error('stock')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                {{-- Image Upload --}}
                                <div class="col-12">
                                    <label class="form-label">Image</label>
                                    <input type="file" class="form-control @error('image') is-invalid @enderror"
                                        wire:model="image" accept="image/*">
                                    @error('image')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror

                                    @if ($image)
                                        <div class="mt-2">
                                            <img src="{{ $image->temporaryUrl() }}" alt="Preview"
                                                class="img-thumbnail" style="max-width: 200px;">
                                        </div>
                                    @elseif($existing_image)
                                        <div class="mt-2">
                                            <img src="{{ Storage::url($existing_image) }}" alt="Current"
                                                class="img-thumbnail" style="max-width: 200px;">
                                        </div>
                                    @endif
                                </div>

                                {{-- Specifications --}}
                                <div class="col-12">
                                    <label class="form-label">Specifications</label>
                                    <div class="card">
                                        <div class="card-body">
                                            <div class="row g-2 mb-3">
                                                <div class="col-md-4">
                                                    <input type="text" class="form-control" wire:model="spec_key"
                                                        placeholder="Specification name">
                                                </div>
                                                <div class="col-md-6">
                                                    <input type="text" class="form-control"
                                                        wire:model="spec_value" placeholder="Value">
                                                </div>
                                                <div class="col-md-2">
                                                    <button type="button" class="btn btn-outline-primary w-100"
                                                        wire:click="addSpec">
                                                        Add
                                                    </button>
                                                </div>
                                            </div>

                                            @if (!empty($specs))
                                                <div class="border-top pt-3">
                                                    @foreach ($specs as $key => $value)
                                                        <div
                                                            class="d-flex justify-content-between align-items-center mb-2">
                                                            <span><strong>{{ $key }}:</strong>
                                                                {{ $value }}</span>
                                                            <button type="button"
                                                                class="btn btn-sm btn-outline-danger"
                                                                wire:click="removeSpec('{{ $key }}')">
                                                                <i class="fas fa-times"></i>
                                                            </button>
                                                        </div>
                                                    @endforeach
                                                </div>
                                            @endif
                                        </div>
                                    </div>
                                </div>

                                {{-- Status Options --}}
                                <div class="col-12">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox"
                                                    wire:model="is_featured" id="is_featured">
                                                <label class="form-check-label" for="is_featured">
                                                    Featured Component
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox"
                                                    wire:model="is_active" id="is_active">
                                                <label class="form-check-label" for="is_active">
                                                    Active
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" wire:click="closeModal">Cancel</button>
                            <button type="submit" class="btn btn-primary">
                                {{ $editingComponentId ? 'Update' : 'Create' }} Component
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    @endif

    {{-- Delete Confirmation Modal --}}
    @if ($confirmingDeletion)
        <div class="modal fade show d-block" tabindex="-1" style="background-color: rgba(0,0,0,0.5);">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Confirm Deletion</h5>
                        <button type="button" class="btn-close"
                            wire:click="$set('confirmingDeletion', false)"></button>
                    </div>
                    <div class="modal-body">
                        <p>Are you sure you want to delete this component? This action cannot be undone.</p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary"
                            wire:click="$set('confirmingDeletion', false)">Cancel</button>
                        <button type="button" class="btn btn-danger" wire:click="delete">Delete Component</button>
                    </div>
                </div>
            </div>
        </div>
    @endif

    {{-- Loading Indicator --}}
    <div wire:loading.delay class="position-fixed top-50 start-50 translate-middle">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
    </div>

    {{-- Scripts --}}
    @push('scripts')
        <script>
            // Alert handling
            window.addEventListener('alert', event => {
                const alert = event.detail[0];

                // Create alert element
                const alertHtml = `
            <div class="alert alert-${alert.type} alert-dismissible fade show" role="alert">
                ${alert.message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;

                // Insert at top of container
                const container = document.querySelector('.container-fluid');
                container.insertAdjacentHTML('afterbegin', alertHtml);

                // Auto-dismiss after 5 seconds
                setTimeout(() => {
                    const alertElement = container.querySelector('.alert');
                    if (alertElement) {
                        const bsAlert = new bootstrap.Alert(alertElement);
                        bsAlert.close();
                    }
                }, 5000);
            });
        </script>
    @endpush

    {{-- Styles --}}
    @push('styles')
        <style>
            .table th[wire\:click] {
                user-select: none;
            }

            .table th[wire\:click]:hover {
                background-color: #f8f9fa;
            }

            .modal.show {
                display: block !important;
            }

            .btn-group .btn {
                border-radius: 0.375rem !important;
                margin-right: 2px;
            }

            .btn-group .btn:last-child {
                margin-right: 0;
            }

            .img-thumbnail {
                border-radius: 0.5rem;
            }

            .badge {
                font-size: 0.75em;
            }

            .spinner-border {
                z-index: 9999;
            }

            .form-check-input:checked {
                background-color: #0d6efd;
                border-color: #0d6efd;
            }

            .card {
                box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
                border: 1px solid rgba(0, 0, 0, 0.125);
            }

            .table-responsive {
                border-radius: 0.5rem;
            }

            .alert {
                border-radius: 0.5rem;
                margin-bottom: 1rem;
            }
        </style>
    @endpush
</div>
