<?php

/**
 * Comprehensive Dusk Test Runner
 * 
 * This script runs Laravel Dusk tests with proper setup and reporting.
 */

require_once __DIR__ . '/vendor/autoload.php';

class DuskTestRunner
{
    private array $testSuites = [
        'payment' => [
            'PaymentFlowTest',
            'PaymentGatewayIntegrationTest',
        ],
        'admin' => [
            'AdminPanelTest',
            'AdminBrowserTest',
        ],
        'products' => [
            'ProductBrowsingTest',
        ],
        'basic' => [
            'BasicApplicationTest',
            'ExampleTest',
        ],
        'all' => [
            'BasicApplicationTest',
            'PaymentFlowTest',
            'AdminPanelTest',
            'ProductBrowsingTest',
            'ExampleTest',
        ]
    ];

    public function run(string $suite = 'all', array $options = []): void
    {
        $this->displayHeader();
        $this->checkPrerequisites();
        $this->setupEnvironment();
        
        $tests = $this->testSuites[$suite] ?? $this->testSuites['all'];
        
        echo "🚀 Running Dusk test suite: " . strtoupper($suite) . "\n";
        echo "Tests to run: " . implode(', ', $tests) . "\n\n";

        $results = [];
        $startTime = microtime(true);

        foreach ($tests as $test) {
            echo "Running {$test}...\n";
            $result = $this->runSingleTest($test, $options);
            $results[$test] = $result;
            
            if ($result['success']) {
                echo "✅ {$test} passed\n";
            } else {
                echo "❌ {$test} failed\n";
                if (!empty($result['output'])) {
                    echo "Output: {$result['output']}\n";
                }
            }
            echo "\n";
        }

        $endTime = microtime(true);
        $this->displaySummary($results, $endTime - $startTime);
    }

    private function displayHeader(): void
    {
        echo "\n";
        echo "╔══════════════════════════════════════════════════════════════╗\n";
        echo "║                    Laravel Dusk Test Runner                 ║\n";
        echo "║                                                              ║\n";
        echo "║  Comprehensive browser testing for payment gateway system   ║\n";
        echo "╚══════════════════════════════════════════════════════════════╝\n";
        echo "\n";
    }

    private function checkPrerequisites(): void
    {
        echo "🔍 Checking prerequisites...\n";

        // Check if Chrome/Chromium is installed
        $chromeCheck = shell_exec('where chrome 2>nul') ?: shell_exec('where chromium 2>nul');
        if (!$chromeCheck) {
            echo "⚠️  Warning: Chrome/Chromium not found in PATH\n";
        } else {
            echo "✅ Chrome/Chromium found\n";
        }

        // Check if ChromeDriver is available
        if (!file_exists(__DIR__ . '/vendor/laravel/dusk/bin/chromedriver-win32.exe')) {
            echo "⚠️  Warning: ChromeDriver not found\n";
        } else {
            echo "✅ ChromeDriver found\n";
        }

        // Check if .env.dusk.local exists
        if (!file_exists(__DIR__ . '/.env.dusk.local')) {
            echo "⚠️  Warning: .env.dusk.local not found\n";
        } else {
            echo "✅ Dusk environment configuration found\n";
        }

        echo "\n";
    }

    private function setupEnvironment(): void
    {
        echo "🔧 Setting up test environment...\n";

        // Set environment variables
        putenv('APP_ENV=dusk.local');
        putenv('DB_CONNECTION=sqlite');
        putenv('DB_DATABASE=:memory:');
        
        echo "✅ Environment configured for testing\n\n";
    }

    private function runSingleTest(string $test, array $options = []): array
    {
        $command = "php artisan dusk tests/Browser/{$test}.php";
        
        if (isset($options['headless']) && !$options['headless']) {
            $command .= ' --without-headless';
        }

        if (isset($options['verbose']) && $options['verbose']) {
            $command .= ' --verbose';
        }

        $output = [];
        $returnCode = 0;
        
        exec($command . ' 2>&1', $output, $returnCode);
        
        return [
            'success' => $returnCode === 0,
            'output' => implode("\n", $output),
            'return_code' => $returnCode
        ];
    }

    private function displaySummary(array $results, float $duration): void
    {
        $total = count($results);
        $passed = count(array_filter($results, fn($r) => $r['success']));
        $failed = $total - $passed;

        echo "╔══════════════════════════════════════════════════════════════╗\n";
        echo "║                        TEST SUMMARY                         ║\n";
        echo "╠══════════════════════════════════════════════════════════════╣\n";
        echo sprintf("║ Total Tests: %-3d | Passed: %-3d | Failed: %-3d          ║\n", $total, $passed, $failed);
        echo sprintf("║ Duration: %-8.2f seconds                               ║\n", $duration);
        echo "╚══════════════════════════════════════════════════════════════╝\n";

        if ($failed > 0) {
            echo "\n❌ Failed Tests:\n";
            foreach ($results as $test => $result) {
                if (!$result['success']) {
                    echo "  - {$test}\n";
                }
            }
        }

        echo "\n";
    }

    public function listSuites(): void
    {
        echo "Available test suites:\n\n";
        foreach ($this->testSuites as $suite => $tests) {
            echo "📋 {$suite}:\n";
            foreach ($tests as $test) {
                echo "  - {$test}\n";
            }
            echo "\n";
        }
    }
}

// CLI Interface
if (php_sapi_name() === 'cli') {
    $options = getopt('s:hv', ['suite:', 'help', 'verbose', 'no-headless', 'list']);
    
    $runner = new DuskTestRunner();
    
    if (isset($options['help']) || isset($options['h'])) {
        echo "Laravel Dusk Test Runner\n\n";
        echo "Usage: php run_dusk_tests.php [options]\n\n";
        echo "Options:\n";
        echo "  -s, --suite <suite>    Run specific test suite (payment, admin, products, all)\n";
        echo "  -v, --verbose          Verbose output\n";
        echo "  --no-headless          Run tests with browser visible\n";
        echo "  --list                 List available test suites\n";
        echo "  -h, --help             Show this help message\n\n";
        exit(0);
    }
    
    if (isset($options['list'])) {
        $runner->listSuites();
        exit(0);
    }
    
    $suite = $options['s'] ?? $options['suite'] ?? 'all';
    $testOptions = [
        'verbose' => isset($options['v']) || isset($options['verbose']),
        'headless' => !isset($options['no-headless'])
    ];
    
    $runner->run($suite, $testOptions);
}