<?php

namespace App\Services;

use App\Models\EmailLog;
use Illuminate\Support\Facades\Log;

class EmailTrackingService
{
    /**
     * Log an email attempt.
     */
    public function logEmailAttempt(
        string $type,
        string $recipient,
        string $subject,
        ?int $relatedId = null,
        ?string $relatedType = null
    ): EmailLog {
        return EmailLog::create([
            'type' => $type,
            'recipient' => $recipient,
            'subject' => $subject,
            'status' => 'pending',
            'related_id' => $relatedId,
            'related_type' => $relatedType,
            'attempts' => 1,
            'sent_at' => now(),
        ]);
    }

    /**
     * Mark an email as successfully sent.
     */
    public function markEmailSent(EmailLog $emailLog): void
    {
        $emailLog->update([
            'status' => 'sent',
            'delivered_at' => now(),
        ]);

        Log::info('Email marked as sent', [
            'email_log_id' => $emailLog->id,
            'type' => $emailLog->type,
            'recipient' => $emailLog->recipient,
        ]);
    }

    /**
     * Mark an email as failed.
     */
    public function markEmailFailed(EmailLog $emailLog, string $errorMessage, int $attempts): void
    {
        $emailLog->update([
            'status' => 'failed',
            'error_message' => $errorMessage,
            'attempts' => $attempts,
            'failed_at' => now(),
        ]);

        Log::error('Email marked as failed', [
            'email_log_id' => $emailLog->id,
            'type' => $emailLog->type,
            'recipient' => $emailLog->recipient,
            'error' => $errorMessage,
            'attempts' => $attempts,
        ]);
    }

    /**
     * Increment retry attempt for an email.
     */
    public function incrementAttempt(EmailLog $emailLog): void
    {
        $emailLog->increment('attempts');
        
        Log::info('Email attempt incremented', [
            'email_log_id' => $emailLog->id,
            'type' => $emailLog->type,
            'recipient' => $emailLog->recipient,
            'attempts' => $emailLog->attempts,
        ]);
    }

    /**
     * Get email statistics for a given period.
     */
    public function getEmailStats(int $days = 30): array
    {
        $startDate = now()->subDays($days);

        $stats = EmailLog::where('created_at', '>=', $startDate)
            ->selectRaw('
                type,
                status,
                COUNT(*) as count,
                AVG(attempts) as avg_attempts
            ')
            ->groupBy('type', 'status')
            ->get();

        $result = [];
        foreach ($stats as $stat) {
            $result[$stat->type][$stat->status] = [
                'count' => $stat->count,
                'avg_attempts' => round($stat->avg_attempts, 2),
            ];
        }

        return $result;
    }

    /**
     * Get failed emails that might need manual intervention.
     */
    public function getFailedEmails(int $limit = 50): \Illuminate\Database\Eloquent\Collection
    {
        return EmailLog::where('status', 'failed')
            ->where('attempts', '>=', 3)
            ->orderBy('failed_at', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * Clean up old email logs.
     */
    public function cleanupOldLogs(int $daysToKeep = 90): int
    {
        $cutoffDate = now()->subDays($daysToKeep);
        
        $deletedCount = EmailLog::where('created_at', '<', $cutoffDate)->delete();
        
        Log::info('Email logs cleanup completed', [
            'deleted_count' => $deletedCount,
            'cutoff_date' => $cutoffDate->toDateString(),
        ]);

        return $deletedCount;
    }
}