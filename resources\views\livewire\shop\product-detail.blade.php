<div>
    @if($component)
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <!-- Breadcrumbs -->
            <nav class="flex mb-6" aria-label="Breadcrumb">
                <ol class="inline-flex items-center space-x-1 md:space-x-3">
                    <li class="inline-flex items-center">
                        <a href="{{ route('shop.index') }}" class="text-gray-700 hover:text-blue-600">
                            Shop
                        </a>
                    </li>
                    <li>
                        <div class="flex items-center">
                            <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            <a href="{{ route('shop.category', $component->category->slug) }}" class="text-gray-700 hover:text-blue-600 ml-1 md:ml-2">
                                {{ $component->category->name }}
                            </a>
                        </div>
                    </li>
                    <li aria-current="page">
                        <div class="flex items-center">
                            <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            <span class="text-gray-500 ml-1 md:ml-2">{{ $component->name }}</span>
                        </div>
                    </li>
                </ol>
            </nav>
            
            <!-- Product Details -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
                <!-- Product Image -->
                <div class="bg-white rounded-lg shadow overflow-hidden">
                    @if($component->image)
                        <img src="{{ $component->image }}" alt="{{ $component->name }}" class="w-full h-auto object-cover">
                    @else
                        <div class="w-full h-96 bg-gray-200 flex items-center justify-center">
                            <span class="text-gray-400">No image available</span>
                        </div>
                    @endif
                </div>
                
                <!-- Product Info -->
                <div class="bg-white rounded-lg shadow p-6">
                    <h1 class="text-3xl font-bold text-gray-900 mb-2">{{ $component->name }}</h1>
                    
                    <div class="flex items-center mb-4">
                        <span class="text-gray-600 mr-4">Brand: <span class="font-medium">{{ $component->brand }}</span></span>
                        <span class="text-gray-600">Model: <span class="font-medium">{{ $component->model }}</span></span>
                    </div>
                    
                    <div class="text-2xl font-bold text-gray-900 mb-6">${{ number_format($component->price, 2) }}</div>
                    
                    @if($component->description)
                        <div class="text-gray-700 mb-6">
                            {{ $component->description }}
                        </div>
                    @endif
                    
                    <!-- Stock Status -->
                    <div class="mb-6">
                        @if($component->stock > 10)
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                                <svg class="-ml-1 mr-1.5 h-2 w-2 text-green-400" fill="currentColor" viewBox="0 0 8 8">
                                    <circle cx="4" cy="4" r="3" />
                                </svg>
                                In Stock
                            </span>
                        @elseif($component->stock > 0)
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800">
                                <svg class="-ml-1 mr-1.5 h-2 w-2 text-yellow-400" fill="currentColor" viewBox="0 0 8 8">
                                    <circle cx="4" cy="4" r="3" />
                                </svg>
                                Low Stock ({{ $component->stock }} left)
                            </span>
                        @else
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-100 text-red-800">
                                <svg class="-ml-1 mr-1.5 h-2 w-2 text-red-400" fill="currentColor" viewBox="0 0 8 8">
                                    <circle cx="4" cy="4" r="3" />
                                </svg>
                                Out of Stock
                            </span>
                        @endif
                    </div>
                    
                    <!-- Add to Cart -->
                    <div class="mb-6">
                        <label for="quantity" class="block text-sm font-medium text-gray-700 mb-2">Quantity</label>
                        <div class="flex">
                            <button 
                                wire:click="decrementQuantity" 
                                class="px-3 py-2 border border-gray-300 bg-gray-100 text-gray-600 rounded-l-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                                {{ $quantity <= 1 ? 'disabled' : '' }}
                            >
                                -
                            </button>
                            
                            <input 
                                type="number" 
                                id="quantity" 
                                wire:model="quantity" 
                                min="1" 
                                max="{{ $component->stock }}" 
                                class="w-16 text-center border-t border-b border-gray-300 focus:outline-none focus:ring-0 focus:border-gray-300"
                                {{ $component->stock <= 0 ? 'disabled' : '' }}
                            >
                            
                            <button 
                                wire:click="incrementQuantity" 
                                class="px-3 py-2 border border-gray-300 bg-gray-100 text-gray-600 rounded-r-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                                {{ $quantity >= $component->stock ? 'disabled' : '' }}
                            >
                                +
                            </button>
                        </div>
                    </div>
                    
                    <div class="flex space-x-4">
                        <button 
                            wire:click="addToCart" 
                            class="flex-1 px-6 py-3 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 {{ $component->stock <= 0 ? 'opacity-50 cursor-not-allowed' : '' }}"
                            {{ $component->stock <= 0 ? 'disabled' : '' }}
                        >
                            Add to Cart
                        </button>
                        
                        <a 
                            href="{{ route('builder.index', ['component_id' => $component->id]) }}" 
                            class="px-6 py-3 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                        >
                            Add to Build
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Specifications -->
            @if($component->specs && count($component->specs) > 0)
                <div class="bg-white rounded-lg shadow p-6 mb-12">
                    <h2 class="text-xl font-bold text-gray-900 mb-4">Specifications</h2>
                    
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <tbody class="bg-white divide-y divide-gray-200">
                                @foreach($component->specs as $key => $value)
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 bg-gray-50 w-1/3">{{ $key }}</td>
                                        <td class="px-6 py-4 whitespace-normal text-sm text-gray-500">{{ $value }}</td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            @endif
            
            <!-- Reviews -->
            @if($component->reviews && $component->reviews->count() > 0)
                <div class="bg-white rounded-lg shadow p-6 mb-12">
                    <h2 class="text-xl font-bold text-gray-900 mb-4">Customer Reviews</h2>
                    
                    <div class="space-y-6">
                        @foreach($component->reviews as $review)
                            <div class="border-b border-gray-200 pb-6 last:border-b-0 last:pb-0">
                                <div class="flex items-center mb-2">
                                    <div class="flex items-center">
                                        @for($i = 1; $i <= 5; $i++)
                                            <svg class="h-5 w-5 {{ $i <= $review->rating ? 'text-yellow-400' : 'text-gray-300' }}" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                            </svg>
                                        @endfor
                                    </div>
                                    <span class="text-gray-900 font-medium ml-3">{{ $review->title }}</span>
                                </div>
                                
                                <div class="text-sm text-gray-500 mb-2">
                                    By {{ $review->user->name }} on {{ $review->created_at->format('M d, Y') }}
                                </div>
                                
                                <div class="text-gray-700">
                                    {{ $review->content }}
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            @endif
            
            <!-- Related Products -->
            @if(count($relatedProducts) > 0)
                <div class="mb-12">
                    <h2 class="text-xl font-bold text-gray-900 mb-6">Related Products</h2>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                        @foreach($relatedProducts as $relatedProduct)
                            <div class="border rounded-lg overflow-hidden hover:shadow-lg transition-shadow">
                                @if($relatedProduct->image)
                                    <img src="{{ $relatedProduct->image }}" alt="{{ $relatedProduct->name }}" class="w-full h-48 object-cover">
                                @else
                                    <div class="w-full h-48 bg-gray-200 flex items-center justify-center">
                                        <span class="text-gray-400">No image</span>
                                    </div>
                                @endif
                                
                                <div class="p-4">
                                    <h3 class="font-semibold text-lg mb-2">
                                        <a href="{{ route('shop.product', $relatedProduct->slug) }}" class="text-gray-900 hover:text-blue-600">
                                            {{ $relatedProduct->name }}
                                        </a>
                                    </h3>
                                    <p class="text-gray-600 mb-1">{{ $relatedProduct->brand }} {{ $relatedProduct->model }}</p>
                                    <p class="text-gray-800 font-bold">${{ number_format($relatedProduct->price, 2) }}</p>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            @endif
        </div>
    @else
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12 text-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900">Product not found</h3>
            <p class="mt-1 text-sm text-gray-500">The product you are looking for does not exist or has been removed.</p>
            <div class="mt-6">
                <a href="{{ route('shop.index') }}" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    Back to Shop
                </a>
            </div>
        </div>
    @endif
    
    <!-- Flash Messages -->
    @if(session()->has('message'))
        <div class="fixed bottom-4 right-4 bg-green-100 border-l-4 border-green-500 text-green-700 p-4 rounded shadow-md" 
             x-data="{ show: true }" 
             x-show="show" 
             x-init="setTimeout(() => show = false, 3000)">
            <p>{{ session('message') }}</p>
        </div>
    @endif
    
    @if(session()->has('error'))
        <div class="fixed bottom-4 right-4 bg-red-100 border-l-4 border-red-500 text-red-700 p-4 rounded shadow-md" 
             x-data="{ show: true }" 
             x-show="show" 
             x-init="setTimeout(() => show = false, 3000)">
            <p>{{ session('error') }}</p>
        </div>
    @endif
</div>