<?php

namespace Database\Factories;

use App\Models\Component;
use App\Models\StockMovement;
use Illuminate\Database\Eloquent\Factories\Factory;

class StockMovementFactory extends Factory
{
    protected $model = StockMovement::class;

    public function definition(): array
    {
        $previousStock = $this->faker->numberBetween(0, 100);
        $quantityChange = $this->faker->numberBetween(-20, 20);
        $newStock = max(0, $previousStock + $quantityChange);

        return [
            'component_id' => Component::factory(),
            'quantity_change' => $quantityChange,
            'previous_stock' => $previousStock,
            'new_stock' => $newStock,
            'type' => $this->faker->randomElement(['sale', 'restock', 'adjustment', 'return', 'damage', 'supplier_sync']),
            'reason' => $this->faker->randomElement(['order_fulfillment', 'supplier_delivery', 'manual_adjustment', 'return_processing', 'damage_report']),
            'reference' => $this->faker->optional()->bothify('REF-####'),
            'metadata' => [
                'component_name' => $this->faker->words(3, true),
                'timestamp' => now()->toISOString()
            ]
        ];
    }

    public function sale(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'sale',
            'quantity_change' => -$this->faker->numberBetween(1, 10),
            'reason' => 'order_fulfillment'
        ]);
    }

    public function restock(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'restock',
            'quantity_change' => $this->faker->numberBetween(10, 50),
            'reason' => 'supplier_delivery'
        ]);
    }
}