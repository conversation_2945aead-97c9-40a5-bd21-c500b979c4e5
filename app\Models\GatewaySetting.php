<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Cache;

class GatewaySetting extends Model
{
    // Supported gateway names
    public const GATEWAY_RAZORPAY = 'razorpay';
    public const GATEWAY_PAYUMONEY = 'payumoney';
    public const GATEWAY_CASHFREE = 'cashfree';

    protected $fillable = [
        'gateway_name', 'is_enabled', 'is_test_mode', 'settings'
    ];
    
    protected $casts = [
        'is_enabled' => 'boolean',
        'is_test_mode' => 'boolean',
        'settings' => 'array'
    ];

    public function scopeEnabled($query)
    {
        return $query->where('is_enabled', true);
    }

    public function scopeByGateway($query, string $gateway)
    {
        return $query->where('gateway_name', $gateway);
    }

    public function scopeTestMode($query)
    {
        return $query->where('is_test_mode', true);
    }

    public function scopeLiveMode($query)
    {
        return $query->where('is_test_mode', false);
    }

    /**
     * Get gateway configuration for a specific gateway
     */
    public static function getGatewayConfig(string $gatewayName): ?array
    {
        $cacheKey = "gateway_config_{$gatewayName}";
        
        return Cache::remember($cacheKey, 3600, function () use ($gatewayName) {
            $setting = self::byGateway($gatewayName)->first();
            return $setting ? $setting->toArray() : null;
        });
    }

    /**
     * Get all enabled gateways
     */
    public static function getEnabledGateways(): array
    {
        $cacheKey = 'enabled_gateways';
        
        return Cache::remember($cacheKey, 3600, function () {
            return self::enabled()->get()->keyBy('gateway_name')->toArray();
        });
    }

    /**
     * Check if a gateway is enabled
     */
    public static function isGatewayEnabled(string $gatewayName): bool
    {
        $config = self::getGatewayConfig($gatewayName);
        return $config && $config['is_enabled'];
    }

    /**
     * Enable the gateway
     */
    public function enable(): bool
    {
        $result = $this->update(['is_enabled' => true]);
        $this->clearCache();
        return $result;
    }

    /**
     * Disable the gateway
     */
    public function disable(): bool
    {
        $result = $this->update(['is_enabled' => false]);
        $this->clearCache();
        return $result;
    }

    /**
     * Toggle gateway status
     */
    public function toggle(): bool
    {
        $result = $this->update(['is_enabled' => !$this->is_enabled]);
        $this->clearCache();
        return $result;
    }

    /**
     * Set test mode
     */
    public function setTestMode(bool $testMode = true): bool
    {
        $result = $this->update(['is_test_mode' => $testMode]);
        $this->clearCache();
        return $result;
    }

    /**
     * Get a specific setting value
     */
    public function getSetting(string $key, $default = null)
    {
        $settings = $this->settings ?? [];
        return $settings[$key] ?? $default;
    }

    /**
     * Set a specific setting value
     */
    public function setSetting(string $key, $value): bool
    {
        $settings = $this->settings ?? [];
        $settings[$key] = $value;
        $result = $this->update(['settings' => $settings]);
        $this->clearCache();
        return $result;
    }

    /**
     * Update multiple settings at once
     */
    public function updateSettings(array $newSettings): bool
    {
        $settings = array_merge($this->settings ?? [], $newSettings);
        $result = $this->update(['settings' => $settings]);
        $this->clearCache();
        return $result;
    }

    /**
     * Get supported gateways list
     */
    public static function getSupportedGateways(): array
    {
        return [
            self::GATEWAY_RAZORPAY => 'Razorpay',
            self::GATEWAY_PAYUMONEY => 'PayUmoney',
            self::GATEWAY_CASHFREE => 'Cashfree',
        ];
    }

    /**
     * Create default settings for all supported gateways
     */
    public static function createDefaults(): void
    {
        $gateways = [
            self::GATEWAY_RAZORPAY => [
                'key_id' => '',
                'key_secret' => '',
                'webhook_secret' => ''
            ],
            self::GATEWAY_PAYUMONEY => [
                'merchant_key' => '',
                'salt' => '',
                'auth_header' => ''
            ],
            self::GATEWAY_CASHFREE => [
                'app_id' => '',
                'secret_key' => '',
                'client_id' => '',
                'client_secret' => ''
            ]
        ];

        foreach ($gateways as $gatewayName => $defaultSettings) {
            self::firstOrCreate(
                ['gateway_name' => $gatewayName],
                [
                    'is_enabled' => false,
                    'is_test_mode' => true,
                    'settings' => $defaultSettings
                ]
            );
        }
    }

    /**
     * Clear cache for this gateway
     */
    protected function clearCache(): void
    {
        Cache::forget("gateway_config_{$this->gateway_name}");
        Cache::forget('enabled_gateways');
    }

    /**
     * Boot method to clear cache on model events
     */
    protected static function boot()
    {
        parent::boot();

        static::saved(function ($model) {
            $model->clearCache();
        });

        static::deleted(function ($model) {
            $model->clearCache();
        });
    }

    /**
     * Check if gateway is in test mode
     */
    public function isTestMode(): bool
    {
        return $this->is_test_mode;
    }

    /**
     * Check if gateway is in live mode
     */
    public function isLiveMode(): bool
    {
        return !$this->is_test_mode;
    }

    /**
     * Get gateway display name
     */
    public function getDisplayNameAttribute(): string
    {
        return self::getSupportedGateways()[$this->gateway_name] ?? $this->gateway_name;
    }

    /**
     * Get status label
     */
    public function getStatusLabelAttribute(): string
    {
        return $this->is_enabled ? 'Enabled' : 'Disabled';
    }

    /**
     * Get mode label
     */
    public function getModeLabelAttribute(): string
    {
        return $this->is_test_mode ? 'Test' : 'Live';
    }
}