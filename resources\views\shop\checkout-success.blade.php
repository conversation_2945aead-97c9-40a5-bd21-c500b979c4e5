@extends('layouts.app')

@section('title', 'Order Complete')

@section('content')
<div class="min-h-screen bg-gray-50 py-12">
    <div class="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="bg-white rounded-lg shadow-sm border p-8 text-center">
            <div class="w-20 h-20 mx-auto mb-6 bg-green-100 rounded-full flex items-center justify-center">
                <svg class="w-10 h-10 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
            </div>
            
            <h1 class="text-3xl font-bold text-gray-900 mb-4">Order Complete!</h1>
            <p class="text-lg text-gray-600 mb-8">Thank you for your purchase. Your order has been successfully placed.</p>
            
            <div class="bg-gray-50 rounded-lg p-6 mb-8">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <p class="text-sm text-gray-600 mb-1">Order Number</p>
                        <p class="text-xl font-semibold text-gray-900">{{ $order->order_number }}</p>
                    </div>
                    
                    <div>
                        <p class="text-sm text-gray-600 mb-1">Total Amount</p>
                        <p class="text-xl font-semibold text-gray-900">${{ number_format($order->total, 2) }}</p>
                    </div>
                    
                    <div>
                        <p class="text-sm text-gray-600 mb-1">Order Status</p>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                            @if($order->status === 'pending') bg-yellow-100 text-yellow-800
                            @elseif($order->status === 'processing') bg-blue-100 text-blue-800
                            @elseif($order->status === 'completed') bg-green-100 text-green-800
                            @else bg-gray-100 text-gray-800
                            @endif">
                            {{ ucfirst($order->status) }}
                        </span>
                    </div>
                    
                    <div>
                        <p class="text-sm text-gray-600 mb-1">Payment Status</p>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                            @if($order->payment_status === 'paid') bg-green-100 text-green-800
                            @elseif($order->payment_status === 'pending') bg-yellow-100 text-yellow-800
                            @elseif($order->payment_status === 'failed') bg-red-100 text-red-800
                            @else bg-gray-100 text-gray-800
                            @endif">
                            {{ ucfirst($order->payment_status) }}
                        </span>
                    </div>
                </div>
            </div>
            
            <div class="text-sm text-gray-600 mb-8">
                <p>A confirmation email has been sent to <strong>{{ $order->billing_email }}</strong></p>
                <p class="mt-2">You can track your order status in your account dashboard.</p>
            </div>
            
            <div class="space-x-4">
                <a href="{{ route('shop.index') }}" class="inline-flex items-center px-6 py-3 bg-blue-600 text-white font-medium rounded-md hover:bg-blue-700 transition-colors">
                    Continue Shopping
                </a>
                
                @auth
                    <a href="{{ route('orders.index') }}" class="inline-flex items-center px-6 py-3 bg-gray-600 text-white font-medium rounded-md hover:bg-gray-700 transition-colors">
                        View All Orders
                    </a>
                @endauth
            </div>
        </div>
        
        <!-- Order Details -->
        <div class="mt-8 bg-white rounded-lg shadow-sm border p-6">
            <h2 class="text-lg font-semibold text-gray-900 mb-4">Order Details</h2>
            
            <div class="space-y-4">
                @foreach($order->items as $item)
                    <div class="flex items-center justify-between py-3 border-b border-gray-200 last:border-b-0">
                        <div class="flex items-center space-x-4">
                            <div class="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
                                <svg class="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z"></path>
                                </svg>
                            </div>
                            <div>
                                <p class="font-medium text-gray-900">{{ $item->name }}</p>
                                <p class="text-sm text-gray-600">Quantity: {{ $item->quantity }}</p>
                            </div>
                        </div>
                        <div class="text-right">
                            <p class="font-medium text-gray-900">${{ number_format($item->price * $item->quantity, 2) }}</p>
                            <p class="text-sm text-gray-600">${{ number_format($item->price, 2) }} each</p>
                        </div>
                    </div>
                @endforeach
            </div>
            
            <!-- Order Totals -->
            <div class="mt-6 pt-6 border-t border-gray-200">
                <div class="space-y-2">
                    <div class="flex justify-between text-sm">
                        <span class="text-gray-600">Subtotal</span>
                        <span class="text-gray-900">${{ number_format($order->subtotal, 2) }}</span>
                    </div>
                    
                    @if($order->tax > 0)
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600">Tax</span>
                            <span class="text-gray-900">${{ number_format($order->tax, 2) }}</span>
                        </div>
                    @endif
                    
                    @if($order->shipping > 0)
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600">Shipping</span>
                            <span class="text-gray-900">${{ number_format($order->shipping, 2) }}</span>
                        </div>
                    @else
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600">Shipping</span>
                            <span class="text-green-600">Free</span>
                        </div>
                    @endif
                    
                    <div class="flex justify-between font-semibold text-lg pt-2 border-t border-gray-200">
                        <span class="text-gray-900">Total</span>
                        <span class="text-gray-900">${{ number_format($order->total, 2) }}</span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Shipping Information -->
        <div class="mt-8 grid grid-cols-1 md:grid-cols-2 gap-8">
            <div class="bg-white rounded-lg shadow-sm border p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Shipping Address</h3>
                <div class="text-sm text-gray-600 space-y-1">
                    <p class="font-medium text-gray-900">{{ $order->shipping_name }}</p>
                    <p>{{ $order->shipping_address }}</p>
                    <p>{{ $order->shipping_city }}, {{ $order->shipping_state }} {{ $order->shipping_zipcode }}</p>
                    <p>{{ $order->shipping_country }}</p>
                    <p class="mt-2">{{ $order->shipping_email }}</p>
                    <p>{{ $order->shipping_phone }}</p>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow-sm border p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Billing Address</h3>
                <div class="text-sm text-gray-600 space-y-1">
                    <p class="font-medium text-gray-900">{{ $order->billing_name }}</p>
                    <p>{{ $order->billing_address }}</p>
                    <p>{{ $order->billing_city }}, {{ $order->billing_state }} {{ $order->billing_zipcode }}</p>
                    <p>{{ $order->billing_country }}</p>
                    <p class="mt-2">{{ $order->billing_email }}</p>
                    <p>{{ $order->billing_phone }}</p>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection