<?php

namespace Tests\Browser\Pages;

use <PERSON><PERSON>\Dusk\Browser;

class PaymentPage extends Page
{
    /**
     * Get the URL for the page.
     */
    public function url(): string
    {
        return '/payment/create';
    }

    /**
     * Assert that the browser is on the page.
     */
    public function assert(Browser $browser): void
    {
        $browser->assertPathIs($this->url())
                ->assertSee('Payment');
    }

    /**
     * Get the element shortcuts for the page.
     */
    public function elements(): array
    {
        return [
            '@amount' => 'input[name="amount"]',
            '@description' => 'input[name="description"]',
            '@currency' => 'select[name="currency"]',
            '@gatewayRazorpay' => 'input[value="razorpay"]',
            '@gatewayPayumoney' => 'input[value="payumoney"]',
            '@gatewayCashfree' => 'input[value="cashfree"]',
            '@payButton' => 'button[type="submit"]',
            '@paymentForm' => 'form[id="payment-form"]',
            '@loadingOverlay' => '#payment-loading-overlay',
            '@errorMessage' => '.bg-red-50',
            '@successMessage' => '.bg-green-50',
        ];
    }

    /**
     * Fill payment form with given data.
     */
    public function fillPaymentForm(Browser $browser, array $data): void
    {
        if (isset($data['amount'])) {
            $browser->type('@amount', $data['amount']);
        }

        if (isset($data['description'])) {
            $browser->type('@description', $data['description']);
        }

        if (isset($data['currency'])) {
            $browser->select('@currency', $data['currency']);
        }

        if (isset($data['gateway'])) {
            $browser->click('@gateway' . ucfirst($data['gateway']));
        }
    }

    /**
     * Submit payment form.
     */
    public function submitPayment(Browser $browser): void
    {
        $browser->click('@payButton');
    }

    /**
     * Wait for payment processing.
     */
    public function waitForPaymentProcessing(Browser $browser): void
    {
        $browser->waitFor('@loadingOverlay', 5)
                ->waitUntilMissing('@loadingOverlay', 30);
    }
}