<?php

namespace App\Exceptions;

use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Throwable;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Validation\ValidationException;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Log;

class Handler extends ExceptionHandler
{
    /**
     * A list of the exception types that are not reported.
     *
     * @var array<int, class-string<Throwable>>
     */
    protected $dontReport = [
        //
    ];

    /**
     * A list of the inputs that are never flashed for validation exceptions.
     *
     * @var array<int, string>
     */
    protected $dontFlash = [
        'current_password',
        'password',
        'password_confirmation',
    ];

    /**
     * Register the exception handling callbacks for the application.
     *
     * @return void
     */
    public function register(): void
    {
        // You can add custom reportable or renderable callbacks here if needed
    }

    public function render($request, Throwable $e): Response|JsonResponse
    {
        // Handle validation errors for API requests
        if ($e instanceof ValidationException) {
            if ($request->expectsJson()) {
                return response()->json([
                    'message' => $e->getMessage(),
                    'errors' => $e->errors(),
                ], $e->status);
            }
        }

        // Handle authorization errors
        if ($e instanceof AuthorizationException) {
            if ($request->expectsJson()) {
                return response()->json(['message' => 'Forbidden'], 403);
            }
            return response()->view('errors.403', ['exception' => $e], 403);
        }

        // Handle HTTP exceptions (404, 500, etc.)
        if ($e instanceof HttpException) {
            $statusCode = $e->getStatusCode();
            if ($request->expectsJson()) {
                return response()->json([
                    'message' => $e->getMessage() ?: Response::$statusTexts[$statusCode] ?? 'Error',
                ], $statusCode);
            }
            if (view()->exists("errors.{$statusCode}")) {
                return response()->view("errors.{$statusCode}", [
                    'exception' => $e
                ], $statusCode);
            }
        }

        // Log unexpected exceptions in non-production
        if (!app()->environment('production')) {
            Log::error($e);
            return parent::render($request, $e);
        }

        // Fallback for all other exceptions
        if ($request->expectsJson()) {
            return response()->json([
                'message' => 'Server Error',
            ], 500);
        }

        return response()->view('errors.500', [
            'exception' => $e
        ], 500);
    }
}