<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Database\Connection;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
       // Register a custom null database connection resolver
        // This allows handling scenarios where a database connection might not be available (installation process)
        // It helps to initiate installation process without having a database connection or env file
        // Temporarily commented out to fix Dusk tests
        // Connection::resolverFor('null', function () {
        //     return new \App\Database\Connectors\NullConnection();
        // });

        // Register payment services
        $this->app->singleton(\App\Services\PaymentGatewayFactory::class);
        $this->app->singleton(\App\Services\TransactionService::class);
        $this->app->singleton(\App\Services\PaymentService::class);
        $this->app->singleton(\App\Services\WebhookService::class);
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Configure webhook rate limiter - Requirements: 6.1, 6.2
        \Illuminate\Support\Facades\RateLimiter::for('webhook', function ($request) {
            return \Illuminate\Cache\RateLimiting\Limit::perMinute(100)
                ->by($request->ip())
                ->response(function () {
                    return response()->json([
                        'success' => false,
                        'error' => 'Too many webhook requests'
                    ], 429);
                });
        });
    }
}
