<?php

namespace Tests\Feature;

use App\Models\Build;
use App\Models\User;
use App\Services\BuilderService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class BuildSharingControllerTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;
    protected User $otherUser;
    protected BuilderService $builderService;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->user = User::factory()->create();
        $this->otherUser = User::factory()->create();
        $this->builderService = app(BuilderService::class);
    }

    public function test_builder_service_integration_with_public_build()
    {
        $build = Build::factory()->create([
            'user_id' => $this->user->id,
            'is_public' => true,
        ]);

        $retrievedBuild = $this->builderService->getBuildById($build->id);
        
        $this->assertNotNull($retrievedBuild);
        $this->assertEquals($build->id, $retrievedBuild->id);
    }

    public function test_builder_service_integration_with_private_build_access()
    {
        $build = Build::factory()->create([
            'user_id' => $this->user->id,
            'is_public' => false,
        ]);

        // Owner can access
        $retrievedBuild = $this->builderService->getBuildById($build->id, $this->user);
        $this->assertNotNull($retrievedBuild);
        
        // Non-owner cannot access
        $retrievedBuild = $this->builderService->getBuildById($build->id, $this->otherUser);
        $this->assertNull($retrievedBuild);
    }

    public function test_builder_service_integration_with_share_token()
    {
        $build = Build::factory()->create([
            'user_id' => $this->user->id,
            'is_public' => false,
            'share_token' => 'valid_token_12345678901234567890',
        ]);

        // Valid token allows access
        $retrievedBuild = $this->builderService->getBuildById(
            $build->id, 
            null, 
            'valid_token_12345678901234567890'
        );
        $this->assertNotNull($retrievedBuild);
        
        // Invalid token denies access
        $retrievedBuild = $this->builderService->getBuildById(
            $build->id, 
            null, 
            'invalid_token'
        );
        $this->assertNull($retrievedBuild);
    }

    public function test_delete_build_as_owner()
    {
        $build = Build::factory()->create([
            'user_id' => $this->user->id,
        ]);

        $response = $this->actingAs($this->user)
            ->delete(route('builder.delete', ['id' => $build->id]));

        $response->assertRedirect(route('builder.index'));
        $response->assertSessionHas('message', 'Build deleted successfully!');
        $this->assertDatabaseMissing('builds', ['id' => $build->id]);
    }

    public function test_delete_build_as_non_owner_returns_error()
    {
        $build = Build::factory()->create([
            'user_id' => $this->user->id,
        ]);

        $response = $this->actingAs($this->otherUser)
            ->delete(route('builder.delete', ['id' => $build->id]));

        $response->assertRedirect();
        $response->assertSessionHas('error');
        $this->assertDatabaseHas('builds', ['id' => $build->id]);
    }

    public function test_clone_public_build()
    {
        $originalBuild = Build::factory()->create([
            'user_id' => $this->user->id,
            'is_public' => true,
            'name' => 'Original Build',
        ]);

        $response = $this->actingAs($this->otherUser)
            ->post(route('builder.clone', ['id' => $originalBuild->id]));

        $response->assertRedirect();
        $response->assertSessionHas('message', 'Build cloned successfully!');
        
        // Check that a new build was created
        $this->assertDatabaseHas('builds', [
            'user_id' => $this->otherUser->id,
            'name' => 'Clone of Original Build',
        ]);
    }

    public function test_clone_private_build_as_non_owner_returns_error()
    {
        $originalBuild = Build::factory()->create([
            'user_id' => $this->user->id,
            'is_public' => false,
        ]);

        $response = $this->actingAs($this->otherUser)
            ->post(route('builder.clone', ['id' => $originalBuild->id]));

        $response->assertRedirect();
        $response->assertSessionHas('error');
    }

    public function test_clone_own_private_build()
    {
        $originalBuild = Build::factory()->create([
            'user_id' => $this->user->id,
            'is_public' => false,
            'name' => 'My Private Build',
        ]);

        $response = $this->actingAs($this->user)
            ->post(route('builder.clone', ['id' => $originalBuild->id]));

        $response->assertRedirect();
        $response->assertSessionHas('message', 'Build cloned successfully!');
        
        // Check that a new build was created
        $this->assertDatabaseHas('builds', [
            'user_id' => $this->user->id,
            'name' => 'Clone of My Private Build',
        ]);
    }

    public function test_clone_nonexistent_build_returns_404()
    {
        $response = $this->actingAs($this->user)
            ->post(route('builder.clone', ['id' => 99999]));

        $response->assertStatus(404);
    }
}