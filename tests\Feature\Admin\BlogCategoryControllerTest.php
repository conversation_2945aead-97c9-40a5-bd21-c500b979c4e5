<?php

use App\Models\BlogPostCategory;
use App\Models\BlogPost;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;

uses(RefreshDatabase::class, WithFaker::class);

// php artisan test tests/Feature/Controllers/Admin/BlogCategoryControllerTest.php

/*
|--------------------------------------------------------------------------
| Index Tests
|--------------------------------------------------------------------------
*/

test('admin can view blog categories index page', function () {
    $admin = User::factory()->create(['role' => 'admin']);
    $this->actingAs($admin);

    $categories = BlogPostCategory::factory()->count(5)->create();

    $response = $this->get(route('admin.blog.categories.index'));

    $response->assertStatus(200);
    $response->assertViewIs('admin.blog.categories.index');
    $response->assertViewHas('categories');
    
    $viewCategories = $response->viewData('categories');
    expect($viewCategories)->toHaveCount(5);
});

test('blog categories are ordered by display_order', function () {
    $admin = User::factory()->create(['role' => 'admin']);
    $this->actingAs($admin);

    // Create categories with different display orders
    $category3 = BlogPostCategory::factory()->create(['display_order' => 3]);
    $category1 = BlogPostCategory::factory()->create(['display_order' => 1]);
    $category2 = BlogPostCategory::factory()->create(['display_order' => 2]);

    $response = $this->get(route('admin.blog.categories.index'));

    $response->assertStatus(200);
    
    $viewCategories = $response->viewData('categories');
    expect($viewCategories[0]->id)->toBe($category1->id);
    expect($viewCategories[1]->id)->toBe($category2->id);
    expect($viewCategories[2]->id)->toBe($category3->id);
});

test('blog categories show post count', function () {
    $admin = User::factory()->create(['role' => 'admin']);
    $this->actingAs($admin);

    $category = BlogPostCategory::factory()->create();
    BlogPost::factory()->count(3)->create(['blog_post_category_id' => $category->id]);

    $response = $this->get(route('admin.blog.categories.index'));

    $response->assertStatus(200);
    
    $viewCategories = $response->viewData('categories');
    expect($viewCategories->first()->posts_count)->toBe(3);
});

test('non-admin users cannot access blog categories index', function () {
    $user = User::factory()->create(['role' => 'user']);
    $this->actingAs($user);

    $response = $this->get(route('admin.blog.categories.index'));

    $response->assertStatus(403);
});

test('guest users cannot access blog categories index', function () {
    $response = $this->get(route('admin.blog.categories.index'));

    $response->assertRedirect(route('admin.login'));
});

/*
|--------------------------------------------------------------------------
| Create Tests
|--------------------------------------------------------------------------
*/

test('admin can view create blog category form', function () {
    $admin = User::factory()->create(['role' => 'admin']);
    $this->actingAs($admin);

    $response = $this->get(route('admin.blog.categories.create'));

    $response->assertStatus(200);
    $response->assertViewIs('admin.blog.categories.create');
});

test('admin can create a new blog category', function () {
    $admin = User::factory()->create(['role' => 'admin']);
    $this->actingAs($admin);

    $categoryData = [
        'name' => 'Technology',
        'description' => 'Technology related posts',
        'display_order' => 1,
    ];

    $response = $this->post(route('admin.blog.categories.store'), $categoryData);

    $response->assertRedirect(route('admin.blog.categories.index'));
    $response->assertSessionHas('success', 'Blog category created successfully.');

    $this->assertDatabaseHas('blog_post_categories', [
        'name' => 'Technology',
        'slug' => 'technology',
        'description' => 'Technology related posts',
        'display_order' => 1,
    ]);
});

test('blog category creation generates slug from name', function () {
    $admin = User::factory()->create(['role' => 'admin']);
    $this->actingAs($admin);

    $categoryData = [
        'name' => 'Web Development & Design',
        'description' => 'Web development posts',
    ];

    $response = $this->post(route('admin.blog.categories.store'), $categoryData);

    $response->assertRedirect(route('admin.blog.categories.index'));

    $this->assertDatabaseHas('blog_post_categories', [
        'name' => 'Web Development & Design',
        'slug' => 'web-development-design',
    ]);
});

test('blog category creation uses default display_order when not provided', function () {
    $admin = User::factory()->create(['role' => 'admin']);
    $this->actingAs($admin);

    $categoryData = [
        'name' => 'Default Category',
        'description' => 'Category with default order',
    ];

    $response = $this->post(route('admin.blog.categories.store'), $categoryData);

    $response->assertRedirect(route('admin.blog.categories.index'));

    $this->assertDatabaseHas('blog_post_categories', [
        'name' => 'Default Category',
        'display_order' => 0,
    ]);
});

test('blog category creation validates required fields', function () {
    $admin = User::factory()->create(['role' => 'admin']);
    $this->actingAs($admin);

    $response = $this->post(route('admin.blog.categories.store'), []);

    $response->assertSessionHasErrors(['name']);
});

test('blog category creation validates name uniqueness', function () {
    $admin = User::factory()->create(['role' => 'admin']);
    $this->actingAs($admin);

    // Create first category
    BlogPostCategory::factory()->create(['name' => 'Technology']);

    // Try to create second category with same name
    $response = $this->post(route('admin.blog.categories.store'), [
        'name' => 'Technology',
        'description' => 'Duplicate category',
    ]);

    $response->assertSessionHasErrors(['name']);
});

test('blog category creation validates name length', function () {
    $admin = User::factory()->create(['role' => 'admin']);
    $this->actingAs($admin);

    $response = $this->post(route('admin.blog.categories.store'), [
        'name' => str_repeat('a', 256), // Exceeds 255 character limit
        'description' => 'Test description',
    ]);

    $response->assertSessionHasErrors(['name']);
});

test('blog category creation validates display_order type', function () {
    $admin = User::factory()->create(['role' => 'admin']);
    $this->actingAs($admin);

    $response = $this->post(route('admin.blog.categories.store'), [
        'name' => 'Test Category',
        'display_order' => 'not-a-number',
    ]);

    $response->assertSessionHasErrors(['display_order']);
});

test('blog category creation validates display_order minimum value', function () {
    $admin = User::factory()->create(['role' => 'admin']);
    $this->actingAs($admin);

    $response = $this->post(route('admin.blog.categories.store'), [
        'name' => 'Test Category',
        'display_order' => -1,
    ]);

    $response->assertSessionHasErrors(['display_order']);
});

/*
|--------------------------------------------------------------------------
| Edit Tests
|--------------------------------------------------------------------------
*/

test('admin can view edit blog category form', function () {
    $admin = User::factory()->create(['role' => 'admin']);
    $this->actingAs($admin);

    $category = BlogPostCategory::factory()->create();

    $response = $this->get(route('admin.blog.categories.edit', $category));

    $response->assertStatus(200);
    $response->assertViewIs('admin.blog.categories.edit');
    $response->assertViewHas('category', $category);
});

test('admin can update blog category', function () {
    $admin = User::factory()->create(['role' => 'admin']);
    $this->actingAs($admin);

    $category = BlogPostCategory::factory()->create([
        'name' => 'Old Name',
        'description' => 'Old description',
        'display_order' => 1,
    ]);

    $updateData = [
        'name' => 'Updated Name',
        'description' => 'Updated description',
        'display_order' => 5,
    ];

    $response = $this->put(route('admin.blog.categories.update', $category), $updateData);

    $response->assertRedirect(route('admin.blog.categories.index'));
    $response->assertSessionHas('success', 'Blog category updated successfully.');

    $this->assertDatabaseHas('blog_post_categories', [
        'id' => $category->id,
        'name' => 'Updated Name',
        'slug' => 'updated-name',
        'description' => 'Updated description',
        'display_order' => 5,
    ]);
});

test('blog category update generates new slug from name', function () {
    $admin = User::factory()->create(['role' => 'admin']);
    $this->actingAs($admin);

    $category = BlogPostCategory::factory()->create([
        'name' => 'Old Name',
        'slug' => 'old-name',
    ]);

    $response = $this->put(route('admin.blog.categories.update', $category), [
        'name' => 'New Category Name',
        'description' => 'Updated description',
    ]);

    $response->assertRedirect(route('admin.blog.categories.index'));

    $this->assertDatabaseHas('blog_post_categories', [
        'id' => $category->id,
        'name' => 'New Category Name',
        'slug' => 'new-category-name',
    ]);
});

test('blog category update validates name uniqueness excluding current category', function () {
    $admin = User::factory()->create(['role' => 'admin']);
    $this->actingAs($admin);

    $category1 = BlogPostCategory::factory()->create(['name' => 'Category One']);
    $category2 = BlogPostCategory::factory()->create(['name' => 'Category Two']);

    // Try to update category2 with category1's name
    $response = $this->put(route('admin.blog.categories.update', $category2), [
        'name' => 'Category One',
        'description' => 'Updated description',
    ]);

    $response->assertSessionHasErrors(['name']);
});

test('blog category update allows same name for same category', function () {
    $admin = User::factory()->create(['role' => 'admin']);
    $this->actingAs($admin);

    $category = BlogPostCategory::factory()->create([
        'name' => 'Test Category',
        'description' => 'Original description',
    ]);

    $response = $this->put(route('admin.blog.categories.update', $category), [
        'name' => 'Test Category', // Same name
        'description' => 'Updated description',
    ]);

    $response->assertRedirect(route('admin.blog.categories.index'));
    $response->assertSessionHas('success', 'Blog category updated successfully.');
});

/*
|--------------------------------------------------------------------------
| Delete Tests
|--------------------------------------------------------------------------
*/

test('admin can delete blog category without posts', function () {
    $admin = User::factory()->create(['role' => 'admin']);
    $this->actingAs($admin);

    $category = BlogPostCategory::factory()->create();

    $response = $this->delete(route('admin.blog.categories.destroy', $category));

    $response->assertRedirect(route('admin.blog.categories.index'));
    $response->assertSessionHas('success', 'Blog category deleted successfully.');

    $this->assertDatabaseMissing('blog_post_categories', [
        'id' => $category->id,
    ]);
});

test('admin cannot delete blog category with associated posts', function () {
    $admin = User::factory()->create(['role' => 'admin']);
    $this->actingAs($admin);

    $category = BlogPostCategory::factory()->create();
    BlogPost::factory()->create(['blog_post_category_id' => $category->id]);

    $response = $this->delete(route('admin.blog.categories.destroy', $category));

    $response->assertRedirect(route('admin.blog.categories.index'));
    $response->assertSessionHas('error', 'Cannot delete category with associated posts.');

    $this->assertDatabaseHas('blog_post_categories', [
        'id' => $category->id,
    ]);
});

test('deleting category with multiple posts is prevented', function () {
    $admin = User::factory()->create(['role' => 'admin']);
    $this->actingAs($admin);

    $category = BlogPostCategory::factory()->create();
    BlogPost::factory()->count(5)->create(['blog_post_category_id' => $category->id]);

    $response = $this->delete(route('admin.blog.categories.destroy', $category));

    $response->assertRedirect(route('admin.blog.categories.index'));
    $response->assertSessionHas('error', 'Cannot delete category with associated posts.');

    $this->assertDatabaseHas('blog_post_categories', [
        'id' => $category->id,
    ]);
});

/*
|--------------------------------------------------------------------------
| API Tests
|--------------------------------------------------------------------------
*/

test('api index returns categories for select2', function () {
    $admin = User::factory()->create(['role' => 'admin']);
    $this->actingAs($admin);

    $categories = BlogPostCategory::factory()->count(3)->create([
        'display_order' => 0,
    ]);

    $response = $this->get(route('admin.blog.categories.api'));

    $response->assertStatus(200);
    $response->assertJsonCount(3);
    
    $responseData = $response->json();
    expect($responseData[0])->toHaveKeys(['id', 'name']);
});

test('api index respects search parameter', function () {
    $admin = User::factory()->create(['role' => 'admin']);
    $this->actingAs($admin);

    BlogPostCategory::factory()->create(['name' => 'Technology']);
    BlogPostCategory::factory()->create(['name' => 'Design']);
    BlogPostCategory::factory()->create(['name' => 'Marketing']);

    $response = $this->get(route('admin.blog.categories.api', ['search' => 'tech']));

    $response->assertStatus(200);
    $response->assertJsonCount(1);
    
    $responseData = $response->json();
    expect($responseData[0]['name'])->toBe('Technology');
});

test('api index returns empty array for no search results', function () {
    $admin = User::factory()->create(['role' => 'admin']);
    $this->actingAs($admin);

    BlogPostCategory::factory()->create(['name' => 'Technology']);

    $response = $this->get(route('admin.blog.categories.api', ['search' => 'nonexistent']));

    $response->assertStatus(200);
    $response->assertJsonCount(0);
});

test('api index orders by display_order then name', function () {
    $admin = User::factory()->create(['role' => 'admin']);
    $this->actingAs($admin);

    $category3 = BlogPostCategory::factory()->create(['name' => 'C', 'display_order' => 3]);
    $category1 = BlogPostCategory::factory()->create(['name' => 'A', 'display_order' => 1]);
    $category2 = BlogPostCategory::factory()->create(['name' => 'B', 'display_order' => 1]);

    $response = $this->get(route('admin.blog.categories.api'));

    $response->assertStatus(200);
    
    $responseData = $response->json();
    expect($responseData[0]['id'])->toBe($category1->id);
    expect($responseData[1]['id'])->toBe($category2->id);
    expect($responseData[2]['id'])->toBe($category3->id);
});

/*
|--------------------------------------------------------------------------
| Authorization Tests
|--------------------------------------------------------------------------
*/

test('non-admin users cannot access create form', function () {
    $user = User::factory()->create(['role' => 'user']);
    $this->actingAs($user);

    $response = $this->get(route('admin.blog.categories.create'));

    $response->assertStatus(403);
});

test('non-admin users cannot create blog categories', function () {
    $user = User::factory()->create(['role' => 'user']);
    $this->actingAs($user);

    $response = $this->post(route('admin.blog.categories.store'), [
        'name' => 'Test Category',
    ]);

    $response->assertStatus(403);
});

test('non-admin users cannot access edit form', function () {
    $user = User::factory()->create(['role' => 'user']);
    $this->actingAs($user);

    $category = BlogPostCategory::factory()->create();

    $response = $this->get(route('admin.blog.categories.edit', $category));

    $response->assertStatus(403);
});

test('non-admin users cannot update blog categories', function () {
    $user = User::factory()->create(['role' => 'user']);
    $this->actingAs($user);

    $category = BlogPostCategory::factory()->create();

    $response = $this->put(route('admin.blog.categories.update', $category), [
        'name' => 'Updated Category',
    ]);

    $response->assertStatus(403);
});

test('non-admin users cannot delete blog categories', function () {
    $user = User::factory()->create(['role' => 'user']);
    $this->actingAs($user);

    $category = BlogPostCategory::factory()->create();

    $response = $this->delete(route('admin.blog.categories.destroy', $category));

    $response->assertStatus(403);
});

test('non-admin users cannot access api index', function () {
    $user = User::factory()->create(['role' => 'user']);
    $this->actingAs($user);

    $response = $this->get(route('admin.blog.categories.api'));

    $response->assertStatus(403);
});

/*
|--------------------------------------------------------------------------
| Edge Cases and Error Handling
|--------------------------------------------------------------------------
*/

test('handles special characters in category name for slug generation', function () {
    $admin = User::factory()->create(['role' => 'admin']);
    $this->actingAs($admin);

    $response = $this->post(route('admin.blog.categories.store'), [
        'name' => 'Special Characters: @#$%^&*()',
        'description' => 'Test description',
    ]);

    $response->assertRedirect(route('admin.blog.categories.index'));

    $this->assertDatabaseHas('blog_post_categories', [
        'name' => 'Special Characters: @#$%^&*()',
        'slug' => 'special-characters-at',
    ]);
});

test('handles unicode characters in category name', function () {
    $admin = User::factory()->create(['role' => 'admin']);
    $this->actingAs($admin);

    $response = $this->post(route('admin.blog.categories.store'), [
        'name' => 'Café & Résumé',
        'description' => 'Unicode test',
    ]);

    $response->assertRedirect(route('admin.blog.categories.index'));

    $this->assertDatabaseHas('blog_post_categories', [
        'name' => 'Café & Résumé',
        'slug' => 'cafe-resume',
    ]);
});

test('handles very long description', function () {
    $admin = User::factory()->create(['role' => 'admin']);
    $this->actingAs($admin);

    $longDescription = str_repeat('This is a very long description. ', 50);

    $response = $this->post(route('admin.blog.categories.store'), [
        'name' => 'Long Description Test',
        'description' => $longDescription,
    ]);

    $response->assertRedirect(route('admin.blog.categories.index'));

    // Just verify the category was created successfully
    $this->assertDatabaseHas('blog_post_categories', [
        'name' => 'Long Description Test',
    ]);
    
    // Verify the description was stored (check that it's not empty and contains part of the original text)
    $category = BlogPostCategory::where('name', 'Long Description Test')->first();
    expect($category)->not->toBeNull();
    expect($category->description)->not->toBeEmpty();
    expect($category->description)->toContain('This is a very long description');
});

test('handles zero display order', function () {
    $admin = User::factory()->create(['role' => 'admin']);
    $this->actingAs($admin);

    $response = $this->post(route('admin.blog.categories.store'), [
        'name' => 'Zero Order Category',
        'display_order' => 0,
    ]);

    $response->assertRedirect(route('admin.blog.categories.index'));

    $this->assertDatabaseHas('blog_post_categories', [
        'name' => 'Zero Order Category',
        'display_order' => 0,
    ]);
});

test('handles large display order values', function () {
    $admin = User::factory()->create(['role' => 'admin']);
    $this->actingAs($admin);

    $response = $this->post(route('admin.blog.categories.store'), [
        'name' => 'Large Order Category',
        'display_order' => 999999,
    ]);

    $response->assertRedirect(route('admin.blog.categories.index'));

    $this->assertDatabaseHas('blog_post_categories', [
        'name' => 'Large Order Category',
        'display_order' => 999999,
    ]);
}); 