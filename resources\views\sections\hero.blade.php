<!-- Hero Section -->
<section class="relative min-h-screen flex items-center justify-center cyber-grid">
    <!-- Animated Background Elements -->
    <div class="absolute inset-0 overflow-hidden">
        <div class="absolute top-20 left-20 w-96 h-96 bg-nexus-primary-500 dark:bg-nexus-primary-600 rounded-full opacity-10 floating"></div>
        <div class="absolute bottom-20 right-20 w-80 h-80 bg-nexus-secondary-500 dark:bg-nexus-secondary-600 rounded-full opacity-10 floating"
            style="animation-delay: 2s;"></div>
        <div
            class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[600px] h-[600px] border border-nexus-primary-500 dark:border-nexus-primary-600 rounded-full opacity-20 pulse-ring">
        </div>
    </div>

    <div class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
        <!-- Main Content -->
        <div class="text-center mb-16">
            <!-- Headline -->
            <h1 class="tech-font text-6xl md:text-8xl font-black mb-6 neon-text" id="headline">
                <span class="nexus-text-gradient">
                    NEXUS
                </span>
                <span class="text-white dark:text-nexus-gray-100 transition-colors duration-300">PC</span>
            </h1>

            <!-- Dynamic Tagline -->
            <div class="h-16 flex items-center justify-center mb-8">
                <p class="tech-font text-2xl md:text-3xl font-bold text-nexus-primary-400 dark:text-nexus-primary-300 transition-colors duration-300" id="tagline">
                    Silent. Powerful. Smart.
                </p>
            </div>

            <!-- Performance Stats -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12 max-w-4xl mx-auto">
                <!-- FPS Benchmark -->
                <div class="stat-card p-6 rounded-xl glow dark:bg-nexus-dark-800 transition-colors duration-300">
                    <div class="flex items-center justify-between mb-3">
                        <h3 class="tech-font text-sm font-semibold text-nexus-primary-400 dark:text-nexus-primary-300 transition-colors duration-300">FPS BENCHMARK</h3>
                        <div class="w-2 h-2 bg-nexus-success-400 dark:bg-nexus-success-500 rounded-full animate-pulse transition-colors duration-300"></div>
                    </div>
                    <div class="text-3xl font-bold text-white dark:text-nexus-gray-100 mb-2 transition-colors duration-300">
                        <span id="fps-counter">0</span>
                        <span class="text-lg text-nexus-gray-400 dark:text-nexus-gray-300 transition-colors duration-300">FPS</span>
                    </div>
                    <p class="text-xs text-nexus-gray-400 dark:text-nexus-gray-300 transition-colors duration-300">4K Ultra Settings</p>
                </div>

                <!-- Thermal Output -->
                <div class="stat-card p-6 rounded-xl glow dark:bg-nexus-dark-800 transition-colors duration-300">
                    <div class="flex items-center justify-between mb-3">
                        <h3 class="tech-font text-sm font-semibold text-nexus-primary-400 dark:text-nexus-primary-300 transition-colors duration-300">THERMAL OUTPUT</h3>
                        <div class="w-2 h-2 bg-nexus-primary-400 dark:bg-nexus-primary-500 rounded-full animate-pulse transition-colors duration-300"></div>
                    </div>
                    <div class="text-3xl font-bold text-white dark:text-nexus-gray-100 mb-2 transition-colors duration-300">
                        <span id="temp-counter">0</span>
                        <span class="text-lg text-nexus-gray-400 dark:text-nexus-gray-300 transition-colors duration-300">°C</span>
                    </div>
                    <p class="text-xs text-nexus-gray-400 dark:text-nexus-gray-300 transition-colors duration-300">Under Load</p>
                </div>

                <!-- Watts Saved -->
                <div class="stat-card p-6 rounded-xl glow dark:bg-nexus-dark-800 transition-colors duration-300">
                    <div class="flex items-center justify-between mb-3">
                        <h3 class="tech-font text-sm font-semibold text-nexus-primary-400 dark:text-nexus-primary-300 transition-colors duration-300">EFFICIENCY</h3>
                        <div class="w-2 h-2 bg-nexus-warning-400 dark:bg-nexus-warning-500 rounded-full animate-pulse transition-colors duration-300"></div>
                    </div>
                    <div class="text-3xl font-bold text-white dark:text-nexus-gray-100 mb-2 transition-colors duration-300">
                        <span id="watts-counter">0</span>
                        <span class="text-lg text-nexus-gray-400 dark:text-nexus-gray-300 transition-colors duration-300">W</span>
                    </div>
                    <p class="text-xs text-nexus-gray-400 dark:text-nexus-gray-300 transition-colors duration-300">Power Saved</p>
                </div>
            </div>
        </div>

        <!-- Component Showcase -->
        <div class="max-w-5xl mx-auto">
            <h2 class="tech-font text-2xl font-bold text-center mb-8 text-nexus-primary-400 dark:text-nexus-primary-300 transition-colors duration-300">COMPONENT PERFORMANCE</h2>

            <div class="grid grid-cols-2 md:grid-cols-4 gap-8 justify-items-center">
                <!-- CPU -->
                <div class="component-icon cursor-pointer" data-tooltip="cpu">
                    <div
                        class="w-20 h-20 bg-gradient-to-br from-nexus-primary-500 to-nexus-secondary-500 rounded-lg flex items-center justify-center relative">
                        <svg class="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 24 24">
                            <path
                                d="M4 4h4v4H4V4zm6 0h4v4h-4V4zm6 0h4v4h-4V4zM4 10h4v4H4v-4zm6 0h4v4h-4v-4zm6 0h4v4h-4v-4zM4 16h4v4H4v-4zm6 0h4v4h-4v-4zm6 0h4v4h-4v-4z" />
                        </svg>
                        <div
                            class="absolute inset-0 rounded-lg border-2 border-nexus-primary-400 opacity-0 hover:opacity-100 transition-opacity duration-300">
                        </div>
                    </div>
                    <p class="tech-font text-sm mt-3 text-center text-nexus-gray-400 dark:text-nexus-gray-300 transition-colors duration-300">CPU</p>
                </div>

                <!-- GPU -->
                <div class="component-icon cursor-pointer" data-tooltip="gpu">
                    <div
                        class="w-20 h-20 bg-gradient-to-br from-nexus-success-500 to-teal-500 rounded-lg flex items-center justify-center relative">
                        <svg class="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 24 24">
                            <path
                                d="M21 16.5c0 .38-.21.71-.53.88l-7.9 4.44c-.16.12-.36.18-.57.18-.21 0-.41-.06-.57-.18l-7.9-4.44A.991.991 0 0 1 3 16.5v-9c0-.38.21-.71.53-.88l7.9-4.44c.16-.12.36-.18.57-.18.21 0 .41.06.57.18l7.9 4.44c.**********.53.88v9z" />
                        </svg>
                        <div
                            class="absolute inset-0 rounded-lg border-2 border-nexus-success-400 opacity-0 hover:opacity-100 transition-opacity duration-300">
                        </div>
                    </div>
                    <p class="tech-font text-sm mt-3 text-center text-nexus-gray-400">GPU</p>
                </div>

                <!-- RAM -->
                <div class="component-icon cursor-pointer" data-tooltip="ram">
                    <div
                        class="w-20 h-20 bg-gradient-to-br from-nexus-accent-500 to-pink-500 rounded-lg flex items-center justify-center relative">
                        <svg class="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M6 7h12v2H6V7zm0 4h12v2H6v-2zm0 4h12v2H6v-2z" />
                        </svg>
                        <div
                            class="absolute inset-0 rounded-lg border-2 border-nexus-accent-400 opacity-0 hover:opacity-100 transition-opacity duration-300">
                        </div>
                    </div>
                    <p class="tech-font text-sm mt-3 text-center text-nexus-gray-400">RAM</p>
                </div>

                <!-- Storage -->
                <div class="component-icon cursor-pointer" data-tooltip="storage">
                    <div
                        class="w-20 h-20 bg-gradient-to-br from-orange-500 to-nexus-error-500 rounded-lg flex items-center justify-center relative">
                        <svg class="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 24 24">
                            <path
                                d="M6 2c-1.1 0-1.99.9-1.99 2L4 20c0 1.1.89 2 2 2h10v-2H6V4h7v5h5v1.67l2-2V8l-6-6H6z" />
                        </svg>
                        <div
                            class="absolute inset-0 rounded-lg border-2 border-orange-400 opacity-0 hover:opacity-100 transition-opacity duration-300">
                        </div>
                    </div>
                    <p class="tech-font text-sm mt-3 text-center text-nexus-gray-400">SSD</p>
                </div>
            </div>
        </div>

        <!-- CTA Button -->
        <div class="text-center mt-16">
            <button
                class="nexus-gradient-primary px-12 py-4 rounded-lg font-semibold tech-font text-lg hover:from-nexus-primary-700 hover:to-nexus-secondary-700 transition-all duration-300 transform hover:scale-105 glow">
                CONFIGURE YOUR BUILD
            </button>
        </div>
    </div>

    <!-- Tooltips -->
    <div id="tooltip" class="tooltip"></div>
</section>