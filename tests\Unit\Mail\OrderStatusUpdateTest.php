<?php

namespace Tests\Unit\Mail;

use App\Mail\OrderStatusUpdate;
use App\Models\Order;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class OrderStatusUpdateTest extends TestCase
{
    use RefreshDatabase;

    public function test_order_status_update_email_has_correct_subject()
    {
        $user = User::factory()->create();
        $order = Order::factory()->create([
            'user_id' => $user->id,
            'order_number' => 'PCB20240101123456789',
            'status' => Order::STATUS_PROCESSING,
        ]);

        $mail = new OrderStatusUpdate($order, Order::STATUS_PENDING);
        $envelope = $mail->envelope();

        $this->assertEquals('Order Processing - PCB20240101123456789', $envelope->subject);
    }

    public function test_order_status_update_email_contains_status_details()
    {
        $user = User::factory()->create();
        $order = Order::factory()->create([
            'user_id' => $user->id,
            'status' => Order::STATUS_COMPLETED,
            'tracking_number' => 'TRK123456789',
            'tracking_carrier' => 'UPS',
            'tracking_url' => 'https://ups.com/track/TRK123456789',
        ]);

        $mail = new OrderStatusUpdate($order, Order::STATUS_PROCESSING, 'Your order has been shipped!');
        $content = $mail->content();

        $this->assertEquals('emails.order-status-update', $content->view);
        $this->assertArrayHasKey('order', $content->with);
        $this->assertArrayHasKey('user', $content->with);
        $this->assertArrayHasKey('previousStatus', $content->with);
        $this->assertArrayHasKey('currentStatus', $content->with);
        $this->assertArrayHasKey('updateMessage', $content->with);
        $this->assertArrayHasKey('trackingInfo', $content->with);
        
        $this->assertEquals($order->id, $content->with['order']->id);
        $this->assertEquals(Order::STATUS_PROCESSING, $content->with['previousStatus']);
        $this->assertEquals(Order::STATUS_COMPLETED, $content->with['currentStatus']);
        $this->assertEquals('Your order has been shipped!', $content->with['updateMessage']);
        
        $trackingInfo = $content->with['trackingInfo'];
        $this->assertEquals('TRK123456789', $trackingInfo['number']);
        $this->assertEquals('UPS', $trackingInfo['carrier']);
        $this->assertEquals('https://ups.com/track/TRK123456789', $trackingInfo['url']);
    }

    public function test_order_status_update_email_handles_no_tracking_info()
    {
        $user = User::factory()->create();
        $order = Order::factory()->create([
            'user_id' => $user->id,
            'status' => Order::STATUS_PROCESSING,
            'tracking_number' => null,
            'tracking_carrier' => null,
            'tracking_url' => null,
        ]);

        $mail = new OrderStatusUpdate($order, Order::STATUS_PENDING);
        $content = $mail->content();

        $this->assertNull($content->with['trackingInfo']);
    }

    public function test_order_status_update_email_handles_null_message()
    {
        $user = User::factory()->create();
        $order = Order::factory()->create([
            'user_id' => $user->id,
            'status' => Order::STATUS_PROCESSING,
        ]);

        $mail = new OrderStatusUpdate($order, Order::STATUS_PENDING);
        $content = $mail->content();

        $this->assertNull($content->with['updateMessage']);
    }

    public function test_order_status_update_email_is_queueable()
    {
        $user = User::factory()->create();
        $order = Order::factory()->create(['user_id' => $user->id]);

        $mail = new OrderStatusUpdate($order, Order::STATUS_PENDING);

        $this->assertInstanceOf(\Illuminate\Contracts\Queue\ShouldQueue::class, $mail);
    }

    public function test_order_status_update_email_serializes_models()
    {
        $user = User::factory()->create();
        $order = Order::factory()->create(['user_id' => $user->id, 'status' => Order::STATUS_PROCESSING]);

        $mail = new OrderStatusUpdate($order, Order::STATUS_PENDING, 'Test message');
        
        // Test that the mail can be serialized and unserialized
        $serialized = serialize($mail);
        $unserialized = unserialize($serialized);
        
        $this->assertEquals($order->id, $unserialized->order->id);
        $this->assertEquals(Order::STATUS_PENDING, $unserialized->previousStatus);
        $this->assertEquals('Test message', $unserialized->updateMessage);
    }

    public function test_tracking_info_with_partial_data()
    {
        $user = User::factory()->create();
        $order = Order::factory()->create([
            'user_id' => $user->id,
            'status' => Order::STATUS_COMPLETED,
            'tracking_number' => 'TRK123456789',
            'tracking_carrier' => 'FedEx',
            'tracking_url' => null, // No URL provided
        ]);

        $mail = new OrderStatusUpdate($order, Order::STATUS_PROCESSING);
        $content = $mail->content();

        $trackingInfo = $content->with['trackingInfo'];
        $this->assertEquals('TRK123456789', $trackingInfo['number']);
        $this->assertEquals('FedEx', $trackingInfo['carrier']);
        $this->assertNull($trackingInfo['url']);
    }
}