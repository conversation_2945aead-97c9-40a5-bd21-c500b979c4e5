@extends('admin.layouts.admin')

@section('title', 'Low Stock Components')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Low Stock Components</h3>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Category</th>
                                    <th>Stock</th>
                                    <th>Price</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($lowStockComponents as $component)
                                <tr>
                                    <td>{{ $component->name }}</td>
                                    <td>{{ $component->category->name ?? 'N/A' }}</td>
                                    <td>
                                        <span class="badge badge-warning">{{ $component->stock }}</span>
                                    </td>
                                    <td>${{ number_format($component->price, 2) }}</td>
                                    <td>
                                        <span class="badge badge-{{ $component->is_active ? 'success' : 'danger' }}">
                                            {{ $component->is_active ? 'Active' : 'Inactive' }}
                                        </span>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection