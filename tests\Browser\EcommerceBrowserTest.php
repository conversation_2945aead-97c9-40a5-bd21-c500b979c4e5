<?php

namespace Tests\Browser;

use App\Models\Component;
use App\Models\ComponentCategory;
use App\Models\User;
use Illuminate\Foundation\Testing\DatabaseMigrations;
use Lara<PERSON>\Dusk\Browser;
use Tests\DuskTestCase;

class EcommerceBrowserTest extends DuskTestCase
{
    use DatabaseMigrations;

    protected User $user;
    protected Component $component1;
    protected Component $component2;
    protected Component $component3;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->user = User::factory()->create([
            'name' => '<PERSON> Doe',
            'email' => '<EMAIL>'
        ]);
        
        // Create categories and components
        $cpuCategory = ComponentCategory::factory()->create(['name' => 'CPU']);
        $gpuCategory = ComponentCategory::factory()->create(['name' => 'GPU']);
        $ramCategory = ComponentCategory::factory()->create(['name' => 'RAM']);
        
        $this->component1 = Component::factory()->create([
            'category_id' => $cpuCategory->id,
            'name' => 'Intel Core i7-12700K',
            'brand' => 'Intel',
            'price' => 399.99,
            'stock' => 10,
            'is_active' => true,
            'is_featured' => true,
        ]);
        
        $this->component2 = Component::factory()->create([
            'category_id' => $gpuCategory->id,
            'name' => 'NVIDIA RTX 4070',
            'brand' => 'NVIDIA',
            'price' => 599.99,
            'stock' => 5,
            'is_active' => true,
            'is_featured' => false,
        ]);
        
        $this->component3 = Component::factory()->create([
            'category_id' => $ramCategory->id,
            'name' => 'Corsair Vengeance 32GB',
            'brand' => 'Corsair',
            'price' => 129.99,
            'stock' => 15,
            'is_active' => true,
            'is_featured' => true,
        ]);
    }

    /** @test */
    public function user_can_browse_and_purchase_components()
    {
        $this->browse(function (Browser $browser) {
            $browser->visit('/shop')
                    ->assertSee('Shop Components')
                    ->assertSee('Featured Products');

            // Should see featured components
            $browser->assertSee($this->component1->name)
                    ->assertSee($this->component3->name)
                    ->assertSee('$399.99')
                    ->assertSee('$129.99');

            // Browse by category
            $browser->click('[data-category="cpu"]')
                    ->waitForText('CPU Components')
                    ->assertSee($this->component1->name)
                    ->assertDontSee($this->component2->name);

            // View product details
            $browser->click('[data-product-id="' . $this->component1->id . '"]')
                    ->waitForLocation('/shop/component/' . $this->component1->id)
                    ->assertSee($this->component1->name)
                    ->assertSee('$399.99')
                    ->assertSee('In Stock (10)')
                    ->assertSee('Intel')
                    ->assertSee('Add to Cart');

            // Add to cart
            $browser->click('#add-to-cart-btn')
                    ->waitForText('Added to cart')
                    ->assertSee('1', '#cart-count');

            // Continue shopping
            $browser->visit('/shop')
                    ->click('[data-category="gpu"]')
                    ->click('[data-product-id="' . $this->component2->id . '"]')
                    ->click('#add-to-cart-btn')
                    ->waitForText('Added to cart')
                    ->assertSee('2', '#cart-count');

            // View cart
            $browser->click('#cart-icon')
                    ->waitFor('#cart-dropdown')
                    ->assertSee($this->component1->name)
                    ->assertSee($this->component2->name)
                    ->assertSee('$999.98')
                    ->click('#view-cart-btn')
                    ->waitForLocation('/cart');

            // Update quantities in cart
            $browser->assertSee('Shopping Cart')
                    ->select('[data-item-id="' . $this->component1->id . '"] .quantity-select', '2')
                    ->waitFor('.cart-updated')
                    ->assertSee('$1,399.97'); // Updated total

            // Remove item from cart
            $browser->click('[data-item-id="' . $this->component2->id . '"] .remove-btn')
                    ->waitFor('.item-removed')
                    ->assertDontSee($this->component2->name)
                    ->assertSee('$799.98'); // Updated total

            // Proceed to checkout
            $browser->click('#checkout-btn')
                    ->waitForLocation('/checkout')
                    ->assertSee('Checkout');
        });
    }

    /** @test */
    public function guest_user_can_complete_checkout_process()
    {
        $this->browse(function (Browser $browser) {
            // Add item to cart as guest
            $browser->visit('/shop/component/' . $this->component1->id)
                    ->click('#add-to-cart-btn')
                    ->waitForText('Added to cart')
                    ->click('#cart-icon')
                    ->click('#checkout-btn')
                    ->waitForLocation('/checkout');

            // Step 1: Shipping Information
            $browser->assertSee('Shipping Information')
                    ->type('#shipping-name', 'John Doe')
                    ->type('#shipping-email', '<EMAIL>')
                    ->type('#shipping-phone', '555-1234')
                    ->type('#shipping-address', '123 Main St')
                    ->type('#shipping-city', 'Anytown')
                    ->select('#shipping-state', 'CA')
                    ->type('#shipping-zipcode', '12345')
                    ->click('#continue-to-shipping-method')
                    ->waitForText('Shipping Method');

            // Step 2: Shipping Method
            $browser->assertSee('Select Shipping Method')
                    ->click('#shipping-standard')
                    ->assertSee('Standard Shipping - $9.99')
                    ->click('#continue-to-billing')
                    ->waitForText('Billing Information');

            // Step 3: Billing Information
            $browser->assertSee('Billing Information')
                    ->check('#same-as-shipping')
                    ->waitFor('.billing-copied')
                    ->click('#continue-to-payment')
                    ->waitForText('Payment Information');

            // Step 4: Payment
            $browser->assertSee('Payment Information')
                    ->click('#payment-credit-card')
                    ->type('#card-number', '****************')
                    ->type('#card-expiry', '12/25')
                    ->type('#card-cvv', '123')
                    ->type('#card-name', 'John Doe')
                    ->click('#review-order')
                    ->waitForText('Review Order');

            // Step 5: Review and Complete
            $browser->assertSee('Order Review')
                    ->assertSee($this->component1->name)
                    ->assertSee('$399.99')
                    ->assertSee('Standard Shipping: $9.99')
                    ->assertSee('Tax: $32.00') // Approximate CA tax
                    ->assertSee('Total: $441.98')
                    ->click('#place-order')
                    ->waitForText('Order Confirmation')
                    ->assertSee('Thank you for your order!')
                    ->assertSee('Order #');
        });
    }

    /** @test */
    public function authenticated_user_has_streamlined_checkout()
    {
        $this->browse(function (Browser $browser) {
            $browser->loginAs($this->user)
                    ->visit('/shop/component/' . $this->component1->id)
                    ->click('#add-to-cart-btn')
                    ->waitForText('Added to cart')
                    ->click('#cart-icon')
                    ->click('#checkout-btn')
                    ->waitForLocation('/checkout');

            // User info should be pre-filled
            $browser->assertInputValue('#shipping-name', $this->user->name)
                    ->assertInputValue('#shipping-email', $this->user->email)
                    ->type('#shipping-phone', '555-1234')
                    ->type('#shipping-address', '123 Main St')
                    ->type('#shipping-city', 'Anytown')
                    ->select('#shipping-state', 'CA')
                    ->type('#shipping-zipcode', '12345')
                    ->click('#continue-to-shipping-method')
                    ->waitForText('Shipping Method');

            // Complete checkout faster
            $browser->click('#shipping-express')
                    ->click('#continue-to-billing')
                    ->check('#same-as-shipping')
                    ->click('#continue-to-payment')
                    ->click('#payment-credit-card')
                    ->type('#card-number', '****************')
                    ->type('#card-expiry', '12/25')
                    ->type('#card-cvv', '123')
                    ->type('#card-name', $this->user->name)
                    ->click('#review-order')
                    ->click('#place-order')
                    ->waitForText('Order Confirmation');

            // Should be able to view order in account
            $browser->click('#view-order-history')
                    ->waitForLocation('/account/orders')
                    ->assertSee('Order History')
                    ->assertSee($this->component1->name);
        });
    }

    /** @test */
    public function user_can_search_and_filter_products()
    {
        // Create additional components for testing
        $amdCpu = Component::factory()->create([
            'category_id' => ComponentCategory::where('name', 'CPU')->first()->id,
            'name' => 'AMD Ryzen 7 5800X',
            'brand' => 'AMD',
            'price' => 299.99,
            'stock' => 8,
            'is_active' => true,
        ]);

        $this->browse(function (Browser $browser) use ($amdCpu) {
            $browser->visit('/shop')
                    ->assertSee('Shop Components');

            // Test search functionality
            $browser->type('#search-input', 'Intel')
                    ->click('#search-btn')
                    ->waitFor('.search-results')
                    ->assertSee($this->component1->name)
                    ->assertDontSee($amdCpu->name);

            // Clear search and test filters
            $browser->clear('#search-input')
                    ->click('#search-btn')
                    ->waitFor('.all-products');

            // Test brand filter
            $browser->click('#filters-toggle')
                    ->waitFor('#filters-panel')
                    ->check('[data-brand-filter="AMD"]')
                    ->waitFor('.filtered-results')
                    ->assertSee($amdCpu->name)
                    ->assertDontSee($this->component1->name);

            // Test price range filter
            $browser->uncheck('[data-brand-filter="AMD"]')
                    ->type('#min-price', '200')
                    ->type('#max-price', '400')
                    ->click('#apply-filters')
                    ->waitFor('.filtered-results')
                    ->assertSee($this->component1->name) // $399.99
                    ->assertSee($amdCpu->name) // $299.99
                    ->assertDontSee($this->component2->name); // $599.99

            // Test category filter
            $browser->clear('#min-price')
                    ->clear('#max-price')
                    ->check('[data-category-filter="GPU"]')
                    ->click('#apply-filters')
                    ->waitFor('.filtered-results')
                    ->assertSee($this->component2->name)
                    ->assertDontSee($this->component1->name);

            // Test sorting
            $browser->uncheck('[data-category-filter="GPU"]')
                    ->click('#apply-filters')
                    ->waitFor('.all-products')
                    ->select('#sort-select', 'price-asc')
                    ->waitFor('.sorted-results')
                    ->assertSeeIn('.product-grid .product-card:first-child', $this->component3->name); // Cheapest
        });
    }

    /** @test */
    public function user_can_manage_cart_effectively()
    {
        $this->browse(function (Browser $browser) {
            $browser->loginAs($this->user)
                    ->visit('/shop');

            // Add multiple items to cart
            $browser->visit('/shop/component/' . $this->component1->id)
                    ->click('#add-to-cart-btn')
                    ->waitForText('Added to cart')
                    ->visit('/shop/component/' . $this->component2->id)
                    ->click('#add-to-cart-btn')
                    ->waitForText('Added to cart')
                    ->visit('/shop/component/' . $this->component3->id)
                    ->select('#quantity-select', '2')
                    ->click('#add-to-cart-btn')
                    ->waitForText('Added to cart');

            // View full cart
            $browser->visit('/cart')
                    ->assertSee('Shopping Cart')
                    ->assertSee($this->component1->name)
                    ->assertSee($this->component2->name)
                    ->assertSee($this->component3->name)
                    ->assertSee('Quantity: 2', '[data-item-id="' . $this->component3->id . '"]');

            // Update quantity using quick buttons
            $browser->click('[data-item-id="' . $this->component1->id . '"] .qty-increase')
                    ->waitFor('.cart-updated')
                    ->assertSee('Quantity: 2', '[data-item-id="' . $this->component1->id . '"]');

            $browser->click('[data-item-id="' . $this->component3->id . '"] .qty-decrease')
                    ->waitFor('.cart-updated')
                    ->assertSee('Quantity: 1', '[data-item-id="' . $this->component3->id . '"]');

            // Apply coupon code
            $browser->type('#coupon-code', 'SAVE10')
                    ->click('#apply-coupon')
                    ->waitForText('Coupon applied')
                    ->assertSee('Discount (SAVE10)')
                    ->assertSee('-$');

            // Save cart for later (wishlist functionality)
            $browser->click('[data-item-id="' . $this->component2->id . '"] .save-for-later')
                    ->waitFor('.item-saved')
                    ->assertSee('Saved for Later')
                    ->assertSee($this->component2->name, '#saved-items');

            // Move back to cart
            $browser->click('#saved-items [data-item-id="' . $this->component2->id . '"] .move-to-cart')
                    ->waitFor('.item-moved')
                    ->assertSee($this->component2->name, '#cart-items');

            // Clear entire cart
            $browser->click('#clear-cart')
                    ->waitFor('#confirm-clear-modal')
                    ->click('#confirm-clear')
                    ->waitForText('Cart cleared')
                    ->assertSee('Your cart is empty');
        });
    }

    /** @test */
    public function user_can_handle_checkout_errors_gracefully()
    {
        $this->browse(function (Browser $browser) {
            $browser->visit('/shop/component/' . $this->component1->id)
                    ->click('#add-to-cart-btn')
                    ->waitForText('Added to cart')
                    ->click('#cart-icon')
                    ->click('#checkout-btn')
                    ->waitForLocation('/checkout');

            // Test validation errors
            $browser->click('#continue-to-shipping-method')
                    ->waitFor('.validation-errors')
                    ->assertSee('Name is required')
                    ->assertSee('Email is required')
                    ->assertSee('Address is required');

            // Fill required fields
            $browser->type('#shipping-name', 'John Doe')
                    ->type('#shipping-email', 'invalid-email') // Invalid email
                    ->type('#shipping-phone', '555-1234')
                    ->type('#shipping-address', '123 Main St')
                    ->type('#shipping-city', 'Anytown')
                    ->select('#shipping-state', 'CA')
                    ->type('#shipping-zipcode', '12345')
                    ->click('#continue-to-shipping-method')
                    ->waitFor('.validation-errors')
                    ->assertSee('Please enter a valid email address');

            // Fix email and continue
            $browser->clear('#shipping-email')
                    ->type('#shipping-email', '<EMAIL>')
                    ->click('#continue-to-shipping-method')
                    ->waitForText('Shipping Method');

            // Complete shipping and billing
            $browser->click('#shipping-standard')
                    ->click('#continue-to-billing')
                    ->check('#same-as-shipping')
                    ->click('#continue-to-payment');

            // Test payment errors
            $browser->click('#payment-credit-card')
                    ->type('#card-number', '****************') // Declined card
                    ->type('#card-expiry', '12/25')
                    ->type('#card-cvv', '123')
                    ->type('#card-name', 'John Doe')
                    ->click('#review-order')
                    ->click('#place-order')
                    ->waitFor('.payment-error')
                    ->assertSee('Your card was declined');

            // Try with valid card
            $browser->clear('#card-number')
                    ->type('#card-number', '****************')
                    ->click('#place-order')
                    ->waitForText('Order Confirmation')
                    ->assertSee('Thank you for your order!');
        });
    }

    /** @test */
    public function user_can_track_order_status()
    {
        $this->browse(function (Browser $browser) {
            $browser->loginAs($this->user);

            // Complete a purchase first
            $browser->visit('/shop/component/' . $this->component1->id)
                    ->click('#add-to-cart-btn')
                    ->waitForText('Added to cart')
                    ->click('#cart-icon')
                    ->click('#checkout-btn')
                    ->type('#shipping-name', $this->user->name)
                    ->type('#shipping-email', $this->user->email)
                    ->type('#shipping-phone', '555-1234')
                    ->type('#shipping-address', '123 Main St')
                    ->type('#shipping-city', 'Anytown')
                    ->select('#shipping-state', 'CA')
                    ->type('#shipping-zipcode', '12345')
                    ->click('#continue-to-shipping-method')
                    ->click('#shipping-standard')
                    ->click('#continue-to-billing')
                    ->check('#same-as-shipping')
                    ->click('#continue-to-payment')
                    ->click('#payment-credit-card')
                    ->type('#card-number', '****************')
                    ->type('#card-expiry', '12/25')
                    ->type('#card-cvv', '123')
                    ->type('#card-name', $this->user->name)
                    ->click('#review-order')
                    ->click('#place-order')
                    ->waitForText('Order Confirmation');

            // Get order number from confirmation page
            $orderNumber = $browser->text('#order-number');

            // Track order using order number
            $browser->visit('/track-order')
                    ->type('#order-number', $orderNumber)
                    ->type('#email', $this->user->email)
                    ->click('#track-order-btn')
                    ->waitFor('.order-status')
                    ->assertSee('Order Status: Processing')
                    ->assertSee($this->component1->name)
                    ->assertSee('Estimated Delivery');

            // View detailed tracking from account
            $browser->visit('/account/orders')
                    ->click('.order-row:first-child .track-btn')
                    ->waitFor('.tracking-details')
                    ->assertSee('Order Timeline')
                    ->assertSee('Order Placed')
                    ->assertSee('Payment Confirmed')
                    ->assertSee('Processing');
        });
    }

    /** @test */
    public function shop_interface_is_responsive_on_mobile()
    {
        $this->browse(function (Browser $browser) {
            $browser->resize(375, 667) // iPhone SE dimensions
                    ->visit('/shop')
                    ->assertSee('Shop Components');

            // Mobile navigation
            $browser->click('#mobile-menu-toggle')
                    ->waitFor('#mobile-menu')
                    ->assertSee('Categories')
                    ->click('[data-mobile-category="cpu"]')
                    ->waitFor('.category-products')
                    ->assertSee($this->component1->name);

            // Mobile product grid
            $browser->assertPresent('.product-grid.mobile-layout')
                    ->click('[data-product-id="' . $this->component1->id . '"]')
                    ->waitForLocation('/shop/component/' . $this->component1->id);

            // Mobile product details
            $browser->assertPresent('.product-details.mobile-layout')
                    ->assertSee($this->component1->name)
                    ->assertSee('$399.99')
                    ->click('#mobile-add-to-cart')
                    ->waitForText('Added to cart');

            // Mobile cart
            $browser->click('#mobile-cart-icon')
                    ->waitFor('#mobile-cart-drawer')
                    ->assertSee($this->component1->name)
                    ->assertSee('$399.99')
                    ->click('#mobile-checkout-btn')
                    ->waitForLocation('/checkout');

            // Mobile checkout should be optimized
            $browser->assertPresent('.checkout-form.mobile-layout')
                    ->assertSee('Shipping Information');
        });
    }

    /** @test */
    public function user_can_compare_products()
    {
        // Create similar components for comparison
        $component4 = Component::factory()->create([
            'category_id' => ComponentCategory::where('name', 'CPU')->first()->id,
            'name' => 'AMD Ryzen 7 5800X',
            'brand' => 'AMD',
            'price' => 299.99,
            'stock' => 8,
            'is_active' => true,
        ]);

        $this->browse(function (Browser $browser) use ($component4) {
            $browser->visit('/shop')
                    ->click('[data-category="cpu"]')
                    ->waitFor('.category-products');

            // Add products to comparison
            $browser->click('[data-product-id="' . $this->component1->id . '"] .compare-btn')
                    ->waitForText('Added to comparison')
                    ->assertSee('1', '#compare-count')
                    ->click('[data-product-id="' . $component4->id . '"] .compare-btn')
                    ->waitForText('Added to comparison')
                    ->assertSee('2', '#compare-count');

            // View comparison
            $browser->click('#compare-btn')
                    ->waitFor('#comparison-modal')
                    ->assertSee('Product Comparison')
                    ->assertSee($this->component1->name)
                    ->assertSee($component4->name)
                    ->assertSee('$399.99')
                    ->assertSee('$299.99')
                    ->assertSee('Intel')
                    ->assertSee('AMD');

            // Add to cart from comparison
            $browser->click('[data-compare-product="' . $this->component1->id . '"] .add-to-cart-btn')
                    ->waitForText('Added to cart')
                    ->assertSee('1', '#cart-count');

            // Remove from comparison
            $browser->click('[data-compare-product="' . $component4->id . '"] .remove-btn')
                    ->waitFor('.product-removed')
                    ->assertSee('1', '#compare-count')
                    ->assertDontSee($component4->name, '#comparison-modal');
        });
    }
}