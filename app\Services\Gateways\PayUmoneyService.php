<?php

namespace App\Services\Gateways;

use App\Contracts\PaymentGatewayInterface;
use App\Models\GatewaySetting;
use App\Exceptions\PaymentGatewayException;
use App\Exceptions\GatewayConfigurationException;

class PayUmoneyService implements PaymentGatewayInterface
{
    private array $config;

    public function __construct()
    {
        $this->loadConfiguration();
    }

    public function createPayment(array $data): array
    {
        try {
            $postData = [
                'key' => $this->config['merchant_key'],
                'txnid' => $data['transaction_id'],
                'amount' => $data['amount'],
                'productinfo' => $data['description'],
                'firstname' => auth()->user()->name ?? 'Customer',
                'email' => auth()->user()->email ?? '',
                'phone' => auth()->user()->phone ?? '',
                'surl' => $data['callback_url'] . '/success',
                'furl' => $data['callback_url'] . '/failure',
                'service_provider' => 'payu_paisa',
            ];

            $postData['hash'] = $this->generateHash($postData);
            $postData['action_url'] = $this->config['is_test_mode'] 
                ? 'https://test.payu.in/_payment'
                : 'https://secure.payu.in/_payment';

            return $postData;

        } catch (\Exception $e) {
            throw new PaymentGatewayException(
                'PayUmoney payment creation failed',
                'PAYUMONEY_PAYMENT_FAILED',
                $e->getMessage()
            );
        }
    }

    public function verifyPayment(string $transactionId, array $data): bool
    {
        $hashString = $this->config['salt'] . '|' . $data['status'] . '|||||||||||' . 
                     $data['email'] . '|' . $data['firstname'] . '|' . 
                     $data['productinfo'] . '|' . $data['amount'] . '|' . 
                     $data['txnid'] . '|' . $this->config['merchant_key'];

        $hash = strtolower(hash('sha512', $hashString));
        
        return $hash === $data['hash'] && $data['status'] === 'success';
    }

    public function handleWebhook(array $payload): array
    {
        return [
            'transaction_id' => $payload['txnid'],
            'status' => $payload['status'],
            'amount' => $payload['amount']
        ];
    }

    public function getPaymentStatus(string $transactionId): string
    {
        return 'pending';
    }

    private function generateHash(array $data): string
    {
        $hashString = $this->config['merchant_key'] . '|' . 
                     $data['txnid'] . '|' . 
                     $data['amount'] . '|' . 
                     $data['productinfo'] . '|' . 
                     $data['firstname'] . '|' . 
                     $data['email'] . '|||||||||||' . 
                     $this->config['salt'];

        return strtolower(hash('sha512', $hashString));
    }

    private function loadConfiguration(): void
    {
        $setting = GatewaySetting::byGateway('payumoney')->enabled()->first();
        
        if (!$setting) {
            throw new GatewayConfigurationException('PayUmoney gateway not configured');
        }

        $this->config = $setting->settings;
    }
}