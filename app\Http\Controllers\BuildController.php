<?php

namespace App\Http\Controllers;

use App\Models\Build;
use App\Services\BuilderService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class BuildController extends Controller
{
    protected BuilderService $builderService;

    public function __construct(BuilderService $builderService)
    {
        $this->builderService = $builderService;
    }

    /**
     * Display the PC builder page.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {        
        return view('builder.index');
    }

    /**
     * Display a specific build.
     *
     * @param int $id
     * @return \Illuminate\View\View
     */
    public function show($id)
    {        
        $build = $this->builderService->getBuildById($id, Auth::user());
        
        if (!$build) {
            abort(404, 'Build not found or you do not have permission to view it.');
        }
        
        return view('builder.show', ['buildId' => $id]);
    }

    /**
     * Display a shared build using share token.
     *
     * @param int $id
     * @param string $token
     * @return \Illuminate\View\View
     */
    public function showShared($id, $token)
    {
        $build = $this->builderService->getBuildById($id, Auth::user(), $token);
        
        if (!$build) {
            abort(404, 'Shared build not found or invalid share token.');
        }
        
        return view('builder.show', ['buildId' => $id, 'isShared' => true]);
    }

    /**
     * Save a new build.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function save(Request $request)
    {        
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'components' => 'nullable|array',
            'components.*' => 'integer|exists:components,id',
            'is_public' => 'boolean'
        ]);
        
        $user = Auth::user();
        
        // Convert component IDs to the format expected by BuilderService
        $components = [];
        if ($request->has('components') && is_array($request->components)) {
            foreach ($request->components as $componentId) {
                $components[] = [
                    'component_id' => $componentId,
                    'quantity' => 1
                ];
            }
        }
        
        $buildData = [
            'name' => $request->name,
            'description' => $request->description,
            'is_public' => $request->boolean('is_public', false),
            'components' => $components
        ];
        
        try {
            $build = $this->builderService->createBuild($buildData, $user);
            
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Build saved successfully!',
                    'build' => $build
                ]);
            }
            
            return redirect()->route('builder.show', ['id' => $build->id])
                ->with('message', 'Build saved successfully!');
        } catch (\Exception $e) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to save build: ' . $e->getMessage()
                ], 500);
            }
            
            return redirect()->back()
                ->withInput()
                ->with('error', 'Failed to save build: ' . $e->getMessage());
        }
    }

    /**
     * Update an existing build.
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {        
        // This is handled by Livewire components
        // This route exists for form submissions if needed
        return redirect()->route('builder.show', ['id' => $id]);
    }

    /**
     * Delete a build.
     *
     * @param int $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function delete($id)
    {        
        try {
            $build = Build::findOrFail($id);
            $this->builderService->deleteBuild($build, Auth::user());
            
            return redirect()->route('builder.index')->with('message', 'Build deleted successfully!');
        } catch (\InvalidArgumentException $e) {
            return redirect()->back()->with('error', $e->getMessage());
        }
    }

    /**
     * Clone a build.
     *
     * @param int $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function clone($id)
    {        
        try {
            $originalBuild = Build::findOrFail($id);
            $clonedBuild = $this->builderService->cloneBuild($originalBuild, Auth::user());
            
            return redirect()->route('builder.show', ['id' => $clonedBuild->id])
                ->with('message', 'Build cloned successfully!');
        } catch (\InvalidArgumentException $e) {
            return redirect()->back()->with('error', $e->getMessage());
        }
    }
}