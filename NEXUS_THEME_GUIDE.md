# Nexus PC Theme Guide

This guide explains how to use the consistent Nexus PC theme colors and utilities across your website.

## Color Palette

### Primary Colors (Blue)
- `nexus-primary-300` - Light blue accent (#93c5fd)
- `nexus-primary-400` - Main blue accent (#60a5fa) 
- `nexus-primary-500` - Primary blue (#3b82f6)
- `nexus-primary-600` - Primary blue dark (#2563eb)
- `nexus-primary-700` - Primary blue darker (#1d4ed8)
- `nexus-primary-900` - Dark blue borders (#1e3a8a)

### Secondary Colors (Cyan)
- `nexus-secondary-400` - Cyan accent (#22d3ee)
- `nexus-secondary-500` - Main cyan (#06b6d4)
- `nexus-secondary-600` - Cyan dark (#0891b2)
- `nexus-secondary-900` - Dark cyan borders (#164e63)

### Accent Colors (Purple)
- `nexus-accent-400` - Purple accent (#c084fc)
- `nexus-accent-500` - Main purple (#a855f7)
- `nexus-accent-600` - Purple dark (#9333ea)
- `nexus-accent-700` - Purple darker (#7e22ce)
- `nexus-accent-900` - Dark purple borders (#4c1d95)

### Status Colors
- `nexus-success-400` - Green accent (#4ade80)
- `nexus-success-500` - Main green (#22c55e)
- `nexus-warning-400` - Yellow accent (#facc15)
- `nexus-warning-500` - Main yellow (#eab308)
- `nexus-error-500` - Main red (#ef4444)

### Dark Theme Colors
- `nexus-dark-800` - Card backgrounds (#1e293b)
- `nexus-dark-900` - Main dark background (#0f172a)
- `nexus-dark-950` - Darkest background (#0a0f1c)

### Text Colors
- `nexus-gray-300` - Light text (#d1d5db)
- `nexus-gray-400` - Medium text (#9ca3af)

## Usage Examples

### Buttons
```html
<!-- Primary Button -->
<button class="px-8 py-4 bg-gradient-to-r from-nexus-primary-600 to-nexus-secondary-600 rounded-lg font-semibold tech-font text-lg hover:from-nexus-primary-700 hover:to-nexus-secondary-700 transition-all duration-300 transform hover:scale-105 glow">
    PRIMARY ACTION
</button>

<!-- Secondary Button -->
<button class="px-6 py-3 bg-transparent border-2 border-nexus-primary-500 rounded-lg font-semibold tech-font text-sm hover:bg-nexus-bg-blue-light transition-all duration-300 transform hover:scale-105">
    SECONDARY ACTION
</button>

<!-- Accent Button -->
<button class="px-6 py-2 bg-nexus-accent-600 rounded-lg font-semibold tech-font text-sm hover:bg-nexus-accent-700 transition-all duration-300 transform hover:scale-105">
    ACCENT ACTION
</button>
```

### Cards
```html
<!-- Feature Card -->
<div class="feature-card p-8 rounded-xl bg-gradient-to-br from-nexus-dark-800 to-nexus-dark-900 border border-nexus-border-blue-light hover:border-nexus-border-blue-strong transition-all duration-300 hover:shadow-glow-blue">
    <div class="w-14 h-14 bg-nexus-bg-blue-medium rounded-lg flex items-center justify-center mb-6">
        <!-- Icon -->
    </div>
    <h3 class="tech-font text-xl font-bold mb-3 text-white">Feature Title</h3>
    <p class="text-nexus-gray-400">Feature description text.</p>
</div>

<!-- Stat Card -->
<div class="stat-card p-6 rounded-xl glow">
    <div class="flex items-center justify-between mb-3">
        <h3 class="tech-font text-sm font-semibold text-nexus-primary-400">METRIC NAME</h3>
        <div class="w-2 h-2 bg-nexus-success-400 rounded-full animate-pulse"></div>
    </div>
    <div class="text-3xl font-bold text-white mb-2">
        <span id="counter">0</span>
        <span class="text-lg text-nexus-gray-400">UNIT</span>
    </div>
    <p class="text-xs text-nexus-gray-400">Description</p>
</div>
```

### Text Styling
```html
<!-- Main Headline -->
<h1 class="tech-font text-6xl md:text-8xl font-black mb-6 neon-text">
    <span class="nexus-text-gradient">NEXUS</span>
    <span class="text-white">PC</span>
</h1>

<!-- Section Title -->
<h2 class="tech-font text-4xl font-bold mb-4 text-nexus-primary-400">SECTION TITLE</h2>

<!-- Body Text -->
<p class="text-nexus-gray-400 max-w-3xl mx-auto">Body text content goes here.</p>
```

### Component Icons
```html
<div class="component-icon cursor-pointer" data-tooltip="cpu">
    <div class="w-20 h-20 bg-gradient-to-br from-nexus-primary-500 to-nexus-secondary-500 rounded-lg flex items-center justify-center relative">
        <!-- SVG Icon -->
        <div class="absolute inset-0 rounded-lg border-2 border-nexus-primary-400 opacity-0 hover:opacity-100 transition-opacity duration-300"></div>
    </div>
    <p class="tech-font text-sm mt-3 text-center text-nexus-gray-400">CPU</p>
</div>
```

### Tables
```html
<table class="w-full border-collapse">
    <thead>
        <tr>
            <th class="p-4 text-center bg-nexus-dark-800 rounded-t-xl">
                <div class="tech-font text-xl font-bold text-nexus-secondary-400 mb-2">PLAN NAME</div>
                <div class="text-white text-2xl font-bold mb-1">$1,299</div>
                <div class="text-nexus-gray-400 text-sm">Description</div>
            </th>
        </tr>
    </thead>
    <tbody class="divide-y divide-nexus-dark-800">
        <tr class="hover:bg-nexus-dark-800/50 transition-colors duration-150">
            <td class="p-4 text-center text-nexus-gray-400">Content</td>
        </tr>
    </tbody>
</table>
```

## Custom Utility Classes

### Glow Effects
- `.glow` - Blue glow effect
- `.glow-cyan` - Cyan glow effect  
- `.glow-purple` - Purple glow effect
- `.glow-green` - Green glow effect

### Text Effects
- `.neon-text` - Blue neon text shadow
- `.neon-text-cyan` - Cyan neon text shadow
- `.neon-text-purple` - Purple neon text shadow

### Backgrounds
- `.cyber-grid` - Animated grid background
- `.nexus-gradient-primary` - Primary gradient (blue to cyan)
- `.nexus-gradient-accent` - Accent gradient (purple to blue)
- `.nexus-text-gradient` - Text gradient effect

### Animations
- `.floating` - Floating animation
- `.pulse-ring` - Pulsing ring animation
- `.component-icon` - Interactive component hover effects

## Font Classes
- `.tech-font` - Orbitron font for headings and tech elements
- `.content-font` - Inter font for body text

## Box Shadow Utilities
- `shadow-glow-blue` - Blue glow shadow
- `shadow-glow-cyan` - Cyan glow shadow
- `shadow-glow-purple` - Purple glow shadow
- `shadow-glow-green` - Green glow shadow
- `shadow-glow-blue-lg` - Large blue glow shadow

## Border Utilities
- `border-nexus-border-blue-light` - Light blue border with opacity
- `border-nexus-border-blue-medium` - Medium blue border with opacity
- `border-nexus-border-blue-strong` - Strong blue border with opacity

## Background Utilities
- `bg-nexus-bg-blue-light` - Light blue background with opacity
- `bg-nexus-bg-blue-medium` - Medium blue background with opacity

## Import the Theme

Add to your main CSS file:
```css
@import './nexus-theme.css';
```

Or include in your Blade layout:
```html
<link rel="stylesheet" href="{{ asset('css/nexus-theme.css') }}">
```

## Dark Mode Support

The theme includes dark mode variants. Toggle with:
```html
<html class="dark">
```

All colors automatically adapt to dark mode context.