<?php $__env->startSection('title', 'Shop Components'); ?>

<?php $__env->startSection('content'); ?>
    <div class="bg-gray-100 min-h-screen">
        <div class="py-8">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <h1 class="text-3xl font-bold text-gray-900 mb-8">Shop Components</h1>
            </div>
            <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('shop.product-list');

$__html = app('livewire')->mount($__name, $__params, 'lw-4232216116-0', $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
        </div>
    </div>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\lara\www\pc-builder\resources\views/shop/index.blade.php ENDPATH**/ ?>