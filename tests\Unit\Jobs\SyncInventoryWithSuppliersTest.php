<?php

namespace Tests\Unit\Jobs;

use App\Jobs\SyncInventoryWithSuppliers;
use App\Services\InventoryService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Log;
use Tests\TestCase;

class SyncInventoryWithSuppliersTest extends TestCase
{
    use RefreshDatabase;

    public function test_job_syncs_with_all_suppliers()
    {
        $mockService = $this->createMock(InventoryService::class);
        $mockService->expects($this->exactly(3)) // 3 suppliers
            ->method('syncWithSupplier')
            ->willReturn([
                'updated' => 2,
                'new' => 1,
                'failed' => 0,
                'errors' => []
            ]);

        $this->app->instance(InventoryService::class, $mockService);

        Log::shouldReceive('info')->times(8); // Start + 3 suppliers + 3 completions + total + warning

        $job = new SyncInventoryWithSuppliers();
        $job->handle($mockService);
    }

    public function test_job_handles_supplier_failures()
    {
        $mockService = $this->createMock(InventoryService::class);
        $mockService->method('syncWithSupplier')
            ->willReturn([
                'updated' => 1,
                'new' => 0,
                'failed' => 1,
                'errors' => ['Test error']
            ]);

        Log::shouldReceive('info')->atLeast()->once();

        $job = new SyncInventoryWithSuppliers();
        $job->handle($mockService);
    }

    public function test_job_logs_completion_summary()
    {
        $mockService = $this->createMock(InventoryService::class);
        $mockService->method('syncWithSupplier')
            ->willReturn([
                'updated' => 5,
                'new' => 2,
                'failed' => 1,
                'errors' => ['Some error']
            ]);

        Log::shouldReceive('info')
            ->once()
            ->with('Starting inventory sync with suppliers');

        Log::shouldReceive('info')
            ->once()
            ->with('Inventory sync completed', [
                'updated' => 15, // 5 * 3 suppliers
                'new' => 6,     // 2 * 3 suppliers
                'failed' => 3,  // 1 * 3 suppliers
                'errors' => ['Some error', 'Some error', 'Some error']
            ]);

        Log::shouldReceive('info')->atLeast()->once(); // Other log calls

        $job = new SyncInventoryWithSuppliers();
        $job->handle($mockService);
    }

    public function test_job_handles_failure()
    {
        $exception = new \Exception('Job failed');
        
        Log::shouldReceive('error')
            ->once()
            ->with('Inventory sync job failed: Job failed');

        $job = new SyncInventoryWithSuppliers();
        $job->failed($exception);
    }

    public function test_job_has_correct_configuration()
    {
        $job = new SyncInventoryWithSuppliers();

        $this->assertEquals(600, $job->timeout);
        $this->assertEquals(2, $job->tries);
    }

    public function test_job_generates_mock_supplier_data()
    {
        $job = new SyncInventoryWithSuppliers();
        
        // Use reflection to access protected method
        $reflection = new \ReflectionClass($job);
        $method = $reflection->getMethod('getMockSupplierData');
        $method->setAccessible(true);
        
        $supplierAData = $method->invoke($job, 'supplier_a');
        $supplierBData = $method->invoke($job, 'supplier_b');
        $supplierCData = $method->invoke($job, 'supplier_c');
        $unknownData = $method->invoke($job, 'unknown');

        $this->assertIsArray($supplierAData);
        $this->assertNotEmpty($supplierAData);
        $this->assertArrayHasKey('brand', $supplierAData[0]);
        $this->assertArrayHasKey('model', $supplierAData[0]);
        $this->assertArrayHasKey('stock', $supplierAData[0]);
        $this->assertArrayHasKey('price', $supplierAData[0]);

        $this->assertIsArray($supplierBData);
        $this->assertNotEmpty($supplierBData);

        $this->assertIsArray($supplierCData);
        $this->assertNotEmpty($supplierCData);

        $this->assertIsArray($unknownData);
        $this->assertEmpty($unknownData);
    }
}