<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

class PaymentAdminMiddleware
{
    /**
     * Handle an incoming request.
     * Requirements: 4.1, 5.1
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Check if user is authenticated
        if (!auth()->check()) {
            $this->logUnauthorizedAccess($request, 'unauthenticated');
            
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'error' => [
                        'code' => 'UNAUTHENTICATED',
                        'message' => 'Authentication required'
                    ]
                ], 401);
            }
            
            return redirect()->route('admin.login')
                           ->with('error', 'Please login to access payment management');
        }

        $user = auth()->user();

        // Check if user is admin
        if ($user->role !== 'admin') {
            $this->logUnauthorizedAccess($request, 'insufficient_role', $user);
            
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'error' => [
                        'code' => 'INSUFFICIENT_PERMISSIONS',
                        'message' => 'Admin access required'
                    ]
                ], 403);
            }
            
            return redirect()->route('dashboard')
                           ->with('error', 'You do not have permission to access payment management');
        }

        // Check if user account is active
        if ($user->status !== 'active') {
            $this->logUnauthorizedAccess($request, 'inactive_account', $user);
            
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'error' => [
                        'code' => 'ACCOUNT_INACTIVE',
                        'message' => 'Account is not active'
                    ]
                ], 403);
            }
            
            return redirect()->route('admin.login')
                           ->with('error', 'Your account is not active');
        }

        // Check for specific payment permissions if they exist
        if (!$this->hasPaymentPermissions($user, $request)) {
            $this->logUnauthorizedAccess($request, 'insufficient_payment_permissions', $user);
            
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'error' => [
                        'code' => 'INSUFFICIENT_PAYMENT_PERMISSIONS',
                        'message' => 'Payment management permissions required'
                    ]
                ], 403);
            }
            
            return redirect()->route('admin.dashboard')
                           ->with('error', 'You do not have permission to manage payments');
        }

        // Log successful access for audit trail
        $this->logSuccessfulAccess($request, $user);

        return $next($request);
    }

    /**
     * Check if user has payment management permissions
     */
    protected function hasPaymentPermissions($user, Request $request): bool
    {
        // For now, all admins have payment permissions
        // This can be extended to check specific permissions from a permissions table
        
        $route = $request->route();
        if (!$route) {
            return true;
        }

        $routeName = $route->getName();
        
        // Define route-specific permissions
        $restrictedRoutes = [
            'admin.gateways.update' => 'manage_payment_gateways',
            'admin.gateways.store' => 'manage_payment_gateways',
            'admin.gateways.destroy' => 'manage_payment_gateways',
            'admin.transactions.destroy' => 'manage_transactions',
            'admin.transactions.refund' => 'manage_transactions',
        ];

        // If route requires specific permission, check it
        if (isset($restrictedRoutes[$routeName])) {
            $requiredPermission = $restrictedRoutes[$routeName];
            
            // Check if user has the specific permission
            // This would typically check a user_permissions or role_permissions table
            // For now, we'll assume all active admins have all permissions
            return $this->userHasPermission($user, $requiredPermission);
        }

        return true; // Allow access to non-restricted routes
    }

    /**
     * Check if user has specific permission
     */
    protected function userHasPermission($user, string $permission): bool
    {
        // This is a placeholder for a more sophisticated permission system
        // In a real application, you would check against a permissions table
        
        // For now, all active admins have all payment permissions
        return $user->role === 'admin' && $user->status === 'active';
        
        // Future implementation might look like:
        // return $user->permissions()->where('name', $permission)->exists();
        // or
        // return $user->hasPermissionTo($permission);
    }

    /**
     * Log unauthorized access attempts
     */
    protected function logUnauthorizedAccess(Request $request, string $reason, $user = null): void
    {
        $logData = [
            'reason' => $reason,
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'path' => $request->path(),
            'method' => $request->method(),
            'timestamp' => now()->toISOString()
        ];

        if ($user) {
            $logData['user_id'] = $user->id;
            $logData['user_email'] = $user->email;
            $logData['user_role'] = $user->role;
        }

        Log::warning('Unauthorized payment admin access attempt', $logData);
        
        // Also log to security channel
        Log::channel('security')->warning('Payment admin access denied', [
            'reason' => $reason,
            'user_id' => $user->id ?? null,
            'ip' => $request->ip(),
            'path' => $request->path(),
            'timestamp' => now()->toISOString()
        ]);
    }

    /**
     * Log successful access for audit trail
     */
    protected function logSuccessfulAccess(Request $request, $user): void
    {
        // Only log sensitive operations, not every page view
        $sensitiveRoutes = [
            'admin.gateways.update',
            'admin.gateways.store',
            'admin.gateways.destroy',
            'admin.transactions.destroy',
            'admin.transactions.refund',
            'admin.gateways.settings.update'
        ];

        $route = $request->route();
        $routeName = $route ? $route->getName() : null;

        if (in_array($routeName, $sensitiveRoutes)) {
            Log::info('Payment admin action performed', [
                'user_id' => $user->id,
                'user_email' => $user->email,
                'action' => $routeName,
                'path' => $request->path(),
                'method' => $request->method(),
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'timestamp' => now()->toISOString()
            ]);

            // Also log to security channel for audit
            Log::channel('security')->info('Payment admin action', [
                'user_id' => $user->id,
                'action' => $routeName,
                'ip' => $request->ip(),
                'timestamp' => now()->toISOString()
            ]);
        }
    }
}