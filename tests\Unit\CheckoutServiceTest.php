<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Services\CheckoutService;
use App\Services\CartService;
use App\Models\Cart;
use App\Models\Order;
use App\Models\User;
use App\Models\Component;
use App\Models\ComponentCategory;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Validation\ValidationException;

class CheckoutServiceTest extends TestCase
{
    use RefreshDatabase;

    protected CheckoutService $checkoutService;
    protected CartService $cartService;
    protected User $user;
    protected Cart $cart;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->cartService = app(CartService::class);
        $this->checkoutService = new CheckoutService($this->cartService);
        
        $this->user = User::factory()->create();
        $this->cart = Cart::factory()->create(['user_id' => $this->user->id]);
    }

    public function test_can_generate_unique_order_number()
    {
        $orderNumber1 = $this->checkoutService->generateOrderNumber();
        $orderNumber2 = $this->checkoutService->generateOrderNumber();

        $this->assertNotEquals($orderNumber1, $orderNumber2);
        $this->assertStringStartsWith('PCB', $orderNumber1);
        $this->assertStringStartsWith('PCB', $orderNumber2);
        $this->assertEquals(20, strlen($orderNumber1)); // PCB + 17 digits (YmdHis + 3 random)
    }

    public function test_can_calculate_shipping_cost()
    {
        // Test free shipping over $100
        $this->cart->update(['total' => 150.00]);
        $address = ['country' => 'US', 'state' => 'CA'];
        
        $shipping = $this->checkoutService->calculateShipping($this->cart, $address);
        $this->assertEquals(0, $shipping);

        // Test standard shipping under $100
        $this->cart->update(['total' => 50.00]);
        $shipping = $this->checkoutService->calculateShipping($this->cart, $address);
        $this->assertEquals(9.99, $shipping);

        // Test express shipping for certain states
        $address = ['country' => 'US', 'state' => 'CA'];
        $shipping = $this->checkoutService->calculateShipping($this->cart, $address);
        $this->assertEquals(9.99, $shipping); // Standard rate when under $100

        // Test international shipping
        $address = ['country' => 'CA', 'state' => 'ON'];
        $shipping = $this->checkoutService->calculateShipping($this->cart, $address);
        $this->assertEquals(29.99, $shipping);
    }

    public function test_can_validate_address()
    {
        $validAddress = [
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'phone' => '555-1234',
            'address' => '123 Main St',
            'city' => 'Anytown',
            'state' => 'CA',
            'zipcode' => '12345',
            'country' => 'US',
        ];

        $this->assertTrue($this->checkoutService->validateAddress($validAddress));
    }

    public function test_address_validation_fails_with_missing_fields()
    {
        $invalidAddress = [
            'name' => 'John Doe',
            // Missing required fields
        ];

        $this->expectException(ValidationException::class);
        $this->checkoutService->validateAddress($invalidAddress);
    }

    public function test_can_update_order_status()
    {
        $order = Order::factory()->create(['status' => 'pending']);

        $updatedOrder = $this->checkoutService->updateOrderStatus(
            $order, 
            'processing', 
            'Order is being processed'
        );

        $this->assertEquals('processing', $updatedOrder->status);
        $this->assertStringContainsString('Order is being processed', $updatedOrder->notes);
    }

    public function test_cannot_update_order_with_invalid_status()
    {
        $order = Order::factory()->create();

        $this->expectException(\InvalidArgumentException::class);
        $this->checkoutService->updateOrderStatus($order, 'invalid_status');
    }

    public function test_can_cancel_order()
    {
        $order = Order::factory()->create(['status' => 'pending']);

        $cancelledOrder = $this->checkoutService->cancelOrder($order, 'Customer request');

        $this->assertEquals('canceled', $cancelledOrder->status);
        $this->assertStringContainsString('Order canceled: Customer request', $cancelledOrder->notes);
    }

    public function test_cannot_cancel_completed_order()
    {
        $order = Order::factory()->create(['status' => 'completed']);

        $this->expectException(\InvalidArgumentException::class);
        $this->checkoutService->cancelOrder($order);
    }

    public function test_cannot_cancel_already_cancelled_order()
    {
        $order = Order::factory()->create(['status' => 'canceled']);

        $this->expectException(\InvalidArgumentException::class);
        $this->checkoutService->cancelOrder($order);
    }

    public function test_can_get_order_by_number()
    {
        $order = Order::factory()->create(['order_number' => 'PCB20240101123456789']);

        $foundOrder = $this->checkoutService->getOrderByNumber('PCB20240101123456789');
        $this->assertNotNull($foundOrder);
        $this->assertEquals($order->id, $foundOrder->id);

        $notFoundOrder = $this->checkoutService->getOrderByNumber('NONEXISTENT');
        $this->assertNull($notFoundOrder);
    }

    public function test_can_get_shipping_options()
    {
        // Test US address with subtotal under $100
        $this->cart->update(['total' => 50.00]);
        $address = ['country' => 'US', 'state' => 'CA'];

        $options = $this->checkoutService->getShippingOptions($this->cart, $address);

        $this->assertIsArray($options);
        $this->assertGreaterThan(0, count($options));
        
        // Should have standard, express, and overnight options
        $optionIds = array_column($options, 'id');
        $this->assertContains('standard', $optionIds);
        $this->assertContains('express', $optionIds); // CA is express state
        $this->assertContains('overnight', $optionIds);

        // Test with free shipping threshold
        $this->cart->update(['total' => 150.00]);
        $options = $this->checkoutService->getShippingOptions($this->cart, $address);
        
        $freeOption = collect($options)->firstWhere('id', 'free');
        $this->assertNotNull($freeOption);
        $this->assertEquals(0, $freeOption['cost']);
    }

    public function test_can_get_international_shipping_options()
    {
        $this->cart->update(['total' => 50.00]);
        $address = ['country' => 'CA', 'state' => 'ON'];

        $options = $this->checkoutService->getShippingOptions($this->cart, $address);

        $this->assertIsArray($options);
        $this->assertEquals(1, count($options)); // Only international option
        
        $option = $options[0];
        $this->assertEquals('international', $option['id']);
        $this->assertEquals(29.99, $option['cost']);
    }

    public function test_can_estimate_delivery_date()
    {
        $address = ['country' => 'US', 'state' => 'CA'];

        $overnightDate = $this->checkoutService->estimateDeliveryDate('overnight', $address);
        $expressDate = $this->checkoutService->estimateDeliveryDate('express', $address);
        $standardDate = $this->checkoutService->estimateDeliveryDate('standard', $address);
        $internationalDate = $this->checkoutService->estimateDeliveryDate('international', $address);

        // Overnight should be sooner than express, express sooner than standard, etc.
        $this->assertTrue($overnightDate->lt($expressDate));
        $this->assertTrue($expressDate->lt($standardDate));
        $this->assertTrue($standardDate->lt($internationalDate));
    }

    public function test_process_checkout_fails_with_empty_cart()
    {
        $shippingData = [
            'shipping' => [
                'name' => 'John Doe',
                'email' => '<EMAIL>',
                'phone' => '555-1234',
                'address' => '123 Main St',
                'city' => 'Anytown',
                'state' => 'CA',
                'zipcode' => '12345',
                'country' => 'US',
            ]
        ];

        $paymentData = [
            'payment_method' => 'credit_card',
            'card_number' => '****************',
            'card_expiry' => '12/25',
            'card_cvv' => '123',
            'card_name' => 'John Doe',
        ];

        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Cart is empty');
        
        $this->checkoutService->processCheckout($this->cart, $shippingData, $paymentData);
    }

    public function test_process_checkout_fails_with_invalid_shipping_data()
    {
        // Add item to cart with proper stock and active status
        $category = ComponentCategory::factory()->create();
        $component = Component::factory()->create([
            'category_id' => $category->id,
            'stock' => 10,
            'is_active' => true,
        ]);
        $this->cart->addItem($component, 1);

        $invalidShippingData = [
            'shipping' => [
                'name' => 'John Doe',
                // Missing required fields
            ]
        ];

        $paymentData = [
            'payment_method' => 'credit_card',
            'card_number' => '****************',
            'card_expiry' => '12/25',
            'card_cvv' => '123',
            'card_name' => 'John Doe',
        ];

        $this->expectException(ValidationException::class);
        
        $this->checkoutService->processCheckout($this->cart, $invalidShippingData, $paymentData);
    }

    public function test_process_checkout_fails_with_invalid_payment_data()
    {
        // Add item to cart with proper stock and active status
        $category = ComponentCategory::factory()->create();
        $component = Component::factory()->create([
            'category_id' => $category->id,
            'stock' => 10,
            'is_active' => true,
        ]);
        $this->cart->addItem($component, 1);

        $shippingData = [
            'shipping' => [
                'name' => 'John Doe',
                'email' => '<EMAIL>',
                'phone' => '555-1234',
                'address' => '123 Main St',
                'city' => 'Anytown',
                'state' => 'CA',
                'zipcode' => '12345',
                'country' => 'US',
            ]
        ];

        $invalidPaymentData = [
            'payment_method' => 'credit_card',
            // Missing required card fields
        ];

        $this->expectException(ValidationException::class);
        
        $this->checkoutService->processCheckout($this->cart, $shippingData, $invalidPaymentData);
    }

    public function test_can_process_successful_checkout()
    {
        // Add item to cart
        $category = ComponentCategory::factory()->create();
        $component = Component::factory()->create([
            'category_id' => $category->id,
            'price' => 40.00,
            'stock' => 10,
            'is_active' => true,
        ]);
        $this->cart->addItem($component, 2);
        $this->cart->updateTotal();

        $shippingData = [
            'shipping' => [
                'name' => 'John Doe',
                'email' => '<EMAIL>',
                'phone' => '555-1234',
                'address' => '123 Main St',
                'city' => 'Anytown',
                'state' => 'CA',
                'zipcode' => '12345',
                'country' => 'US',
            ]
        ];

        $paymentData = [
            'payment_method' => 'credit_card',
            'card_number' => '****************',
            'card_expiry' => '12/25',
            'card_cvv' => '123',
            'card_name' => 'John Doe',
        ];

        $order = $this->checkoutService->processCheckout($this->cart, $shippingData, $paymentData);

        $this->assertInstanceOf(Order::class, $order);
        $this->assertEquals('pending', $order->status);
        $this->assertEquals($this->user->id, $order->user_id);
        $this->assertStringStartsWith('PCB', $order->order_number);
        
        // Check order totals
        $this->assertEquals(80.00, $order->subtotal); // 2 * $40
        $this->assertEquals(6.40, $order->tax); // 8% of subtotal
        $this->assertEquals(9.99, $order->shipping); // Standard shipping
        $this->assertEquals(96.39, $order->total); // subtotal + tax + shipping

        // Check shipping address
        $this->assertEquals('John Doe', $order->shipping_name);
        $this->assertEquals('<EMAIL>', $order->shipping_email);
        $this->assertEquals('123 Main St', $order->shipping_address);

        // Check order items were created
        $this->assertEquals(1, $order->items()->count());
        $orderItem = $order->items()->first();
        $this->assertEquals($component->id, $orderItem->component_id);
        $this->assertEquals(2, $orderItem->quantity);
        $this->assertEquals(40.00, $orderItem->price);

        // Check cart was cleared
        $this->cart->refresh();
        $this->assertEquals(0, $this->cart->items()->count());
        $this->assertEquals(0, $this->cart->total);
    }
}