@extends('layouts.app')

@section('title', $category->name)

@section('content')
    <x-breadcrumb :segments="$breadcrumbs" :pageTitle="$pageTitle" :metaDescription="$metaDescription" />
    <div class="container mx-auto px-4 py-6 max-w-6xl">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 relative">
            <!-- Main Content -->
            <div class="lg:col-span-2 space-y-6">
                <!-- Top Ad Placement -->
                {{-- <x-ad-placement page-type="blog" section="category-top" /> --}}

                @if ($posts->isEmpty())
                    <div
                        class="rounded-lg bg-white dark:bg-bg-dark shadow-md border border-border-light dark:border-border-dark">
                        <div class="p-6 flex flex-col items-center text-center">
                            <p class="text-xl font-semibold text-text-primary-light dark:text-text-primary-dark">No blog
                                posts</p>
                            <p class="text-text-secondary-light dark:text-text-secondary-dark mt-2">Check back soon</p>
                        </div>
                    </div>
                @else
                    <h2 class="text-4xl font-bold mb-8 text-text-primary-light dark:text-text-primary-dark">
                        {{ $category->name }}</h2>
                    <p class="mb-8 text-text-secondary-light dark:text-text-secondary-dark leading-relaxed">
                        {{ $category->description }}</p>

                    <div class="grid md:grid-cols-2 gap-6">
                        @foreach ($posts as $post)
                            <article
                                class="rounded-lg bg-white dark:bg-bg-dark shadow-xl hover:shadow-2xl hover:scale-105 transition-all duration-300 border border-border-light dark:border-border-dark overflow-hidden">
                                @if ($post->featured_image)
                                    <figure class="relative">
                                        <picture>
                                            <source srcset="{{ uploads_url($post->featured_image) }}">
                                            <img src="{{ uploads_url('assets/default-blog.webp') }}"
                                                alt="{{ $post->title }}"
                                                class="w-full h-48 object-cover transition-transform duration-300 hover:scale-110"
                                                loading="lazy">
                                        </picture>
                                        <div class="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
                                    </figure>
                                @endif

                                <div class="p-6">
                                    <h2 class="text-xl font-bold mb-3 leading-tight">
                                        <a href="{{ route('blog.show', $post->slug) }}"
                                            class="text-text-primary-light dark:text-text-primary-dark hover:text-primary-light dark:hover:text-primary-dark transition-colors duration-200">
                                            {{ $post->title }}
                                        </a>
                                    </h2>

                                    <div
                                        class="flex items-center text-text-secondary-light dark:text-text-secondary-dark text-sm mb-4">
                                        <div class="relative">
                                            <div
                                                class="w-8 h-8 rounded-full bg-primary-light dark:bg-primary-dark flex items-center justify-center">
                                                <span class="text-white text-xs font-semibold">
                                                    {{ substr($post->author->name, 0, 1) }}
                                                </span>
                                                {{-- <img src="{{ get_user_avatar($post->author) }}"
                                                    alt="{{ $post->author->name }}" 
                                                    class="w-8 h-8 rounded-full object-cover" 
                                                    loading="lazy"> --}}
                                            </div>
                                        </div>
                                        <span class="ml-3 font-medium">{{ $post->author->name }}</span>
                                        <div class="mx-3 text-border-light dark:text-border-dark">•</div>
                                        <time datetime="{{ optional($post->published_at)->toDateString() }}"
                                            class="text-xs">
                                            {{ optional($post->published_at)->diffForHumans() }}
                                        </time>
                                    </div>

                                    <p class="text-text-secondary-light dark:text-text-secondary-dark leading-relaxed mb-4">
                                        {{ $post->excerpt ?? Str::limit($post->content, 150) }}
                                    </p>

                                    @if ($post->tags && $post->tags->count() > 0)
                                        <div class="flex flex-wrap gap-2">
                                            @foreach ($post->tags as $tag)
                                                <a href="{{ route('blog.tag', $tag->slug) }}"
                                                    class="px-3 py-1 text-xs font-medium rounded-full 
                                                           border border-primary-light dark:border-primary-dark 
                                                           text-primary-light dark:text-primary-dark 
                                                           hover:bg-primary-light dark:hover:bg-primary-dark 
                                                           hover:text-white dark:hover:text-white 
                                                           transition-all duration-200 
                                                           hover:shadow-md">
                                                    {{ $tag->name }}
                                                </a>
                                            @endforeach
                                        </div>
                                    @endif
                                </div>
                            </article>
                            <!-- between_posts Ad Placement (after every 4th post) -->
                            {{-- @if ($loop->iteration % 4 == 0 && !$loop->last)
                                <div class="w-auto inline-block">
                                    <x-ad-placement page-type="blog" section="between_posts" />
                                </div>
                            @endif --}}
                        @endforeach
                    </div>
                    <!-- Bottom Ad Placement -->
                    {{-- <x-ad-placement page-type="blog" section="category-bottom" /> --}}
                    <div class="mt-12 flex justify-center">
                        <div class="pagination-wrapper">
                            {{ $posts->links() }}
                        </div>
                    </div>
                @endif
            </div>

            <!-- Sidebar -->
            <div class="lg:col-span-1">
                <div class="lg:sticky lg:top-20">
                    <div
                        class="bg-white dark:bg-bg-dark rounded-lg shadow-lg border border-border-light dark:border-border-dark p-6">
                        @include('layouts.partials.blog-sidebar')
                    </div>
                </div>
            </div>
        </div>
    </div>

    <style>
        /* Custom pagination styles */
        .pagination-wrapper .pagination {
            @apply flex items-center justify-center space-x-2;
        }

        .pagination-wrapper .pagination .page-link {
            @apply px-3 py-2 text-sm font-medium rounded-md border border-border-light dark:border-border-dark text-text-secondary-light dark:text-text-secondary-dark bg-white dark:bg-bg-dark hover:bg-primary-light dark:hover:bg-primary-dark hover:text-white dark:hover:text-white hover:border-primary-light dark:hover:border-primary-dark transition-all duration-200;
        }

        .pagination-wrapper .pagination .page-item.active .page-link {
            @apply bg-primary-light dark:bg-primary-dark text-white border-primary-light dark:border-primary-dark;
        }

        .pagination-wrapper .pagination .page-item.disabled .page-link {
            @apply opacity-50 cursor-not-allowed hover:bg-white dark:hover:bg-bg-dark hover:text-text-secondary-light dark:hover:text-text-secondary-dark hover:border-border-light dark:hover:border-border-dark;
        }

        /* Enhanced card hover effects */
        article:hover {
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        }

        .dark article:hover {
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.4);
        }

        /* Smooth transitions for theme switching */
        * {
            transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
        }

        /* Category specific styling */
        .category-header {
            background: linear-gradient(135deg,
                    theme('colors.primary-light') 0%,
                    theme('colors.accent-light') 100%);
        }

        .dark .category-header {
            background: linear-gradient(135deg,
                    theme('colors.primary-dark') 0%,
                    theme('colors.accent-dark') 100%);
        }
    </style>
@endsection
